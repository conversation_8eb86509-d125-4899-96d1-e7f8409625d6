{"ConfirmedOrders.display": "Display", "ConfirmedOrders.goToPage": "Go to page", "ConfirmedOrders.itemName": "Confirmed ", "ConfirmedOrders.orderList.action": "Action", "ConfirmedOrders.orderList.createdAt": "Created date", "ConfirmedOrders.orderList.customer": "Customer", "ConfirmedOrders.orderList.orderID": "Order ID", "ConfirmedOrders.orderList.revenue": "Revenue", "ConfirmedOrders.orderList.status": "Status", "ConfirmedOrders.page": "Page", "ConfirmedOrders.searchField": "Search...", "ConfirmedOrders.status": "Status", "HOUR-ABBREVIATION": "h", "MONTHS-SHORT.1": "Jan", "MONTHS-SHORT.10": "Oct", "MONTHS-SHORT.11": "Nov", "MONTHS-SHORT.12": "Dec", "MONTHS-SHORT.2": "Feb", "MONTHS-SHORT.3": "Mar", "MONTHS-SHORT.4": "Apr", "MONTHS-SHORT.5": "May", "MONTHS-SHORT.6": "Jun", "MONTHS-SHORT.7": "Jul", "MONTHS-SHORT.8": "Aug", "MONTHS-SHORT.9": "Sep", "MONTHS.1": "January", "MONTHS.10": "October", "MONTHS.11": "November", "MONTHS.12": "December", "MONTHS.2": "February", "MONTHS.3": "March", "MONTHS.4": "April", "MONTHS.5": "May", "MONTHS.6": "June", "MONTHS.7": "July", "MONTHS.8": "August", "MONTHS.9": "September", "WEEKDAYS-METRIC.0": "Monday", "WEEKDAYS-METRIC.1": "Tuesday", "WEEKDAYS-METRIC.2": "Wednesday", "WEEKDAYS-METRIC.3": "Thursday", "WEEKDAYS-METRIC.4": "Friday", "WEEKDAYS-METRIC.5": "Saturday", "WEEKDAYS-METRIC.6": "Sunday", "WEEKDAYS-SHORT-METRIC.0": "Mon", "WEEKDAYS-SHORT-METRIC.1": "<PERSON><PERSON>", "WEEKDAYS-SHORT-METRIC.2": "Wed", "WEEKDAYS-SHORT-METRIC.3": "<PERSON>hu", "WEEKDAYS-SHORT-METRIC.4": "<PERSON><PERSON>", "WEEKDAYS-SHORT-METRIC.5": "Sat", "WEEKDAYS-SHORT-METRIC.6": "Sun", "WEEKDAYS-SHORT.0": "Sun", "WEEKDAYS-SHORT.1": "Mon", "WEEKDAYS-SHORT.2": "<PERSON><PERSON>", "WEEKDAYS-SHORT.3": "Wed", "WEEKDAYS-SHORT.4": "<PERSON>hu", "WEEKDAYS-SHORT.5": "<PERSON><PERSON>", "WEEKDAYS-SHORT.6": "Sat", "WEEKDAYS.0": "Sunday", "WEEKDAYS.1": "Monday", "WEEKDAYS.2": "Tuesday", "WEEKDAYS.3": "Wednesday", "WEEKDAYS.4": "Thursday", "WEEKDAYS.5": "Friday", "WEEKDAYS.6": "Saturday", "DAY": "day", "DAYS": "days", "HOUR": "hour", "HOURS": "hours", "MINUTE": "minute", "MINUTES": "´minutes", "active": "Active", "addOrder.payment.title": "Payment method", "addOrder.address.missingAddressStage": "The service you have selected does not contain any stages with addresses. Please verify the stages of your product.", "addOrder.address.selectSection": "Select apartment", "addOrder.address.fromCustomer": "(Last used for this customer)", "addOrder.address.tempAddressName": "Address", "addOrder.address.tempStageName": "order", "addOrder.assign.resources": "Resources", "addOrder.assign.resourcesPlaceholder": "Select resources", "addOrder.assign.users": "Employees", "addOrder.assign.user": "Employee", "addOrder.assign.teams": "Employees", "addOrder.assign.team": "Team", "addOrder.assign.usersPlaceholder": "Select employees", "addOrder.createOrderButton": "Create order", "addOrder.createScheduleButton": "Create schedule", "addOrder.notes.title": "Notes", "addOrder.customer.addPartnerButton": " Add partner", "addOrder.customer.addPartnerTitle": "Partner", "addOrder.customer.brregSearchLabel": "Search Brønnøysundregisteret", "addOrder.customer.brregSearchLabelPlaceholder": "Search for a company name or organisation number...", "addOrder.customer.businessCustomerAddress": "Company address", "addOrder.customer.businessCustomerAddressSearchPlaceholder": "Search for company address", "addOrder.customer.businessToggle.private": "Private customer", "addOrder.customer.businessToggle.business": "Business customer", "addOrder.customer.cancelButton": "Cancel", "addOrder.customer.companyName": "Company name", "addOrder.customer.noName": "No name", "addOrder.customer.createCustomerButton": "Create customer", "addOrder.customer.createCustomerTitle": "Create new customer", "addOrder.customer.createNewCustomerButton": "Create new customer", "addOrder.customer.customerSearchPlaceholder": "Search for customer", "addOrder.customer.email": "E-mail", "addOrder.customer.noEmail": "No Email", "addOrder.customer.emailError": "Invalid e-mail", "addOrder.customer.firstName": "First name", "addOrder.customer.firstNameError": "Invalid first name.", "addOrder.customer.invalidCompanyName": "Invalid company name", "addOrder.customer.invalidEmail": "Invalid E-mail", "addOrder.customer.invalidOrgNumber": "Invalid organisation number", "addOrder.customer.invoiceRecipientLabel": "Invoice recipient", "addOrder.customer.lastName": "Last name", "addOrder.customer.lastNameError": "Invalid last name.", "addOrder.customer.noMatchesFound": "No matches found", "addOrder.customer.orgNumber": "Organisation number", "addOrder.customer.phone": "Phone", "addOrder.customer.noPhone": "No phone", "addOrder.customer.phoneError": "Invalid phone number", "addOrder.customer.phoneErrorLength": "Phone number is too short", "addOrder.customer.searchPartnerPlaceholder": "Search by partner name, or partner company name", "addOrder.customer.searching": "Searching...", "addOrder.customer.title": "Customer", "addOrder.customer.paymentRecipient": "Payment recipient", "addOrder.customer.serviceRecipient": "Service recipient", "addOrder.customer.serviceRecipient.searchPlaceholder": "Search for service recipient", "addOrder.customer.new.title": "Create customer", "addOrder.customer.useAsServiceRecipient": "Use as service recipient", "addOrder.missingFields.address": "n address for all stages", "addOrder.missingFields.customer": " customer", "addOrder.missingFields.duration_hours": " duration", "addOrder.missingFields.duration_hours_range": " duration range", "addOrder.missingFields.execution date": "n execution date", "addOrder.missingFields.service": " service", "addOrder.missingFields.time": " valid time", "addOrder.missingFields.title1": "Please choose a", "addOrder.missingFields.title2": "before creating order", "addOrder.preview.noProducts": "Service product not selected", "addOrder.preview.priceExVat": "Price ex. VAT", "addOrder.preview.priceIncVat": "Total", "addOrder.preview.product": "Product", "addOrder.preview.productTotal": "Total", "addOrder.preview.quantity": "Quantity", "addOrder.preview.subtotal": "Subtotal", "addOrder.preview.title": "Order summary", "addOrder.preview.unitTypeHoursTooltip": "The quantity is an estimate, actual quantity will be determined by the logged duration of the order", "addOrder.preview.vat": "VAT", "addOrder.preview.estimatedWork": "Estimated work time", "addOrder.preview.estimatedTransport": "Estimated transport time", "addOrder.schedule.date": "Da<PERSON>", "addOrder.schedule.duration": "Estimated duration", "addOrder.schedule.durationRange": "Estimated duration", "addOrder.schedule.arrivalWindow": "Arrival window", "addOrder.schedule.error.setBothTimes": "Both times must be set.", "addOrder.schedule.error.arrivalFromBeforeArrivalTo": "'Arrival From' time must be before 'Arrival To' time.", "addOrder.schedule.arrivalWindow.from": "From", "addOrder.schedule.arrivalWindow.to": "To", "addOrder.schedule.durationValidationError": "Duration to must be greater than duration from.", "addOrder.schedule.hourAbbr": "h", "addOrder.schedule.range": "Range", "addOrder.schedule.multiDay": "Multi-day", "addOrder.schedule.autoEstimate": "Auto estimate", "addOrder.schedule.time": "Start time", "addOrder.schedule.endDate": "End date", "addOrder.schedule.endTime": "End time", "addOrder.schedule.to": "to", "addOrder.service.addAdditionalProductButton": "Add additional product", "addOrder.service.additionalProducts": "Additional products", "addOrder.service.additionalProductsSearchPlaceholder": "Search for additional products", "addOrder.service.lockedQuantityHoursTooltip": "Quantity is locked to the duration of the order. The quantity will be set to 1 on order creation.", "addOrder.service.noManualPriceRule": "No manual price rules available for this product", "addOrder.service.priceRules": "Price rules", "addOrder.service.quantity": "Quantity", "addOrder.service.searchPlaceholder": "Search for service product", "addOrder.service.products.searchPlaceholder": "Search for product", "addOrder.service.products.addCustomProduct": "Add custom product", "addOrder.service.products.customProductUnitAbbreviation": "pcs", "addOrder.service.selectPriceRules": "Select price rules", "addOrder.service.title": "Service", "addOrder.title": "Create order", "addOrder.title.schedule": "Create order series", "addOrder.addInvoiceReference.button": "Add invoice reference", "addOrder.addInvoiceReference.title": "Invoice reference", "addOrder.addInvoiceReference.placeholder": "Enter reference", "addOrder.comment.title": "Invoice comment", "addOrder.comment.button": "Add invoice comment", "affiliates.itemName": "Businesses", "affiliates.details.backButton": "Businesses", "affiliates.details.backButton.private": "Customers", "affiliates.itemName.private": "Private customers", "affiliates.addNew.private": "Add customer", "affiliates.importCustomers": "Import customers", "affiliates.list.name.private": "Name", "affiliates.list.email": "Email", "affiliates.list.phone": "Phone", "affiliates.addNew": "Add business", "affiliates.list.name": "Company name", "affiliates.list.orgNumber": "Organisation number", "affiliates.list.partnersOnly": "Partners only", "affiliates.list.customersOnly": "Customers only", "affiliates.list.subContractorsOnly": "Subcontractors only", "affiliates.list.subContractor": "Subcontractor", "affiliates.list.customer": "Customer", "affiliates.list.partner": "Partner", "affiliates.details.edit": "Edit", "affiliates.details.orders": "Orders", "affiliates.details.prices": "Prices", "affiliates.details.totalAmountIncVat": "Total amount (inc. VAT)", "affiliates.details.contacts": "Business contacts", "affiliates.details.contacts.add": "Add contact", "affiliates.details.products.searchPlaceholder": "Search for a product to set custom prices for the business", "affiliates.details.products.searchPlaceholder.private": "Search for a product to set custom prices for the customer", "affiliates.details.generalSettings.title": "Settings", "affiliates.details.generalSettings.disableSms": "Turn off SMSs for this customer", "affiliates.details.generalSettings.disableSms.tooltip": "Activate to turn off all automatic order text messages for this customer", "affiliates.details.generalSettings.disableEmail": "Turn off emails for this customer", "affiliates.details.generalSettings.disableEmail.tooltip": "Activate to turn off all automatic order emails for this customer", "affiliates.details.generalSettings.hidePrices": "Hide prices for service recipient", "affiliates.details.generalSettings.hidePrices.tooltip": "Enabling this will hide prices and order totals from the service recipient on orders where this business is either partner or payment recipient", "affiliates.details.generalSettings.useAsCustomer": "Use as customer", "affiliates.details.generalSettings.useAsCustomer.tooltip": "By tagging the company as a customer, the company and the company's contacts will be displayed in the customer search when you create orders.", "affiliates.details.generalSettings.useAsPartner": "Use as partner", "affiliates.details.generalSettings.useAsPartner.tooltip": "By tagging the company as a partner, you can attach the company as a partner to orders you create. On a partner order the partner's prices will be used, regardless of who is selected as payment recipient.", "affiliates.details.generalSettings.useAsSubContractor": "Use as subcontractor", "affiliates.details.generalSettings.useAsSubContractor.tooltip": "By tagging the company as a subcontractor, you can send orders to the subcontractor who can then assign the order to their employees. Payment of the order will still be made to your company.", "affiliates.details.subContractorSettings.title": "Subcontractor settings", "affiliates.details.subContractorSettings.hidePrices": "Hide prices for subcontractor", "affiliates.details.subContractorSettings.hidePrices.tooltip": "Enabling this will hide all prices that stem from your company for this subcontractor.", "affiliates.details.subContractorSettings.fetchProducts": "Subcontractor can fetch my products", "affiliates.details.subContractorSettings.fetchProducts.tooltip": "If this is enabled, the subcontractor will be able to fetch your products in the employee app to create new order lines if needed. This will only be possible on jobs where the subcontractor is assigned.", "affiliates.details.invoiceDetails.title": "Invoice details", "affiliates.details.invoiceDetails.invoiceEmail": "Invoice email", "affiliates.details.invoiceDetails.invoiceDueDate": "Due date", "affiliates.details.invoiceDetails.invoiceDueDatePostFix": "days", "affiliates.details.invoiceDetails.sendType": "Invoice type", "affiliates.details.invoiceDetails.consolidation": "Invoice consolidation", "affiliates.details.invoiceConsolidation.title": "Consolidated invoices", "affiliates.details.invoiceConsolidation.sendSelected": "Send selected invoices", "affiliates.details.consolidatedInvoicePayments.title": "Payments to be added to consolidated invoice", "affiliates.details.consolidatedInvoicePayments.consolidateButton": "Send now with chosen payments", "affiliates.details.addresses.title": "Addresses", "affiliates.details.addresses.add": "Add address", "affiliates.details.notes.title": "Notes", "affiliates.details.notes.copyToOrder": "Show in order", "affiliates.details.notes.copyToOrder.tootltip": "If this is active, the note will be copied to orders where this contact is added as a customer. Deleting the note in the order will not affect this note.", "affiliates.prices.modal.header": "Set up custom prices", "affiliates.prices.modal.prices": "Custom prices for ", "affiliates.prices.modal.subtext": "Here you can define your own prices for this company. This applies whether the company is a customer or a partner.", "affiliates.prices.modal.newBasePrice": "Set new base price", "affiliates.prices.modal.productVariants": "Product variants", "affiliates.prices.modal.noProductVariants": "There are no product variants for this product", "affiliates.prices.modal.automaticPriceRules": "Automatic price rules", "affiliates.prices.modal.noAutomaticPriceRules": "There are no price rules for this product", "affiliates.import.title": "Import customers from accounting", "affiliates.import.button": "Import selected", "addressSearch.label": "", "applicationUpdateMessage": "We have made some updates, please press 'ok' to update.", "applicationUnrecoverableState": "The application is in an unrecoverable state and needs to reload. Please press 'OK' to continue.", "applicationUpdateTitle": "Update Available", "applicationUpdateNow": "Update Now", "applicationUpdateLater": "Later", "applicationUpdateCountdown": "Auto-updating in {{seconds}} seconds", "apps.accounting": "Accounting", "apps.all": "All", "apps.deleteBtn": "Uninstall app", "apps.deleteModal.cancel": "Cancel", "apps.deleteModal.deleteItem": "Are you sure you want to install this app? By proceeding with the installation of this item, any pre-existing accounting integration will be uninstalled and replaced", "apps.deleteModal.install": "Install", "apps.editApp": "Edit", "apps.noAppsSelected": "No apps are currently selected", "apps.instApp": "Install", "apps.installBtn": "Install", "apps.itemName": "Apps", "apps.myApps": "My apps", "apps.powGo": "The integration suits everyone who wants to transfer customers, orders, and refunds to Power Office Go", "apps.tripletex": "The integration suits everyone who wants to transfer customers, orders, and refunds to Tripletex", "apps.zapier": "This integration is ideal for enhancing data management and operational processes", "areYouSure": "Are you sure?", "calendar.daily": "Daily", "calendar.monthly": "Monthly", "calendar.today": "Today", "calendar.weekly": "Weekly", "calendar.resources": "Resources", "calendar.employees": "Employees", "cancel": "Cancel", "cancelModal.abortBtn": "Abort", "cancelModal.bodytext": "Are you sure you want to cancel this order? This action can not be undone", "cancelModal.cancelBtn": "Cancel order", "cancelModal.header": "Are you sure?", "cancelModal.notify": "Send an email notification to the customer", "chat-widget.agentLabel": "Support", "chat-widget.autoResponseMessage": "Hi, how can i help you?", "chat-widget.browserSideAuthorLabel": "", "chat-widget.collapsedMode": "chip", "chat-widget.conversationEndConfirmationQuestion": "Are you sure you want to end this chat?", "chat-widget.conversationEndLabel": "Your chat session has ended. Thanks for chatting!", "chat-widget.conversationEndMenuLinkLabel": "End <PERSON>", "chat-widget.conversationEndTranscriptPlaceholder": "Email to send transcript of chat", "chat-widget.conversationRatingLabel": "How would you rate this chat?", "chat-widget.conversationRatingPlaceholder": "How can we improve?", "chat-widget.conversationRatingThankYou": "Thanks for rating your chat session!", "chat-widget.conversationTranscriptSentThankYou": "Thanks! You will receive your transcript shortly.", "chat-widget.defaultCancelBtnLabel": "Cancel", "chat-widget.defaultNoBtnLabel": "No", "chat-widget.defaultOkBtnLabel": "Ok", "chat-widget.defaultSendBtnLabel": "Send", "chat-widget.defaultYesBtnLabel": "Yes", "chat-widget.noAnswerWithEmail": "Oops! Sorry no one has responded yet. We have your email on file if you need to leave or you can continue to wait.", "chat-widget.noAnswerWithoutEmail": "Oops! Sorry no one has responded yet. We have your email on file if you need to leave or you can continue to wait.", "chat-widget.offlineEmailPlaceholder": "Email", "chat-widget.offlineGreeting": "Sorry we are away, but we would love to hear from you and chat soon!", "chat-widget.offlineMessagePlaceholder": "Your message here", "chat-widget.offlineNamePlaceholder": "Name (optional but helpful)", "chat-widget.offlineSendButton": "Send", "chat-widget.offlineThankYouMessage": "Thanks for your message. We will be in touch soon!", "chat-widget.offlineTitle": "Contact Us", "chat-widget.onlineMessagePlaceholder": "Type message here...", "chat-widget.onlineTitle": "How can we help you?", "chat-widget.requestScreenshotAllowLabel": "Take screenshot", "chat-widget.requestScreenshotDeclineLabel": "Decline", "chat-widget.requestScreenshotText": "Operator would like to take a screenshot of your browser. Confirm below.", "chat-widget.requireInfoGreeting": "Enter your name and email to start chatting!", "chat-widget.requireInfoSubmitBtn": "Start", "chat-widget.status": "online", "chat-widget.titleColor": "#3f51b5", "chat-widget.titleFontColor": "#fff", "chat-widget.widgetAlignment": "bottomRight", "common.at": "at", "common.add": "Add", "common.backToSettings": "Back to Settings", "common.create": "Create", "common.cancel": "Cancel", "common.close": "Close", "common.save": "Save", "common.delete": "Delete", "common.remove": "Remove", "common.name": "Name", "common.continue": "Continue", "common.companyName": "Company name", "common.orgNumber": "Organisation number", "common.firstName": "First name", "common.lastName": "Last name", "common.email": "Email", "common.sms": "SMS", "common.phone": "Phone", "common.address": "Address", "common.edit": "Edit", "common.redigate": "Edit", "common.change": "Change", "common.send": "Send", "common.active": "Active", "common.inactive": "Inactive", "common.invoice": "Invoice", "common.noAddress": "No address selected", "common.and": "and", "common.days": "days", "common.noPhone": "Phone number missing", "common.noEmail": "Email missing", "common.loading": "Loading", "common.noData": "No data", "common.select": "Select", "common.to": "to", "common.open": "Open", "common.on": "on", "common.noResults": "No results found", "common.refund": "Refund", "common.by": "by", "common.vat": "VAT", "common.yes": "Yes", "common.no": "No", "common.date": "Date", "common.duration": "Duration", "common.settings": "Settings", "common.ok": "OK", "common.total": "Total", "common.download": "Download", "common.deleted": "Deleted", "components.addressSearch.customAddress.title": "Create address", "components.addressSearch.addressSearchPlaceholder": "Search for address", "components.addressSearch.unitSearchPlaceholder": "Select apartment (optional)", "components.addressSearch.createNew": "Can't find the address? Create a new one", "components.addressSearch.customAddress.street": "Street name", "components.addressSearch.customAddress.number": "Number", "components.addressSearch.customAddress.letter": "Letter", "components.addressSearch.customAddress.postalCode": "Postal code", "components.addressSearch.customAddress.city": "City", "components.addressSearch.customAddress.propertyType": "Property type", "companySearch.label": "Search Brønnøysundregisteret", "companySearch.placeholder": "Search for a company name or orgnumber...", "createEvent.addressLabel": "Address", "createEvent.addressPlaceholder": "Search for address", "createEvent.endDateLabel": "End time", "createEvent.errorMessageEndTime": "End time must be after start time", "createEvent.headerEvent": "Create event", "createEvent.headerOrder": "Create order", "createEvent.selectEventAllDayLabel": "All day", "createEvent.selectEventDescriptionLabel": "Event description", "createEvent.selectEventDescriptionPlaceholder": "Description of the event", "createEvent.selectEventNameLabel": "Name", "createEvent.selectEventNamePlaceholder": "Name of event", "createEvent.selectEventPlaceholder": "Select event type", "createEvent.selectEventResourcesPlaceholder": "Add resources", "createEvent.selectEventTypeLabel": "Event type", "createEvent.selectEventUsersPlaceholder": "Add users", "createEvent.selectedEventResourcesLabel": "Resources", "createEvent.selectedEventUsersLabel": "Users", "createEvent.startDateLabel": "Start time", "createEvent.tabs.createOrder": "Order", "createEvent.tabs.createOther": "Other", "createEvent.tabs.connectToOrder": "Connect to order", "createEvent.tabs.createEmployee": "Employee", "createEvent.btn.createEvent": "Create event", "createEvent.btn.editEvent": "Edit event", "conflictModal.header": "Conflict", "conflictModal.subHeader": "This user is assigned to the following jobs that intersects with this job:", "conflictModal.subHeader.absence": "This user has the following registered absence that intersects with this job:", "conflictModal.btn.goBack": "Ignore", "conflictModal.btn.continue": "Continue", "conflictModal.typeLabel": "Type:", "conflictModal.timeLabel": "Time:", "searchEventsModal.header": "Search for orders", "searchEventsModal.noParamsSet": "At least one search criteria must be set", "searchEventsModal.noResultFound": "No search results found", "searchEventModal.form.orderIdLabel": "Order number", "searchEventModal.form.orderIdPlaceholder": "Search for order number", "searchEventModal.form.productLabel": "Product name", "searchEventModal.form.productPlaceholder": "Search for product", "searchEventModal.form.addressLabel": "Address", "searchEventModal.form.addressPlaceholder": "Search for address", "searchEventModal.form.customerLabel": "Customer", "searchEventModal.form.commentLabel": "Invoice comment", "searchEventModal.form.emailLabel": "Customer email", "searchEventModal.form.customerPlaceholder": "Search for name", "searchEventModal.form.commentPlaceholder": "Search for invoice comment", "searchEventModal.form.customerPhoneLabel": "Customer phone", "searchEventModal.form.customerPhonePlaceholder": "Search for phone", "searchEventsModal.btn.cancel": "Cancel", "searchEventsModal.btn.search": "Search", "customers.businessCustomers.accountingId": "Accounting ID", "customers.businessCustomers.active": "Active", "customers.businessCustomers.addNewCustomer": "Add customer", "customers.businessCustomers.address": "Company address", "customers.businessCustomers.addContact": "Add business contact", "customers.businessCustomers.contact": "Business contact", "customers.businessCustomers.city": "City", "customers.businessCustomers.contactName": "Name", "customers.businessCustomers.contactPerson": "Contact Person", "customers.businessCustomers.customerId": "Customer ID", "customers.businessCustomers.email": "Email", "customers.businessCustomers.invoiceEmail": "Invoice email", "customers.businessCustomers.invoiceDueDate": "Due date", "customers.businessCustomers.invoiceDueDatePostFix": "days", "customers.businessCustomers.invoiceConsolidation": "Invoice consolidation", "customers.businessCustomers.invoiceSendType": "Invoice type", "customers.businessCustomers.firstName": "First name", "customers.businessCustomers.inactive": "Inactive", "customers.businessCustomers.invalidAccountId": "Invalid account ID", "customers.businessCustomers.invalidAddress": "Invalid address", "customers.businessCustomers.invalidCity": "Invalid city", "customers.businessCustomers.invalidCompanyName": "Invalid company name", "customers.businessCustomers.invalidEmail": "Invalid email address", "customers.businessCustomers.invalidFirstName": "Invalid first name", "customers.businessCustomers.invalidLastName": "<PERSON>valid last name", "customers.businessCustomers.invalidOrganisationNumber": "Invalid organisation number", "customers.businessCustomers.invalidPhone": "Invalid phone number", "customers.businessCustomers.invalidPostalCode": "Invalid postal code", "customers.businessCustomers.itemName": "Business Customers", "customers.businessCustomers.lastName": "Last name", "customers.businessCustomers.name": "Company name", "customers.businessCustomers.new.create": "Create", "customers.businessCustomers.new.editPageName": "Edit business customer", "customers.businessCustomers.new.newPageName": "New business customer", "customers.businessCustomers.new.save": "Save", "customers.businessCustomers.numberOfOrders": "Number of orders", "customers.businessCustomers.organisationNumber": "Organisation number", "customers.businessCustomers.phone": "Phone", "customers.businessCustomers.postalCode": "Postal code", "customers.businessCustomers.primaryAddress": "Primary address", "customers.businessCustomers.revenue": "Revenue", "customers.businessCustomers.searchField": "Search...", "customers.businessCustomers.otherActions": "Other actions", "customers.businessCustomers.editInfo": "Edit customer", "customers.businessCustomers.newConsolidatedInvoice": "Create new consolidated invoice setup", "customers.businessCustomers.editAccountingInfo": "Edit accounting info", "customers.businessCustomers.editInvoiceDetails": "Edit invoice details", "customers.businessCustomers.invoiceDetails": "Invoice details", "customers.orderList.address": "Address", "customers.orderList.orderDate": "Created at", "customers.orderList.jobDate": "Job date", "customers.orderList.orderId": "Order ID", "customers.orderList.revenue": "Revenue", "customers.orderList.status": "Status", "customers.orderList.type": "Type", "customers.privateCustomers.accountingId": "Accounting ID", "customers.privateCustomers.active": "Active", "customers.privateCustomers.addNewCustomer": "Add customer", "customers.privateCustomers.address": "Address", "customers.privateCustomers.city": "City", "customers.privateCustomers.contactName": "Name", "customers.privateCustomers.contactPerson": "Contact Person", "customers.privateCustomers.customerId": "Customer ID", "customers.privateCustomers.email": "Email", "customers.privateCustomers.firstName": "First name", "customers.privateCustomers.notEditable": "The user connected to this customer is registered as a customer at multiple companies. Personal data for this customer can therefore not be changed.", "customers.privateCustomers.inactive": "Inactive", "customers.privateCustomers.invalidAccountId": "Invalid account ID", "customers.privateCustomers.invalidAddress": "Invalid address", "customers.privateCustomers.invalidCity": "Invalid city", "customers.privateCustomers.invalidCompanyName": "Invalid company name", "customers.privateCustomers.invalidEmail": "Invalid email address", "customers.privateCustomers.invalidFirstName": "Invalid first name", "customers.privateCustomers.invalidLastName": "<PERSON>valid last name", "customers.privateCustomers.invalidOrganisationNumber": "Invalid organisation number", "customers.privateCustomers.invalidPhone": "Invalid phone number", "customers.privateCustomers.invalidPostalCode": "Invalid postal code", "customers.privateCustomers.itemName": "Private customers", "customers.privateCustomers.lastName": "Last name", "customers.privateCustomers.name": "Name", "customers.privateCustomers.accountingLabel": "Map a customer from accounting to this customer", "customers.privateCustomers.accountingPlaceholder": "Search for the customer on your accounting system", "customers.privateCustomers.invoiceDueDate": "Invoice due date", "customers.privateCustomers.new.create": "Create", "customers.privateCustomers.new.editPageName": "Edit private customer", "customers.privateCustomers.new.newPageName": "New private customer", "customers.privateCustomers.new.save": "Save", "customers.privateCustomers.numberOfOrders": "Number of Orders", "customers.privateCustomers.organisationNumber": "Organisation number", "customers.privateCustomers.phone": "Phone", "customers.privateCustomers.postalCode": "Postal code", "customers.privateCustomers.primaryAddress": "Primary address", "customers.privateCustomers.revenue": "Revenue", "customers.privateCustomers.searchField": "Search...", "customers.privateCustomers.createdAt": "Created at:", "customers.privateCustomers.deals": "Deals", "customers.privateCustomers.bookedRevenue": "Booked Revenue", "customers.privateCustomers.jobs": "Jobs", "customers.privateCustomers.job": "Job", "customers.privateCustomers.allCustomers": "All customers", "dashboard.itemName": "Dashboard", "dashboard.widgets.weeklyRevenue": "Revenue this week", "dashboard.widgets.last5orders": "Last 5 Orders", "dashboard.widgets.monthlyRevenue": "", "dashboard.widgets.newOrdersThisMonth": "New orders this month", "dashboard.widgets.newOrdersToday": "New orders today", "dashboard.widgets.plannedJobsToday": "Planned jobs today", "dashboard.widgets.resourceCalendar": "Calendar", "dashboard.widgets.revenue.currentMonth": "Current Month", "dashboard.widgets.revenue.itemName": "Revenue", "dashboard.widgets.revenue.previousMonth": "Previous Month", "dashboard.widgets.revenue.yAxisTitle": "Revenue(NOK)", "date-range-picker.last30Days": "Last 30 Days", "date-range-picker.last7Days": "Last 7 Days", "date-range-picker.lastMonth": "Last Month", "date-range-picker.lastYear": "Last Year", "date-range-picker.monthToDate": "Month to Date", "date-range-picker.thisMonth": "This Month", "date-range-picker.thisYear": "This Year", "date-range-picker.to": "to", "date-range-picker.today": "Today", "date-range-picker.yearToDate": "Year to Date", "date-range-picker.yesterday": "Yesterday", "deleteBtn.bodytext": "Are you sure you want to delete this item?", "deleteBtn.closeBtn": "Close", "deleteBtn.deleteBtn": "Delete", "deleteBtn.header": "Delete", "editEvent.addressLabel": "Address", "editEvent.addressPlaceholder": "Search for address", "editEvent.endDateLabel": "End time", "editEvent.errorMessageEndTime": "End time must be after start time", "editEvent.headerEvent": "Edit event", "editEvent.headerOrder": "Edit order", "editEvent.save": "Save", "editEvent.selectEventAllDayLabel": "All day", "editEvent.selectEventDescriptionLabel": "Event description", "editEvent.selectEventDescriptionPlaceholder": "Description of the event", "editEvent.selectEventNameLabel": "Name", "editEvent.selectEventNamePlaceholder": "Name of event'", "editEvent.selectEventPlaceholder": "Select event type", "editEvent.selectEventResourcesPlaceholder": "Add resources", "editEvent.selectEventTypeLabel": "Event type", "editEvent.selectEventUsersPlaceholder": "Add users", "editEvent.selectedEventResourcesLabel": "Resources", "editEvent.selectedEventUsersLabel": "Users", "editEvent.startDateLabel": "Start time", "employees.add-employee.title": "Add employee", "employees.edit-employee.title": "Edit employee", "employees.edit": "Edit Employee", "employees.addEmployee": "Add employee", "employees.import": "Import", "employees.companyRole": "Company Role", "employees.email": "Email", "employees.employee-details.addProduct": "Add product to order", "employees.employee-details.assignableForJobs": "Available for job assignments", "employees.employee-details.availability": "Availability", "employees.employee-details.clickHereToUploadImage": "Click here to upload image", "employees.employee-details.coreAttributesTitle": "Employee settings for Core", "employees.employee-details.editDiscount": "Edit discount", "employees.employee-details.editQuantity": "Edit product quantity", "employees.employee-details.email": "Email", "employees.employee-details.accountingLabel": "Connect this employee to an employee in your accounting system", "employees.employee-details.hourlyRate": "Hourly rate", "employees.employee-details.hourlyRatePlaceholder": "Specify the standard hourly wage for this employee", "employees.employee-details.employeeAttributesTitle": "In the Crew App, the employee can:", "employees.employee-details.employeeInformation": "Employee information", "employees.employee-details.equalPhone": "The phone number matches another user", "employees.employee-details.firstName": "First name", "employees.employee-details.generalInformation": "General information", "employees.employee-details.invalidEmail": "Invalid email", "employees.employee-details.invalidFirstName": "Invalid first name", "employees.employee-details.invalidLastName": "<PERSON>valid last name", "employees.employee-details.invalidPhone": "Invalid phone number", "employees.employee-details.invalidPhoneNumber": "Invalid phone number", "employees.employee-details.invalidRole": "Invalid role", "employees.employee-details.lastName": "Last name", "employees.employee-details.logo": "Employee image", "employees.employee-details.makeExternalNotes": "Make external notes", "employees.employee-details.makeExternalReports": "Make external reports", "employees.employee-details.notifications": "Notifications", "employees.employee-details.pageTitle": "Employee Settings", "employees.employee-details.accordion.generalInformation": "General information", "employees.employee-details.paymentPageAccess": "View payment and order summary information", "employees.employee-details.phone": "Phone", "employees.employee-details.properties": "Properties", "employees.employee-details.receiveAccountingIntegrationFailedNotification": "Receive email in case of accounting errors", "employees.employee-details.receiveOrderAcceptedNotificationEmail": "Receive an email when a customer accepts a quote", "employees.employee-details.receiveOrderAcceptedNotificationSMS": "Receive an SMS when a customer accepts a quote", "employees.employee-details.receiveOrderFinishedNotificationSMS": "Receive an SMS when an order is finished", "employees.employee-details.receiveUnpaidOrderAlert": "Receive email notification for unpaid orders", "employees.employee-details.receiveUnconfirmedOrderAlert": "Receive email notification for upcoming unconfirmed orders", "employees.employee-details.receiveSubContractorOrderNotification": "Receive email notification for updates on jobs for sub contractors", "employees.employee-details.receiveOrderRatingNotification": "Receive email notification when customer gives order rating", "employees.employee-details.receiveEmbedOrderNotification": "Receive email notification for new orders from booking form", "employees.employee-details.receiveEmailErrorNotification": "Receive email notification for email errors", "employees.employee-details.receiveCustomerMessageNotification": "Receive email notification when a customer sends a message", "employees.employee-details.receiveUnconfirmedOrderAlert.tooltip": "The system will send you a daily email alert about orders to be completed the following day, but which have not yet been confirmed by either the company or the customer", "employees.employee-details.receiveUnpaidOrderAlert.tooltip": "The system automatically sends two reminders to the customer if the order is not paid. If the payment is still not received after the second reminder, you will receive an email informing you of this, provided this setting is enabled.", "employees.employee-details.recurringOrderCreationFailed": "Receive email for recurring order creation failed", "employees.employee-details.crew-access.accessControl": "Access Control for crew app", "employees.employee-details.crew-access.selectAllPermissions": "Select All Permissions", "employees.employee-details.crew-access.expandAll": "Expand All", "employees.employee-details.crew-access.compactAll": "Compact All", "employees.employee-details.crew-access.orders": "Orders", "employees.employee-details.crew-access.changeDateAndTime": "Change date and time", "employees.employee-details.crew-access.editDiscount": "Add and change discount", "employees.employee-details.crew-access.confirmOrder": "Confirm order", "employees.employee-details.crew-access.sendToPayment": "Send to payment", "employees.employee-details.crew-access.editOrderLines": "Add and edit orderlines", "employees.employee-details.crew-access.completeJob": "Complete job", "employees.employee-details.crew-access.viewPrices": "See prices", "employees.employee-details.crew-access.viewOrderLines": "View order lines in crew app", "employees.employee-details.crew-access.viewOrderLines.tooltip": "Allows employees to view order lines in the employee app.", "employees.employee-details.crew-access.cancelOrder": "Cancel order", "employees.employee-details.crew-access.editEmployees": "Add and change employee", "employees.employee-details.crew-access.createOrder": "Create new order", "employees.employee-details.crew-access.geoLock": "Geo lock checkin", "employees.employee-details.crew-access.geoLockOut": "Geo lock checkout", "employees.employee-details.crew-access.start_work_order_outside_execution_date": "Start work order outside of execution date", "employees.employee-details.crew-access.calendar": "Calendar", "employees.employee-details.crew-access.viewAllOrdersAsCrew": "See all company orders", "employees.employee-details.crew-access.viewAllOrdersAsCrew.tooltip": "Normally, employees can only see their own orders, but by activating this setting, they will be able to see all orders in the company.", "employees.employee-details.crew-access.viewAllEventsAsCrew": "See all company events", "employees.employee-details.crew-access.viewAllEventsAsCrew.tooltip": "Normally, employees can only see their own events, but by activating this setting, they will be able to see all events in the company.", "employees.employee-details.crew-access.createEvent": "Create new event", "employees.employee-details.crew-access.editOwnEvent": "Edit own event", "employees.employee-details.crew-access.editCompanyEvents": "Edit company events", "employees.employee-details.crew-access.editCompanyEvents.tooltip": "Normally, employees can only edit their own events, but by activating this setting, they will be able to edit all events in the company.", "employees.employee-details.crew-access.viewUnconfirmedOrders": "See unconfirmed orders", "employees.employee-details.crew-access.timetracking": "Timetracking", "employees.employee-details.crew-access.editTimeTracking": "Edit time tracking", "employees.employee-details.crew-access.createManualTimeTracking": "Add manual time trackings", "employees.employee-details.crew-access.edit_resources": "Edit resources", "employees.employee-details.crew-access.add_payment_recipient": "Add payment recipient", "employees.employee-details.resendInvitation": "Resend invitation", "employees.employee-details.notConnectedToAccounting": "Not connected", "employees.employee-details.accountingId": "Accounting ID", "employees.employee-details.role": "Role", "employees.employee-details.role.admin.description": "Has access to both the Crew app and Core", "employees.employee-details.role.crew.description": "Has access to the Crew app only", "employees.employee-details.noInvitationRole": "Employees with this role will not receive an email invitation", "employees.employee-details.noSalariesCreated": "No salaries have been created yet. Click the Create button to add a salary.", "employees.employee-details.noVacationDaysCreated": "No vacation days have been created yet. Click the Create button to add vacation days.", "employees.employee-details.noAbsencesCreated": "No absences have been created yet. Click the Create button to add an absence period.", "employees.employee-details.selectRole": "Choose role", "employees.employee-details.showInCalendar": "Show in calendar", "employees.employee-details.skipInvitation": "Create without invite", "employees.employee-details.skipInvitation.tooltip": "Normally, an invitation is sent to the employee when they are created. If you do not want to send an invitation now, you can do this later.", "employees.employee-details.save": "Save", "employees.employee-details.sendToPayment": "Send to payment", "employees.employee-details.setPaymentToExternal": "Handle order payment externally", "employees.employee-details.viewPrices": "View prices", "employees.employee-details.accessControl": "Access control", "employees.employee-details.accessControlCrew": "Access Control", "employees.employee-details.accessControl.viewAllEventsAsCrew": "Access to all events in crew calendar ", "employees.employee-details.accessControl.viewAllEventsAsCrew.tooltip": "Enable this to give access to all events, assigned and non-assigned, in the crew calendar. Disable to give access to assigned events only.", "employees.employee-details.accessControl.viewAllOrdersAsCrew": "Access to all orders in crew calendar", "employees.employee-details.accessControl.viewAllOrdersAsCrew.tooltip": "Enable this to give access to all orders, assigned and non-assigned, in the crew calendar. Disable to give access to assigned orders only.", "employees.employee-details.accessControl.viewPrices": "View prices in crew app", "employees.employee-details.accessControl.viewPrices.tooltip": "Allows employees to view company prices in the employee app.", "employees.employee-details.accessControl.viewOrderLines": "View order lines in crew app", "employees.employee-details.accessControl.viewOrderLines.tooltip": "Allows employees to view order lines in the employee app.", "employees.employee-details.accessControl.sendToPayment": "Send to payment in crew app", "employees.employee-details.accessControl.sendToPayment.tooltip": "Allows employees to send an order to payment.", "employees.employee-details.accessControl.geoLock": "Enable check-in geo lock", "employees.employee-details.accessControl.geoLock.tooltip": "Enable this to ensure that the employee cannot check-in on a job without being within a given distance from the job address. The range can be specified in company settings.", "employees.employee-details.accessControl.geoLockOut.tooltip": "Enable this to ensure that the employee cannot check-out on a job without being within a given distance from the job address. The range can be specified in company settings.", "employees.employee-details.accessControl.start_work_order_outside_execution_date.tooltip": "Enable this to allow the employee to start a job outside of the job's execution date.", "employees.id": "ID", "employees.itemName": "Employees", "employees.name": "Name", "employees.phone": "Phone", "errors.email": "Please enter a valid email", "errors.max": "Over maximum allowed value", "errors.maxlength": "Over maximum allowed length", "errors.min": "Below minimum value", "errors.minlength": "Below minimum length", "errors.pattern": "Please follow the specified format", "errors.phone": "Please enter a valid phonenumber", "errors.required": "This field is required", "eventDetails.ResourcesAndReports": "Employees and resources", "eventDetails.addressAndRoute": "Address and route", "eventDetails.checkLists": "Reports and checklists", "eventDetails.generalInformation": "General information", "eventDetails.notesAndCQ": "Notes and customer questionnaire", "forgotPassword.backToLogin": "Back to login", "forgotPassword.backToLoginLabel1": "Back to ", "forgotPassword.backToLoginLabel2": "<PERSON><PERSON>", "forgotPassword.description": "Enter your phone number and we'll send you an email with instructions to reset your password", "forgotPassword.emailLabel": "Phone", "forgotPassword.emailPlaceholder": "Enter your phone number", "forgotPassword.submitButtonLabel": "Submit", "forgotPassword.successMessage": "An email has been sent to you with instructions on how to reset your password", "forgotPassword.title": "Reset password", "forgotPassword.shortPasswordError": " Password must be at least 3 characters long", "forgotPassword.shortPasswordErrorConfirm": " Confirm Password must be at least 3 characters long", "forgotPassword.validPhone": "", "hours": "hours", "inactive": "Inactive", "justNow": "just now", "labels.email": "Email", "labels.firstName": "First Name", "labels.lastName": "Last Name", "labels.phone": "Phone", "leftSideNav.itemName": "<PERSON><PERSON>", "leftSideNav.navItem.apps.itemName": "Apps", "leftSideNav.navItem.support.itemName": "Crew app", "leftSideNav.navItem.customers.businessCustomers": "Business customers", "leftSideNav.navItem.customers.itemName": "Privat", "leftSideNav.navItem.businesses.itemName": "Business", "leftSideNav.navItem.customers.privateCustomers": "Private", "leftSideNav.navItem.dashboard.itemName": "Dashboard", "leftSideNav.navItem.employees.itemName": "Employees", "leftSideNav.navItem.orders.allOrders": "All orders", "leftSideNav.navItem.orders.itemName": "Orders", "leftSideNav.navItem.workOrders.itemName": "jobs", "leftSideNav.navItem.receivedWorkOrders.itemName": "Job requests", "leftSideNav.navItem.orderSchedules.itemName": "Recurring orders", "leftSideNav.navItem.orders.orderConfirmation": "Order confirmation", "leftSideNav.navItem.partners.itemName": "Partners", "leftSideNav.navItem.partners.paperCompanies": "Paper companies", "leftSideNav.navItem.partners.partnerCompanies": "Partner companies", "leftSideNav.navItem.products.allProducts": "All products", "leftSideNav.navItem.products.itemName": "Products", "leftSideNav.navItem.products.productCategories": "Product categories", "leftSideNav.navItem.reports.allReports": "All products", "leftSideNav.navItem.reports.itemName": "Reports", "leftSideNav.navItem.reports.financialReports": "Financial", "leftSideNav.navItem.reports.employeeTimeTracking": "Time tracking", "leftSideNav.navItem.reports.employeeReports": "Employee report", "leftSideNav.navItem.reports.orderReports": "Order reports", "leftSideNav.navItem.resourceCalendar.itemName": "Calendar", "leftSideNav.navItem.resources.itemName": "Resources", "leftSideNav.navItem.payments.itemName": "Payments", "leftSideNav.navItem.salary.itemName": "Salary and hours", "leftSideNav.navItem.salary.approval": "Approve hours", "leftSideNav.navItem.salary.approvedHours": "Approved hours", "leftSideNav.navItem.settings.accounting": "Accounting", "leftSideNav.navItem.settings.company": "Company", "leftSideNav.navItem.settings.embed": "Booking form", "leftSideNav.navItem.settings.salary": "Salary", "leftSideNav.navItem.settings.calculations": "Calculations", "leftSideNav.navItem.settings.integrations": "Integrations", "leftSideNav.navItem.settings.itemName": "Settings", "leftSideNav.navItem.contacts.itemName": "Contacts", "leftSideNav.navItem.settings.notifications": "Notifications", "leftSideNav.navItem.settings.payment": "Payment", "leftSideNav.navItem.settings.billing": "Billing", "leftSideNav.navItem.settings.resources": "Resources", "leftSideNav.navItem.settings.calendar": "Calendar", "leftSideNav.navItem.superadmin.companies": "Companies", "leftSideNav.navItem.superadmin.itemName": "Super Admin", "leftSideNav.navItem.templates.itemName": "Templates", "leftSideNav.navItem.create.itemName": "Create", "leftSideNav.navItem.create.order": "Order", "leftSideNav.navItem.templates.customerQuestions": "Customer Questions", "leftSideNav.navItem.templates.tasks": "Tasks", "leftSideNav.navItem.templates.workOrders": "Jobs", "leftSideNav.navItem.templates.importantInformation": "Viktig informasjon", "login.description": "Enter your phone number and password to access admin panel.", "login.forgotPasswordLabel": "Forgot your password?", "login.invalidPassword": "Invalid password", "login.invalidPhone": "Invalid phone number", "login.loginButtonLabel": "<PERSON><PERSON>", "login.loginFailedMessage": "<PERSON><PERSON> failed: ", "login.loginFailedReason": "Wrong phone or password", "login.passwordLabel": "Password", "login.passwordLengthError": "Password must be at least 6 characters", "login.passwordRequiredError": "Password is required", "login.signUpLabel1": "Don't have an account?", "login.signUpLabel2": "Sign Up", "login.title": "Sign in to Between", "login.usernameIsRequired": "Phone is required", "login.usernameLabel": "Phone", "minutes": "minutes", "no": "No", "addOrder.paymentOption.title": "How would you like to get paid?", "addOrder.paymentOption.fixedPrice": "Fixed price", "addOrder.paymentOption.perVisit": "Per visit", "orderSchedules.itemName": "Recurring orders", "orderSchedules.fixedPayment.date": "Monthly invoice date", "orderSchedules.fixedPayment.every": "Every", "orderSchedules.fixedPayment.description": "Invoice description", "orderSchedules.fixedPayment.description.tooltip": "The description will be used as product description for invoice.", "orderSchedules.fixedPayment.reference": "Invoice reference", "orderSchedules.fixedPayment.month": "month", "orderSchedules.fixedPayment.vatRate": "Vat rate", "orderSchedules.fixedPayment.totalPrice": "Invoice amount", "orderSchedules.fixedPayment.title": "Fixed payment", "orderSchedules.fixedPayment.button": "Create fixed payment", "orderSchedules.creationFailed": "Order creation failed", "orderSchedules.createNewSchedule": "Create new schedule", "orderSchedules.weekdays": "Weekdays", "orderSchedules.monthWeekViewTooltip": "Click for week based monthly schedule", "orderSchedules.monthDateViewTooltip": "Click for date based monthly schedule", "orderSchedules.inAdvance": "How many job should be scheduled in advance?", "orderSchedules.inAdvance.tooltip1": "Example: A repeated job starting today will create", "orderSchedules.inAdvance.tooltip2": "jobs. As soon as one of these are finished or its execution date surpassed, the next job will be created.", "orderSchedules.inAdvance.suffix": "jobs", "orderSchedules.repeat": "How often?", "orderSchedules.every": "Every", "orderSchedules.daily": "Daily", "orderSchedules.weekly": "Weekly", "orderSchedules.monthly": "Monthly", "orderSchedules.day": "day", "orderSchedules.week": "week", "orderSchedules.month": "month", "orderSchedules.days": "days", "orderSchedules.weeks": "weeks", "orderSchedules.months": "months", "orderSchedules.descriptionLabel.workOrder": "The job will repeat", "orderSchedules.descriptionLabel.payment": "The payment will repeat", "orderSchedules.descriptionLabel.consolidatedInvoice": "A consolidated invoice will be sent", "orderSchedules.ordersInAdvance": "Orders in advance", "orderSchedules.ordersInAdvanceTooltip": "Set how many future orders the system shall continually maintain. The system will check daily if any new orders needs to be created to maintain the correct amount of future orders.", "orderSchedules.dateLabel": "Day of month", "orderSchedules.nthLabel": "On the", "orderSchedules.nthWeekdayLabel": "Weekday", "orderSchedules.nthWeekItem.1": "1st", "orderSchedules.nthWeekItem.2": "2nd", "orderSchedules.nthWeekItem.3": "3rd", "orderSchedules.nthWeekItem.4": "4th", "orderSchedules.nthWeekItem.5": "Last", "orderSchedules.useDateLabel": "Use date", "orderSchedules.nthWeekItem.suffix": "", "orderSchedules.lastDay": "Last day of month", "orderSchedules.enableCustomerConfirmation": "Disable customer questionnaire", "orderSchedules.enableCustomerNotifications": "Disable customer SMS notifications", "orderSchedules.noWeekdaysSelected": "Please select at least one weekday", "orderSchedules.missingEvery": "You must set a frequency to get a schedule preview", "orderSchedules.createNewScheduleAtAddOrder": "Create repeating order", "orderSchedules.mainProductName": "Service", "orderSchedules.orderScheduleId": "ID", "orderSchedules.customerName": "Customer", "orderSchedules.source": "Source", "orderSchedules.status": "Status", "orderSchedules.fixedPayment": "Fixed payment", "orderSchedules.nextExecution": "Next execution", "orderSchedules.createdAt": "Created at", "orderSchedules.deleteModal.title": "Cancel existing orders", "orderSchedules.deleteModal.body": "Do you wish to cancel any existing not-yet-started orders already created by this schedule?", "orderSchedules.deleteModal.switchLabel": "Cancel already created orders", "orderSchedules.first.workOrder": "First job", "orderSchedules.first.payment": "First payment", "orderSchedules.first.consolidatedInvoice": "First invoice", "orderSchedules.last.workOrder": "Last job", "orderSchedules.last.payment": "Last payment", "orderSchedules.total": "Total", "orderSchedules.startTime": "Start time", "orderSchedules.estimatedDuration": "Estimated duration", "orderSchedules.startDate": "Start date", "orderSchedules.nextJobAt": "Next job at", "orderSchedules.changeSchedule": "Edit schedule", "orderSchedules.detailsCard.title": "Recurring order", "orderSchedules.acceptSchedule": "Accept schedule", "orderSchedules.scheduleAccepted": "Schedule accepted", "orderSchedules.activate": "Activate schedule", "orderSchedules.deactivate": "Pause schedule", "orderSchedules.acceptScheduleCustomer": "Accept schedule for customer", "orderSchedules.scheduleAcceptedCustomer": "Schedule accepted by customer", "orderSchedules.cancelSchedule": "Cancel schedule", "orderSchedules.forceOrderCreation": "Force order creation", "orderSchedules.orders.title": "Orders", "orderSchedules.orders.noData": "Orders will be created when the schedule is activated", "orderSchedules.orders.upcomingOrders": "Upcoming orders", "orderSchedules.orders.noUpcomingOrders": "No upcoming orders", "orderSchedules.orders.pastOrders": "Past orders", "orderSchedules.orders.noPastOrders": "No past orders", "orderSchedules.orders.fixedPayments": "Fixed payments", "orderSchedules.orders.noFixedPayments": "No fixed payments", "orderSchedules.orders.fixedPayments.instanceId": "Payment ID", "orderSchedules.orders.fixedPayments.createdAt": "Created at", "orderSchedules.orders.fixedPayments.invoiceSentAt": "Invoice sent at", "orderSchedules.orders.fixedPayments.capturedAt": "Invoice paid at", "orderSchedules.paymentMethodChosen": "is preselected for this schedule", "orderSchedules.payment": "Payment method", "orderSchedules.payment.fixedPayment": "This series is set up with fixed payment.", "orderSchedules.payment.fixedPayment.inactive": "Fixed payment is deactivated. Active the order series to activate the fixed payment.", "orderSchedules.payment.fixedPayment.invoiceSent": "Invoice will be sent the", "orderSchedules.payment.fixedPayment.monthEvery": "every", "orderSchedules.payment.fixedPayment.instances": "Active payments", "orderSchedules.payment.fixedPayment.instances.sent": "Invoice sent at", "orderSchedules.payment.fixedPayment.noInstances": "The series has no active unpaid payments", "orderSchedules.frequencyExplainer": "Ex. a job beginning on Wed, Sep 20th would repeat", "orderSchedules.frequencyExplainer2": "on Wednesday", "orderSchedules.nameExplainer": "Give this recurring option a name that will be visible to the customer", "orderSchedules.frequency": "Start time", "orderSchedules.startAfter": "Start after", "orderSchedules.startAfterTooltip": "The schedule will not create anything until this date has been passed", "orderSchedules.stopAfter": "Stop after", "orderSchedules.stopAfterTooltip": "The schedule will not create anything after this date", "orders.overview.archiveSelected": "Archive selected orders", "orders.overview.cancelSelected": "Cancel selected orders", "orders.actions": "Actions", "orders.addNewOrder": "Add New Order", "orders.all": "All", "orders.unpaid": "Unpaid", "orders.clear": "Clear", "orders.archivedBtn": "Archived", "orders.accountingUnsyncedBtn": "Not posted in accounting", "orders.today": "Today", "orders.last7Days": "Last 7 days", "orders.last30Days": "Last 30 days", "orders.last90Days": "Last 90 days", "orders.last120Days": "Last 120 days", "orders.customDays": "Custom", "orders.filterStatus": "Order status", "orders.filterEmployee": "Employee", "orders.filterProducts": "Products", "orders.orderNumber": "Order number", "orders.filterDate": "Execution date", "orders.jobDate": "Job date", "orders.orderDate": "Execution date", "orders.createdDate": "Created date", "orders.asc": "Ascending", "orders.desc": "Descending", "orders.reset": "Reset", "orders.sortBy": "Sort by", "orders.custom": "Custom", "orders.starting": "Starting", "orders.ending": "Ending", "orders.apply": "Apply", "orders.archived": "Show archived", "orders.accountingUnsynced": "Not posted in accounting", "orders.confirmation": "Confirmation", "orders.createNewOrder.addresses": "Addresses", "orders.createNewOrder.businessCustomer.search": "Search Address", "orders.createNewOrder.createOrder": "Create order", "orders.createNewOrder.customer": "Customer", "orders.createNewOrder.employee": "Employee and Resources", "orders.createNewOrder.schedule": "Schedule", "orders.createNewOrder.service": "Service", "orders.createNewOrder.title": "Add new order", "orders.display": "Display", "orders.goToPage": "Go to page", "orders.itemName": "Orders", "orders.quoteSent": "Quote sent", "orders.repeating": "Repeating view", "orders.quoteNotSent": "Quote not sent", "orders.quoteAll": "All", "orders.filterQuote": "Quote status", "orders.filterSchedules": "Order type", "orders.filterSchedules.all": "All", "orders.filterSchedules.repeating": "Repeating", "orders.filterSchedules.single": "Single orders", "orders.newOrder.address.noAddresses": "This order does not contain any addresses yet", "orders.newOrder.address.addressNotSaved": "The address is not saved. You must either select an address from the dropdown menu or create a new adress", "orders.newOrder.address.noLatLng": "This address does not have latitude or longitude, and cannot be shown on the map", "orders.newOrder.address.noStreetView": "Street view is not available for this address", "orders.newOrder.address.addressName": "Adress name", "orders.newOrder.address.bathrooms": "Bathrooms", "orders.newOrder.address.bedrooms": "Bedrooms", "orders.newOrder.address.elevator": "Elevator", "orders.newOrder.address.floors": "Floors", "orders.newOrder.address.floor": "Floor", "orders.newOrder.address.garage": "Garage", "orders.newOrder.address.no": "No", "orders.newOrder.address.search": "Search...", "orders.newOrder.address.sectionID": "Apartment ID", "orders.newOrder.address.selectSection": "Select apartment", "orders.newOrder.address.size": "Size", "orders.newOrder.address.totalRooms": "Total rooms", "orders.newOrder.address.sectionId": "Apartment no", "orders.newOrder.address.type": "Type", "orders.newOrder.address.yes": "Yes", "orders.newOrder.addressSection.addAddressStage": "Add new address", "orders.newOrder.addressSection.addOrder": "Add order", "orders.newOrder.addressSection.address": "Address", "orders.newOrder.addressSection.setAddressLater": "Set address later", "orders.newOrder.addressSection.addressPrefix": "Address for", "orders.newOrder.addressSection.back": "Back", "orders.newOrder.addressSection.close": "Close", "orders.newOrder.addressSection.creatingOrder": "Creating order...", "orders.newOrder.addressSection.durationHours": "Duration", "orders.newOrder.addressSection.executionDate": "Execution date", "orders.newOrder.addressSection.orderCreated": "Order created!", "orders.newOrder.addressSection.saveOrder": "Save Order", "orders.newOrder.addressSection.startDate": "Start time", "orders.newOrder.customer.customerInformation": "Add customer", "orders.newOrder.customer.enterCustomerName": "Enter the name of the customer", "orders.newOrder.customer.fillFormBelow": "Search for existing, or add new customer", "orders.newOrder.customer.newCustomer": "New customer", "orders.newOrder.customer.next": "Next", "orders.newOrder.customer.noMatchesFound": "No matches Found", "orders.newOrder.customer.searchForCustomer": "Search for customer", "orders.newOrder.customer.searching": "Searching...", "orders.newOrder.newCustomer.addNewCustomer": "Add new customer", "orders.newOrder.newCustomer.anErrorOccurred": "An error has occurred. Please contact administrator.", "orders.newOrder.newCustomer.back": "Back", "orders.newOrder.newCustomer.customerInformation": "Customer information", "orders.newOrder.newCustomer.email": "Email", "orders.newOrder.newCustomer.fillFormBelow": "Fill the form below in order to create a customer.", "orders.newOrder.newCustomer.name": "Name", "orders.newOrder.newCustomer.firstName": "First name", "orders.newOrder.newCustomer.lastName": "Last name", "orders.newOrder.newCustomer.phone": "Phone", "orders.newOrder.newCustomer.saveCustomer": "Save customer", "orders.newOrder.newCustomer.userExists": "User already exists, the form has been updated.", "orders.newOrder.priceRules.activatePriceRule": "Activate a price rule", "orders.newOrder.priceRules.activePriceRules": "Active price rules", "orders.newOrder.priceRules.noPriceRulesSelected": "No price rules selected", "orders.newOrder.priceRules.priceRules": "Price rules", "orders.newOrder.products.back": "Back", "orders.newOrder.products.next": "Next", "orders.newOrder.products.title": "Choose product", "orders.orderDetails.acceptOrderButton": "Accept order", "orders.orderDetails.customer.refreshPrices.title": "Customer prices", "orders.orderDetails.partner.refreshPrices.title": "Partner prices", "orders.orderDetails.customer.refreshPrices.bold": "This order contains products with where this customer has special prices.", "orders.orderDetails.partner.refreshPrices.bold": "This order contains products with where this partner has special prices.", "orders.orderDetails.refreshPrices.regular": "Do you want to update the order lines in this order with these prices?", "orders.orderDetails.initiateOrderButton": "Initiate order", "orders.orderDetails.printQuote": "Print quote", "orders.orderDetails.printWorkOrder": "Print work order", "orders.orderDetails.acceptanceInfo": "Order acceptance", "orders.orderDetails.acceptedBy": "Accepted by", "orders.orderDetails.acceptedCustomer": "customer", "orders.orderDetails.acceptedEmployee": "employee", "orders.orderDetails.accountingError": "Entry posting failed", "orders.orderDetails.accountingPaymentNotMade": "Payment not finished yet", "orders.orderDetails.accountingStatus": "Accounting status", "orders.orderDetails.accountingStatus.order": "Order", "orders.orderDetails.accountingStatus.getPdf": "Download invoice", "orders.orderDetails.accountingStatus.noAccounting": "Nothing have been posted in accounting", "orders.orderDetails.accountingStatus.payment": "Payment", "orders.orderDetails.accountingStatus.checkPayment": "Get last payment status from accounting", "orders.orderDetails.accountingSuccess": "Posted in accounting", "orders.orderDetails.addressCard.bathrooms": "Bathrooms", "orders.orderDetails.addressCard.shedSize": "Shed", "orders.orderDetails.addressCard.bedrooms": "Bedrooms", "orders.orderDetails.addressCard.addressName": "Description", "orders.orderDetails.addressCard.cancelButton": "Cancel", "orders.orderDetails.addressCard.change": "Change", "orders.orderDetails.addressCard.closeButton": "Close", "orders.orderDetails.addressCard.durationHours": "Duration", "orders.orderDetails.addressCard.editAddress": "Edit address", "orders.orderDetails.addressCard.editAddressNote": "Be aware, If you change any of these values the order will be unassigned from the crew", "orders.orderDetails.addressCard.editAddressTitle": "Edit address", "orders.orderDetails.addressCard.editButton": "Edit", "orders.orderDetails.addressCard.elevator": "Elevator", "orders.orderDetails.addressCard.executionDate": "Execution date", "orders.orderDetails.addressCard.floors": "Floors", "orders.orderDetails.addressCard.floor": "Floor", "orders.orderDetails.addressCard.garage": "Garage", "orders.orderDetails.addressCard.newAddress": "New address", "orders.orderDetails.addressCard.saveButton": "Save", "orders.orderDetails.addressCard.size": "Size", "orders.orderDetails.addressCard.totalRooms": "Total rooms", "orders.orderDetails.addressCard.type": "Type", "orders.orderDetails.addressCard.totalDistance": "Total distance", "orders.orderDetails.addressCard.totalTime": "Duration", "orders.orderDetails.addressFrom": "Address from", "orders.orderDetails.addressInformation": "Address information", "orders.orderDetails.addressRouteTab": "Address and route", "orders.orderDetails.addressRoutes.movingRoute": "Route", "orders.orderDetails.addressRoutes.movingRouteMessage": "Moving route is not available for the given addresses", "orders.orderDetails.addressRoutes.streeViewMessage": "Street view not available for this address", "orders.orderDetails.addressRoutes.title": "Street view", "orders.orderDetails.addressTo": "Address to", "orders.orderDetails.sendQuote.header": "Send quote", "orders.orderDetails.sendQuote.selectSendMethod": "Choose how the quote should be sent", "orders.orderDetails.sendQuote.noPhone": "No phone number registered on the recipient.", "orders.orderDetails.sendQuote.foreignPhone": "A foreign phone number is registered for the customer. You cannot send offers via SMS to foreign numbers.", "orders.orderDetails.sendQuote.noAffiliateContact": "You must add a business customer contact for this order to send quote via SMS.", "orders.orderDetails.sendQuote.noEmail": "No email registered on the recipient.", "orders.orderDetails.sendQuote.sms": "Send by SMS", "orders.orderDetails.sendQuote.email": "Send by email", "orders.orderDetails.sendQuote.noPayment": "Are you sure you want to send the quote without a payment option?", "orders.orderDetails.SendCustomerConfirmation.header": "order confirmation", "orders.orderDetails.SendCustomerConfirmation.toggle": "send order confirmation by email to customer", "order.orderDetails.quoteHiddenPriceModal.title": "Prices are hidden", "order.orderDetails.quoteHiddenPriceModal.boldText": "The selected partner has 'Hide prices' enabled, and is set as payment recipient.", "order.orderDetails.quoteHiddenPriceModal.regularText": "The customer will not be able to view any prices for this order, nor accept it in the customer portal. The order must therefore be accepted manually in Core.", "order.orderDetails.quoteHiddenPriceModal.yes": "Send quote", "order.orderDetails.quoteHiddenPriceModal.no": "Cancel", "order.orderDetails.finishJobVerifyModal.boldText": "By completing this job, the number of hours may be altered.", "order.orderDetails.finishJobVerifyModal.regularText": "When you complete the job, the number of hours is automatically set to the actual number of hours recorded by the crew. Remember to double-check your order lines before sending to payment.", "order.orderDetails.finishJobVerifyModal.yes": "Proceed", "order.orderDetails.finishJobVerifyModal.no": "Cancel", "orders.orderDetails.addPaymentRecipient.header": "Add payment recipient", "orders.orderDetails.addPaymentRecipient.description": "The recipient will receive an SMS with a link to the payment page.", "order.orderDetails.sendToPaymentVerifyModal.boldText": "Sending the order to payment will lock the order.", "order.orderDetails.sendToPaymentVerifyModal.regularText": "The order will not be editable after being sent to payment. This action cannot be undone.", "order.orderDetails.sendToPaymentVerifyModal.regularSecondaryText": "NB: There is no phone number registered for this customer, and the customer will therefore not receive the payment SMS.", "order.orderDetails.sendToPaymentVerifyModal.yes": "Send", "order.orderDetails.sendToPaymentVerifyModal.no": "Cancel", "order.orderDetails.sendToPaymentVerifyModal.fixedPriceOrderLines.title": "The following order lines has tiered quantity pricing", "order.orderDetails.sendToPaymentVerifyModal.fixedPriceOrderLines.description": "Do you wish to convert their quantity to 1?", "order.orderDetails.sendToPaymentVerifyModal.fixedPriceOrderLines.subtext": "This will not affect the total price of the order line", "notifications.markAllAsRead": "Mark all as read", "order.orderDetails.sendToPaymentVerifyModal.fixedPriceOrderLines.converted": "Order lines has been converted", "orders.orderDetails.archiveOrder": "Archive order", "orders.orderDetails.cancelOrder": "Cancel order", "orders.orderDetails.deleteOrder": "Delete order", "orders.orderDetails.duplicateOrder": "Duplicate order", "orders.orderDetails.cargo.itemsTotal": "items total", "orders.orderDetails.cargo.title": "Cargo", "orders.orderDetails.cargoTab": "Cargo", "orders.orderDetails.checklistsTab": "Reports and checklists", "orders.orderDetails.uploadedBy": "Uploaded by", "orders.orderDetails.attachments.invoiceAttached": "Attachment is attached to invoice", "orders.orderDetails.attachments.cannotDelete": "Attached to invoice, cannot be deleted", "orders.orderDetails.attachments.attachToInvoice": "Attach to invoice", "orders.orderDetails.attachments.modal.title": "Upload file", "orders.orderDetails.attachments.modal.button": "Choose file", "orders.orderDetails.attachments.modal.dragHere": "Drag and drop file here", "orders.orderDetails.attachments.modal.dropHere": "Drop file to upload", "orders.orderDetails.attachments.modal.or": "or", "orders.orderDetails.attachments.modal.uploading": "Uploading...", "orders.orderDetails.customerHasNotRated": "No rating given by customer", "orders.orderDetails.customerHasNotRatedYet": "Customer has not responded yet", "orders.orderDetails.customerRating": "Rating", "orders.orderDetails.addServiceRecipient": "Add as service recipient", "orders.orderDetails.subcontractors": "Subcontractors", "order.orderDetails.orderScheduleLabel": "This order is part of an order schedule", "order.orderDetails.orderScheduleLink": "Go to schedule", "orders.orderDetails.detailsCard.closeButtonLabel": "Close", "orders-orderDetails-detailsCard-repeatingJobs": "Job plan", "orders.orderDetails.detailsCard.date": "Invalid date", "notifications.categories.generic": "Generic", "notifications.categories.order": "Order", "notifications.categories.payment": "Payment", "notifications.categories.workOrder": "Work Order", "notifications.categories.note": "Note", "notifications.categories.rating": "Rating", "orders.orderDetails.detailsCard.durationLabel": "Duration", "orders.orderDetails.detailsCard.nextExecution": "Next execution", "orders.orderDetails.detailsCard.nextExecution.notPlanned": "This schedule has not been planned yet", "orders.orderDetails.detailsCard.editButtonLabel": "Edit", "orders.orderDetails.detailsCard.editOrderNote": "Be aware, If you change any of these values the order will be unassigned from the crew", "orders.orderDetails.detailsCard.editOrderTitle": "Edit order", "orders.orderDetails.detailsCard.executionDateLabel": "Execution date", "orders.orderDetails.detailsCard.startDateLabel": "Jobs shall be created after", "orders.orderDetails.detailsCard.from": "Duration from", "orders.orderDetails.detailsCard.hours": "Hours", "orders.orderDetails.detailsCard.jobDateLabel": "Job date", "orders.orderDetails.detailsCard.jobTimeLabel": "Start time", "orders.orderDetails.detailsCard.range": "Range", "orders.orderDetails.detailsCard.refundButtonLabel": "Refund", "orders.orderDetails.detailsCard.required": "Please enter a valid duration", "orders.orderDetails.detailsCard.saveButtonLabel": "Save", "orders.orderDetails.detailsCard.title": "Order", "orders.orderDetails.detailsCard.to": "Duration to", "orders.orderDetails.createWorkOrder.title": "Create job", "orders.orderDetails.createWorkOrder.schedule.title": "Create job plan", "orders.orderDetails.createWorkOrder.copyAddresses": "Copy addresses from order", "orders.orderDetails.editWorkOrder.title": "Edit job", "orders.orderDetails.editWorkOrder.schedule.title": "Edit job plan", "orders.orderDetails.duplicateAddress.deleteAddress": "Delete address", "orders.orderDetails.duplicateAddress.disabledToolTip": "Addresses cannot be added after job has been completed", "orders.orderDetails.duplicateAddress.selectAddress": "Search for address", "orders.orderDetails.duplicateAddress.selectStage": "Select stage you want to add the address to", "orders.orderDetails.duplicateAddress.title": "Add address", "orders.orderDetails.editButton": "Edit", "orders.orderDetails.emailLabel": "Email:", "orders.orderDetails.externalPayment": "External payment", "orders.orderDetails.hiddenPricesLabel": "Prices hidden", "orders.orderDetails.hiddenPricesTooltip": "The customer will not be able to view any prices for this order as the selected partner has 'Hide prices' enabled, and is set as payment recipient.", "orders.orderDetails.externalPaymentTooltip": "Set the payment of this order to be handled externally outside the regular payment flow", "orders.orderDetails.externalPaymentWarning": "Because of this orders status, sending this to external payment will close the order and mark it as paid. Are you sure you want to continue?", "orders.orderDetails.finishJob": "Mark job as completed", "orders.orderDetails.finishJobTooltip": "Override the crew app and mark the job as completed", "orders.orderDetails.notAccepted": "Not accepted yet", "orders.orderDetails.notesOrderInfoTab": "Notes and order info", "orders.orderDetails.orderAcceptedMessage": "Order is accepted", "orders.orderDetails.orderNotes.NotCustomerQuestionnaire": "No questions available", "orders.orderDetails.orderNotes.fromCustomerPortal": "Questions from customer portal", "orders.orderDetails.orderNotes.fromEmbed": "Questions from booking form", "orders.orderDetails.orderNotes.addNote": "Add note", "orders.orderDetails.orderNotes.customerInput": "Customer input", "orders.orderDetails.orderNotes.customerNotResponded": "Customer has not responded", "orders.orderDetails.orderNotes.customerQuestionnaire": "Customer questionnaire", "orders.orderDetails.orderNotes.embedCustomerQuestionnaire": "Booking customer questionnaire", "orders.orderDetails.orderNotes.daysAgo": "days ago", "orders.orderDetails.orderNotes.deleted": "Deleted", "orders.orderDetails.orderNotes.description": "Please add notes.", "orders.orderDetails.orderNotes.external": "Visible to customer", "orders.orderDetails.orderNotes.hoursAgo": "hours ago", "orders.orderDetails.orderNotes.internal": "Internal", "orders.orderDetails.orderNotes.internalNote": "Internal note", "orders.orderDetails.orderNotes.justNow": "just now", "orders.orderDetails.orderNotes.messageDeletedBy": "Message deleted by", "orders.orderDetails.orderNotes.minutesAgo": "minutes ago", "orders.orderDetails.orderNotes.secondsAgo": "seconds ago", "orders.orderDetails.orderNotes.title": "Notes", "orders.orderDetails.orderNotes.updated": "Updated", "orders.orderDetails.orderNotes.visibleToCustomerToolTip": "Notes marked as 'Visible to customer' will be shown to the customer. Unchecked notes are only visible to company employees", "orders.orderDetails.orderNumber": "Order #", "orders.orderDetails.orderPartner.closeButtonLabel": "Close", "orders.orderDetails.orderPartner.delete": "Delete partner", "orders.orderDetails.orderPartner.disabledToolTip": "Partner cannot be changed after order has been sent to payment", "orders.orderDetails.orderPartner.partner": "Partner", "orders.orderDetails.orderPartner.paymentRecipient": "Payment recipient", "orders.orderDetails.paymentRecipient.modal.title": "Select payment recipient", "orders.orderDetails.paymentRecipient.modal.customer": "Select customer", "orders.orderDetails.paymentRecipient.modal.paymentRecipient": "Select payment recipient", "orders.orderDetails.paymentRecipient.modal.serviceRecipient": "Select service recipient", "orders.orderDetails.paymentRecipient.modal.orSet": "Set payment recipient and service recipient individually", "orders.orderDetails.orderPartner.saveButtonLabel": "Save", "orders.orderDetails.orderPartner.searchForPartner": "Search for partner", "orders.orderDetails.orderPartner.searchLabel": "Enter the name of the customer", "orders.orderDetails.orderPartner.title": "Add partner", "orders.orderDetails.orderPartner.titleSubtext": "Search for partner below to add to the order", "orders.orderDetails.orderProducts.addCustomProduct": "Custom product", "orders.orderDetails.orderProducts.addCustomProductBtn": "Net payment", "orders.orderDetails.orderProducts.addProduct": "Add product", "orders.orderDetails.orderProducts.editProduct": "Edit product", "orders.orderDetails.orderProducts.addPricerule": "Add pricerule", "orders.orderDetails.orderProducts.chooseProductVariant": "Choose product variant", "orders.orderDetails.orderProducts.noProductVariantSelected": "No product variant added", "orders.orderDetails.orderProducts.productVariants": "Product variants", "orders.orderDetails.orderProducts.activeAProductVariants": "Activate a product variant", "orders.orderDetails.orderProducts.activeProductVariants": "Active product variants", "orders.orderDetails.orderProducts.cancel": "Cancel", "orders.orderDetails.orderProducts.cancelCustomProduct": "Back", "orders.orderDetails.orderProducts.cancelCustomProductBtn": "Net payment", "orders.orderDetails.orderProducts.customProductPrice": "Price", "orders.orderDetails.orderProducts.customProductTitle": "Name", "orders.orderDetails.orderProducts.customProductModalTitle": "Custom product", "orders.orderDetails.orderProducts.discount": "Discount", "orders.orderDetails.orderProducts.refundOrderLine": "Refund", "orders.orderDetails.orderProducts.discountAmount": "Discount amount", "orders.orderDetails.orderProducts.edit": "edit", "orders.orderDetails.orderProducts.grandTotal": "Subtotal", "orders.orderDetails.orderProducts.id": "ID", "orders.orderDetails.orderProducts.netPayment": "Net payment", "orders.orderDetails.orderProducts.noManualPriceRule": "No manual price rules available for this order line", "orders.orderDetails.orderProducts.noPriceRules": "No manual price rules available for this product", "orders.orderDetails.orderProducts.orderSummary": "Order Summary", "orders.orderDetails.orderProducts.outOfStock": "Out of stock", "orders.orderDetails.orderProducts.performRefund": "Perform", "orders.orderDetails.orderProducts.price": "Unit price", "orders.orderDetails.orderProducts.product": "Product", "orders.orderDetails.orderProducts.quantity": "Quantity", "orders.orderDetails.orderProducts.quantityRequired": "Quantity is required", "orders.orderDetails.orderProducts.useFixedPrice": "Use fixed price", "orders.orderDetails.orderProducts.fixedPrice": "Fixed price", "orders.orderDetails.orderProducts.setTotalPrice": "Fixed price", "orders.orderDetails.orderProducts.refund": "Refund", "orders.orderDetails.orderProducts.refundEditQuantityNotAllowedFixedPrice": "Since a fixed price price rule is used, the quantity of the refund cannot be set", "orders.orderDetails.orderProducts.refundNotAllowedQuantity": "Quantity is too low for refund", "orders.orderDetails.orderProducts.refundPaymentMethodNotAllowed": "Refund is not possible for this payment method", "orders.orderDetails.orderProducts.refunded": "Refunded", "orders.orderDetails.orderProducts.refundedAmount": "Refunded amount", "orders.orderDetails.orderProducts.save": "Save", "orders.orderDetails.orderProducts.remove": "Remove", "orders.orderDetails.orderProducts.selectProduct": "Select product", "orders.orderDetails.orderProducts.setDiscount": "Set discount", "orders.orderDetails.orderProducts.modalHeader": "Choose the orderline to discount", "orders.orderDetails.orderProducts.produtcs": "Producs", "orders.orderDetails.orderProducts.unitPrice": "Unit price", "orders.orderDetails.orderProducts.setDiscountDisabledTooltip": "Discount cannot be altered after the order has been sent to payment", "orders.orderDetails.orderProducts.discountAmountAlreadySetTooltip": "Orders with discount order lines can not be set up with discount in percentage. Delete the discount order lines first.", "orders.orderDetails.orderProducts.sorry": "Sorry", "orders.orderDetails.orderProducts.submitCustomProduct": "Save", "orders.orderDetails.orderProducts.total": "Total", "orders.orderDetails.orderProducts.usePercent": "Use percent", "orders.orderDetails.orderProducts.salesPriceExVat": "Total excl. VAT", "orders.orderDetails.orderProducts.vatAmount": "VAT", "orders.orderDetails.orderProducts.totalPricePopup.bold": "Setting a fixed price will remove all automation for this order line.", "orders.orderDetails.orderProducts.totalPricePopup.regular": "This means that all price rules and any automatic quantity or price calculations will be removed. This action is not reversible.", "orders.orderDetails.orderProducts.totalPricePopup.yes": "Proceed", "orders.orderDetails.orderProducts.totalPricePopup.no": "Cancel", "orders.orderDetails.orderProducts.invalidAmount": "Invalid discount amount", "orders.orderDetails.orderProducts.invalidAmountTooltip": "The discount amount is too low. The lowest amount you can set on this order line is ", "orders.orderDetails.orderReports.extern": "Visible to customer", "orders.orderDetails.orderReports.internal": "Internal", "orders.orderDetails.otherActions": "Other actions", "orders.orderDetails.paymentInformation": "Payment information", "orders.orderDetails.paymentInformation.modal.title": "Preselect payment method", "orders.orderDetails.paymentInformation.paymentMethod": "Payment method", "orders.orderDetails.paymentInformation.invoiceSettings": "Invoice settings", "orders.orderDetails.paymentInformation.default.modal.regular": "The customer can now choose freely between the following options when the order is sent to payment:", "orders.orderDetails.paymentInformation.default.modal.regularNoMethods": "IMPORTANT: None of the payment methods in your company are available for selection by the customer. If you do not make a payment method available for the customer (or change the payment method on this order), the customer will not be able to pay.", "orders.orderDetails.paymentInformation.external.modal.regularWithAccounting": "Payment is handled externally (like a card terminal or cash payment).", "orders.orderDetails.paymentInformation.external.modal.regularNoAccounting": "Payment is handled externally (like a card terminal, cash payment, or invoice).", "orders.orderDetails.paymentInformation.invoice.modal.regular.sent": "Invoice has been sent to the payment recipient.", "orders.orderDetails.paymentStatusLabel": "Payment status:", "orders.orderDetails.paymentStatus.markAsPaid": "Mark as paid", "orders.orderDetails.fullyRefunded": "Refunded", "orders.orderDetails.fullyRefundedPendingInvoice": "Invoice sent, order refunded", "orders.orderDetails.fullyRefundedAndPaid": "Invoice paid, order refunded", "orders.orderDetails.partiallyRefunded": "Partially refunded", "orders.orderDetails.partiallyRefundedPendingInvoice": "Invoice sent, order partially refunded", "orders.orderDetails.partiallyRefundedAndPaid": "Invoice paid, order partially refunded", "orders.orderDetails.paymentTypeLabelBeforeSentToPayment": "Preselected method:", "orders.orderDetails.paymentTypeLabelAfterSentToPayment": "Payment method:", "orders.orderDetails.invoiceNotSent": "Invoice not sent", "orders.orderDetails.invoiceSent": "Invoice sent", "orders.orderDetails.invoiceSendAgain": "Send again", "orders.orderDetails.invoiceSettings.title": "Invoice settings", "orders.orderDetails.invoiceSettings.invoiceSendType": "Invoice send type", "orders.orderDetails.invoiceSettings.dueDate": "Due date", "orders.orderDetails.invoiceSettings.dueDatePostFix": "days", "orders.orderDetails.invoiceSettings.invoiceReference": "Invoice reference", "orders.orderDetails.invoiceSettings.invoiceComment": "Invoice comment", "orders.orderDetails.invoiceSettings.invoiceEmail": "Invoice email", "orders.orderDetails.invoiceSettings.disabledLabel": "Locked after invoice is sent", "orders.orderDetails.phoneLabel": "Phone:", "orders.orderDetails.refund.VAT": "VAT", "orders.orderDetails.refund.fullRefund": "Refund whole order", "orders.orderDetails.refund.availableRefund": "available for refund", "orders.orderDetails.refund.cancelButtonLabel": "Cancel", "orders.orderDetails.refund.fixedPrice": "Fixed price", "orders.orderDetails.refund.id": "ID", "orders.orderDetails.refund.price": "Price", "orders.orderDetails.refund.product": "Product", "orders.orderDetails.refund.oneRefundOnlyTitle": "Only one refund allowed", "orders.orderDetails.refund.oneRefundOnlyBody": "Because of limitations in Fiken, a payment can only be refunded once, be it a partial or full refund.", "orders.orderDetails.refund.productsSubTotal": "Products subtotal", "orders.orderDetails.refund.quantity": "Quantity", "orders.orderDetails.refund.reasonForRefund": "Reason for refund", "orders.orderDetails.refund.refund": "Refund", "orders.orderDetails.refund.refundAmount": "Refund Amount", "orders.orderDetails.refund.refundSummary": "Refund Summary", "orders.orderDetails.refund.refundTotal": "Refund total", "orders.orderDetails.refund.sendRefundNotification": "Send a notification to the customer", "orders.orderDetails.refund.total": "Total", "orders.orderDetails.refundButtonLabel": "Refund", "orders.orderDetails.sendConfirmationButtonLabel": "Send order confirmation email", "orders.orderDetails.removeOrderFromArchive": "Retrieve from archive", "orders.orderDetails.resources.crew": "Crew", "orders.orderDetails.resources.enlargedImage": "Enlarged image", "orders.orderDetails.resources.finished": "Finished", "orders.orderDetails.resources.noCrewAssigned": "No crew has been assigned", "orders.orderDetails.resources.noReportsMade": "No items have been added", "orders.orderDetails.resources.reports": "Reports", "orders.orderDetails.resources.timeTracking": "Time tracking", "orders.orderDetails.resources.timeTracking.toggleLabel": "Per employee", "orders.orderDetails.resources.timeTracking.noTrackings": "The job has not started, time tracking not available", "orders.orderDetails.resources.timeTracking.noCheckins": "No employees have checked into this order", "orders.orderDetails.resources.totalTime": "Total invoiceable time", "orders.orderDetails.resources.totalUserTime": "Total", "orders.orderDetails.resources.ongoing": "Ongoing", "orders.orderDetails.resourcesReportsTab": "Employees and resources", "orders.orderDetails.savingAddress": "Saving address...", "orders.orderDetails.sendQuoteButton": "Send quote", "orders.orderDetails.confirmManually": "Confirm manually", "orders.orderDetails.confirmManually.withoutConfirmation": "Confirm", "orders.orderDetails.confirmManually.withConfirmation": "Confirm and order send confirmation", "orders.orderDetails.sendQuoteNoAffiliateContact": "In order to send a quote directly from <PERSON>, you must select or create a business contact for this order", "orders.orderDetails.sendToPayment": "Send to payment", "orders.orderDetails.sendToSubContractor": "Send to subcontractor", "orders.orderDetails.creditInvoice": "Credit invoice", "orders.orderDetails.creditInvoice.tooltip": "Partially refunded invoices cannot be credited.", "orders.orderDetails.resetAccounting": "Reverse accounting record", "orders.orderDetails.captureManually": "Capture payment manually", "orders.orderDetails.markOrderAsPaid": "Mark as paid", "orders.orderDetails.sendToSubContractor.title": "Send job to subcontractor", "orders.orderDetails.sendToSubContractor.searchPlaceholder": "Search for subcontractor", "orders.orderDetails.sendPaymentSms": "Add payment recipient", "orders.orderDetails.sendToPaymentPaymentBtn": "Send to payment", "orders.orderDetails.sendToPaymentPaymentTooltip": "Send this order to payment, this can only be done when the order status is Finished and order total is greater than 0,-", "orders.orderDetails.sendToPaymentTooltip": "Notifies the customer that the order can be paid. Can only be done when job is completed", "orders.orderDetails.sentToPaymentPaymentBtn": "Sent to payment", "orders.orderDetails.goToReportinatorReport": "Go to valuation report", "orders.orderDetails.createToReportinatorReport": "Create new valuation report for order", "orders.orderDetails.enableProject": "Enable projectview", "orders.orderDetails.disableProject": "Disable projectview", "orders.orderDetails.closeOrder": "Close order", "order.orderDetails.closeOrderVerifyModal.boldText": "This will close the order.", "order.orderDetails.closeOrderVerifyModal.regularText": "If you have any pending payments or unfinished jobs, we recommend you handle these first.", "orders.orderDetails.stage.orderTasks.noTasks": "No tasks in this stage", "orders.orderDetails.stage.orderTasks.tasks": "Tasks", "orders.orderDetails.stage.stageNotStarted": "Not started", "orders.orderDetails.stage.stageOngoing": "Ongoing", "orders.orderDetails.stage.title": "Select stage", "orders.orderDetails.title": "Order Details", "orders.orderDetails.orderDiscount.title": "Order discount", "orderDetails.website": "from Website", "orderDetails.dateTime.reschedule": "Reschedule", "orderDetails.dateTime.createSchedule": "Create schedule", "orderDetails.dateTime.editSchedule": "Edit schedule", "orderDetails.dateTime.dateTime": "Date & time", "orderDetails.dateTime.repeats": "Repeats", "orderDetails.dateTime.addAnotherDate": "Add timeslot", "orderDetails.comment.title": "Invoice comment", "orderDetails.comment.add": "Add invoice comment", "workOrder.deletePaymentModal.titleTranslationKey": "Delete attached payment?", "workOrder.deletePaymentModal.bodyRegularTranslationKey": "There is a payment attached to this job.", "workOrder.deletePaymentModal.bodyRegularSecondaryTranslationKey": "Do you wish to delete this along with the job?", "workOrder.deleteChildrenModal.titleTranslationKey": "Delete unstarted jobs?", "workOrder.deleteChildrenModal.bodyRegularTranslationKey": "This schedule has created some jobs that has not been started yet.", "workOrder.deleteChildrenModal.bodyRegularSecondaryTranslationKey": "Do you wish to delete them as well?", "workOrder.list.title": "Jobs", "workOrder.list.column.title": "Title", "workOrder.list.column.status": "Status", "workOrder.list.column.customer": "Customer", "workOrder.list.column.paymentStatus": "Payment status", "workOrder.list.column.date": "Date", "workOrder.list.column.time": "Time", "workOrder.list.column.assignees": "Assigned employees", "workOrder.list.column.subcontractor": "Subcontractor/Authority", "workOrder.list.column.address": "Address", "workOrder.list.receivedFrom": "Received from", "workOrder.list.showAll": "Show all jobs", "workOrder.list.next5": "Next 5 upcoming jobs", "workOrder.list.deleteSelected": "Delete selected jobs", "workOrder.list.modal.title": "Jobs for", "workOrder.list.filter.status": "Status", "workOrder.list.filter.execution": "Execution date", "workOrder.list.filter.employees": "Employees", "workOrder.list.filter.resources": "Resources", "workOrder.list.filter.received": "Received as contractor", "workOrder.list.filter.sent": "Sent to contractor", "workOrder.list.filter.unplanned": "Unplanned", "workOrder.list.sorting.created": "Created at", "workOrder.list.delete": "Delete", "workOrder.list.finish": "Finish", "workOrder.receivedList.column.contractingAuthority": "Contracting authority", "workOrder.receivedList.column.receivedAt": "Received at", "workOrder.receivedList.youHaveNotAnswered": "You have not answered", "workOrder.receivedList.accepted": "Accepted", "workOrder.receivedList.declinedBy": "Decline by", "workOrder.receivedList.column.declined": "Declined", "orderDetails.refundPayments.title": "Refunds", "orderDetails.payments.title": "Payments", "orderDetails.payments.add": "Add payment", "orderDetails.payments.noPayments": "No payments created yet", "orderDetails.payments.invoiceDate": "Invoice date", "orderDetails.payments.paymentStatusInvoiceNotSent": "Invoice not sent", "orderDetails.payments.paymentMethod": "Payment method", "orderDetails.payments.dueDate": "Due date", "orderDetails.payments.invoiceEmail": "Invoice e-mail", "orderDetails.payments.invoiceReference": "Invoice reference", "orderDetails.payments.invoiceSendType": "Send type", "orderDetails.payments.paidAt": "Payment date", "orderDetails.payments.paidAmount": "Paid amount", "orderDetails.payments.paymentStatus": "Payment status", "orderDetails.payments.paymentRecipient": "Recipient", "orderDetails.payments.autoSendAt": "Auto send at", "orderDetails.payments.notPaidYet": "Not paid yet", "orderDetails.payments.setByConsolidatedInvoice": "Set by consolidated invoice", "orderDetails.payments.invoicePDF": "Invoice PDF", "orderDetails.payments.download": "Download", "orderDetails.payments.accountingStatus": "Accounting status", "orderDetails.payments.paymentAccountingStatus": "Payment accounting status", "orderDetails.payments.paymentReference": "Payment reference", "orderDetails.payments.eventLog": "Open event log", "orderDetails.payments.project": "Project", "orderDetails.payments.department": "Department", "orderDetails.payments.goToConsolidationContainer": "Open consolidated invoice", "orderDetails.payments.projectAndDepartment": "Set project and department", "orderDetails.payments.projectAndDepartment.projectOnly": "Set project", "orderDetails.payments.projectAndDepartment.title": "Set project and department for payment", "orderDetails.payments.projectAndDepartment.title.projectOnly": "Set project for payment", "orderDetails.payments.projectAndDepartment.project.placeholder": "Search for project in accounting", "orderDetails.payments.projectAndDepartment.project.label": "Project", "orderDetails.payments.projectAndDepartment.department.placeholder": "Search for department in accounting", "orderDetails.payments.projectAndDepartment.department.label": "Department", "orderDetails.payments.subscriptionEnabled": "Subscription available", "orderDetails.payments.comment": "Comment", "orderDetails.payments.invoiceNotSent": "Payment must be sent first", "orderDetails.payments.paymentReminder": "Payment reminder", "orderDetails.payments.paymentReminder.disabledToolTip": "Can't be enabled after payment has been sent", "orderDetails.payments.nextPaymentReminder": "Next payment reminder", "orderDetails.payments.sentAt": "Payment sent", "orderDetails.payments.list.title": "Payments", "orderDetails.payments.list.paymentStatus": "Status", "orderDetails.payments.list.totalAmount": "Amount", "orderDetails.payments.list.createdAt": "Created at", "orderDetails.payments.list.sentAt": "<PERSON><PERSON>", "orderDetails.payments.list.paidAt": "Paid at", "orderDetails.payments.list.paid": "Paid", "orderDetails.payments.list.autoSendAt": "Auto send at", "orderDetails.payments.list.autoSendAtButSent": "Payment is sent, it was planned for", "order.orderDetails.customer.title": "Customer", "order.orderDetails.customer.changeCustomer": "Change customer", "order.orderDetails.customer.setCustomer": "Choose customer", "order.orderDetails.customer.paymentRecipient": "Payment recipient", "order.orderDetails.customer.addAffiliateContact": "Add business contact", "order.orderDetails.customer.changeAffiliateContact": "Change business contact", "order.orderDetails.customer.setPaymentRecipient": "Set other payment recipient", "order.orderDetails.customer.addAffiliateContactModal.title": "Add business contact", "order.orderDetails.customer.addAffiliateContactModal.searchPlaceholder": "Select business contact (optional)", "order.orderDetails.customer.addAffiliateContactModal.affiliateContact": "Business contact", "order.orderDetails.customer.addAffiliateContactModal.createNew": "Create new business contact", "order.orderDetails.customer.serviceRecipient": "Service recipient", "order.orderDetails.customer.noServiceRecipient": "Service recipient is not specified", "order.orderDetails.customer.removeServiceRecipientModalInfo": "This will remove the current service recipient and set the payment recipient as recipient of both payment and service", "order.orderDetails.customer.paymentMethod": "Payment method", "order.orderDetails.customer.paymentMethod.selectPayment": "Preselect a payment for this order", "order.orderDetails.customer.paymentMethod.paymentChosen": " is preselected for this order", "order.orderDetails.customer.paymentMethod.dueDate": "Due date", "order.orderDetails.customer.paymentMethod.dueDate.in": "In", "order.orderDetails.customer.paymentMethod.dueDate.for": "", "order.orderDetails.customer.paymentMethod.dueDate.ago": "ago", "order.orderDetails.customer.paymentMethod.days": "days", "order.orderDetails.customer.paymentMethod.sendAs": "Send as", "order.orderDetails.customer.paymentMethod.sentAs": "<PERSON><PERSON> as", "orderDetails.customer.editCustomer.title": "Edit customer", "order.orderDetails.customer.partner": "Partner", "order.orderDetails.customer.partner.selectPartner": "Select a partner for this order", "order.orderDetails.customer.partner.partnerChosen": "is selected for this order", "order.orderDetails.customer.partner.contact": "Contact", "orderDetails.addresses.multiModal.boldKey": "This will add the address to all jobs in the order", "orderDetails.addresses.multiModal.regularKey": "Do you want to do continue?", "orderDetails.addresses.deleteModal.multiple.boldKey": "This address is in use by multiple jobs in this order, and deleting it will remove it for all jobs.", "orderDetails.addresses.deleteModal.multiple.regularKey": "Do you want to do continue?", "orderDetails.addresses.multipleWorkOrders": "Multiple jobs", "orderDetails.jobAddress.title": "Addresses", "orderDetails.jobAddress.modal.title": "Edit address", "orderDetails.jobAddress.streetview.title": "Street view", "orderDetails.jobAddress.streetview.openStreetview": "Street view", "orderDetails.jobAddress.streetview.notAvailable": "Street view is not available", "orderDetails.customerFeedback.title": "Questionnaire", "orderDetails.customerFeedback.questionnaire": "Questionnaire", "orderDetails.customerFeedback.questionnaire.creatQuestion": "Create questions", "orderDetails.customerFeedback.questionnaire.notAnswered": "The customer will be able to answer the questions in the customer portal", "orderDetails.customerFeedback.questionnaire.Answered": "Customer has answered the questionnaire", "orderDetails.customerFeedback.questionnaire.button": "Click to see results", "orderDetails.customerFeedback.questionnaire.result": "Questionnaire", "orderDetails.customerFeedback.questionnaire.modal.title": "Questionnaire", "orderDetails.customerFeedback.questionnaire.modal.header": "Customer questionnaire", "orderDetails.customerFeedback.questionnaire.clickHere": "Click here to create a template", "orderDetails.customerFeedback.questionnaire.noTemplate": "You have no customer question templates", "orderDetails.customerFeedback.CustomerRating": "Customer review", "orderDetails.customerFeedback.CustomerRating.notRated": "No rating given by customer", "orderDetails.customerFeedback.CustomerRating.customerHasNotRatedYet": "Customer has not responded yet", "orderDetails.customerFeedback.questionnaire.selectTemplates": "Select templates", "orderDetails.customerFeedback.questionnaire.noTemplatesAvailable": "No templates available.", "orderDetails.customerFeedback.questionnaire.addSelectedTemplates": "Add Selected templates", "orderDetails.customerFeedback.questionnaire.createCq": "Create question", "orderDetails.customerFeedback.questionnaire.orderCustomerQuestions": "Customer questions", "orderDetails.customerFeedback.questionnaire.choices": "Choices", "orderDetails.customerFeedback.questionnaire.verifySetChoiceModal.title": "Do you want to answer for the customer?", "orderDetails.customerFeedback.questionnaire.customerInput": "Customer answer", "orderDetails.customerFeedback.questionnaire.addMoreTemplatesLabel": "Add from templates", "orderDetails.customerFeedback.questionnaire.selectTemplatePlaceholder": "Select a template", "orderDetails.customerFeedback.questionnaire.addButton": "Add", "orderDetails.customerFeedback.questionnaire.deleteButton": "Delete", "orderDetails.customerFeedback.questionnaire.selectTemplate": "Select customer question templates", "orderDetails.customerFeedback.questionnaire.selectTemplate.text": "Choose customer question templates you would like to include in the questionnaire", "orderDetails.crewChecklist.newTask.label": "New task", "orderDetails.crewChecklist.newTask.taskName.placeholder": "Task description", "orderDetails.crewChecklist.newTask.taskComment.placeholder": "Comment (optional)", "orderDetails.crewChecklist.newTask.button": "Add task", "orderDetails.crewChecklist.newTaskGroup.label": "New category", "orderDetails.crewChecklist.newTaskGroup.placeholder": "Enter a category name", "orderDetails.crewChecklist.newTaskGroup.button": "Create task category", "orderDetails.crewChecklist.fromTemplate.placeholder": "Add from task template", "orderDetails.crewChecklist.modal.header": "Checklist", "orderDetails.crewChecklist.modal.addChecklist": "Add checklist", "orderDetails.crewChecklist.modal.noTemplates": "No checklist template", "orderDetails.crewChecklist.modal.generateTemplate": "Generate a checklist template", "orderDetails.crewChecklist.modal.addChecklistTemplate": "Add checklist templates", "orderDetails.crewChecklist.selectWorkOrder": "Select a job to view check list", "orderDetails.crewChecklist.title": "Checklist", "orderDetails.crewChecklist.addChecklist": "Add checklist", "orderDetails.crewChecklist.checklistOngoing": "The crew has started to check of items in the checklist", "orderDetails.crewChecklist.checklistFinished": "Checklist is complete!", "orderDetails.crewChecklist.notStarted": "The crew has not started checking items off the checklist", "orderDetails.crewChecklist.repeating": "Here you can manage the checklists used by your job plan", "orderDetails.crewChecklist.checklistButton": "See full checklist", "orderDetails.crewChecklist.finished": "Finished jobs", "orderDetails.crewChecklist.upcoming": "Upcoming jobs", "orderDetails.crewChecklist.noUpcomingWorkOrders": "This order has no upcoming jobs", "orderDetails.crewChecklist.noFinishedWorkOrders": "This order has no finished jobs", "orderDetails.timetracking.title": "Timetracking", "orderDetails.timetracking.convertTrackedTime": "Register tracked time", "orderDetails.timetracking.convertTrackedTimeTooltip": "Activate this to define the employees' salary basis by the time they have entered themselves (check in and check out). When this is deactivated, the salary basis will be defined by the planned duration of the assignment.", "orderDetails.timetracking.btn.job": "Job", "orderDetails.timetracking.btn.crew": "Crew", "orderDetails.timetracking.job.start": "Start", "orderDetails.timetracking.job.end": "End", "orderDetails.timetracking.job.total": "Total", "orderDetails.timetracking.job.noData": "No registration", "orderDetails.timetracking.modal.title": "Time tracking settings", "orderDetails.timetracking.modal.activity.order": "Select activity for the order", "orderDetails.timetracking.modal.activity.workOrder": "Select activity for the job", "orderDetails.timetracking.modal.activity.transportMinutes.activate": "Add automatic time tracking for transport", "orderDetails.timetracking.modal.activity.transportMinutes.activate.description": "If you activate this, you can enter a fixed number of minutes to be registered as a separate time tracking for each employee assigned to this job. The time tracking will be created when the job is completed.", "orderDetails.timetracking.modal.activity.transportMinutes.activate.disabled": "You have not selected a default activity for transport. This can be done under Settings -> Sal<PERSON>", "orderDetails.timetracking.modal.activity.transportMinutes": "Number of minutes of transport time", "orderDetails.timetracking.modal.pause.toggleLabel": "Add automatic break", "orderDetails.timetracking.modal.pause.infoHelp": "When you add a break automatically, a break will be added at the end of the job's time tracking for all employees assigned to the job. You can specify a specific time for the start of the break if you want a given period for the break.", "orderDetails.timetracking.modal.pause.duration": "Break duration", "orderDetails.timetracking.modal.pause.start": "Start time for break (optional)", "orderDetails.notes.title": "Notes", "orderDetails.notes.visibleToCustomer": "Public", "orderDetails.notes.addNote": "Add note", "orderDetails.notes.unconfirmedWarning.title": "The customer will not be notified", "orderDetails.notes.unconfirmedWarning.bodyRegular": "The customer will not receive an email notification about new messages as long as the order is unconfirmed and/or a quote has not been sent.", "orderDetails.notes.tooltip": "Public", "orderDetails.notes.daysAgo": "days ago", "orderDetails.notes.deleted": "Deleted", "orderDetails.notes.description": "Please add notes.", "orderDetails.notes.editNote": "Edit note/message", "orderDetails.notes.hoursAgo": "hours ago", "orderDetails.notes.internal": "Internal", "orderDetails.notes.external": "Public", "orderDetails.notes.internalNote": "Internal note", "orderDetails.notes.justNow": "just now", "orderDetails.notes.messageDeletedBy": "Message deleted by", "orderDetails.notes.minutesAgo": "minutes ago", "orderDetails.notes.secondsAgo": "seconds ago", "orderDetails.notes.updated": "Updated", "orderDetails.notes.visibleToCustomerToolTip": "Notes marked as 'Public' will be shown to the customer. Unchecked notes are only visible to company employees", "orderDetails.notes.tabs.customer": "Customer messages", "orderDetails.notes.tabs.internal": "Internal notes", "orderDetails.notifications.title": "Customer notifications", "orderDetails.notifications.emailToggle": "Emails", "orderDetails.notifications.smsToggle": "Automatic customer notifications", "orderDetails.reports.title": "Crew reports", "orderDetails.attachments.title": "Attachments", "orders.orderDetails.attachments.visibleToCustomer": "Available to customer", "orders.orderDetails.attachments.visibleToCrew": "Available to crew", "orderDetails.reports.total": "Total", "orderDetails.reports.attachmentsAdded": "Attachments added", "orderDetails.reports.btn": "See all Attachments", "orderDetails.reports.modal.title": "All attachments ", "orderDetails.timeline.title": "Timeline", "orderDetails.timeline.all": "All", "orderDetails.timeline.filterBy": "Filter by", "orderDetails.timeline.noData": "No data", "orderDetails.timeline.by": "by", "orderDetails.timeline.today": "Today", "orderDetails.timeline.opened": "Opened", "orderDetails.timeline.notOpened": "Not opened", "orderDetails.projectsAndDepartments.title": "Project and department", "orderDetails.projectsAndDepartments.title.projectOnly": "Project", "orderDetails.projectsAndDepartments.project": "Project", "orderDetails.projectsAndDepartments.project.placeholder": "Search for accounting projects", "orderDetails.projectsAndDepartments.department": "Department", "orderDetails.projectsAndDepartments.department.placeholder": "Search for accounting departments", "orderDetails.orderLines.title": "Order lines", "orderDetails.orderLines.pricesIncVat": "Prices incl. VAT", "orderDetails.orderLines.pricesExVat": "Prices excl. VAT", "orderDetails.orderLines.createPayment": "Send to payment", "orderDetails.orderLines.description": "Description", "orderDetails.orderLines.quantity": "Quantity", "orderDetails.orderLines.unitPrice": "Unit price", "orderDetails.orderLines.discount": "Discount", "orderDetails.orderLines.discount.modal.title": "Set discount", "orderDetails.orderLines.discount.modal.label": "Set discount for order lines that is checked", "orderDetails.orderLines.total": "Total", "orderDetails.orderLines.status": "Status", "orderDetails.orderLines.unit": "Unit", "orderDetails.orderLines.vat": "VAT", "orderDetails.orderLines.chooseProduct": "Choose product (optional)", "orderDetails.orderLines.paymentStatusTooltip": "This order line has not been added to any payment yet.", "orderDetails.orderLines.lockedTooltip": "This order line is part of a payment, and is therefore locked. If the payment has not been sent you can edit the order line within the payment, click the lock to open it.", "orderDetails.orderLines.productLinkedTooltip1": "This order line is connected to the product", "orderDetails.orderLines.productLinkedTooltip2": "Click to go to the product", "orderDetails.orderLines.addOrderLine": "Add order line", "orderDetails.orderLines.disabledForUnSavedTemplate": "Save the template before adding order lines", "orderDetails.orderLines.addPriceRule": "Add price rule", "orderDetails.orderLines.onlyOneManualPriceRuleAllowed": "Only one product variant is allowed per order line", "orderDetails.orderLines.noManualPriceRulesAvailable": "No product variants available", "orderDetails.orderLines.addPriceRulePlaceholder": "Add a product variant", "orderDetails.orderLines.disabledPriceRuleDescription": "Delete any active amount-based price rules to add this", "orderDetails.orderLines.totalExVat": "Total excl. VAT", "orderDetails.orderLines.paymentDisabledTooltip.noPaymentRecipient": "Select a customer before sending to payment", "orderDetails.orderLines.paymentDisabledTooltip.noPaymentRecipientAndNotConfirmed": "The order must be confirmed and a customer selected before sending to payment", "orderDetails.orderLines.paymentDisabledTooltip.notConfirmed": "The order must be confirmed before sending to payment", "orderDetails.orderLines.paymentDisabledTooltip.noOrderLines": "No order lines are available for payment creation", "orderDetails.orderLines.paymentDisabledTooltip.orderCancelled": "The order is cancelled", "orderDetails.orderLines.paymentDisabledTooltip.workOrderHasPayment": "This work order already has a payment", "orderDetails.orderLines.addedByCustomerTooltip.creation": "This order line has been added by the customer when the order was created", "orderDetails.orderLines.addedByCustomerTooltip.updated": "This order line has been added by the customer in the customer portal", "orderDetails.orderLines.deleteChecked": "Delete selected order lines", "orderDetails.orderLines.connectToWorkOrder": "Connect to job", "orderDetails.orderLines.connectToWorkOrder.disconnect": "Disconnect from job", "orderDetails.orderLines.connectToWorkOrder.select": "Choose job", "orderDetails.paymentSchedules.title": "Repating payment", "orderDetails.paymentSchedules.title.perWorkOrder": "Repeating payment per job", "orderDetails.paymentSchedules.noSubscriptionActive": "Card not registered", "orderDetails.paymentSchedules.autoSendDisabled": "No automatic sending", "orderDetails.paymentSchedules.autoSendDisabled.toolTip": "Payment will not be automatically sent to the customer when a job is finished. Click to enable automatic sending.", "orderDetails.paymentSchedules.autoSendEnabled": "Sent automatically", "orderDetails.paymentSchedules.autoSendEnabled.toolTip": "Payment will be automatically sent to the customer when a job is finished. Click to disable automatic sending.", "orderDetails.paymentSchedules.autoSendDisabled.consolidated": "No automatic adding", "orderDetails.paymentSchedules.autoSendDisabled.toolTip.consolidated": "Payment will not be automatically added to the active consolidated invoice when a job is finished. Click to enable this.", "orderDetails.paymentSchedules.autoSendEnabled.consolidated": "Automatically added", "orderDetails.paymentSchedules.autoSendEnabled.toolTip.consolidated": "Payment will be automatically added to the active consolidated invoice when a job is finished. Click to disable this.", "orderDetails.paymentSchedules.nextPayment": "Next", "orderDetails.paymentSchedules.nextPayment.empty": "No next payment", "orderDetails.paymentSchedules.lastPayment": "Previous", "orderDetails.paymentSchedules.lastPayment.empty": "No previous payment", "orderDetails.paymentSchedules.totalPayments": "Total", "orderDetails.paymentSchedules.totalUnpaidSentPayments": "Total unpaid sent", "orderDetails.paymentSchedules.frequency": "Created automatically for each", "orderDetails.paymentSchedules.frequency.sent": "<PERSON><PERSON>", "orderDetails.paymentSchedules.active": "Active", "orderDetails.paymentSchedules.deactivated": "Repeating payment not active", "orderDetails.paymentSchedules.addCustomerBeforeActivating": "Add a customer to the order before activating", "orderDetails.paymentSchedules.confirmOrderBeforeActivating": "The order must be confirmed before activating. When the order is confirmed, the repeating payment will be activated automatically", "orderDetails.paymentSchedules.activeTooltip": "If this is deactivated, new payments will not be created.", "orderDetails.paymentSchedules.startsAfter": "Starts from", "orderDetails.paymentSchedules.stopsAfter": "Ends at", "orderDetails.createWorkOrder": "Create job", "orderDetails.createRepeatingWorkOrder": "Create job plan", "orderDetails.createRepeatingPayment": "Create repeating payment", "orderDetails.createPayment": "Create single payment", "orderDetails.paymentStatus5WithRepeatingPayment": "Repeating payment", "paymentDetails.title.create": "Create payment", "paymentDetails.title.create.repeating": "Create repeating payment", "paymentDetails.title.update": "Edit payment", "paymentDetails.title.update.repeating": "Edit repeating payment", "paymentDetails.goToOrder": "Go to order", "paymentDetails.paymentMethodDescription.id3": "This payment method lets the customer choose the payment method. When the payment is sent, the customer will receive and SMS/Email with a link to the customer portal. Here, the customer can choose between the different payment methods available. ", "paymentDetails.paymentMethodDescription.id4": "This payment method can be used when you wish to capture a payment without utilising any of the payment solutions in Between. When the payment is sent, it will automatically be marked as paid, and you need to ensure that the payment is paid manually.", "paymentDetails.paymentMethodDescription.id10": "Create an invoice and send this through your accounting system. Sending of the invoice is done through Between, and the payment status of the invoice will be updated within 1 hour after it has been paid.", "paymentDetails.paymentMethodDescription.id14": "By choosing this payment method, the customer can register their credit card, and each payment created by this repeating payment will then be automatically captured as soon as it is sent.", "paymentDetails.paymentMethodDescription.id15": "This payment method will add this payment to the specified consolidated invoice.", "paymentDetails.repeatingTypeTooltip.fixed": "A fixed repeating payment will automatically create and send payments with a given interval, regardless of any scheduled jobs. Innstillingen du ", "paymentDetails.repeatingTypeTooltip.perJob": "Repeating payment per job will automatically create a new payment each time a job is finished.", "paymentDetails.selectPaymentMethod": "Payment method", "paymentDetails.selectPaymentMethod.noCustomer": "Select a customer to make invoice available", "paymentDetails.selectPaymentMethod.consolidatedDisabled": "Invoice consolidation is not activated for this customer", "paymentDetails.selectPaymentMethod.consolidatedDisabledBecauseInvoice": "Reverse payment accounting sync to select consolidated invoice", "paymentDetails.selectPaymentMethod.invoiceDisabledBecausePushed": "Reverse payment accounting sync to select invoice", "paymentDetails.selectPaymentMethod.disabledDeleteTooltip": "Reverse accounting sync for this payment before deleting", "paymentDetails.saveWithoutSend.create": "Save without sending", "paymentDetails.saveAndSend.create": "Send to payment", "paymentDetails.saveWithoutSend.update": "Save", "paymentDetails.saveAndSend.update.again": "Send again", "paymentDetails.saveAndSend.update": "Send payment", "paymentDetails.paymentReminders": "Payment reminders", "paymentDetails.paymentReminders.tooltip": "If this is activated, payment reminders will be sent according to your notification settings. If this is deactivated, it cannot be activated again after the payment has been sent.", "paymentDetails.firstPaymentReminder": "First payment reminder", "paymentDetails.invoiceSettings": "Invoice settings", "paymentDetails.invoiceSettings.invoiceSendType": "Invoice send type", "paymentDetails.downloadInvoicePDF": "Download invoice as PDF", "paymentDetails.sendReceipt": "Send receipt", "paymentDetails.tabs.single": "Single payment", "paymentDetails.tabs.repeating": "Per job", "paymentDetails.tabs.fixed": "Fixed repeating payment", "paymentDetails.infoBox.repeatingPayment": "A repeating payment will automatically create payments with a given interval. The number of payments created in advance will follow the rules of the selected job plan.", "paymentDetails.infoBox.fixedRepeatingPayment": "A fixed repeating payment will automatically create payments with a given interval.", "paymentDetails.selectWorkOrder": "Select job plan", "paymentDetails.workOrder": "Job plan", "paymentDetails.subscriptionPaymentMethodName": "Automatic capture (Quickpay)", "paymentDetails.repeatingType": "How do you wish to get paid?", "paymentDetails.dayOfMonth": "When should the payment/invoice be sent?", "paymentDetails.dayOfMonth.first": "The first day of the month", "paymentDetails.dayOfMonth.last": "The last day of the month", "paymentDetails.firstPayment": "When should the first payment/invoice be sent?", "paymentDetails.firstPayment.disabled": "First possible date", "paymentDetails.lastPayment": "Duration", "paymentDetails.lastPayment.disabled": "No stop date", "paymentDetails.createRepeatingPayment": "Create repeating payment", "paymentDetails.beforeFirstPayment": "Before the first payment", "paymentDetails.afterLastPayment": "After the last payment", "paymentDetails.firstPaymentOn": "First payment", "paymentDetails.lastPaymentOn": "Last payment", "paymentDetails.totalPayments": "Total", "paymentDetails.fixedPaymentStopUnitType.never": "No given duration", "paymentDetails.fixedPaymentStopUnitType.weeks": "Week(s)", "paymentDetails.fixedPaymentStopUnitType.months": "Month(s)", "paymentDetails.fixedPaymentStopUnitType.years": "Year(s)", "paymentDetails.repeatingPlaceholderToggleDisabledTooltip.noCustomer": "You must select a customer before you can create the repeating payment", "paymentDetails.repeatingPlaceholderToggleDisabledTooltip.noOrderLines": "You must add at least one order line before you can create the repeating payment", "paymentDetails.repeatingPlaceholderToggleDisabledTooltip.noSchedule": "You must select a job plan before you can create the repeating payment", "paymentDetails.noOrderLinesSelected": "No order lines selected", "paymentDetails.cannotSendPm14": "Cannot send payment when payment method is auto-capture", "paymentDetails.noPaymentMethodSelected": "No payment method selected", "paymentDetails.alreadySent": "Payment has already been sent", "paymentDetails.noConsolidatedInvoiceSelected": "Select consolidated invoice setup before saving", "paymentDetails.noInvoiceSendTypeSelected": "Select invoice send type before saving", "paymentDetails.invalidInvoiceEmail": "Invalid invoice email", "paymentDetails.invalidDueDate": "Invalid due date", "paymentDetails.consInvNoOrderLinesSelected": "The consolidated invoice contains no order lines, or the total amount is zero", "paymentDetails.consolidatedInvoice.title": "Consolidated invoice", "paymentDetails.consolidatedInvoice.name": "Consolidated invoice name", "paymentDetails.consolidatedInvoice.name.tooltip": "Give your consolidated invoice a recognisable name", "paymentDetails.consolidatedInvoice.autoSend": "Send automatically", "paymentDetails.consolidatedInvoice.autoSend.tooltip": "Select this option to send the consolidated invoice automatically at given intervals", "paymentDetails.consolidatedInvoice.sendManually": "Send manually", "paymentDetails.consolidatedInvoice.sendManually.tooltip": "Select this option when you want to send the consolidated invoice manually", "paymentDetails.consolidatedInvoice.selectInvoice": "Select consolidated invoice setup", "paymentDetails.consolidatedInvoice.createNew": "Create new invoice consolidation setup", "paymentDetails.consolidatedInvoice.placeholder": "Select or create an invoice consolidation setup", "payments.list.title": "Payments", "payments.list.createdAt": "Created at", "payments.list.totalAmount": "Total amount", "payments.list.capturedAt": "Captured at", "payments.list.status": "Payment status", "payments.list.paymentMethod": "Payment method", "payments.list.autoSendAt": "Auto send at", "payments.list.paymentSentAt": "Payment sent at", "payments.list.customer": "Customer", "payments.list.products": "Products", "payments.list.paymentStatus.failed": "Payment failed", "payments.list.sorting.created_at": "Created at", "payments.list.sorting.sent": "Sent at", "payments.list.sorting.captured": "Paid at", "payments.list.sorting.amount": "Amount", "workOrderDetails.workOrderTitleAndDescription": "Title and description", "workOrderDetails.workOrderTitle": "Title", "workOrderDetails.workOrderDescription": "Description", "workOrderDetails.workOrderDescription.showMore": "Show more", "workOrderDetails.workOrderDescription.showLess": "Show less", "workOrderDetails.workOrderTitlePlaceholder": "Job title...", "workOrderDetails.workOrderDescriptionPlaceholder": "Job description...", "workOrderDetails.subcontractor.title": "Subcontractor view", "workOrderDetails.subcontractor.accepted": "Accepted by", "workOrderDetails.subcontractor.declined": "Declined by", "workOrderDetails.subcontractor.declined.reason": "Reason", "workOrderDetails.subcontractor.declined.noReason": "No reason given", "workOrderDetails.subcontractor.pending": "Waiting for response", "workOrderDetails.dateAndTime.title": "Date and time", "workOrderDetails.dateAndTime.arrival": "Arrival time", "workOrderDetails.dateAndTime.arrivalTooltip": "Set an arrival window to give the customer an idea of when the job will start", "workOrderDetails.open": "Open job", "workOrderDetails.createWithoutOrderLines": "Create without order lines", "workOrderDetails.createWithoutOrderLines.tooltip": "The job template you have selected contains order lines. Use this button to create the job without the template order lines.", "workOrderDetails.contractor.asCompany.title": "Sent to contractor", "workOrderDetails.contractor.asCompany.description": "This work order has been sent to the following contractor", "workOrderDetails.contractor.asCompany.pending": "The contractor has not yet answered to this contract yet", "workOrderDetails.contractor.title": "Contractor view", "workOrderDetails.contractor.viewingAs": "You are viewing this job as a contractor.", "workOrderDetails.contractor.owner": "Contracting authority", "workOrderDetails.contractor.ownerContact": "Assigned by", "workOrderDetails.contractor.acceptedBy": "Accepted by", "workOrderDetails.contractor.declinedBy": "Declined by", "workOrderDetails.contractor.declinedMessage": "Your company has declined this job, and is therefore not available for changes.", "workOrderDetails.contractor.pending": "Your company has not responded to this contract yet", "workOrderDetails.contractor.accept": "Accept", "workOrderDetails.contractor.decline": "Decline", "workOrderDetails.contractor.remove": "Remove contractor", "workOrderDetails.pastDateModal.title": "Set execution date back in time?", "workOrderDetails.pastDateModal.bodyBold": "You have set an execution date in the past.", "workOrderDetails.pastDateModal.bodyRegular": "Are you sure you wish to continue?", "workOrderDetails.modal.create.title": "Create job", "workOrderDetails.modal.createSchedule.title": "Create job plan", "workOrderDetails.modal.create.createNoExecution": "Create without time", "workOrderDetails.goToOrder": "Go to order", "workOrderDetails.card.noDateSet": "No date or time set", "workOrderDetails.card.setDate": "Set date", "workOrderDetails.createFromTemplate": "Job templates", "workOrderDetails.createFromTemplate.showAll": "Show all", "workOrderDetails.createFromTemplate.noTemplates.title": "Tip", "workOrderDetails.createFromTemplate.noTemplates.description": "Create job templates to quickly create an order with a predefined job.", "workOrderDetails.actionButtons.duplicate": "Duplicate job", "workOrderDetails.actionButtons.finish": "Finish job", "workOrderDetails.actionButtons.hideInOrderLinesList": "Hide job in order line list", "workOrderDetails.actionButtons.showInOrderLinesList": "Show job in order line list", "workOrderDetails.finishBeforeConfirmedModal.title": "Order is no confirmed", "workOrderDetails.finishBeforeConfirmedModal.bodyBold": "The order containing this job has not been confirmed yet.", "workOrderDetails.finishBeforeConfirmedModal.bodyRegular": "Are you sure you want to finish this job?", "workOrderDetails.duplicateModal.title": "Duplicate job", "workOrderDetails.duplicateModal.chosenDates": "<PERSON>sen dates", "workOrderDetails.duplicateModal.users": "Copy assigned users", "workOrderDetails.duplicateModal.resources": "Copy assigned resources", "workOrderDetails.duplicateModal.checklists": "Copy checklists", "workOrderDetails.duplicateModal.orderLines": "Copy order lines", "workOrderDetails.duplicateModal.customerQuestions": "Copy customer questions", "workOrderDetails.quantityProposalModal.title": "Order line quantity proposal", "workOrderDetails.quantityProposalModal.description": "Based on the time registered on this job, we have the following suggestions for changing order lines", "workOrderDetails.quantityProposalModal.close": "Finish without proposal change", "workOrderDetails.repeatingView.infoBox.title": "You are viewing a template for job plan.", "workOrderDetails.repeatingView.infoBox.description": "If you want to edit a single job, please go directly to the job.", "workOrderDetails.repeatingView.updateChildrenModal.title": "Update existing jobs", "workOrderDetails.repeatingView.updateChildrenModal.bodyBoldTranslationKey": "This schedule has future unstarted jobs.", "workOrderDetails.repeatingView.updateChildrenModal.bodyMutedTranslationKey": "If you select no, this change will only be applied to jobs that has not been already created.", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.executionUpdate": "Do you wish to update date and time for these as well?", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.addressUpdate": "Do you wish to update the addresses for these as well?", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.checkListUpdate": "Do you wish to update the checklists for these as well?", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.assignUpdate": "Do you wish to add this employee for these as well?", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.activity": "Do you wish to update the default activity for these as well?", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.registrationType": "Do you wish to update the registration type for these as well?", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.unassignUpdate": "Do you wish to remove this employee for these as well?", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.textUpdate": "Do you wish to update title and description for these as well?", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.orderLineCreation": "Do you wish to add this order line to these jobs as well?", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.orderLineUpdate": "Do you wish to update the corresponding order line at these jobs as well?", "workOrderDetails.repeatingView.updateChildrenModal.bodyBoldTranslationKey.repeatingPaymentCreation": "The selected schedule for this payment has future unstarted jobs.", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.repeatingPaymentCreation": "Do you wish to add the order lines of this payment to these existing jobs?", "workOrderDetails.repeatingView.title": "Time and frequency", "workOrderDetails.repeatingView.startDate": "Start date", "workOrderDetails.repeatingView.endDate": "End date", "workOrderDetails.repeatingView.startTime": "Start time", "workOrderDetails.repeatingView.endTime": "End time", "workOrderDetails.repeatingView.estimatedDuration": "Estimated duration", "workOrderDetails.repeatingView.hours": "hours", "workOrderDetails.repeatingView.minutes": "minutes", "workOrderDetails.repeatingView.scheduleButton": "Schedule", "workOrderDetails.repeatingView.scheduleButton.verifySchedule": "Verify time and frequency", "workOrderDetails.repeatingView.showAllJobs": "Show jobs", "workOrderDetails.createOrderView.title": "Create order", "workOrderDetails.createOrderView.createOrder": "Create order", "workOrderDetails.createOrderView.createEmptyOrder": "Create without job", "workOrderDetails.createOrderView.orderType": "Order type", "workOrderDetails.createOrderView.orderType.single": "Single", "workOrderDetails.createOrderView.orderType.repeating": "Repeating", "scheduleSetup.startStop.start": "Start date", "scheduleSetup.startStop.stop": "Stop date", "scheduleSetup.startStop.immediately": "Start immediately", "scheduleSetup.startStop.never": "Never", "orderDetails.noRepeatingWorkOrder": "This order does not have a job plan", "orderDetails.noWorkOrder": "This order does not have a job", "orderDetails.repeatingWorkOrder": "Job plan", "orderDetails.workOrder": "Job", "orderDetails.workOrderScheduleCard.noExecutionTooltip": "You must specify a start time before you can create any jobs", "orderDetails.workOrderCardContainer": "Jobs", "orderDetails.workOrderCardContainer.button": "Add jobs", "orderDetails.orderLines.addComment": "Add comment", "orderDetails.orderLines.addProduct.customProduct": "Custom Product", "orderDetails.orderLines.edit.title": "Edit products", "orderDetails.orderLines.orderLines.multiple": "order lines", "orderDetails.orderLines.orderLines.single": "order line", "orderDetails.orderLines.workOrderConnectedTooltip": "This order line is connected to a job, click to open it", "orderDetails.orderLines.trackTimeTooltip": "This order line will be used to capture the hours worked on the order. If the order line is connected to a job, it will only capture time for that specific job.", "orderDetails.orderLines.trackTimeLabel.order": "Use this order line to capture hours worked on the order", "orderDetails.orderLines.trackTimeLabel.workOrder": "Use this order line to capture hours worked on the connected job", "orderDetails.orderLines.trackTimeDisabledTooltip.workOrder": "The job connected to this order line already has a order line with time tracking enabled. Disable this first.", "orderDetails.orderLines.trackTimeDisabledTooltip.order": "This order already has a order line with time tracking enabled. Disable this first.", "orderDetails.orderLines.sentToPayment": "Sent to payment", "orderDetails.orderLines.addedToPayment": "Added to payment, but not sent", "orderDetails.orderLines.woPopup.noAddress": "Job has no address", "orderDetails.orderLines.woPopup.totalSum": "Total (sum)", "orderDetails.orderLines.woPopup.totalFromTo": "Total (from-to)", "orderDetails.orderLines.woPopup.noTrackings": "No timetrackings registered at this job", "orderDetails.summary.sentToPayment": "Send to payment", "orders.orderList.action": "Action", "orders.orderList.archiveTooltip": "Archive order", "orders.orderList.confirmed": "Confirmed", "orders.orderList.createdAt": "Created date", "orders.orderList.executionAt": "Execution date", "orders.orderList.comment": "Comment", "orders.orderList.address": "<PERSON>ress", "orders.orderList.containsSchedule": "Repeating", "orders.orderList.feedbackRating": "Rating", "orders.orderList.feedbackRating.tooltip": "Feedback comment", "orders.orderList.feedbackRating.tooltip.noComment": "No comment", "orders.orderList.feedbackRating.tooltip.noRating": "No Rating", "orders.orderList.customer": "Customer", "orders.orderList.customer.viewCustomer": "View customer", "orders.orderList.serviceRecipient": "Service recipient", "orders.orderList.date": "Date", "orders.orderList.orderID": "Order ID", "orders.orderList.title": "Title", "orders.orderList.phone": "Phone", "orders.orderList.source": "Source", "orders.orderList.revenue": "Revenue", "orders.orderList.sum": "Sum", "orders.orderList.serviceName": "Service", "orders.orderList.status": "Status", "orders.orderList.payment": "Payment", "orders.orderList.noData": "No orders found", "orders.orderList.total": "Total", "orders.page": "Page", "orders.searchField": "Search...", "orders.advancedSearch": "Advanced search", "orders.status": "Status", "orders.contact": "Contact", "orders.unpaidOrders": "Unpaid Orders", "orders.unpaidOrdersTooltip": "This will only show orders that are unpaid and has order statuses 'Completed' or 'Payment Ready'", "partner.settings.attributes.newAttributePlaceholder": "New attribute", "partner.settings.attributes.newAttributeTypePlaceholder": "Select type", "partner.tabs.contact": "Contacts", "partner.tabs.orders": "Orders", "partner.tabs.pricing": "Partner price", "partner.tabs.settings": "Settings", "partners.contacts.new.companyContactsLabel": "Company contacts", "partners.contacts.new.createNewCompanyUser": "Create new user at partner company", "partners.contacts.new.companyContactsPlaceholder": "Search for company contacts", "partners.paperCompanies.companyId": "Company ID", "partners.paperCompanies.companyName": "Company name", "partners.paperCompanies.contactInfo": "Contact information", "partners.paperCompanies.edit": "Edit", "partners.paperCompanies.contactList": "Contact list", "partners.paperCompanies.createNewContact": "Create new contact", "partners.paperCompanies.createNewPaperCompany": "Create new company", "partners.paperCompanies.createdAt": "Created at", "partners.paperCompanies.editPaperCompany": "Edit partner company", "partners.paperCompanies.email": "E-mail", "partners.paperCompanies.employee.header": "Edit partner", "partners.paperCompanies.employee.addHeader": "Add partner contact", "partners.paperCompanies.employee.attributes": "Partner attributes", "partners.paperCompanies.employee.create": "Create", "partners.paperCompanies.employee.edit": "Edit", "partners.paperCompanies.employee.email": "E-mail", "partners.paperCompanies.employee.firstName": "First name", "partners.paperCompanies.employee.lastName": "Last name", "partners.paperCompanies.employee.phone": "Phone", "partners.paperCompanies.employee.roleDescription": "Role description", "partners.paperCompanies.employee.modal.Header": "Add partner employee", "partners.paperCompanies.organisationNumber": "Organisation number", "partners.paperCompanies.phone": "Phone", "partners.paperCompanies.settings.general.header": "General settings", "partners.paperCompanies.settings.accounting.header": "Accounting settings", "partners.paperCompanies.settings.accounting.invoiceConsolidation": "Invoice consolidation", "partners.paperCompanies.settings.accounting.dueDateDays": "Due date (days)", "partners.paperCompanies.settings.hidePaymentForCustomer": "Hide price information for customer", "partners.paperCompanies.settings.tooltip": "Price information will only be hidden from the customer if partner is set as payment recipient", "partners.paperCompanies.title": "Partner companies", "partners.settings": "Settings", "partners.settings.attributes": "Attributes", "partners.settings.attributes.addNew": "Add new attribute", "partners.settings.attributes.attributeName": "Name", "partners.settings.attributes.attributeType": "Type", "partners.settings.attributes.noAttribute": "No attributes selected", "partners.settings.save": "Save", "partners.settings.close": "Close", "partners.settings.title": "Partner settings", "placeholders.eMail": "Enter e-mail", "placeholders.firstName": "Enter first name", "placeholders.lastName": "Enter last name", "placeholders.phone": "Enter phonenumber", "product-list.active": "Active", "product-list.addProduct": "Add product", "product-list.importProducts": "Import products", "product-list.dateAdded": "Created at", "product-list.id": "ID", "product-list.inactive": "Inactive", "product-list.name": "Name", "product-list.price": "Price", "product-list.productType": "Product type", "product-list.status": "Status", "product-list.createProductModal.title": "Create a product", "product-list.createProductModal.serviceLabel": "Add product name", "product-list.createProductModal.placeholder": "Product name", "product-list.createProductModal.dropdownPlaceholder": "Select product type", "product-list.createProductModal.nameDescription": "Give this product a name which broadly describes it. For example, Home Cleaning rather than 1 Bedroom Home Cleaning", "product-list.createProductModal.serviceQuestion": "How do you wish to be paid for this product?", "product-list.createProductModal.nameDescription2": "Once the product is created, you can create multiple variants of it to further customize it.", "product-list.createProductModal.nameDescriptionTip": "Tips: Give this product a name which broadly describes it. For example, Home Cleaning rather than 1 Bedroom Home Cleaning", "productDetails.title": "Products", "productDetails.backBtn": "Products", "productDetails.serviceDetails": "Product details", "productDetails.serviceDetails.name": "Name", "productDetails.serviceDetails.description": "Description", "productDetails.serviceDetails.useProductNameAsWorkOrderTitle": "Use product name as title proposal for jobs", "productDetails.serviceDetails.selectIcon": "Icon", "productDetails.serviceDetails.description.hint": "he description will be visible for the customer when they order from a booking form or in a quote.", "productDetails.serviceDetails.productType": "Product type", "productDetails.serviceDetails.productType.selectProductType": "Select product type", "productDetails.serviceDetails.basePriceIncVat": "Baseprice incl. VAT", "productDetails.serviceDetails.basePriceExVat": "Grunnpris excl. VAT", "productDetails.serviceDetails.basePrice.hint": "The base price is how much this product costs without any service modifier options selected.", "productDetails.serviceDetails.unitType": "Unit type", "productDetails.serviceDetails.unitType.selectUnitType": "Choose unit type", "productDetails.serviceDetails.unitType.hint1": "The job duration of a customer’s appointment is determined by the service’s base duration + the duration of any selected modifier options.", "productDetails.serviceDetails.unitType.hint2": "Job duration is used when checking availability and also determines how much time is blocked off on the schedule.", "productDetails.serviceDetails.taxRate": "Tax rate", "productDetails.serviceDetails.taxRate.selectTaxRate": "Select tax rate", "productDetails.serviceDetails.linkProduct": "Link product to accounting system", "productDetails.serviceDetails.linkProduct.hint": "Link product tooltip", "productDetails.serviceDetails.subText": "Name, description, price and unit type", "productDetails.productVariants": "Product-variants", "productDetails.productVariants.subText": "Different versions of the same product, each with its own price.", "productDetails.productVariants.info": "Add product variant", "productDetails.productVariants.description": "Product variants allow you to offer different versions of a product, each with its own price. This means you don't have to create entirely new products for each variation.", "productDetails.productVariants.btn": "New Modifier Group", "productDetails.productVariants.unitPrice": "Unit Price", "productDetails.productVariants.totalPrice": "Total Price", "productDetails.productVariants.value": "Verdi", "productDetails.productVariants.priceRuleName": "Name", "productDetails.productVariants.price": "Price per", "productDetails.productVariants.addVariant": "Add variant", "productDetails.productVariants.unitPrice.description": "Set a fixed unit price for your order line, allowing the price per unit to be adjusted manually.", "productDetails.productVariants.totalPriceAdjustment": "Total price adjustment", "productDetails.productVariants.totalPriceAdjustment.description": "Adjust the total price of your order by a fixed percentage, allowing you to either increase or decrease the overall cost.", "productDetails.productVariants.modal.productName": "Product variant name", "productDetails.productVariants.modal.productName.placeholder": "Enter Product variant name", "productDetails.productVariants.modal.price": "<PERSON><PERSON>", "productDetails.customerQuestionnaire": "Customer questionnaire", "productDetails.customerQuestionnaire.subText": "Send custom questionnaires to your customers via SMS", "productDetails.customerQuestionnaire.title": "Get information from the customer through a questionnaire.", "productDetails.customerQuestionnaire.description1": "Create a questionnaire to be sent to your customers via SMS 2 days before the job date. Include single or multiple choice questions to gather important information, such as parking availability.", "productDetails.customerQuestionnaire.description2": "Customers booking online can make selections from these groups to customize this service and get an accurate price quote.", "productDetails.customerQuestionnaire.description3": "Create a questionnaire to be sent to your customers via SMS 2 days before the job date.", "productDetails.priceAdjustmentRules": "Automatic price rules", "productDetails.priceAdjustmentRules.addRule": "Add rule", "productDetails.priceAdjustmentRules.subText": "Customize pricing when given conditions are met", "productDetails.priceAdjustmentRules.info": "Automatic pricing rules allow you to automatically increase or decrease the product price based on different rules.", "productDetails.priceAdjustmentRules.timeBasedModifier": "Time-based pricing", "productDetails.priceAdjustmentRules.timeBasedModifier.info": "This rule regulates pricing based on the day and time of day. For example higher prices for work performed outside regular working hours, like overtime.", "productDetails.priceAdjustmentRules.timeBasedModifier.info.short": "Adjust the price based on the day and time the work is performed", "productDetails.priceAdjustmentRules.timeBasedModifier.day": "Day", "productDetails.priceAdjustmentRules.timeBasedModifier.time": "Time", "productDetails.priceAdjustmentRules.timeBasedModifier.priceAdj": "Price Adj.", "productDetails.priceAdjustmentRules.timeBasedModifier.newPrice": "New price pr unit", "productDetails.priceAdjustmentRules.timeBasedModifier.weekdays": "Weekdays", "productDetails.priceAdjustmentRules.timeBasedModifier.timeRange": "Time range", "productDetails.priceAdjustmentRules.timeBasedModifier.value": "Value", "productDetails.priceAdjustmentRules.timeBasedModifier.adjustedPrice": "Adjusted price", "productDetails.priceAdjustmentRules.timeBasedModifier.modal.info": "Adjust the pricing of this service based on the day and time of the appointment", "productDetails.priceAdjustmentRules.timeBasedModifier.modal.on": "On", "productDetails.priceAdjustmentRules.timeBasedModifier.modal.errorOn": "on", "productDetails.priceAdjustmentRules.timeBasedModifier.modal.weekdays": "Weekdays", "productDetails.priceAdjustmentRules.timeBasedModifier.modal.prosent": "Prosent", "productDetails.priceAdjustmentRules.timeBasedModifier.modal.overlapError": "The new rule overlaps with an existing rule.", "productDetails.priceAdjustmentRules.timeBasedModifier.modal.existingRule": "Existing rule", "productDetails.priceAdjustmentRules.timeBasedModifier.modal.timeValidationError": "Start time must be less than end time", "productDetails.priceAdjustmentRules.volumeDiscount": "Adjust unit price by quantity", "productDetails.priceAdjustmentRules.modal.title": "Add new unit price", "productDetails.priceAdjustmentRules.volumeDiscount.info": "Create rules that adjust the unit price within specified intervals. You can set multiple intervals to vary the price based on different quantities.", "productDetails.priceAdjustmentRules.volumeDiscount.short": "Create rules that adjust the unit price within specified intervals.", "productDetails.priceAdjustmentRules.between": "Between", "productDetails.priceAdjustmentRules.volumeDiscount.newPrice": "Price per unit", "productDetails.priceAdjustmentRules.quantityRange": "Interval", "productDetails.priceAdjustmentRules.newPriceInclVat": "New Price per Unit", "productDetails.priceAdjustmentRules.newPriceIncVat": "New Price per Unit Incl. VAT", "productDetails.priceAdjustmentRules.newFixedPriceExVat": "Fixed Price Excl. VAT", "productDetails.priceAdjustmentRules.newPriceExVat": "New Price per Unit Excl. VAT", "productDetails.priceAdjustmentRules.newFixedPriceInclVat": "Fixed Price Incl. VAT", "productDetails.priceAdjustmentRules.tieredQuantityPricing": "Fixed price based on quantity", "productDetails.priceAdjustmentRules.tieredQuantityPricing.add": "Add tiered quantity pricing", "productDetails.priceAdjustmentRules.tieredQuantityPricing.info": "Set fixed prices for specific quantity intervals. This allows you to define a fixed price within specific quantity ranges. For example, a cleaning company can set the price to 1000 NOK for areas between 0-50 sqm, 2000 NOK for 51-100 sqm, and so on", "productDetails.priceAdjustmentRules.tieredQuantityPricing.totalPriceExVat": "Total price excl. VAT", "productDetails.priceAdjustmentRules.tieredQuantityPricing.totalPriceIncVat": "Total price incl. VAT", "productDetails.priceAdjustmentRules.tieredQuantityPricing.info.short": "This rule sets a fixed price for specified quantity intervals.", "productDetails.priceAdjustmentRules.tieredQuantityPricing.value": "Value", "productDetails.priceAdjustmentRules.tieredQuantityPricing.modal.rangeComparisonError": "Range 'from' must be less than range 'to'.", "productDetails.priceAdjustmentRules.tieredQuantityPricing.modal.existingRuleRange": "Existing rule range", "productDetails.priceAdjustmentRules.tieredQuantityPricing.modal.rangeOverlapError": "Range 'from' must be less than range 'to'.", "productDetails.priceAdjustmentRules.tieredQuantityPricing.modal.priceRuleName": "Name", "productDetails.priceAdjustmentRules.tieredQuantityPricing.modal.enterPriceRuleName": "Enter price rule name", "productDetails.importantInformation": "Important information", "productDetails.importantInformation.subText": "Showcase attributes about this service or your business when customers book online", "productDetails.importantInformation.addImportantInformation": "Add important information that the customer will see when ordering this product", "productDetails.importantInformation.addModal.close": "Close", "productDetails.importantInformation.addModal.description": "Description", "productDetails.importantInformation.addModal.descriptionPlaceholder": "Description...", "productDetails.importantInformation.addModal.modalTitle": "Add new section", "productDetails.importantInformation.addModal.required": "Require customer approval upon reading", "productDetails.importantInformation.addModal.save": "Save", "productDetails.importantInformation.addModal.title": "Title", "productDetails.importantInformation.addNewInformation": "Add new information", "productDetails.importantInformation.customerName": "Customer name", "productDetails.importantInformation.importantInformation": "Important information", "productDetails.importantInformation.noInformationAvailable": "No product information available", "productDetails.importantInformation.orderEstimate": "Order estimate", "productDetails.importantInformation.preview": "Preview", "productDetails.crewChecklist": "Checklist", "productDetails.crewChecklist.subText": "Helps crew remember all tasks to be done", "productDetails.crewChecklist.addChecklist": "Add checklist", "productDetails.crewChecklist.taskGroup.tooltip": "Enter a name for this group of options. This name is displayed at top of the group when customers are booking online.", "productDetails.crewChecklist.stage.tooltip": "Customers and team members will be able to select from this group to customize the service being scheduled. Each modifier option can have its own price and duration, which will be added to the service if selected.", "productDetails.crewChecklist.addTemplate": "Add Template", "productDetails.crewChecklist.noTemplate": "No Temolates", "productDetails.crewChecklist.reportedDeviation": "Comment", "productDetails.crewChecklist.noTasks": "No checklists are added", "productDetails.upsell": "Upsell", "productDetails.upsell.subText": "Allow customers to choose additional products when ordering via the booking form or receiving a quote", "productDetails.upsell.title": "Boost sales with upsells", "productDetails.upsell.description": "Allow customers to choose additional products when ordering via the booking form or receiving a quote. This way, customers can see and purchase other items they might need.", "productDetails.upsell.addProducts": "Add products", "productDetails.upsell.selectedProducts": "Selected products", "productDetails.upsell.noSelectedProducts": "You have not selected any upsell products", "productDetails.upsell.selectedUpsell": "Selected upsell products", "productDetails.upsell.connect": "Connect other products to this service and offer them together in the booking page.", "productDetails.upsell.selectedProducts.tooltip": "View and manage the products you’ve chosen to offer alongside your main service. These are what your customers will see as additional options when booking.", "productDetails.upsell.availableProducts": "Available Products", "productDetails.upsell.availableProducts.tooltip": "Browse the full list of products you can connect to your service. To add any to your upsell options, simply select them from this list.", "productDetails.upsell.preview": "Preview", "productDetails.upsell.wouldYouLike": "Would you like to also purchase?", "productDetails.upsell.continue": "Continue", "productDetails.upsell.addService": "Add service", "productDetails.upsell.manage": "Manage upsell products", "productDetails.upsell.searchProducts": "Search products", "productDetails.upsell.noAvailable": "No available products", "productDetails.bookingPage": "Booking Page", "productDetails.bookingPage.subText": "Visible on booking page", "productDetails.advancedSettings": "Advanced settings", "productDetails.advancedSettings.subheading": "Change the advanced settings", "productDetails.advancedSettings.info": "Advanced settings allow you to further customize the service. You can add requirements for multiple addresses during service execution, duplicate addresses, and automate time calculations. For questions, please contact support.", "productDetails.advancedSettings.subText": "", "productDetails.advancedSettings.addStage": "Add stage", "productDetails.advancedSettings.billable": "Billable", "productDetails.advancedSettings.billableTooltip": "If the stage is billable, it will be included in the automatic price calculation that is based on the logged duration of the order", "productDetails.advancedSettings.billableTransport": "Billable transport", "productDetails.advancedSettings.billableTransportTooltip": "If the stage is set to billable transport, the transport phase will be included in the automatic price calculation that is based on the logged duration of the order", "productDetails.advancedSettings.duplicatable": "Duplicatable", "productDetails.advancedSettings.duplicatableTooltip": "If the stage is set to be duplicatable, the stage is available for duplication at order creation", "productDetails.advancedSettings.useInCalculation": "Use in calculation", "productDetails.advancedSettings.useInCalculationTooltip": "Decide whether the address at the stage should be used in the automatic calculation of quantity/duration for orders", "productDetails.advancedSettings.includeTransport": "Include transport", "productDetails.advancedSettings.includeTransportTooltip": "If the stage is set to include transport, the stage will implement a trackable transport phase before the stage is started", "productDetails.advancedSettings.setAddress": "Set address", "productDetails.advancedSettings.setAddressTooltip": "If the stage is set to include an address, the stage will require address input on order creation", "productDetails.advancedSettings.settings": "Settings", "productDetails.advancedSettings.settingsTooltip": "Customers and team members will be able to select from this group to customize the service being scheduled. Each modifier option can have its own price and duration, which will be added to the service if selected.", "productDetails.advancedSettings.stageName": "Stage name", "productDetails.advancedSettings.stageNameTooltip": "Enter a name for this group of options. This name is displayed at top of the group when customers are booking online.", "productDetails.advancedSettings.infoTitle": "Automatic job creation", "productDetails.advancedSettings.infoDescription": "Use these settings to automatically create a job when an order line with this product is added to an order. Define the job title, and default address setup.", "productDetails.advancedSettings.workOrderEnable": "Create job automatically", "productDetails.advancedSettings.workOrderTitle": "Job title", "productDetails.advancedSettings.addressesTitle": "Address title", "productDetails.recurringOptions": "Recurring Orders", "productDetails.recurringOptions.subText": "Connect other products to this service and offer them together in the booking page", "productDetails.importantInformation.title": "Important information", "productDetails.importantInformation.highlight": "Highlight the features of this service or your business, answer common questions customers might have, and showcase reviews form other customers.", "productDetails.importantInformation.addInfo.title": "Add information regarding this product", "productDetails.importantInformation.addInfo.description": "Important information contains essential details displayed in the booking form and quote, ensuring that customers see crucial information. You can create your own information categories and custom texts. Customers sign for the important information, which helps reduce complaints and misunderstandings.", "productDetails.importantInformation.modal.header.description": "Adjust the pricing of this service based on the day and time of the appointment", "productDetails.importantInformation.modal.sectionHeader": "Section header", "productDetails.importantInformation.modal.description": "Description", "productDetails.importantInformation.modal.required": "Description", "productDetails.importantInformation.noStages": "No Stages Available", "productDetails.importantInformation.addStage": "No product stages have been added. Please add a stage.", "products.checklists.header": "Checklists", "products.checklists.addGroupModal.addButton": "Add group", "products.checklists.addGroupModal.addButton.task": "Add task", "products.checklists.addGroupModal.AddTemplate": "Select a task template", "products.checklists.templates.title": "Crew checklist templates", "products.checklists.templates.choose": "Choose crew checklist templates you would like to include in this job", "products.checklists.templates.none": "You have no checklist templates", "products.checklists.templates.createOne": "Click here to create one", "products.checklists.templates.addChecklistButton": "Add checklist", "products.checklists.templates.addCustomChecklistButton": "Add custom checklist", "products.checklists.templates.addChosenTemplates": "Add chosen templates", "products.checklists.enterTaskGroupName": "Enter task group name", "products.checklists.addChecklistBtn": "Add checklist", "products.checklists.addGroup": "Add group", "products.checklists.addGroupModal.addChecklistGroupFor": "Add checklist group for", "products.checklists.addGroupModal.close": "Close", "products.checklists.addGroupModal.groupName": "Group name", "products.checklists.addGroupModal.save": "Save", "products.checklists.addGroupModal.taskGroupName": "Task group name", "products.checklists.addStageBeforeCreatingTasks": "You need to add a stage before creating tasks and task groups", "products.checklists.addTaskModal.addTaskFor": "Add task for", "products.checklists.addTaskModal.close": "Close", "products.checklists.addTaskModal.save": "Save", "products.checklists.addTaskModal.taskName": "Task name", "products.checklists.categories": "Checklist categories", "products.checklists.categories.noTaskGroups": "No checklists. Add a new checklist or import from your templates", "products.checklists.categoriesDescipription": "Checklist categories allow you to organize your checklists into groups for your crew", "products.checklists.checklist": "Checklist for", "products.checklists.checklistCrewApp": "Checklist crew-app", "products.checklists.checklistDescription.title": "Helps crew remember all tasks to be done", "products.checklists.checklistDescription": "A checklist for your crew helps ensure they don’t forget any tasks and aids them in doing the job correctly. For a given job to be marked complete, all checklist items must either be done, or a deviation must be reported.", "products.checklists.checklists": "checklists", "products.checklists.contract": "Written contract with customer", "products.checklists.create": "Create", "products.checklists.createChecklistforStage": "Add a unique checklist only for this activity.", "products.checklists.createCrewChecklist": "Create crew checklist", "products.checklists.editCrewChecklist": "Edit checklist", "products.checklists.createNewChecklist": "Create a checklist for", "products.checklists.crewChecklist": "Crew checklist", "products.checklists.defaultName": "Checklist", "products.checklists.items": "Checklist items", "products.checklists.categoryName": "Category name", "products.checklists.newListItem": "New list item", "products.checklists.noSpacing": "The task name cannot be blank or consist only of spaces", "products.checklists.noStageSelected": "No stage selected", "products.checklists.noChecklist": "This stage has no checklists", "products.checklists.preview": "Preview", "products.checklists.save": "Save", "products.checklists.selectStage": "Select stage", "products.checklists.sluk": "Emptied drain", "products.checklists.soppel": "Emptied trash", "products.checklists.stage.group.addTask": "Add task", "products.checklists.stage.stage": "Stage:", "products.checklists.stage.taskGroup": "task group", "products.checklists.stageTasks": "tasks", "products.checklists.taskPlaceholder": "Task name", "products.checklists.taskRequired": "You need at least one task", "products.checklists.categorized": "Categorized", "products.checklists.newCategory": "New Category", "products.checklists.templateName": "Template name", "products.editProduct.checklists": "Checklists", "products.editProduct.customerQuestionnaire": "Customer questionnaire", "products.editProduct.generalInformation": "General information", "products.editProduct.importantInformation": "Important information", "products.editProduct.inventory": "Inventory", "products.editProduct.recurringOptions": "Recurring options", "products.editProduct.quantityCalculations": "Calculations", "products.editProduct.quantityCalculations.noSource": "No source", "products.editProduct.pricing": "Pricing", "products.editProduct.stages": "Stages", "products.editProduct.upsellProducts": "Upsell products", "products.generalInfo.active": "Active", "products.generalInfo.activeSwitchContainer": "active-switch-container", "products.generalInfo.addStageBeforeActivating": "Please add a stage before activating the service product", "products.generalInfo.description": "Description", "products.generalInfo.enterPrice": "Enter price", "products.generalInfo.enterProductDescription": "Give a description of the product", "products.generalInfo.enterProductTitle": "Enter product title", "products.generalInfo.inactiveSwitchContainer": "inactive-switch-container", "products.generalInfo.price": "Price", "products.generalInfo.productDescription": "Product description", "products.generalInfo.productIcon": "Product icon", "products.generalInfo.productTitle": "Product Title", "products.generalInfo.productType": "Product type", "products.generalInfo.saveProduct": "Save product", "products.generalInfo.savingProduct": "Saving product...", "products.generalInfo.connectToAccounting": "Link your product with an existing product in your accounting system", "products.generalInfo.searchAccounting": "Search for product in accounting system", "products.generalInfo.selectAUnit": "Select a unit", "products.generalInfo.selectProductType": "Select product type", "products.generalInfo.selectVatType": "Select VAT type", "products.generalInfo.unitNotAvailable": "unavailable for", "products.generalInfo.unitType": "Unit type", "products.generalInfo.vatType": "VAT type", "products.icons.title": "Please select an icon:", "products.importantInformation.addImportantInformation": "Add important information that the customer will see when ordering this product", "products.importantInformation.addModal.close": "Close", "products.importantInformation.addModal.description": "Description", "products.importantInformation.addModal.descriptionPlaceholder": "Description...", "products.importantInformation.addModal.modalTitle": "Add new section", "products.importantInformation.addModal.required": "Require customer approval upon reading", "products.importantInformation.addModal.save": "Save", "products.importantInformation.addModal.title": "Title", "products.importantInformation.addNewInformation": "Add new information", "products.importantInformation.customerName": "Customer name", "products.importantInformation.importantInformation": "Important information", "products.importantInformation.noInformationAvailable": "No product information available", "products.importantInformation.orderEstimate": "Order estimate", "products.importantInformation.preview": "Preview", "products.inventory.availableOutOfStock": "Available when out of stock", "products.inventory.enterSku": "Enter SKU", "products.inventory.enterStockQuantity": "Enter current stock quantity", "products.inventory.enterWeight": "Enter weight", "products.inventory.sku": "SKU", "products.inventory.stockQuantity": "Stock quantity", "products.inventory.weight": "Weight", "products.newProduct.checklists": "Checklists", "products.newProduct.customerQuestionnaire": "Customer questionnaire", "products.newProduct.generalInformation": "General information", "products.newProduct.importantInformation": "Important information", "products.newProduct.inventory": "Inventory", "products.newProduct.pageTitle": "New product", "products.newProduct.pricing": "Pricing", "products.newProduct.stages": "Stages", "products.newProduct.upsellProducts": "Upsell products", "products.overview.title": "Products", "products.pricing.addNewPriceGroup": "Add new price group", "products.pricing.addPricingModal.automaticTriggers": "Automatically activated", "products.pricing.addPricingModal.closeButtonLabel": "Close", "products.pricing.addPricingModal.groupNameLabel": "Price Rule Group Name", "products.pricing.addPricingModal.groupNamePlaceholder": "Name of pricing rule group", "products.pricing.addPricingModal.manualTriggers": "Manually activated", "products.pricing.addPricingModal.modalTitle": "Add price rule group", "products.pricing.addPricingModal.priceNullUnitPrice": "Price of 0 is not acceptable when using this price trigger", "products.pricing.addPricingModal.saveButtonLabel": "Save group", "products.pricing.addPricingModal.triggerLabel": "Choose trigger", "products.pricing.addPricingModal.triggerPlaceholder": "Select...", "products.pricing.addPricingModal.triggerSelectId": "trigger_type", "products.pricing.editPricingModal.closeButtonLabel": "Close", "products.pricing.editPricingModal.deleteGroup": "Delete Group", "products.pricing.editPricingModal.groupNameLabel": "Price Rule Group Name", "products.pricing.editPricingModal.groupNamePlaceholder": "Name of pricing rule group", "products.pricing.editPricingModal.modalTitle": "Edit price rule group", "products.pricing.editPricingModal.saveButtonLabel": "Save group", "products.pricing.editPricingModal.sure": "Sure?", "products.pricing.group.addNewPriceRule": "Add new price rule", "products.pricing.group.edit": "edit", "products.pricing.group.rule.calculation": "Calculation", "products.pricing.group.rule.endTime": "End time", "products.pricing.group.rule.errorOverlappingQuantity": "Overlapping", "products.pricing.group.rule.errorOverlappingTime": "Overlapping", "products.pricing.group.rule.newPriceExVat": "New price excl. VAT", "products.pricing.group.rule.newPriceIncVat": "New price incl. VAT", "products.pricing.group.rule.priceRuleName": "Price rule name", "products.pricing.group.rule.qtyFrom": "<PERSON><PERSON> from", "products.pricing.group.rule.qtyTo": "Qty to", "products.pricing.group.rule.startTime": "Start time", "products.pricing.group.rule.totalPrice": "Total price", "products.pricing.group.rule.value": "Value", "products.pricing.group.rule.weekdays": "Weekdays", "products.pricing.header": "Edit price", "products.pricing.includeEmbed": "Include in embed", "products.pricing.includeEmbedTooltip": "Select this option if you wish to incorporate the price rule in the embed code", "products.pricing.price": "Price", "products.pricing.individualTimeTracking": "Individual time tracking", "products.pricing.individualTimeTrackingTooltip": "Enable to accumulate time registered at a job. This means that two employees working simultaneously will double the price the customer pays for the job.", "products.pricing.pricingGroup": "pricing-group", "products.pricing.testPricing": "Test pricing", "products.pricing.testPricingModal.calculatedPriceLabel": "Calculated product price", "products.pricing.testPricingModal.closeButtonLabel": "Close", "products.pricing.testPricingModal.dateLabel": "Date", "products.pricing.testPricingModal.endTimeLabel": "End time", "products.pricing.testPricingModal.productLabel": "Product", "products.pricing.testPricingModal.productNameLabel": "Product", "products.pricing.testPricingModal.quantityInfoLabel": "Quantity", "products.pricing.testPricingModal.quantityLabel": "Quantity", "products.pricing.testPricingModal.quantityPlaceholder": "Enter the quantity you want to test with", "products.pricing.testPricingModal.simulateOrderButtonLabel": "Simulate order", "products.pricing.testPricingModal.startTimeLabel": "Start time", "products.pricing.testPricingModal.title": "Test price rules", "products.pricing.testPricingModal.totalPriceLabel": "Total price", "products.pricing.testPricingModal.unitPriceLabel": "Unit price", "products.pricing.testPricingModal.totalPriceAdjustment": "Total price adjustment", "products.product-details.pricing.price-rule-groups.price-rules.calculationTypes.amount": "Amount", "products.product-details.pricing.price-rule-groups.price-rules.calculationTypes.percentage": "Percentage", "products.product-details.pricing.price-rule-groups.price-rules.manual-default-name": "Manual price rule", "products.quantityCalculations.addNewCalculation": "Add calculation", "products.quantityCalculations.editCalculation": "Edit calculation", "products.quantityCalculations.testCalculation": "Test calculation", "products.quantityCalculations.testCalculation.run": "Calculate", "products.quantityCalculations.testCalculation.result": "Result", "products.quantityCalculations.testCalculation.crewSize": "Crew size", "products.quantityCalculations.calculationsTitle": "Calculations", "products.quantityCalculations.valueSource": "Data source", "products.quantityCalculations.operation": "Operation", "products.quantityCalculations.factor": "Factor", "products.quantityCalculations.equationOperation": "Equation operation", "products.quantityCalculations.enableCalculationLabel": "Enable automatic calculation for this product", "products.questionnaire.template.header": "Add template", "products.questionnaire.template.yourTemplates": "Your templates", "products.questionnaire.template.subheader": "Select from the templates you already have made", "products.questionnaire.template.templateName": "Template name", "productDetails.questionnaire.template": "Questionnaire template", "products.questionnaire.template.questions": "Questions", "products.questionnaire.template.viewQuestion": "View questions", "products.questionnaire.addChoice": "Add option", "products.questionnaire.addQuestionnaire": "Add questionnaire", "products.questionnaire.addQuestionnaireModal.addCommentBtn": "Add comment field", "products.questionnaire.addQuestionnaireModal.close": "Close", "products.questionnaire.addQuestionnaireModal.commentDescription": "Ask customers to add comments or additional details when selecting this option", "products.questionnaire.addQuestionnaireModal.commentPlaceholder": "e.g. why did you select this option?", "products.questionnaire.addQuestionnaireModal.continue": "Continue", "products.questionnaire.addQuestionnaireModal.description": "Description", "products.questionnaire.addQuestionnaireModal.descriptionPlaceholder": "Add a description here", "products.questionnaire.addQuestionnaireModal.ghostTextPlaceholder": "", "products.questionnaire.addQuestionnaireModal.header": "Create new question", "products.questionnaire.addQuestionnaireModal.multiSelect": "Multi-Select", "products.questionnaire.addQuestionnaireModal.newOption": "Add new option", "products.questionnaire.addQuestionnaireModal.optionNameRequired": "You need at least one option.", "products.questionnaire.addQuestionnaireModal.optionPlaceholder": "Option {{value}}", "products.questionnaire.addQuestionnaireModal.options": "Options", "products.questionnaire.addQuestionnaireModal.preview": "Preview", "products.questionnaire.addQuestionnaireModal.question": "Question", "products.questionnaire.addQuestionnaireModal.question.header": "customer question", "products.questionnaire.addQuestionnaireModal.questionPlaceholder": "Enter your question here", "products.questionnaire.addQuestionnaireModal.questionType": "Question Type", "products.questionnaire.addQuestionnaireModal.required": "Required", "products.questionnaire.addQuestionnaireModal.save": "Save", "products.questionnaire.addQuestionnaireModal.selectionType": "Selection Type", "products.questionnaire.addQuestionnaireModal.singleSelect": "Single Select", "products.questionnaire.addQuestionnaireModal.specificationTextRequired": "This field is required", "products.questionnaire.addQuestionnaireModal.tooltipMultipleSelect": "Enables the customer to select multiple answers from the given options. Useful for questions where several responses can be applicable at the same time.", "products.questionnaire.addQuestionnaireModal.tooltipQuestion": "Enter a name for this question. This name is displayed at top of the group when customers are booking online.", "products.questionnaire.addQuestionnaireModal.tooltipRequired": "Require customers to select a modifier from this group", "products.questionnaire.addQuestionnaireModal.tooltipSingleSelect": "Allows the customer to choose only one answer from the provided options. Ideal for questions where only one response is appropriate or valid.", "products.questionnaire.editQuestionnaire.title": "Edit questionnaire", "products.questionnaire.addShortAnswer": "Add short answer item", "products.questionnaire.choiceModal.addRequest": "Add a request for further specification", "products.questionnaire.choiceModal.addShortItem": "Add short answer item", "products.questionnaire.choiceModal.choiceName": "Choice name", "products.questionnaire.choiceModal.close": "Close", "products.questionnaire.choiceModal.modalTitle": "Add choice", "products.questionnaire.choiceModal.remove": "Remove", "products.questionnaire.choiceModal.save": "Save", "products.questionnaire.continueBtn": "Continue", "products.questionnaire.customerName": "Customer name", "products.questionnaire.customerQuestionnaire": "Customer questionnaire", "products.questionnaire.customerQuestionnaireLabel": "Add custom form fields to collect additional info", "products.questionnaire.doorman": "<PERSON>man", "products.questionnaire.editBtn": "Edit", "products.questionnaire.emptyQuestionnaireDescripption1": "Customer questionnaire you to collect extra information from your customers during the booking process using custom fields.", "products.questionnaire.emptyQuestionnaireDescripption2": "Customers booking online can make selections from these groups to customize this service and get an accurate price quote.", "products.questionnaire.hiddenKey": "Hidden key", "products.questionnaire.howDoWeGetIn": "How do we get in?", "products.questionnaire.intakeDescription": "Intake questions allow you to collect extra information from your customers during the booking process using custom fields.", "products.questionnaire.intakeQuestions": "Intake Questions", "products.questionnaire.newQuestionnaireBtn": "New Questionnaire", "products.questionnaire.noQuestionnaireAvailable": "No questionnaire available", "products.questionnaire.orderConfirmation": "Order confirmation", "products.questionnaire.preview": "Preview", "products.questionnaire.someoneIsHome": "Someone is home", "products.stage.addNewStage": "Add new stage", "products.stage.addOrEditStages": "Add or edit stages for this product", "products.stage.addStageModal.closeButtonLabel": "Close", "products.stage.addStageModal.customStageNameLabel": "Custom stage name", "products.stage.addStageModal.modalTitle": "Add new stage", "products.stage.addStageModal.saveStageButtonLabel": "Save stage", "products.stage.addStageModal.stageTypeLabel": "Stage type", "products.stage.addStageModal.stagesMarkedNotice": "Stages marked with (*) will add a second address field to the order", "products.stage.advancedView": "Advanced view", "products.stage.billable": "Billable", "products.stage.billableTooltip": "If the stage is billable, it will be included in the automatic price calculation that is based on the logged duration of the order", "products.stage.billableTransport": "Billable transport", "products.stage.billableTransportTooltip": "If the stage is set to billable transport, the transport phase will be included in the automatic price calculation that is based on the logged duration of the order", "products.stage.customerName": "Customer name", "products.stage.duplicatable": "Duplicatable", "products.stage.duplicatableTooltip": "If the stage is set to be duplicatable, the stage is available for duplication at order creation", "products.stage.useInCalculation": "Use in calculation", "products.stage.useInCalculationTooltip": "Decide whether the address at the stage should be used in the automatic calculation of quantity/duration for orders", "products.stage.finished": "Finished", "products.stage.includeTransport": "Include transport", "products.stage.includeTransportTooltip": "If the stage is set to include transport, the stage will implement a trackable transport phase before the stage is started", "products.stage.notifyCustomer": "Notify customer", "products.stage.notifyCustomerTooltip": "If the stage is set to notify customer, the customer will receive a notification that the job has been started whenever the stage is started (this will not override company notification settings)", "products.stage.pleaseAddAStageForPreview": "Please add a stage for preview", "products.stage.preview.orderID": "Order #57", "products.stage.preview.title": "Preview", "products.stage.preview.yourOrderStatus": "Your order status", "products.stage.setAddress": "Set address", "products.stage.setAddressTooltip": "If the stage is set to include an address, the stage will require address input on order creation", "products.stage.setsQuantity": "Sets quantity", "products.stage.setsQuantityTooltip": "If the stage is set to set quantity, the size of the property defined in the stage address will be set as the order quantity", "products.stage.stageName": "Stage name", "products.stage.stageTutorial": "Stage tutorial", "products.stage.stageType": "Stage type", "products.stage.yourOrderStatus": "Your order status", "products.upsellProducts.add": "Add", "products.upsellProducts.availableUpsellProducts": "Available upsell products", "products.upsellProducts.customerName": "Customer name", "products.upsellProducts.noUpsellProducts": "No upsell products", "products.upsellProducts.noUpsellProductsAvailable": "No upsell products available", "products.upsellProducts.orderEstimate": "Order estimate", "products.upsellProducts.preview": "Preview", "products.upsellProducts.savedUpsellProducts": "Saved upsell products", "products.recurringOptions": "Recurring options", "products.recurringOptions.description": "Define the recurring frequencies below if you want to allow customers to book this service as a recurring appointment.", "products.recurringOptions.addRecurringOption": "Add recurring option", "products.recurringOptions.createRecurringOption": "Create recurring option", "products.recurringOptions.frequency": "Frequency", "products.recurringOptions.discount": "Discount", "products.recurringOptions.name": "Name", "products.recurringOptions.every": "Every", "products.recurringOptions.week": "week(s)", "products.recurringOptions.descriptionLabel": "Name", "products.recurringOptions.nthWeekItem.1": "1st", "products.recurringOptions.nthWeekItem.2": "2nd", "products.recurringOptions.nthWeekItem.3": "3rd", "products.recurringOptions.nthWeekItem.4": "4th", "registerUser.confirmPassword": "Confirm your password", "registerUser.confirmPasswordLabel": "Enter your password again", "registerUser.description": "Confirm email and password to gain access to Between.", "registerUser.email": "Email", "registerUser.emailLabel": "ola_<PERSON><PERSON><EMAIL>", "registerUser.loginLinkLabel": "Between Core", "registerUser.password": "Password", "registerUser.passwordLabel": "Enter your new password ", "registerUser.registered": "You have successfully registered. Please login to continue.", "registerUser.submitButtonLabel": "Confirm", "registerUser.title": "Register user", "registerUser.error.title": "Invalid invitation", "registerUser.error.description": "The invitation you have used is invalid. Please verify that you are using your most recent invitation, as previous invitations are invalidated whenever you receive a new one.", "reports.capturedPayments": "Captured Payments", "reports.financialSummary": "Financial summary", "reports.netSales": "Net sales", "reports.orderList.captured": "Captured", "reports.orderList.customer": "Customer", "reports.orderList.id": "Order ID", "reports.orderList.netSales": "Net sales", "reports.orderList.paymentMethod": "Payment method", "reports.orderList.totalSales": "Total sales", "reports.orderList.accountingStatus": "Accounting status", "reports.orderList.vat": "VAT", "reports.refunds": "Refunds", "reports.sales": "Sales", "reports.salesList": "Sales", "reports.salesReport": "Sales Report (PDF)", "reports.totalSales": "Total sales", "reports.transactionReportPdf": "Transaction Report (PDF)", "reports.transactionReportXlsx": "Transaction Report (XLSX)", "reports.vat": "VAT", "reports.employeeReports.table.title": "Employees", "reports.employeeReports.table.execNumSales": "Total orders", "reports.employeeReports.table.execSalesIncVat": "Orders inc vat", "reports.employeeReports.table.execSalesExVat": "Orders ex vat", "reports.employeeReports.table.numSent": "Sent payments", "reports.employeeReports.table.sentSalesIncVat": "Total inc vat (Sent)", "reports.employeeReports.table.sentSalesExVat": "Total ex vat (Sent)", "reports.employeeReports.table.numSales": "Number of sales", "reports.employeeReports.table.salesIncVat": "Total sales inc vat (Paid)", "reports.employeeReports.table.salesExVat": "Total sales ex vat (Paid)", "reports.employeeReports.title": "Employee report", "reports.timeTracking.title": "Time tracking for employees", "reports.timeTracking.hideToggle": "Hide employees without trackings", "reports.timeTracking.hideUntrackedRows": "Hide rows without tracked time", "reports.employeeReports.table.total": "Total", "reports.timeTracking.table.employee": "Employee", "reports.timeTracking.table.totalTime": "Total time", "reports.timeTracking.table.totalTrackedTime": "Total tracked time", "reports.timeTracking.table.totalAssignedTime": "Total assigned time", "reports.timeTracking.table.order": "Order", "reports.timeTracking.table.description": "Description", "reports.timeTracking.table.startStop": "Start/Stop", "reports.timeTracking.table.trackedTime": "Tracked duration", "reports.timeTracking.table.assignedTime": "Assignment duration", "reports.timeTracking.table.date": "Date", "reports.timeTracking.noAccountingToolTip": "The employee is not connected to your accounting system", "orders.orderList.orderNumber": "Order Number", "orders.orderList.total_amount_ex_vat": "Total Amount Ex. VAT", "orders.orderList.total_amount_inc_vat": "Total Amount Inc. VAT", "orders.orderList.total_vat_amount": "Total VAT Amount", "orders.orderList.calculated_discount_amount": "Discount Amount", "orders.orderList.discount_reason": "Discount Reason", "orders.orderList.sales_price": "Sales Price", "orders.orderList.sales_price_ex_vat": "Sales Price Ex. VAT", "orders.orderList.sales_price_vat_amount": "Sales Price VAT Amount", "orders.orderList.execution_at": "Execution Date", "orders.orderList.execution_to": "Execution To", "orders.orderList.payment_recipient_name": "Payment Recipient Name", "orders.orderList.service_recipient_name": "Service Recipient Name", "orders.orderList.payment_status_name": "Payment Status Name", "orders.orderList.payment_method_name": "Payment Method Name", "orders.orderList.order_notes": "Order Notes", "orders.orderList.main_product_name": "Main Product Name", "orders.orderList.quote_sent_at": "Quote <PERSON>", "orders.orderList.refunded_amount": "Refunded Amount", "orders.orderList.invoice_sent_at": "Invoice Sent At", "orders.orderList.invoice_send_type_name": "Invoice Send Type", "orders.orderList.invoice_due_date_days": "Invoice Due Date Days", "orders.orderList.invoice_email": "Invoice Email", "orders.orderList.invoice_reference_text": "Invoice Reference Text", "orders.orderList.order_source_name": "Order Source Name", "orders.orderList.captured_at": "Captured At", "orders.orderList.created_at": "Created At", "orders.orderList.assignedCrew": "Crew", "orders.orderList.order_status_name": "Orderstatus", "orders.orderList.export": "Export to Excel", "reports.timeTracking.table.backBtn": "Timetracking", "reports.timeTracking.table.addModal": "Add new time", "reports.timeTracking.table.export": "Export", "reports.timeTracking.modal.editTitle": "Edit time registration", "reports.timeTracking.modal.title": "Time registration", "reports.timeTracking.modal.duration": "Duration", "reports.timeTracking.modal.date": "Date", "reports.timeTracking.modal.description": "Description", "reports.timeTracking.modal.comment": "Comment", "reports.timeTracking.modal.useInSalary": "Show in hours approval", "reports.timeTracking.modal.autoRegistered": "Automatically created", "reports.timeTracking.modal.employee": "Employee", "reports.timeTracking.modal.selectEmployee": "Select employee", "reports.timeTracking.modal.job": "Job (optional)", "reports.timeTracking.modal.activity": "Activity", "reports.timeTracking.modal.project": "Project (optional)", "reports.timeTracking.modal.department": "Department (optional)", "reports.timeTracking.modal.start": "Start", "reports.timeTracking.modal.stop": "Stop", "resetPassword.confirmPassword": "Confirm password", "resetPassword.confirmPasswordLabel": "Enter password again", "resetPassword.description": "Enter new password", "resetPassword.password": "Password", "resetPassword.passwordLabel": "Enter new password", "resetPassword.submitButtonLabel": "Confirm", "resetPassword.title": "Reset password", "resources.EditResource": "", "resources.addNewResource": "Add new resource", "resources.addResource": "Add resource", "resources.chooseResourceType": "Choose resource type", "resources.clickHereToUploadImage": "Click here to upload image", "resources.createdAt": "Created", "resources.editName": "Edit resource", "resources.editVehicleData": "Edit Vehicle Information", "resources.error.noVehicleFound": "No vehicle found", "resources.error.specialChar": "Special character are not allowed", "resources.image": "Image", "resources.itemName": "Resources", "resources.name": "Name", "resources.namePlaceholder": "Enter name", "resources.registrationLabel": "Search for vehicle with registration number", "resources.registrationPlaceholder": "Registration number", "resources.resourceDescription": "Description", "resources.resourceDescriptionPlaceholder": "Enter description", "resources.resourceTypeName": "Type", "resources.save": "Save", "resources.saveVehicleData": "Save Vehicle Information", "resources.search": "Search", "resources.searchRegistrationNumber": "Search for vehicle using registration number", "resources.unknown": "Not specified", "resources.assettype": "Asset type", "resources.vehicleDetails": "Vehicle Details", "response_assets.created": "Created", "response_assets.workOrderDuplicateNoExecution": "You must set execution time for this work order before duplicating", "response_assets.workOrderDateAndTimeError": "A job cannot span multiple days", "response_assets.contractDeclined": "Job declined", "response_assets.contractAccepted": "The job is accepted, and has been moved to your job list", "response_assets.no_phone": "User does not have a phone number", "response_assets.no_email": "User does not have an email", "response_assets.invoiceCredited": "Invoice credited", "response_assets.product_accounting_vat_mismatch": "Product from accounting does not match the VAT type of this product.", "response_assets.duplicated_and_navigated": "Order duplicated.", "response_assets.sms_sent": "SMS sent", "response_assets.order_confirmation_email_sent": "Email sent", "response_assets.nope": "Nope!", "response_assets.saved": "Saved", "response_assets.sent": "<PERSON><PERSON>", "response_assets.deleted": "Deleted", "response_assets.cannot_unassign": "Cannot unassign order due to order status.", "response_assets.checklist_space": "Task name must contain a character", "response_assets.company_created": "Company created", "response_assets.company_updated": "Company updated", "response_assets.contactCreated": "Contact created", "response_assets.businessAndContactCreated": "Business and contact created", "response_assets.contactCreationError": "Could not create contact", "response_assets.customerSaved": "Customer saved", "response_assets.customer_user_exists": "", "response_assets.different_password": "Password do not match", "response_assets.duplicate_email": "Email is already in use by an existing user", "response_assets.duplicate_phone": "Phone number is already in use by an existing user", "response_assets.duplicate_product_name": "Product name already exists", "response_assets.employee_created": "Employee was created", "response_assets.employee_delete_limit": "At least one employee must remain; you cannot delete all employees.", "response_assets.employee_updated": "Employee updated", "response_assets.event_created": "Event added", "response_assets.event_deleted": "Event deleted", "response_assets.event_updated": "Event updated", "response_assets.pdf_generated_successfully": "PDF generated", "response_assets.quote_sent_successfully": "Quote sent", "response_assets.sms_name_updated": "Sender name updated", "response_assets.existing_user_email": "Email is already assigned to an existing user", "response_assets.existing_user_phone": "Phone number is already assigned to an existing user", "response_assets.existing_organisation_number": "A company with this organisation number already exists.", "response_assets.expired_token": "Session expired, please log in to continue.", "response_assets.negative_order_amount": "Order cannot have a negative total price when sent to payment", "response_assets.negative_order_line_amount": "Order lines cannot have a negative total price when order is sent to payment", "response_assets.cannot_remove_last_company_admin": "Cannot remove the last company admin", "response_assets.global_response_bad_request": "Something wrong happened, contact support for help.", "response_assets.global_response_created": "New item has been successfully created.", "response_assets.global_response_duplicate_error": "Something wrong happened, a duplicate entry was found.", "response_assets.global_response_forbidden": "Not allowed.", "response_assets.global_response_gone": "Sorry, we could not find what you were looking for.", "response_assets.global_response_internal_server_error": "Something wrong happened, contact support for help.", "response_assets.global_response_method_not_allowed": "The method used in the request is not allowed for the requested URL.", "response_assets.global_response_no_content": "No content available to display.", "response_assets.global_response_payload_too_large": "The request payload is too large. Please reduce the size and try again.", "response_assets.global_response_service_unavailable": "The server is currently unable to handle the request due to temporary overloading or maintenance of the server.", "response_assets.global_response_success": "The operation was successful!", "response_assets.global_response_unauthorized": "Unauthorized access. Please log in to continue.", "response_assets.global_response_unsupported_media_type": "Unsupported media type.", "response_assets.global_response_updated": "The item has been successfully updated.", "response_assets.notAvailableInOfflineMode": "This operation is not available in offline mode", "response_assets.inputFieldSpacing": "Input field must contain a character and cannot exist only of spaces ", "response_assets.invitation_failed": "Invitation could not be sent", "response_assets.invitation_sent": "Invitation sent", "response_assets.logo_size": "Please ensure that the photo's file size remains under 10MB.", "response_assets.missing_company_id": "You have not selected a company.", "response_assets.multiple_fixed_percentage": "Only one 'Fixed Price - Percentage' trigger is allowed.", "response_assets.no_address_found": "No address found", "response_assets.no_addresses_found": "", "response_assets.no_billable": "At least one stage has to be billable", "response_assets.order_is_already_paid": "Order is already paid.", "response_assets.order_locked_order_status": "The order is locked and cannot be changed due to order status.", "response_assets.order_locked_payment_status": "The order is locked and cannot be changed due to payment status.", "response_assets.order_not_assigned": "", "response_assets.out_of_stock": "Selected quantity not available in stock.", "response_assets.partner_company_exist": "Partner company name already exist", "response_assets.photo_size": "Please ensure that the photo's file size remains under 10MB.", "response_assets.product_created": "Product creation successful", "response_assets.product_updated": "Product updated", "response_assets.resource_created": "Resource created", "response_assets.resource_updated": "Resource updated", "response_assets.taskNameValidation": "Task name cannot be empty", "response_assets.test_price_rule_end_time": "The end time needs to be after the start time.", "response_assets.unauthorised_for_core": "You are not authorized to access this platform.", "response_assets.updated": "Updated", "response_assets.url_does_not_exist": "The requested URL does not exist.", "response_assets.wrong_password": "Wrong phone or password.", "response_assets.embedDeleted": "Booking form deleted", "response_assets.embedCreated": "Booking form created", "response_assets.embedUpdated": "Booking form updated", "response_assets.embedProductAdded": "Product added", "response_assets.embedProductUpdated": "Product updated", "response_assets.embedProductDeleted": "Product removed", "response_assets.discount_amount_too_low": "Discount amount can’t be zero or lower than zero", "response_assets.embedCodeCopied": "Code is added to clipboard", "response_assets.discount_greater_than_100": "Discount percentage cannot be greater than 100%", "response_assets.copy": "Copied!", "response_assets.user_is_customer_at_other_companies": "Customer is a user at another company and cannot be edited", "response_assets.overlap": "Time tracking overlaps", "response_assets.timeTracking_updated": "Time tracking updated", "response_assets.timeTracking_created": "Time tracking created", "response_assets.syncSuccessfull": "Calendar updated", "response_assets.syncFailed": "Data sync failed", "response_assets.colorUpdatedSuccess": "Color updated", "response_assets.eventTitleTagsUpdatedSuccess": "Event title tags updated", "response_assets.StartEndDatsNotSameDay": "Start and end date must be on the same day", "response_assets.no_multi_day_work_orders": "No multi-day jobs, please create a new job for each day", "response_assets.exceedFileSize": "File size exceeds 10 MB. Please select a smaller file", "response_assets.multiDayWorkOrderDateAndTimeError": "Job cannot start after it ends", "response_assets.settings.billing.cardAddedSuccess": "Payment method added successfully", "response_assets.networkError": "Network error, your internet connection is unstable. Your changes are not saved.", "salary.approval.title": "Approve hours", "salary.approval.addTimeTracking": "Add time tracking", "salary.approval.approvals.title": "Payroll runs", "salary.approval.list.ongoing": "Ongoing", "salary.approval.list.periods": "Work periods", "salary.approval.list.approve": "Approve", "salary.approval.list.review": "Review", "salary.approval.list.time": "Time", "salary.approval.list.total": "Total", "salary.approval.list.activity": "Activity", "salary.approval.list.description": "Description", "salary.approval.list.comment": "Comment", "salary.approval.list.status": "Status", "salary.approval.week": "Week", "salary.approval.approve": "Approve", "salary.approval.finishPeriod": "Mark period as finished", "salary.approvedHours.title": "Monthly overview", "salary.approvedHours.period": "Period", "salary.approvedHours.addForEmployee": "Add for employee", "salary.approvedHours.createSalaryRun": "Create payroll run", "salary.approvedHours.employee": "Employee", "salary.approvedHours.employee.placeholder": "Choose employee", "salary.approvals.title": "Payroll runs", "salary.approvals.transferToAccounting": "Transfer to accounting", "salary.approvals.excel": "Excel", "salary.approvals.list.createdAt": "Created at", "salary.approvals.list.createdBy": "Created by", "salary.approvals.list.syncedAccounting": "Synced to accounting", "salary.approvedHours.noHoursAvailable": "No hours available for payroll run", "salary.approvedHours.createSalaryRun.withoutAccountingCheck": "Create without accounting check", "salary.approvedHours.createSalaryRun.withoutMonthly": "Create without monhtly salaries", "salary.approvedHours.activities": "Activities", "salary.approvedHours.supplements": "Supplements", "salary.approvedHours.activityEmployee": "Employee/Activity", "salary.approvedHours.workingHoursModal.create": "Create new time tracking", "salary.approvedHours.workingHoursModal.edit": "Registered time", "salary.approvedHours.workingHoursModal.type": "Type", "salary.approvedHours.workingHoursModal.activity": "Activity", "salary.approvedHours.workingHoursModal.salaryType": "Salary type", "salary.approvedHours.workingHoursModal.hours": "Hours", "salary.approvedHours.workingHoursModal.quantity": "Quantity", "salary.approvedHours.workingHoursModal.comment": "Comment", "salary.approvedHours.workingHoursModal.totalAmount": "Total salary amount", "salary.approvedHours.workingHoursModal.trackings": "Trackings", "salary.approvedHours.workingHoursModal.noTrackings": "No trackings available", "salary.approvedHours.workingHoursModal.time": "Time", "salary.approvedHours.workingHoursModal.description": "Description", "salary.approvedHours.workingHoursModal.source": "Source", "salary.approvedHours.workingHoursModal.source.jobHours": "Job duration", "salary.approvedHours.workingHoursModal.source.manual": "Registered by user", "salary.approvedHours.workingHoursModal.noSalary": "The employee does not have an active salary agreement", "salary.absence.title": "Vacations and absences", "salary.absence.modal.title.create": "Create absence", "salary.absence.modal.title.edit": "Edit absence", "salary.absence.modal.absenceType": "Type of absence", "salary.absence.modal.absenceType.placeholder": "Select absence type", "salary.absence.modal.description": "Description", "salary.absence.modal.checkConflicts": "Check conflicts", "salary.activeFrom": "Active from", "salary.activeTo": "Active to", "salary.selectDate": "Select date", "salary.absence.conflictModal.title": "has the following jobs within the period", "salary.absence.conflictModal.description": "You can now choose to go into each job and handle the assignment of substitutes manually, or you can choose a substitute in the drop-down menu below the job list. After selecting a substitute, you can choose whether to replace {{name}} with the substitute in the jobs you have marked, or if you simply want to add the substitute to selected jobs.", "salary.absence.conflictModal.substitute.placeholder": "Select one or multiple substitutes", "salary.absence.conflictModal.substitute.add": "Add substitute", "salary.absence.conflictModal.substitute.replace": "Replace with substitute", "salary.absence.conflictModal.substitute.remove": "Remove {{name}} from selected jobs", "salary.absence.conflictModal.list.job": "Job", "salary.absence.conflictModal.list.executionDate": "Job date", "salary.absence.list.employee": "Employee", "salary.pause": "Break", "response_assets.settings.company.operateExVat.change.success": "VAT display mode has been updated successfully", "save": "Save", "seconds": "seconds", "selectorini.disabled": "Disabled", "selectorini.noItemsSelected": "No items selected", "selectorini.noEmployeeSelected": "No employees added to this order", "selectorini.noResourceSelected": "No resource added to this order", "selectorini.createNewAddress": "Can't find the address? Create a new one", "selectorini.searchAddressPlaceholder": "Search for address", "settings.company.edit-company.title": "Edit company", "settings.company.address": "Address", "settings.company.archiveOnFinished": "Archive orders when finished", "settings.company.autoFinishWorkOrders": "Finish started jobs automatically at midnight", "settings.company.autoFinishAllWorkOrders": "Finish started and unstarted jobs automatically at midnight, given that the order is confirmed", "settings.company.splitOrderEvent": "Split order spanning multiple days into multiple events", "settings.company.splitOrderEvent.tooltip": "Enabling this will create multiple events connected to the same order if the order execution time spans multiple days. The start and end time for each event will be defined by the opening hours set for your company.", "settings.company.showEmployeeInitialsOnly": "Show employee initials instead of profile images in quick selection lists", "settings.company.showEmployeeInitialsOnly.tooltip": "Enable this to show employee initials instead of profile images in quick selection lists. This can be useful if your employees have profile images that look alike in smaller formats.", "settings.company.manualOrderConfirmationEmail": "Enforce manual sending order confirmation email", "settings.company.manualOrderConfirmationEmail.tooltip": "Enabling this will stop the system from automatically sending order confirmation emails to customers when the customer accept an order. Unless you accept the order yourself, you will have to manually send the order confirmation from the order details page.", "settings.company.customerCancelWorkOrder": "Allow customers to cancel jobs", "settings.company.defaultRepeatingOrders": "Company works with both repeating and single orders", "settings.company.customerCanOnlyAccept": "Confirm orders automatically when customer accepts quote in customer portal", "settings.company.customerCanOnlyAccept.tooltip": "Enabling this will set the order to Confirmed automatically when a customer accepts an order-quote in customer portal. If deactivated, orders accepted by customers must be explicitly confirmed in Core in order to proceed.", "settings.company.smsName": "Text message sender name", "settings.company.city": "City", "settings.company.companyInformation": "Company information", "settings.company.companyName": "Company name", "settings.company.country": "Country", "settings.company.email": "Email", "settings.company.error.address": "Invalid address", "settings.company.error.city": "Invalid city", "settings.company.error.companyName": "Invalid company name", "settings.company.error.country": "Invalid country", "settings.company.error.email": "Invalid e-mail", "settings.company.error.organisationNumber": "Invalid organistaion number", "settings.company.error.phone": "Invalid phone", "settings.company.error.postalCode": "Invalid postal code", "settings.company.logo": "Logo", "settings.company.orders": "Orders", "settings.company.employees": "Employees", "settings.company.employees.geo_lock_switch_label": "Use geo-lock for check-in at jobs", "settings.company.employees.geo_lock_range_label": "Max check-in-distance from job address", "settings.company.contractor.title": "Agreements as contractor", "settings.company.contractor.showCalendar": "Calendar access", "settings.company.contractor.tooltip": "This list shows all companies that has registered your company as a contractor. If you give access to your calendar, the given company will be able to see a anonymised version of your calendar.", "settings.company.title": "Company settings", "settings.company.editCompanyDetails": "Edit company details", "settings.company.quantityCalculationResolutions.label": "Time adjustment for time-based services", "settings.company.quantityCalculationResolutions.none": "No adjustment", "settings.company.quantityCalculationResolutions.15min": "Adjust to nearest quarter", "settings.company.quantityCalculationResolutions.30min": "Adjust to nearest half hour", "settings.company.quantityCalculationResolutions.60min": "Adjust to nearest hour", "settings.company.communication": "Communication", "settings.company.smsNameDescription": "The name that will appear as the sender of text messages sent from Between. 4 to 11 characters - Only numbers, regular characters, spaces, and dashes allowed. ", "settings.company.phoneNumber": "Phone number", "settings.company.postalCode": "Postal code", "settings.company.pricePresentation.pricePresentationForBusiness": "Display prices inc VAT for business customers", "settings.company.pricePresentation.pricePresentationForPrivate": "Display prices inc VAT for private customers", "settings.company.pricePresentation.operateExVat": "Treat all prices ex VAT", "settings.company.calendarSettings": "Calendar settings", "settings.company.openingHours": "Opening hours", "settings.company.openingHours.from": "From", "settings.company.openingHours.to": "To", "settings.company.calendarSettings.timeFrom": "Time from", "settings.company.calendarSettings.timeTo": "Time to", "settings.company.pricePresentation.title": "Price presentation", "settings.company.registrationNumber": "Organisation number", "settings.company.save": "Save", "settings.company.saved": "Saved!", "settings.company.uploadImage": "Click here to upload image", "settings.company.companyColor": "Company color", "settings.company.operateExVat.change.title": "Change VAT Display Mode", "settings.company.operateExVat.change.warning": "Warning: This will change how prices are displayed throughout the system", "settings.company.operateExVat.change.description": "Please review all your prices after making this change to ensure they are displayed correctly. This change will affect how prices are shown in all parts of the system.", "response.assets.settings.company.operateExVat.change.success": "VAT display mode has been updated successfully", "settings.company.quoteValidityDays": "Quote validity days", "settings.company.quoteValidityDays.tooltip": "The number of days a quote is valid for. after this period, the quote will expire and cannot be accepted by the customer", "settings.integrations.accounting.activationDescription": "On what date do you want us to start this integration?", "settings.integrations.accounting.activationSelect": "Select an activation date", "settings.integrations.accounting.activationTitle": "Activation Date", "settings.integrations.accounting.activationDateDescription": "The integration with Tripletex starts from a desired date. All transactions after this date are integrated", "settings.integrations.accounting.betweenMethods": "Between payment methods:", "settings.integrations.accounting.connection": "Connection", "settings.integrations.accounting.connectionContinue": "Continue", "settings.integrations.accounting.connectionTest": "Save token", "settings.integrations.accounting.editHeader": "Edit tripletex integration", "settings.integrations.accounting.editHeader.fiken": "Edit Fiken integration", "settings.integrations.accounting.employeeToken": "Employee token", "settings.integrations.accounting.enterYourToken": "Enter your token here", "settings.integrations.accounting.externalPayments": "External payments", "settings.integrations.accounting.finish": "Finish", "settings.integrations.accounting.freePayments": "Free orders", "settings.integrations.accounting.requireManualPush": "Don't push to accounting automatically", "settings.integrations.accounting.usePaymentDate": "Use send-to-payment as invoice date", "settings.integrations.accounting.integrationError": "Something went wrong, please contact support", "settings.integrations.accounting.integrationSuccess": "Integration successfully set up", "settings.integrations.accounting.intergrationIsActive": "Integration is active", "settings.integrations.accounting.intergrationWasStartedAt": "Integration was activated at", "settings.integrations.accounting.linkToTokenInfo": "Click here to find out how to generate an API key in Tripletex:", "settings.integrations.accounting.mapMethodsDescription": "Below we are automatically fetching your Tripletex payment methods.", "settings.integrations.accounting.mapMethodsTitle": "Map Between payment methods to your Tripletex payment methods.", "settings.integrations.accounting.mapMethodsWhy": "Why do I need to do this?", "settings.integrations.accounting.methodBackendError": "Something went wrong, please contact support", "settings.integrations.accounting.methodError": "All payment methods must be mapped to continue", "settings.integrations.accounting.methodMapping": "Payment method mapping", "settings.integrations.accounting.methodsContinue": "Continue", "settings.integrations.accounting.methodsSave": "Save", "settings.integrations.accounting.methodsSuccess": "Payment methods successfully saved", "settings.integrations.accounting.paymentMethodsContinue": "Continue", "settings.integrations.accounting.paymentMethodsCreate": "Create payment methods in Tripletex", "settings.integrations.accounting.paymentMethodsDescription": "The same payment methods used in Between must now be created in Tripletex.", "settings.integrations.accounting.paymentMethodsTitle": "Payment methods", "settings.integrations.accounting.paymentMethodsVideo": "Refer to the guidance provided by Tripletex, which demonstrates how to accomplish this.", "settings.integrations.accounting.postExternalInAccountingTooltip": "Choose whether orders with external payments should be posted in accounting. The actual payment will not be posted, only the order.", "settings.integrations.accounting.postFreeInAccountingTooltip": "Choose whether free orders should be posted in accounting. The actual payment will not be posted, only the order.", "settings.integrations.accounting.postInAccounting": "Post order in accounting", "settings.integrations.accounting.postInAccountingTooltip": "", "settings.integrations.accounting.selectDatePlaceholder": "Select date", "settings.integrations.accounting.selectMethod": "Select method to map", "settings.integrations.accounting.setUpYourConnection": "Set up your connection to Tripletex", "settings.integrations.accounting.startDateUpdated": "Start date updated", "settings.integrations.accounting.step1": "Check 'Custom setup'", "settings.integrations.accounting.step2": "Select 'All accesses'", "settings.integrations.accounting.step3": "Application name: Between", "settings.integrations.accounting.step4": "Name of the key is: Between", "settings.integrations.accounting.stepGuideHeading": "Under step 2, point 5, do the following:", "settings.integrations.accounting.stop": "Stop Intergration", "settings.integrations.accounting.stopDescription": "If you wish to disable the Tripletex integration, use the button below.", "settings.integrations.accounting.title": "Set up your Tripletex integration", "settings.integrations.accounting.tokenError": "<PERSON><PERSON> is invalid", "settings.integrations.accounting.tokenInfo": "An API key (employee-token) is a code you retrieve from your Tripletex account. This code enables Between and Tripletex to communicate.", "settings.integrations.accounting.tokenSuccess": "Token is valid", "settings.integrations.accounting.tripletexMethods": "Your Tripletex payment methods:", "settings.integrations.accounting.updateDateBtn": "Update date", "settings.integrations.powerofficego.accountBackendError": "Something went wrong, please contact support", "settings.integrations.powerofficego.accountContinue": "Continue", "settings.integrations.powerofficego.accountError": "All VAT rates must be mapped to continue", "settings.integrations.powerofficego.accountMapping": "Map accounts to VAT codes", "settings.integrations.powerofficego.accountMappingDescription": "", "settings.integrations.powerofficego.accountSave": "Save", "settings.integrations.powerofficego.accountSuccess": "Mapping successful", "settings.integrations.powerofficego.activationDescription": "On what date do you want us to start this integration?", "settings.integrations.powerofficego.activationSelect": "Select an activation date", "settings.integrations.powerofficego.activationTitle": "Activation Date", "settings.integrations.powerofficego.betweenMethods": "Between payment methods:", "settings.integrations.powerofficego.connection": "Connection", "settings.integrations.powerofficego.connectionContinue": "Continue", "settings.integrations.powerofficego.connectionTest": "Save token", "settings.integrations.powerofficego.editHeader": "Edit PowerOffice Go integration", "settings.integrations.powerofficego.employeeToken": "Employee token", "settings.integrations.powerofficego.enterYourToken": "Enter your token here", "settings.integrations.powerofficego.externalPayments": "External payments", "settings.integrations.powerofficego.finish": "Finish", "settings.integrations.powerofficego.integrationError": "Something went wrong, please contact support", "settings.integrations.powerofficego.integrationSuccess": "Integration successfully set up", "settings.integrations.powerofficego.intergrationIsActive": "Integration is active", "settings.integrations.powerofficego.intergrationWasStartedAt": "Integration was activated at", "settings.integrations.powerofficego.linkToTokenInfo": "Click here to find out how to generate an API key in PowerOffice Go:", "settings.integrations.powerofficego.mapAccountsTitle": "Map VAT codes to your PowerOffice Go accounts.", "settings.integrations.powerofficego.mapMethodsDescription": "Below we are automatically fetching your PowerOffice Go payment methods.", "settings.integrations.powerofficego.mapMethodsTitle": "Map Between payment methods to your PowerOffice Go payment methods.", "settings.integrations.powerofficego.mapMethodsWhy": "Why do I need to do this?", "settings.integrations.powerofficego.methodBackendError": "Something went wrong, please contact support", "settings.integrations.powerofficego.methodError": "All payment methods must be mapped to continue", "settings.integrations.powerofficego.methodMapping": "Payment method mapping", "settings.integrations.powerofficego.methodsContinue": "Continue", "settings.integrations.powerofficego.methodsSave": "Save", "settings.integrations.powerofficego.methodsSuccess": "Payment methods successfully saved", "settings.integrations.powerofficego.paymentMethodsContinue": "Continue", "settings.integrations.powerofficego.paymentMethodsCreate": "Create payment methods in PowerOffice Go", "settings.integrations.powerofficego.paymentMethodsDescription": "You now need to create the same payment methods as you have in Between in PowerOffice Go.", "settings.integrations.powerofficego.paymentMethodsTitle": "Payment methods", "settings.integrations.powerofficego.paymentMethodsVideo": "Below is a video on how you can do this", "settings.integrations.powerofficego.pogoAccounts": "PowerOffice Go accounts", "settings.integrations.powerofficego.postInAccounting": "Post order in accounting", "settings.integrations.powerofficego.postInAccountingTooltip": "Choose whether orders with external payments should be posted in accounting. The actual payment will not be posted, only the order.", "settings.integrations.powerofficego.selectDatePlaceholder": "Select date", "settings.integrations.powerofficego.selectMethod": "Select method to map", "settings.integrations.powerofficego.setUpYourConnection": "Set up your connection to PowerOffice Go", "settings.integrations.powerofficego.startDateUpdated": "Start date updated", "settings.integrations.powerofficego.step1": "Check 'Custom setup'", "settings.integrations.powerofficego.step2": "Select 'All accesses'", "settings.integrations.powerofficego.step3": "Application name: Between", "settings.integrations.powerofficego.step4": "Name of the key is: Between", "settings.integrations.powerofficego.stepGuideHeading": "Under step 2, point 5, do the following:", "settings.integrations.powerofficego.stop": "Stop Intergration", "settings.integrations.powerofficego.stopDescription": "If you wish to disable the PowerOffice Go integration, use the button below.", "settings.integrations.powerofficego.title": "Set up your PowerOffice Go integration", "settings.integrations.powerofficego.tokenError": "Key is invalid", "settings.integrations.powerofficego.tokenInfo": "An API key (employee-token) is a code you retrieve from your PowerOffice Go account. This code enables Between and PowerOffice Go to communicate.", "settings.integrations.powerofficego.tokenSuccess": "Key is valid", "settings.integrations.powerofficego.tripletexMethods": "Your PowerOffice Go payment methods:", "settings.integrations.powerofficego.updateDateBtn": "Update date", "settings.integrations.powerofficego.vatRates": "VAT rates", "settings.notifications.enabled": "Enabled", "settings.notifications.message": "Message", "settings.notifications.enabledCheckbox": "enabled", "settings.notifications.hours": "Hours", "settings.notifications.notificationSettings": "Notification Settings", "settings.notifications.notificationType": "Notification type", "settings.payment.accountId": "Account ID", "settings.payment.apiKey": "Api Key", "settings.payment.authKey": "Auth Key", "settings.payment.clientId": "Client ID", "settings.payment.clientSecret": "Client Secret", "settings.payment.dinteroPaymentCredentials": "Dintero payment credentials", "settings.payment.merchantId": "Merchant ID", "settings.payment.paymentSettings": "Payment Settings", "settings.payment.paymentSettings.general": "General settings", "settings.payment.paymentSettings.general.autoSyncOnSendToPayment": "Syncronize with accounting when payments are sent", "settings.payment.paymentSettings.general.autoSyncOnSendToPayment.tooltip": "If this is turned off, payments will only be syncronised with accounting when they are paid or sent as invoices.", "settings.payment.quickpayPaymentCredentials": "Quickpay", "settings.payment.rivertyPaymentCredentials": "Riverty", "settings.payment.save": "Save keys", "settings.payment.secretKey": "Secret Key", "settings.payment.sveaPaymentCredentials": "Svea", "settings.payment.external": "Externally handled payments", "settings.payment.privateCustomerAvailable": "Can be chosen by private customers", "settings.payment.businessCustomerAvailable": "Can be chosen by business customers", "settings.payment.disabledActiveTooltip": "You cannot activate a payment method before the payment method credentials are saved and verified", "settings.payment.disabledCustomerActivationTooltip": "Please activate payment method first", "settings.payment.disabledInvoiceActiveTooltip": "You must setup an accounting integration before you can activate invoice as a payment method", "settings.payment.privateCustomerActivationTooltip": "By activating this function, private customers will be able to select this payment method in the customer portal", "settings.payment.businessCustomerActivationTooltip": "By activating this function, business customers will be able to select this payment method in the customer portal", "settings.payment.autoSendInvoice": "Send invoices automatically when order is sent to payment", "settings.payment.markExternalAsPaid": "Automatically mark orders with external payment handling as paid when sent to payment", "settings.payment.subscriptionAllowedLabel": "Allow this payment method to be used for subscriptions", "settings.payment.subscriptionNotAvailable": "Not available for this payment method", "settings.payment.subscriptionAllowedTooltip": "If this is enabled, customers can use this payment method to authorise future payments for subscription orders", "settings.payment.dueDateBusiness": "Default due date for business customers", "settings.payment.dueDateBusiness.tooltip": "Set the default due date to be applied for new business customers. The default due date for each customer can also be set individually in the customer settings", "settings.payment.dueDatePrivate": "Default due date for private customers", "settings.payment.dueDatePrivate.tooltip": "Set the default due date to be applied for new private customers. The default due date for each customer can also be set individually in the customer settings", "settings.billing.title": "Billing", "settings.billing.paymentMethod": "Payment Method", "settings.billing.cardNumber": "Card Number", "settings.billing.cardNumberRequired": "Card number is required", "settings.billing.cardNumberInvalid": "Please enter a valid 16-digit card number", "settings.billing.cardholderName": "Cardholder Name", "settings.billing.cardholderNameRequired": "Cardholder name is required", "settings.billing.expiryMonth": "Expiry Month", "settings.billing.expiryMonthRequired": "Expiry month is required", "settings.billing.expiryYear": "Expiry Year", "settings.billing.expiryYearRequired": "Expiry year is required", "settings.billing.cvv": "CVV", "settings.billing.cvvRequired": "CVV is required", "settings.billing.saveCard": "Save Card", "settings.billing.addCard": "Add Payment Method", "settings.billing.changeCard": "Change Payment Method", "settings.billing.tryAgain": "Try Again", "settings.billing.noCardMessage": "You don't have a payment method on file. Please add a credit card to continue.", "settings.billing.noMainPayment": "The subscription payment is not ready yet for your company.", "settings.billing.redirectingMessage": "Redirecting to secure payment page...", "settings.billing.doNotCloseWindow": "Please do not close this window.", "settings.billing.cardAddedSuccess": "Payment method added successfully", "settings.billing.paymentProviderError": "There was an error processing your payment method. Please try again.", "settings.billing.genericError": "An error occurred. Please try again.", "settings.billing.verifyingPayment": "Verifying Payment", "settings.billing.verifyingPaymentMessage": "We are verifying your payment. This may take a few moments.", "settings.billing.paymentVerificationTimeout": "Payment verification timed out. Please try again.", "settings.billing.paymentVerificationError": "There was an error verifying your payment. Please try again.", "settings.billing.active": "Active", "settings.billing.expires": "Expires", "settings.billing.invoices": "Invoices", "settings.billing.noInvoices": "No invoices available", "settings.billing.invoiceId": "Invoice ID", "settings.billing.date": "Date", "settings.billing.amount": "Amount", "settings.billing.status": "Status", "settings.billing.actions": "Actions", "settings.billing.download": "Download", "settings.resources.closeTimeLabel": "Close time:", "settings.resources.friday": "Friday", "settings.resources.monday": "Monday", "settings.resources.openTimeLabel": "Open time:", "settings.resources.orderLimitPerDayLabel": "Order limit per day", "settings.resources.orderWarningLimitLabel": "Order warning limit", "settings.resources.resourceFormTitle": "Resource Settings:", "settings.resources.resourceSettings": "Resource Settings", "settings.resources.saturday": "Saturday", "settings.resources.save": "Save", "settings.resources.sunday": "Sunday", "settings.resources.thursday": "Thursday", "settings.resources.tuesday": "Tuesday", "settings.resources.wednesday": "Wednesday", "settings.calculations.title": "Calculation settings", "settings.calculations.includeTransportInCalculationLabel": "Include transport time in automatic order duration calculation", "settings.calculations.includeTransportInCalculationTooltip": "If this is active, the time it takes to drive the route from the main office, through each address in the order, and back again, will be added to the order duration when calculating duration automatically. The time is calculated by Google Maps", "settings.calculations.defaultValuesCardTitle": "Default values for", "settings.calculations.useIfUnspecifiedPropertyType": "Use these values if the property type for an order is not specified", "settings.calculations.livableArea": "Square meters", "settings.calculations.livableArea.suffix": "sqm", "settings.calculations.room.suffix": "units", "settings.calculations.rooms": "Number of rooms", "settings.calculations.bathrooms": "Number of bathrooms", "settings.calculations.floor": "Floor", "settings.calculations.floor.suffix": "floor", "settings.calculations.numberOfFloors": "Number of floors", "settings.calculations.shedArea": "Size of shed", "settings.calendar.pageTitle": "Calendar settings", "settings.calendar.selectFieldTitle": "Select Fields for Title", "settings.calendar.noFieldSelected": "No field selected", "settings.calendar.colorTitle": "Calendar event colors", "settings.calendar.colorConfiguration": "Color Configuration", "settings.calendar.eventTypeColor": "Event Type", "settings.calendar.jobStatusColors": "Job Status Colors", "settings.calendar.workTemplateColors": "Job Template Colors", "settings.calendar.noWorkOrders": "No job templates available", "settings.calendar.eventTitleConfiguration": "Event Title Configuration", "settings.calendar.titleSetup": "Select which fields to use for the event title in the calendar. The fields will be displayed in the order they are selected.", "settings.calendar.availableVariables": "Available variables", "settings.calendar.selectedVariables": "Selected variables", "settings.calendar.noSelectedFields": "No fields selected", "settings.calendar.saveEventTitle": "Save", "settings.calendar.disable": "Disable", "settings.calendar.enable": "Enable", "settings.salary.general": "General settings", "settings.salary.standardJobActivity": "Default activity for jobs", "settings.salary.standardTransportActivity": "Default activity for transport to and from jobs", "settings.salary.defaultFixedSalary": "Default salary type for fixed salaries", "settings.salary.defaultVacationActivity": "Activity for vacations", "settings.salary.defaultVacationActivity.tooltip": "Whenever this activity is used for an employee absence, the duration of the absence will be subtracted from the available vacation days for the employee.", "settings.salary.enableTimetrackingWarning": "Mark time trackings where there is a difference between planned and actual time", "settings.salary.timetrackingWarningPercentage.label": "Percentage difference for marking", "settings.salary.timetrackingWarningPercentage.tooltip": "If this is set to e.g. 25%, a job with a planned time of 1 hour will mark all time registrations with a duration of less than 45 minutes or more than 1 hour and 15 minutes.", "settings.salary.activities": "Activities", "settings.salary.activities.create": "Create activity", "settings.salary.activities.standard": "Default activity for jobs", "settings.salary.salaryTypes": "Salary types", "settings.salary.salaryTypes.create": "Create salary type", "settings.salary.salaryRules": "Salary rules", "settings.salary.salaryRules.create": "Create salary rule", "settings.salary.activities.modal.noStandardSalaryType": "No standard salary type", "settings.salary.activities.modal.notUsedInSalaryCalculation": "Will  not be used in salary calculation", "settings.salary.activities.modal.title.create": "Create activity", "settings.salary.activities.modal.title.edit": "Edit activity", "settings.salary.activities.modal.type": "Activity type", "settings.salary.activities.modal.standardSalaryType": "Default salary type for activity", "settings.salary.activities.modal.activityFromAccounting": "Activity from accounting", "settings.salary.activities.modal.color": "Activity color", "settings.salary.activities.modal.standardSalaryTypeRequired": "This activity type requires a default salary type", "settings.salary.salaryRules.modal.title.create": "Create salary rule", "settings.salary.salaryRules.modal.title.edit": "Edit salary rule", "settings.salary.salaryRules.modal.ruleType": "Rule type", "settings.salary.salaryRules.modal.numberOfHoursForActivation": "Number of hours for activation", "settings.salary.salaryRules.modal.activatedSalaryType": "Salary type to be activated", "settings.salary.salaryRules.modal.activeFrom": "Active from", "settings.salary.salaryRules.modal.activeTo": "Active to", "settings.salary.salaryRules.modal.periodCrash": "The period overlaps with another period for the same rule type", "settings.salary.salaryTypes.modal.title.create": "Create salary type", "settings.salary.salaryTypes.modal.title.edit": "Edit salary type", "settings.salary.salaryTypes.modal.type": "Salary type category", "settings.salary.salaryTypes.modal.accounting": "Salary type from accounting", "eventStatus.unconfirmed": "Unconfirmed", "eventStatus.notStarted": "Not started", "eventStatus.ongoing": "Ongoing", "eventStatus.done": "Done", "eventTypes.activities": "Activities", "titleFields.workOrderId": "Job ID", "titleFields.workOrderTitle": "Job title", "titleFields.customerName": "Customer name (Service recipient)", "titleFields.paymentRecipientName": "Customer name (Payment recipient)", "titleFields.orderId": "Order ID", "titleFields.address": "Address", "superadmin.companies.addNewCompany": "Add new company", "superadmin.companies.address": "Address", "superadmin.companies.city": "City", "superadmin.companies.companyDetails": "Company details", "superadmin.companies.gotoCompany": "Go to company", "superadmin.companies.companyInformation": "Company information", "superadmin.companies.companyName": "Company name", "superadmin.companies.country": "Country", "superadmin.companies.createdAt": "Created at", "superadmin.companies.email": "E-mail", "superadmin.companies.invoiceEmail": "Invoice e-mail", "superadmin.companies.firstName": "First name", "superadmin.companies.id": "ID", "superadmin.companies.invalidAddress": "Invalid address", "superadmin.companies.invalidCity": "Invalid city", "superadmin.companies.invalidCompanyName": "Invalid company name", "superadmin.companies.invalidCountry": "Invalid country", "superadmin.companies.invalidEmail": "Invalid e-mail", "superadmin.companies.invalidFirstName": "Invalid first name", "superadmin.companies.invalidLastName": "<PERSON>valid last name", "superadmin.companies.invalidLogo": "Invalid logo", "superadmin.companies.invalidOrgNumber": "Invalid organisation number", "superadmin.companies.invalidPhone": "Invalid phone", "superadmin.companies.invalidPostalCode": "Invalid postal code", "superadmin.companies.inviteAdmin": "Invite additional company admin", "superadmin.companies.itemName": "Companies", "superadmin.companies.companyType.all": "All", "superadmin.companies.companyType": "Company Type", "superadmin.companies.search": "Search", "superadmin.companies.lastName": "Last name", "superadmin.companies.logo": "Company logo", "superadmin.companies.name": "Name", "superadmin.companies.orgNumber": "Organisation number", "superadmin.companies.phone": "Phone", "superadmin.companies.postalCode": "Postal code", "superadmin.companies.save": "Save", "superadmin.companies.userInvitation": "User Invitation", "superadmin.itemName": "Super Admin", "tableFooter.display": "Display", "tableFooter.goToPage": "Go to page", "tableFooter.of": "of", "tableFooter.page": "Page", "tableFooter.to": "to", "tableFooter.showing": "Showing", "tableFooter.entries": "entries", "tableBody.noData": "No data", "top-bar.profile-dropdown.logout": "Logout", "top-bar.profile-dropdown.profile": "Profile", "top-bar.profile-dropdown.terms": "Terms", "verifyPopup.areYouSure": "Are you sure?", "verifyPopup.bodyBold": "Are you sure you wish to proceed?", "verifyPopup.bodyRegular": "This may not be undone.", "verifyPopup.yes": "Yes", "verifyPopup.no": "No", "imageProcessor.header": "Crop image", "unknown": "N/A", "pdf-invoice.quotationTitle": "Quotation", "pdf-invoice.s3ImageAlt": "S3 Image", "pdf-invoice.quoteToHeading": "Quote to", "pdf-invoice.quotationDetailsHeading": "Quotation Details", "pdf-invoice.quotationNumber": "Quotation", "pdf-invoice.quotationDate": "Quotation Date", "pdf-invoice.executionDate": "Execution Date", "pdf-invoice.salesPerson": "Sales Person", "pdf-invoice.seller-title": "Do you have any questions? Contant me!", "pdf-invoice.phone": "Phone", "pdf-invoice.email": "Email", "pdf-invoice.deliveryAddress": "Delivery address:", "pdf-invoice.productName": "Product Name", "pdf-invoice.qty": "Qty", "pdf-invoice.unitPrice": "Unit price", "pdf-invoice.amount": "Amount", "pdf-invoice.totalExVAT": "Subtotal", "pdf-invoice.discount": "Discount", "pdf-invoice.vat": "VAT", "pdf-invoice.total": "Total (inc VAT)", "pdf-invoice.totalEx": "Total (ex VAT)", "pdf-invoice.importantInformation": "Important information", "pdf-invoice.generated-by": "This pdf is generated by Between. www.between.as ", "pdf-invoice.filename": "Qoute for {{name}} #{{orderNumber}}", "pdf-invoice.fixedPayments": "Repeated fixed payment", "pdf-invoice.workOrderPayments": "Repeated payment per job", "pdf-invoice.singlePayment": "Single payment", "pdf-invoice.willBeSent": "Will be sent", "pdf-workOrder.filename": "Job for {{name}} #{{orderNumber}}", "backButton.businessCustomer": "Business customer", "backButton.privateCustomer": "Private customer", "backButton.employee": "Employee profile", "backButton.resources": "Rescource", "phoneInput.invalidPhone": "Invalid phone number", "phoneInput.required": "Phone number is required", "phoneInput.placeholder": "Enter phone number...", "phoneInput.favorites": "Favorites", "embedSettings.list.header": "Booking form", "embedSettings.list.subheader": "Booking forms", "embedSettings.list.description": "Create custom booking page where customers can book or request your services. You can have several booking pages for different purposes", "embedSettings.list.editButton": "Edit", "embedSettings.list.createButton": "New form", "embedSettings.name.label": "Name", "embedSettings.name.placeholder": "Booking form", "embedSettings.generateBtn": "Generate code", "embedSettings.preview": "Preview", "embedSettings.codeGenerator.header": "Generate code snippet", "embedSettings.codeGenerator.platformSupport.header": "Platform support", "embedSettings.codeGenerator.platformSupport.description": "", "embedSettings.codeGenerator.embedCode.header": "Embed code", "embedSettings.codeGenerator.embedCode.description": "Copy and paste the code into your website´s HTML. The booking button will be shown where you place the code.", "embedSettings.codeGenerator.copyCode": "Copy text", "embedSettings.codeGenerator.doneBtn": "Close", "embedSettings.preview.header": "Preview", "embedSettings.appearance.header": "Appearance", "embedSettings.appearance.seller": "Contact for orders from booking form", "embedSettings.appearance.primaryColor.label": "Primary color", "embedSettings.appearance.primaryColor.description": "Color for background, buttons, and icons", "embedSettings.appearance.buttonColor.label": "Button colors", "embedSettings.appearance.buttonColor.description": "Customize the colors of the booking button", "embedSettings.appearance.buttonColor.backgroundColor": "Background color", "embedSettings.appearance.buttonColor.borderColor": "Border color", "embedSettings.appearance.buttonColor.hoverBackgroundColor": "Hover background color", "embedSettings.appearance.buttonColor.hoverBorderColor": "Hover border color", "embedSettings.appearance.buttonColor.textColor": "Text color", "embedSettings.appearance.buttonColor.textHoverColor": "Text hover color", "embedSettings.appearance.borderRadius.label": "Button border radius", "embedSettings.appearance.borderRadius.description": "Customize the border radius of the booking button", "embedSettings.appearance.ButtonText.label": "Button text", "embedSettings.appearance.ButtonText.description": "Customize the text of the booking button", "embedSettings.services.header": "Services", "embedSettings.services.productSearch.label": "Select services you would like to show in the booking", "embedSettings.services.addQuestionsBtn": "+ Question", "embedSettings.services.editQuestionsBtn": "Questions", "embedSettings.services.selectedProducts.label": "Services", "embedSettings.services.questions.header": "Edit questions", "embedSettings.services.allowRecurring": "Allow recurring bookings", "embedSettings.services.modal.title": "Settings for", "embedSettings.services.modal.customerBtn": "Customer questionnaire", "embedSettings.services.modal.recurringBtn": "Recurring orders", "embedSettings.services.modal.recurring.schedule": "Schedule this service as a recurring booking?", "embedSettings.services.modal.recurring.scheduletxt": "Let customers schedule this service as a recurring booking by adding recurring frequencies that customers will be able to choose from. You can also offer discounts for certain frequencies", "embedSettings.config.header": "Settings", "embedSettings.config.showPrices.label": "Show prices for the customer", "embedSettings.config.showPrices.tooltip": "Display prices for all products to the customer. If this is turned off, prices are not displayed.", "embedSettings.config.Otb.label": "Require signature from customer", "embedSettings.config.Otb.tooltip": "Activating this setting ensures that the customer must authenticate with a PIN code to confirm their request.", "embedSettings.config.upsell.label": "Show upsell products", "embedSettings.config.upsell.tooltip": "Activated if you want the customer to be able to choose selected upsell products in addition to the main product.", "embedSettings.config.exVAT.label": "Show prices ex VAT", "embedSettings.config.exVAT.tooltip": "Display prices excluding VAT for all products to the customer. If this is turned off, prices are displayed including VAT.", "embedSettings.marketing.header": "Marketing", "embedSettings.marketing.ga.header": "Google Tag", "embedSettings.marketing.ga.googleTrackingId.label": "Tracking id", "embedSettings.marketing.ga.googleTrackingId.placeholder": "Google tracking id", "embedSettings.marketing.ga.googleConversionSendTo.label": "Conversion 'send to'", "embedSettings.marketing.ga.googleConversionSendTo.placeholder": "Google Conversion 'send to'", "embedModal.chooseProduct.header": "<PERSON><PERSON> wanted service", "embedModal.chooseProduct.startsFrom": "Starts from", "embedModal.addresses.header": "Addresses", "embedModal.addresses.addNew": "+ Add new", "embedModal.addresses.address": "address", "embedModal.addresses.quantity": "How many item for", "embedModal.addresses.addressLabelPrefix": "Address", "embedModal.addresses.addressFieldPlaceholder": "Search...", "embedModal.addresses.sectionLabel": "Section number", "embedModal.addresses.sectionPlaceholder": "Choose section", "embedModal.addresses.sectionDetails.type": "Type", "embedModal.addresses.sectionDetails.bedrooms": "Bedrooms", "embedModal.addresses.sectionDetails.garage": "garage", "embedModal.addresses.sectionDetails.totalRooms": "Total rooms", "embedModal.addresses.sectionDetails.size": "Size", "embedModal.addresses.bathrooms": "Bathrooms", "embedModal.addresses.sectionDetails.elevator": "Elevator", "embedModal.addresses.sectionDetails.floors": "Floors", "embedModal.addresses.sectionDetails.yes": "Yes", "embedModal.addresses.sectionDetails.no": "No", "embedModal.addresses.sectionDetails.unknown": "Unknown", "embedModal.upsell.headerServices": "Would you like to add other services?", "embedModal.upsell.headerStandard": "Would you like to add other products?", "embedModal.upsell.addBtn": "Add", "embedModal.upsell.removeBtn": "Remove", "embedModal.upsell.quantityTimeUsedNotice": "Quantity will be determined by time used", "embedModal.upsell.inventoryReachedNotice": "Inventory reached", "embedModal.dateAndTime.header": "Date & Time", "embedModal.dateAndTime.description": "Pick a date and time for your move, and we'll be there", "embedModal.dateAndTime.availableMessage": "If your date is not avialable, please contact us at ", "embedModal.dateAndTime.or": "or", "embedModal.dateAndTime.fullDateTooltip": "We might be fully booke on this day, if so we will contact you after you have compleated the booking", "embedModal.specifications.requiredDescription": "This question is required in order to continue", "embedModal.instructions.header": "Special Notes or Instructions", "embedModal.instructions.description": "Enter your notes here", "embedModal.customerInformation.header": "Customer information", "embedModal.customerInformation.firstName": "First name", "embedModal.customerInformation.firstNamePlaceholder": "Enter your first name", "embedModal.customerInformation.lastName": "Last name", "embedModal.customerInformation.lastNamePlaceholder": "Enter your last name", "embedModal.customerInformation.email": "Email", "embedModal.customerInformation.emailPlaceholder": "Enter your email", "embedModal.customerInformation.phone": "Phone", "embedModal.customerInformation.phonePlaceholder": "Enter your phone", "embedModal.confirmation.header": "Confirm & review", "embedModal.confirmation.thankYou": "Thank you for your booking", "embedModal.confirmation.buttonFinish": "Go to order in customer portal", "embedModal.confirmation.buttonConfirm": "Confirm booking", "embedModal.confirmation.getOtp": "Confirm PIN code", "embedModal.confirmation.otpHeader": "Enter the PIN code from the SMS.", "embedModal.confirmation.invalidOtp": "Invalid PIN code", "embedModal.confirmation.confirmWithoutOTP": "Confirm", "embedModal.confirmation.didntReceiveSMS": "Didn’t receive a sms?", "embedModal.confirmation.sendSMSAgain": "Send sms again", "embedModal.confirmation.otpSentPleaseTryAgain": "OTP sent, please try again in", "embedModal.confirmation.seconds": "seconds", "embedModal.confirmation.termsOfSale": "Terms of sale", "embedModal.confirmation.confirmTerms": "By confirming you accept the ", "embedModal.confirmation.finished.headerOtp": "Thank you for your order!", "embedModal.confirmation.finished.header": "Thank you for your request!", "embedModal.confirmation.finished.bodyOtp": "We have also sent you a confirmation on email. You can always see your order in the customer portal.", "embedModal.confirmation.finished.body": "We will contact you as soon as possible.", "embedModal.confirmation.finished.contactOtp": "Any other questions, contact us on", "embedModal.confirmation.finished.close": "Close", "embedModal.summary.header": "Summary", "embedModal.summary.price": "Price", "embedModal.summary.openToSeePrice": "Open to see price", "embedModal.summary.name": "Name", "embedModal.summary.phone": "Phone", "embedModal.summary.email": "Email", "embedModal.summary.address": "Address", "embedModal.summary.date": "Date", "embedModal.summary.hourLabel": "pr hour", "embedModal.summary.hourBased": "Based on hours", "embedModal.summary.totalPrice": "Total price", "embedModal.summary.calculatingPrice": "Calculating price...", "embedModal.summary.noTotalDescription": "One or more products is based on hourly rate. Total amount is calculated after the job is done.", "embedModal.footer.header": "Product Information", "embedModal.continue": "Continue", "embedModal.days.Sunday": "Sunday", "embedModal.days.Monday": "Monday", "embedModal.days.Tuesday": "Tuesday", "embedModal.days.Wednesday": "Wednesday", "embedModal.days.Thursday": "Thursday", "embedModal.days.Friday": "Friday", "embedModal.days.Saturday": "Saturday", "embedModal.months.January": "January", "embedModal.months.February": "February", "embedModal.months.March": "March", "embedModal.months.April": "April", "embedModal.months.May": "May", "embedModal.months.June": "June", "embedModal.months.July": "July", "embedModal.months.August": "August", "embedModal.months.September": "September", "embedModal.months.October": "October", "embedModal.months.November": "November", "embedModal.months.December": "December", "templates.favourite": "Favourite", "templates.tasks.overview.title": "Task templates", "templates.tasks.createNew": "Create template", "templates.cq.overview.title": "Customer questions templates", "template.cq.back": "Back", "template.cq.createNew": "New Question", "template.cq.templateName": "Template name", "template.newTemplate": "New template", "template.cq.noCustomerQuestions": "No questions. Create a new question ", "templates.workOrder.overview.title": "Job templates", "templates.workOrder.createNew": "Create job template", "templates.workOrder.edit": "Edit job template", "templates.workOrder.modal.name": "Template name", "templates.workOrder.modal.cardTitle": "Title and description", "templates.workOrder.modal.title": "Job title", "newVersionAvailable": "A new version is available", "templates.workOrder.modal.description": "Job description", "templates.workOrder.modal.addresses": "Customize address setup", "templates.workOrder.modal.taskTemplates": "Task templates", "templates.workOrder.modal.taskTemplates.description": "Select checklist-templates you want to include in the job template", "templates.workOrder.modal.cqTemplates": "Customer question templates", "templates.workOrder.modal.cqTemplates.description": "Select questionnaire-templates you want to include in the job template", "calendar.loadingEvents": "Loading events...", "templates.importantInformation.title": "Important information templates", "templates.importantInformation.createNew": "Create template", "tablerino.dateRange.next7Days": "Next 7 days", "tablerino.dateRange.next30Days": "Next 30 days", "tablerino.dateRange.today": "Today", "tablerino.dateRange.last7Days": "Last 7 days", "tablerino.dateRange.last30Days": "Last 30 days", "tablerino.dateRange.last90Days": "Last 90 days", "tablerino.dateRange.last120Days": "Last 120 days", "tablerino.dateRange.custom": "Custom", "tablerino.noSavedViews": "No saved views", "tablerino.savedViews": "Saved views", "tablerino.myViews": "My views", "tablerino.addView": "+ Add view", "tablerino.saveCurrentView": "Save view", "tablerino.updateView": "Update view", "tablerino.saveView": "Save view", "tablerino.viewName": "View name", "tablerino.enterViewName": "Enter view name", "tablerino.confirmDeleteView": "Are you sure you want to delete this view?", "notifications.header": "Notifications", "notifications.empty": "No new notifications.", "notifications.show_more": "Show More", "notifications.customize": "Customize feed", "notifications.internalNote": "Receive notification on internal notes", "Notification.settings": "Notification settings", "notifications.back": "Back", "notifications.finishedNotification": "Receive notification when a job is finished", "notifications.acceptedNotification": "Receive notification when a quote is accepted", "notifications.upcomingOrderNotAcceptedNotification": "Receive notification when an upcoming job is not accepted", "notifications.rating": "Receive notification when a job is rated", "notifications.embed": "Receive notification when a booking is made through the booking form", "notifications.emailFail": "Receive notification when an email errors", "notifications.customerMessage": "Receive notification when a customer sends a message", "notifications.subcontractorNotification": "Receive notification when a subcontractor is assigned to a job", "notifications.assignEmployee": "Receive notification when an employee is assigned to a job", "timetracking.registrationtypes.supplement": "Salary supplement", "timetracking.registrationtypes.activity": "Activity", "notifications.enableNotifications": "To enable notifications: {{instructions}}", "notifications.notificationsBlockedTitle": "Notifications Blocked", "notifications.notificationSetupFailedTitle": "Notification Setup Failed", "notifications.notificationSetupFailedBody": "Unable to set up notifications. Please try again later.", "notifications.notificationErrorTitle": "Notification Error", "notifications.notificationErrorBody": "There was an error setting up notifications. Please check your browser settings.", "notifications.chromeInstruction": "Click the icon next to the address bar → Site Settings → Notifications → Allow", "notifications.firefoxInstruction": "Click the icon next to the address bar → Clear Permission → Reload the page", "notifications.safariInstruction": "Safari → Preferences → Websites → Notifications → Find our site and allow notifications", "notifications.defaultInstruction": "Please check your browser settings to enable notifications and reload the page", "notifications.crewPush": "Push in app", "notifications.corePush": "Push in Core", "notifications.whereToReceive": "Where to receive notifications", "notifications.orderScope": "Order scope", "notifications.ordersAsProjectLeader": "Orders where I'm a project leader", "notifications.myOrders": "Only my orders", "notifications.onlyCreatedOrders": "Only orders the user has created or is assigned", "notifications.onlyAssignedOrders": "Only orders assigned to user", "notifications.allOrders": "All orders", "notifications.customer_cancel_work_order": "Customer cancels job", "settings.navigation.company": "Company", "settings.navigation.payment": "Payment", "settings.navigation.billing": "Billing", "settings.navigation.calendar": "Calendar", "settings.navigation.notifications": "Notifications", "settings.navigation.embed": "Booking form", "settings.navigation.salary": "Salary", "settings.navigation.calculations": "Calculations", "settings.navigation.resources": "Resources", "settings.navigation.integrations": "Integrations", "settings.navigation.apps": "Apps & Integrations", "settings.navigation.employees": "Employees", "settings.navigation.templates": "Templates", "settings.title": "Settings", "settings.billing.dueDate": "Forfallsdato", "settings.billing.paymentHistory": " Payment History"}