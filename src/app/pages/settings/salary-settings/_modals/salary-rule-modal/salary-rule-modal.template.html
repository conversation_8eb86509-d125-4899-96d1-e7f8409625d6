<div class="modal-header d-flex justify-content-between align-items-center">
  <h4 class="text-center" style="flex-grow: 1;">{{ (salaryRule ? 'settings.salary.salaryRules.modal.title.edit' : 'settings.salary.salaryRules.modal.title.create') | translate }}</h4>
</div>
<div class="modal-body row p-3">
  <div class="">
    <label>{{'settings.salary.salaryRules.modal.ruleType' | translate}}</label>
    <app-selectorini
      [disableFocusOnLoad]="true"
      [predefinedSearchResults]="ruleTypes"
      [selectedItem]="selectedRuleType"
      [searchMainDisplayKeys]="['salary_rule_type_name']"
      [searchSubDisplayKeys]="['description']"
      [directSelection]="true"
      (itemSelectedEmitter)="onSelectedRuleType($event)"
    ></app-selectorini>
  </div>

  <div *ngIf="selectedRuleType?.salary_rule_type_id === 0">
      <div class="row mt-2">
        <!-- Weekdays -->
        <div class="form-group col" id="timeTriggerWeekdays">
          <label class="mb-0 form-label">{{ "productDetails.priceAdjustmentRules.timeBasedModifier.modal.weekdays" | translate }}</label>
          <div class="weekday-row d-flex flex-wrap">
            <div *ngFor="let day of daysMapping" class="checkbox-container">
              <div class="checkbox-wrapper-8">
                <input class="tgl tgl-skewed" id="day-{{day.value}}" type="checkbox" [checked]="day.active" (change)="day.active = !day.active" />
                <label class="tgl-btn" title="{{day.name}}" [attr.data-tg-off]="day.abbreviation" [attr.data-tg-on]="day.abbreviation" for="day-{{day.value}}"></label>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="d-flex align-items-center flex-wrap col-9 mt-2">

        <div class="col-lg d-flex flex-wrap pe-2">
          <!-- Time trigger - Start time -->
          <div class="form-group col-4 col-sm-4" style="min-width:100px">
            <label class="mb-0 form-label" for="start_time">{{ "productDetails.priceAdjustmentRules.between" | translate }}</label>
            <div class="input-group input-group-sm">
              <input name="start_time" required type="time" id="start_time" class="form-control form-control-sm" [(ngModel)]="start_time" />
            </div>
          </div>

          <p class="mt-3 mb-0 mx-1">{{"common.and" | translate }}</p>

          <!-- Time trigger - End time -->
          <div class="form-group col-4 col-sm-4"  style="min-width:100px">
            <label class="mb-0 form-label" for="end_time"></label>
            <div class="input-group input-group-sm">
              <input name="end_time" required type="time" id="end_time" class="form-control form-control-sm" [(ngModel)]="end_time" />
            </div>
          </div>
        </div>
      </div>
  </div>

  <div *ngIf="selectedRuleType && [1, 2].includes(selectedRuleType!.salary_rule_type_id)" class="mt-2">
    <label>{{'settings.salary.salaryRules.modal.numberOfHoursForActivation' | translate}}</label>
    <app-input
      [editMode]="true"
      [type]="'number'"
      [control]="numHoursControl"
    ></app-input>
  </div>

  <div class="mt-2">
    <label>{{'settings.salary.salaryRules.modal.activatedSalaryType' | translate}}</label>
    <app-selectorini
      [disableFocusOnLoad]="true"
      [predefinedSearchResults]="salaryTypes"
      [selectedItem]="selectedSalaryType"
      [searchMainDisplayKeys]="['salary_type_name']"
      [directSelection]="true"
      (itemSelectedEmitter)="onSelectedSalaryType($event)"
    ></app-selectorini>
  </div>

  <div class="d-flex gap-4">
    <div class="mt-2 position-relative">
      <label>{{'settings.salary.salaryRules.modal.activeFrom' | translate}}</label>
      <div *ngIf="!activeFrom" class="clickable-text cursor-pointer" (click)="pickerinoFrom.toggle($event)">{{'salary.selectDate' | translate}}</div>
      <div *ngIf="activeFrom" class="clickable-text cursor-pointer" (click)="pickerinoFrom.toggle($event)" (mouseenter)="activeFromHovered = true;" (mouseleave)="activeFromHovered = false;">
        {{displayDate(activeFrom, false, false)}}
        <i *ngIf="activeFrom !== null && this.activeFromHovered" class="fa-regular fa-xmark cursor-pointer px-1" (click)="activeFromChanged(null)"></i>
      </div>
       <app-datepickerino
        #pickerinoFrom
        [popup]="true"
        [selectedDates]="[activeFrom!]"
        [referenceDate]="salaryRule?.active_from"
        (datesSelectedEmitter)="activeFromChanged($event[0])"
      ></app-datepickerino>
    </div>

    <div class="mt-2 position-relative">
      <label>{{'settings.salary.salaryRules.modal.activeTo' | translate}}</label>
      <div *ngIf="!activeTo" class="clickable-text cursor-pointer" (click)="pickerinoTo.toggle($event)">{{'salary.selectDate' | translate}}</div>
      <div *ngIf="activeTo" class="clickable-text cursor-pointer" (click)="pickerinoTo.toggle($event)" (mouseenter)="activeToHovered = true;" (mouseleave)="activeToHovered = false;">
        {{displayDate(activeTo, false, false)}}
        <i *ngIf="activeTo !== null && this.activeToHovered" class="fa-regular fa-xmark cursor-pointer px-1" (click)="activeToChanged(null)"></i>
      </div>
       <app-datepickerino
        #pickerinoTo
        [popup]="true"
        [selectedDates]="[activeTo!]"
        [referenceDate]="salaryRule?.active_to"
        (datesSelectedEmitter)="activeToChanged($event[0])"
      ></app-datepickerino>
    </div>
  </div>

</div>

<div class="modal-footer justify-content-end pe-2 gap-2">
  <app-button
    [translationKey]="'common.save'"
    [ngbTooltip]="periodCrash ? ('settings.salary.salaryRules.modal.periodCrash' | translate) : null"
    [loading]="loading"
    [disabled]="!this.selectedRuleType || !this.selectedSalaryType || (this.selectedRuleType.salary_rule_type_id === 0 && (!this.start_time || !this.end_time)) || periodCrash"
    (buttonClick)="save()"
  ></app-button>
  <app-button
    [themeStyle]="'secondary'"
    [translationKey]="'common.cancel'"
    (buttonClick)="cancel()"
  ></app-button>
</div>
