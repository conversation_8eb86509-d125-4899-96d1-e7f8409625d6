import {Component, Input, OnInit} from '@angular/core';
import {NgbActiveModal, NgbTooltip} from '@ng-bootstrap/ng-bootstrap';
import {CompanySalaryRuleResponse, CompanySalaryTypeResponse, SalaryRuleTypeResponse} from "../../../../../@shared/models/timetracking.interfaces";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {<PERSON><PERSON><PERSON>O<PERSON>, NgIf} from "@angular/common";
import {TrackingService} from "../../../../../@shared/services/tracking.service";
import {SelectoriniComponent} from "../../../../../@shared/components/selectorini/selectorini.component";
import {InputComponent} from "../../../../../@shared/components/input/input.component";
import {FormControl, FormsModule, Validators} from "@angular/forms";
import {_CRM_TTR_13, _CRM_TTR_14} from "../../../../../@shared/models/input.interfaces";
import {ButtonComponent} from "../../../../../@shared/components/button/button.component";
import {DatepickerinoComponent} from "../../../../../@shared/components/datepickerino/datepickerino.component";
import {displayDate, UtilsService} from "../../../../../@core/utils/utils.service";
import {StandardImports} from "../../../../../@shared/global_import";

@Component({
    selector: 'app-salary-rule-modal',
    templateUrl: './salary-rule-modal.template.html',
    styleUrls: ['./salary-rule-modal.component.css'],
    standalone: true,
  imports: [StandardImports, SelectoriniComponent, DatepickerinoComponent]
})

export class SalaryRuleModalComponent implements OnInit {

  @Input() salaryRule: CompanySalaryRuleResponse | null = null;
  ruleTypes: SalaryRuleTypeResponse[] = [];
  existingSalaryRules: CompanySalaryRuleResponse[] = [];
  selectedRuleType: SalaryRuleTypeResponse | null = null;
  salaryTypes: CompanySalaryTypeResponse[] = [];
  selectedSalaryType: CompanySalaryTypeResponse | null = null;
  loading: boolean = false;
  daysMapping: { name: string, value: number, abbreviation: string, active: boolean }[] = [];
  start_time: string | null = "16:00";
  end_time: string | null = "23:59";
  activeFrom: Date | null = null;
  activeTo: Date | null = null;
  activeFromHovered: boolean = false;
  activeToHovered: boolean = false;
  numHoursControl = new FormControl(0, [Validators.required, Validators.min(0)]);
  periodCrash: boolean = false;

  constructor(public activeModal: NgbActiveModal, private trackingService: TrackingService, private translateService: TranslateService, public utilsService: UtilsService) {
    for (let i = 1; i <= 7; i++) {
      this.daysMapping.push({
        name: this.translateService.instant("WEEKDAYS-METRIC." + (i - 1).toString()),
        value: i - 1,
        abbreviation: this.translateService.instant("WEEKDAYS-SHORT-METRIC." + (i - 1).toString())[0],
        active: false
      });
    }
  }

  ngOnInit(): void {
    if (this.salaryRule) {
      this.activeFrom = this.salaryRule.active_from;
      this.activeTo = this.salaryRule.active_to;
      this.start_time = this.salaryRule.start_time;
      this.end_time = this.salaryRule.end_time;
      this.numHoursControl.setValue(this.salaryRule.num_hours);
      this.daysMapping.forEach(day => {day.active = (this.salaryRule!.weekdays || []).includes(day.value)});
    }

    this.trackingService.getSalaryRuleTypes().subscribe((types) => {
      this.ruleTypes = types.sort((a, b) => a.salary_rule_type_id - b.salary_rule_type_id).filter((type) => type.salary_rule_type_id !== 3);
      if (this.salaryRule) {
        this.selectedRuleType = this.ruleTypes.find((ruleType) => ruleType.salary_rule_type_id === this.salaryRule!.salary_rule_type_id)!;
      }
    });

    this.trackingService.getSalaryRules().subscribe((existingSalaryRules) => {
      this.existingSalaryRules = existingSalaryRules.filter((rule) => {
        return rule.salary_rule_id !== this.salaryRule?.salary_rule_id;
      });
    });

    this.trackingService.getSalaryTypes().subscribe((salaryTypes) => {
      this.salaryTypes = salaryTypes;
      if (this.salaryRule) {
        this.selectedSalaryType = this.salaryTypes.find((salaryType) => salaryType.salary_type_id === this.salaryRule!.salary_type_id)!;
      }
    });
  }

  onSelectedRuleType(ruleType: SalaryRuleTypeResponse | any) {
    this.periodCrash = false;
    this.selectedRuleType = ruleType;
    this.periodCrash = this.crashesWithExistingSalaries();
  }

  onSelectedSalaryType(salaryType: CompanySalaryTypeResponse | any) {
    this.selectedSalaryType = salaryType;
  }

  activeFromChanged(date: Date | null) {
    this.periodCrash = false;
    this.activeFrom = date;
    this.periodCrash = this.crashesWithExistingSalaries();
  }

  activeToChanged(date: Date | null) {
    this.periodCrash = false;
    this.activeTo = date;
    this.periodCrash = this.crashesWithExistingSalaries();
  }

  crashesWithExistingSalaries() {
    if (this.selectedRuleType?.salary_rule_type_id === 0) {
      return false;
    }

    let existingRules = this.existingSalaryRules.filter((rule) => {
      return rule.salary_rule_type_id === this.selectedRuleType!.salary_rule_type_id;
    });

    if (!this.activeFrom && !this.activeTo && existingRules.length > 0) {
      return true;
    }

    if (existingRules.some((rule) => { return !rule.active_from && !rule.active_to })) {
      return true;
    }

    if (this.activeFrom && !this.activeTo) {
      for (let rule of existingRules) {
        if (!rule.active_to || rule.active_to > this.activeFrom) {
          return true;
        }
      }
    }

    if (!this.activeFrom && this.activeTo) {
      for (let rule of existingRules) {
        if (!rule.active_from || rule.active_from < this.activeTo) {
          return true;
        }
      }
    }

    if (this.activeFrom && this.activeTo) {
      for (let rule of existingRules) {
        if (rule.active_from && !rule.active_to) {
          if (rule.active_from < this.activeTo) {
            return true;
          }
        }
        if (!rule.active_from && rule.active_to) {
          if (rule.active_to > this.activeFrom) {
            return true;
          }
        }
        if (rule.active_from && rule.active_to) {
          if (rule.active_from > this.activeFrom && rule.active_to < this.activeTo) {
            return true;
          }
          if (rule.active_to > this.activeFrom && rule.active_to < this.activeTo) {
            return true;
          }
          if (rule.active_from > this.activeFrom && rule.active_from < this.activeTo) {
            return true;
          }
          if (rule.active_from < this.activeFrom && rule.active_to > this.activeTo) {
            return true;
          }
        }
      }
    }
    return false;
  }

  save() {
    if (!this.selectedRuleType) {
      return;
    }
    if (this.selectedRuleType.salary_rule_type_id === 0 && (!this.start_time || !this.end_time)) {
      return;
    }

    this.loading = true;
    if (this.salaryRule) {
      let payload: _CRM_TTR_14 = {
        salary_rule_id: this.salaryRule.salary_rule_id,
        salary_rule_type_id: this.selectedRuleType.salary_rule_type_id,
        salary_type_id: this.selectedSalaryType!.salary_type_id,
        active_from: this.activeFrom,
        active_to: this.activeTo,
      }

      if (this.selectedRuleType.salary_rule_type_id === 0) {
        payload.weekdays = this.daysMapping.filter(day => day.active).map(day => day.value);
        payload.start_time = this.start_time!;
        payload.end_time = this.end_time!;
        payload.num_hours = 0;
      }

      if ([1, 2].includes(this.selectedRuleType.salary_rule_type_id)) {
        payload.num_hours = this.numHoursControl.value!;
        payload.weekdays = [];
        payload.start_time = null;
        payload.end_time = null;
      }

      this.trackingService.updateSalaryRule(payload).subscribe((rule) => {
        this.activeModal.close(rule);
        this.loading = false;
      }, error => {
        this.loading = false;
      });
    } else {
      let payload: _CRM_TTR_13 = {
        salary_rule_type_id: this.selectedRuleType.salary_rule_type_id,
        salary_type_id: this.selectedSalaryType!.salary_type_id,
        active_from: this.activeFrom,
        active_to: this.activeTo,
      }

      if (this.selectedRuleType.salary_rule_type_id === 0) {
        payload.weekdays = this.daysMapping.filter(day => day.active).map(day => day.value);
        payload.start_time = this.start_time!;
        payload.end_time = this.end_time!;
        payload.num_hours = 0;
      }

      if ([1, 2].includes(this.selectedRuleType.salary_rule_type_id)) {
        payload.num_hours = this.numHoursControl.value!;
        payload.weekdays = [];
        payload.start_time = null;
        payload.end_time = null;
      }

      this.trackingService.createSalaryRule(payload).subscribe((activity) => {
        this.activeModal.close(activity);
        this.loading = false;
      }, error => {
        this.loading = false;
      });
    }
  }

  cancel() {
    this.activeModal.close(false)
  }

  protected readonly displayDate = displayDate;
}
