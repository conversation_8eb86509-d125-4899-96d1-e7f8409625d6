import {Component, Input, OnInit} from '@angular/core';
import { NgbActiveModal} from '@ng-bootstrap/ng-bootstrap';
import {CompanySalaryTypeResponse, SalaryCalculationTypeResponse} from "../../../../../@shared/models/timetracking.interfaces";
import {TrackingService} from "../../../../../@shared/services/tracking.service";
import {SelectoriniComponent} from "../../../../../@shared/components/selectorini/selectorini.component";
import {FormControl, Validators} from "@angular/forms";
import {_CRM_TTR_17, _CRM_TTR_18} from "../../../../../@shared/models/input.interfaces";
import {StorageService} from "../../../../../@core/services/storage.service";
import {IntegrationsService} from "../../../../../@shared/services/integrations.service";
import {ExternalSalaryTypeResponse} from "../../../../../@shared/models/integrations.interfaces";
import {StandardImports} from "../../../../../@shared/global_import";

@Component({
    selector: 'app-salary-type-modal',
    templateUrl: './salary-type-modal.template.html',
    standalone: true,
  imports: [StandardImports, SelectoriniComponent]
})

export class SalaryTypeModalComponent implements OnInit {

  @Input() salaryType: CompanySalaryTypeResponse | null = null;
  nameControl: FormControl = new FormControl('', [Validators.required]);
  valueControl: FormControl = new FormControl(0, [Validators.required, Validators.min(0)]);
  salaryAccountingEnabled: boolean = false;
  loading: boolean = false;
  selectedAccountingSalaryType: ExternalSalaryTypeResponse | null = null;
  accountingSalaryUpdated: boolean = false;
  calculationTypes: SalaryCalculationTypeResponse[] = [];
  selectedCalculationType: SalaryCalculationTypeResponse | null = null;
  accountingSalaries: ExternalSalaryTypeResponse[] = [];

  constructor(public activeModal: NgbActiveModal, private trackingService: TrackingService, private storageService: StorageService, public integrationService: IntegrationsService) { }

  ngOnInit(): void {
    this.storageService.salaryAccountingEnabled$.subscribe((enabled) => {
      this.salaryAccountingEnabled = enabled;
      this.integrationService.getSalaryTypesFromAccounting('').subscribe((salaries) => {
        this.accountingSalaries = salaries;
      });
    });

    this.trackingService.getSalaryCalculationTypes().subscribe((calculationTypes) => {
      this.calculationTypes = calculationTypes;
      if (this.salaryType) {
        this.selectedCalculationType = this.calculationTypes.find((ct) => ct.calculation_type_id == this.salaryType?.calculation_type_id)!;
      }
    });

    if (this.salaryType) {
      this.nameControl.setValue(this.salaryType.salary_type_name);
      this.valueControl.setValue(this.salaryType.value);

      if (this.salaryType.accounting_id) {
        this.selectedAccountingSalaryType = {
          external_salary_type_id: this.salaryType.accounting_id,
          external_salary_type_name: this.salaryType.accounting_name!,
          external_salary_type_code: '',
          display: this.salaryType.accounting_name!
        }
      }
    }
  }

  onSelectedAccountingSalaryTypeChange(salaryType: ExternalSalaryTypeResponse | any) {
    this.selectedAccountingSalaryType = salaryType;
    this.accountingSalaryUpdated = true;
  }

  save() {
    if (this.nameControl.invalid || ([1, 2, 3].includes(this.selectedCalculationType?.calculation_type_id!) && this.valueControl.invalid)) {
      return;
    }
    this.loading = true;
    if (this.salaryType) {
      let payload: _CRM_TTR_18 = {
        salary_type_id: this.salaryType.salary_type_id,
        salary_type_name: this.nameControl.value,
        calculation_type_id: this.selectedCalculationType?.calculation_type_id,
        value: this.selectedCalculationType?.calculation_type_id !== 0 ? this.valueControl.value : null,
      }

      if (this.accountingSalaryUpdated) {
        payload.accounting_id = this.selectedAccountingSalaryType?.external_salary_type_id;
        payload.accounting_name = this.selectedAccountingSalaryType?.external_salary_type_name;
      }

      this.trackingService.updateSalaryType(payload).subscribe((st) => {
        this.activeModal.close(st);
        this.loading = false;
      }, error => {
        this.loading = false;
      });
    } else {
      let payload: _CRM_TTR_17 = {
        salary_type_name: this.nameControl.value,
        accounting_id: null,
        calculation_type_id: this.selectedCalculationType?.calculation_type_id!,
        value: this.selectedCalculationType?.calculation_type_id !== 0 ? this.valueControl.value : null,
      }

      if (this.accountingSalaryUpdated) {
        payload.accounting_id = this.selectedAccountingSalaryType?.external_salary_type_id;
        payload.accounting_name = this.selectedAccountingSalaryType?.external_salary_type_name;
      }

      this.trackingService.createSalaryType(payload).subscribe((st) => {
        this.activeModal.close(st);
        this.loading = false;
      }, error => {
        this.loading = false;
      });
    }
  }

  onSelectedSalaryTypeChange(salaryType: SalaryCalculationTypeResponse | any) {
    this.selectedCalculationType = salaryType;
  }

  cancel() {
    this.activeModal.close(false)
  }
}
