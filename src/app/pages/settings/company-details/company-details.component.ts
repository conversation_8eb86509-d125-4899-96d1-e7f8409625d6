import {Component, OnInit} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import {CompanyGeneralSettingsResponse, CompanyResponse} from "../../../@shared/models/company.interfaces";
import {SettingsService} from "../../../@shared/services/settings.service";
import {ToastService} from "../../../@core/services/toast.service";
import {_CRM_AFF_11, _CRM_COY_22, _CRM_COY_4} from "../../../@shared/models/input.interfaces";
import {CompanyService} from "../../../@shared/services/company.service";
import {formatTimeHM, getFormControl} from "../../../@core/utils/utils.service";
import {TranslateService} from "@ngx-translate/core";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {
  EditCompanyDetailsModalComponent
} from "./_modals/edit-company-details-modal/edit-company-details-modal.component";
import {StorageService} from "../../../@core/services/storage.service";
import {ReversedAffiliateResponse} from "../../../@shared/models/affiliate.interfaces";
import {AffiliateService} from "../../../@shared/services/affiliate.service";
import {TablerinoColumn, TablerinoComponent} from "../../../@shared/components/tablerino/tablerino.component";
import {BehaviorSubject} from "rxjs";
import { VerifyPopupModal } from 'src/app/@shared/components/verify-popup-modal/verify-popup-modal';
import {StandardImports} from "../../../@shared/global_import";
import {CardComponent} from "../../../@shared/components/layout/card/card.component";
import {SelectoriniComponent} from "../../../@shared/components/selectorini/selectorini.component";

@Component({
    selector: 'app-company-details',
    templateUrl: './company-details.component.html',
    styleUrls: ['./company-details.component.css'],
    standalone: true,
  imports: [StandardImports, CardComponent, SelectoriniComponent, TablerinoComponent]
})
export class CompanyDetailsComponent implements OnInit {
  companyForm: FormGroup;
  loading = false;
  success = false;
  selectedLogo: File;
  imagePreview: string;
  companyDetails: CompanyResponse;
  originalFormValues: any;
  operateExVat: boolean;
  pricePresentationPrivate: boolean;
  pricePresentationBusiness: boolean;
  archiveOrderWhenFinished: boolean;
  autoFinishWorkOrders: boolean;
  autoFinishAllWorkOrders: boolean;
  manualOrderConfirmationEmail: boolean;
  autoConfirmCustomerAcceptance: boolean = true;
  companyContractorRelations: ReversedAffiliateResponse[] = [];
  quote_expiry_days: number;
  openingHoursForm: FormGroup = new FormGroup({
    start_time: new FormControl(),
    end_time: new FormControl(),
  });
  showEmployeeInitialsOnly: boolean = false;
  editMode: boolean = false;
  enableGeoLock: boolean = false;
  geoLockRangeControl: FormControl = new FormControl(null, [Validators.required, Validators.min(1), Validators.max(2000), Validators.maxLength(4)]);
  quoteValidityDaysControl: FormControl = new FormControl(null, [Validators.min(1)]);
  defaultRepeatingOrders: boolean = false;

  quoteValidityDaysEnabled: boolean = true;
  isReadonly: boolean = true;
  companySettings: CompanyGeneralSettingsResponse;
  companyAddressValid = false;
  smsNameControl: FormControl;
  smsNamePattern: RegExp = /^[A-Za-z0-9\s\-æøå]+$/;
  quantityCalculationResolutions = [
    {display: '', key: 'settings.company.quantityCalculationResolutions.none', value: 0.01},
    {display: '', key: 'settings.company.quantityCalculationResolutions.15min', value: 0.25},
    {display: '', key: 'settings.company.quantityCalculationResolutions.30min', value: 0.5},
    {display: '', key: 'settings.company.quantityCalculationResolutions.60min', value: 1},
  ];
  selectedQuantityCalculationResolution = this.quantityCalculationResolutions[0];

  calendarViewPortFromControl: FormControl = new FormControl(0, [Validators.required, Validators.min(0), Validators.max(23), Validators.maxLength(2)]);
  calendarViewPortToControl: FormControl = new FormControl(24, [Validators.required, Validators.min(1), Validators.max(24), Validators.maxLength(2)]);

  customerCancelWorkOrder: boolean = false;
  requireCrewWorkOrderConfirmation: boolean = false;

  contractorListColumnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([
    {
      name: 'id',
      labelKey: 'common.name',
      formatter: (rel: ReversedAffiliateResponse) => rel.owning_company_name,
      sort: true,
      visible: true,
    },
    {
      name: 'id',
      labelKey: 'settings.company.contractor.showCalendar',
      formatter: (rel: ReversedAffiliateResponse) => rel.owning_company_name,
      sort: true,
      visible: true,
      cellTemplateName: 'toggleSwitch',
      cellTemplateSourceKey: 'show_contractor_calendar',
    },
  ])

  constructor(
    private fb: FormBuilder,
    private settingsService: SettingsService,
    private toastService: ToastService,
    public companyService: CompanyService,
    private translateService: TranslateService,
    private modalService: NgbModal,
    private storageService: StorageService,
    private affiliateService: AffiliateService
  ) {
  }

  ngOnInit(): void {
    this.smsNameControl = new FormControl('', [Validators.minLength(4), Validators.maxLength(11), Validators.pattern(this.smsNamePattern)]);

    for (let resolution of this.quantityCalculationResolutions) {
      resolution.display = this.translateService.instant(resolution.key);
    }

    this.companyForm = new FormGroup({
      logo_url: new FormControl(),
      logo_url_new: new FormControl(),
    });

    this.affiliateService.getContractorRelationsAsContractor().subscribe(res => {
      this.companyContractorRelations = res.sort((a, b) => a.owning_company_name.localeCompare(b.owning_company_name));
    });

    this.settingsService.getCompanyDetails().subscribe(res => {
      this.companyDetails = res;
      // this.originalAddress = res.address;
      this.initForm();
      this.originalFormValues = this.companyForm.getRawValue();  // store the original form values
    });

    this.settingsService.getCompanyGeneralSettings().subscribe(res => {
      this.operateExVat = res.operate_ex_vat;
      this.pricePresentationPrivate = !!res.include_vat_in_price_private;
      this.pricePresentationBusiness = !!res.include_vat_in_price_business;
      this.archiveOrderWhenFinished = !!res.archive_order_when_finished;
      this.autoFinishWorkOrders = res.auto_finish_work_orders;
      this.autoFinishAllWorkOrders = res.auto_finish_all_work_orders;
      this.smsNameControl.setValue(res.sms_name);
      this.companySettings = res;
      this.calendarViewPortFromControl.setValue(res.calendar_start_hour);
      this.calendarViewPortToControl.setValue(res.calendar_end_hour);
      this.selectedQuantityCalculationResolution = this.getCalculationResolutionFromValue(res.order_line_quantity_calculation_resolution);
      this.geoLockRangeControl.setValue(res.geo_lock_range);
      this.enableGeoLock = res.geo_lock_range != null;
      this.manualOrderConfirmationEmail = !!res.manual_order_confirmation_email
      this.showEmployeeInitialsOnly = !!res.show_employee_initials_only;
      this.autoConfirmCustomerAcceptance = !res.customer_can_only_accept;
      this.quoteValidityDaysControl.setValue(res.quote_expiry_days);
      this.customerCancelWorkOrder = !!res.customer_cancel_work_order;
      this.defaultRepeatingOrders = res.default_repeating_orders;
      this.requireCrewWorkOrderConfirmation = res.require_crew_work_order_confirmation;
    });
    this.settingsService.getOpeningHours().subscribe(res => {
      this.openingHoursForm.setValue({
        start_time: formatTimeHM(res.start_time),
        end_time: formatTimeHM(res.end_time),
      });
    });
  }

  updateCompanyOpeningHours() {
    console.log(this.openingHoursForm.value.start_time, this.openingHoursForm.value.end_time)
    let startDate: Date = new Date();
    let startTime: string = this.openingHoursForm.value.start_time;
    startDate.setHours(parseInt(startTime.split(':')[0]), parseInt(startTime.split(':')[1]));
    let endDate: Date = new Date();
    let endTime: string = this.openingHoursForm.value.end_time;
    endDate.setHours(parseInt(endTime.split(':')[0]), parseInt(endTime.split(':')[1]));

    let payload: _CRM_COY_4 = {
      start_time: startDate,
      end_time: endDate,
    }

    this.settingsService.updateOpeningHours(payload).subscribe(res => {
      this.toastService.successToast('updated');
    });

  }

  changes: { [key: string]: boolean } = {

    company_name: false,
    organisation_number: false,
    phone: false,
    email: false,
    address: false,
    postal_code: false,
    city: false,
    country: false,
  };

  initForm(): void {
    this.companyForm = this.fb.group({
      logo_url: [this.companyDetails.logo_url],
      logo_url_new: [],
    });
    this.companyForm.valueChanges.subscribe();
  }

  onFileSelected(event: any) {
    const file = event.target.files[0];
    if (file) {
      this.selectedLogo = file;
      this.imagePreview = URL.createObjectURL(file);

      this.settingsService.uploadCompanyLogo(file).subscribe({
        next: (response) => {
          this.companyDetails.logo_url = response.logo_url;
          this.storageService.registerApplicationsAndGeneralSettings();
        }
      });
    }
  }



  // onFileSelected(event: Event): void {
  //   const input = event?.target as HTMLInputElement;
  //   if (input && input.files && input.files.length > 0) {
  //     const file = input.files[0];
  //     const MAX_SIZE = 10485760;
  //     if (file) {
  //       resizeAndConvertToJpeg(file, 0.8).then((compressedFile: Blob) => {
  //         const compressedImageFile = new File([compressedFile], file.name, {
  //           type: 'image/jpeg',
  //         });
  //         this.openImageProcessorModal(compressedImageFile);
  //         input.value = '';
  //
  //       }).catch(error => {
  //         this.toastService.errorToast('error_compressing_logo');
  //       });
  //     }
  //   }
  // }


  // openImageProcessorModal(file: File) {
  //   const modalRef = this.modalService.open(ImageProcessorComponent, {size: 'md' });
  //   modalRef.componentInstance.imageFile = file;
  //   modalRef.componentInstance.ratio = 8 / 2;
  //   modalRef.result.then((croppedResult) => {
  //     const blob = croppedResult.blob;
  //     const croppedFile = new File([blob], file.name, {type: blob.type});
  //     this.selectedLogo = croppedFile;
  //     this.setImagePreviewAndUpload(croppedFile);
  //   }, (reason) => {
  //     console.log(reason);
  //   });
  // }


  setImagePreviewAndUpload(file: File) {
    const reader = new FileReader();
    reader.onload = (e: any) => {this.imagePreview = e.target.result;};
    reader.readAsDataURL(file);
    this.uploadPhoto();
  }

  uploadPhoto() {
    if (this.companyForm.value.logo_url_new && this.selectedLogo) {
      this.loading = true;
      this.settingsService.uploadCompanyLogo(this.selectedLogo).subscribe(res => {
        this.loading = false;
      });
    }
  }

  autoFinishWorkOrdersChange(value: any) {
    const payload: _CRM_COY_22 = {
      auto_finish_work_orders: this.autoFinishWorkOrders,
    }
    this.settingsService.updateCompanyGeneralSettings(payload).subscribe(res => {
      this.autoFinishWorkOrders = res.auto_finish_work_orders;
      this.toastService.successToast('updated');
    });
  }

  autoFinishAllWorkOrdersChange(value: any) {
    const payload: _CRM_COY_22 = {
      auto_finish_all_work_orders: this.autoFinishAllWorkOrders,
    }
    if (this.autoFinishAllWorkOrders) {
      this.autoFinishWorkOrders = true;
      payload.auto_finish_work_orders = true;
    }

    this.settingsService.updateCompanyGeneralSettings(payload).subscribe(res => {
      this.autoFinishAllWorkOrders = res.auto_finish_all_work_orders;
      this.autoFinishWorkOrders = res.auto_finish_work_orders;
      this.toastService.successToast('updated');
    });
  }

  defaultRepeatingOrdersChange(value: any) {
    const payload: _CRM_COY_22 = {
      default_repeating_orders: this.defaultRepeatingOrders,
    }

    this.settingsService.updateCompanyGeneralSettings(payload).subscribe(res => {
      this.storageService.registerApplicationsAndGeneralSettings();
      this.defaultRepeatingOrders = res.default_repeating_orders;
      console.log('defaultRepeatingOrders', this.defaultRepeatingOrders);
      this.toastService.successToast('updated');
    });
  }


  archiveOrderWhenFinishedChange(value: any) {
    const payload: _CRM_COY_22 = {
      archive_order_when_finished: this.archiveOrderWhenFinished ? 1 : 0,
    }
    this.settingsService.updateCompanyGeneralSettings(payload).subscribe(res => {
      this.archiveOrderWhenFinished = !!res.archive_order_when_finished;
      this.toastService.successToast('updated');
    });
  }

  setSmsName() {
    if (!this.smsNameControl.valid) {
      return;
    }
    let name = this.smsNameControl.value;
    const payload: _CRM_COY_22 = {
      sms_name: name,
    }
    this.settingsService.updateCompanyGeneralSettings(payload).subscribe(res => {
      this.toastService.successToast('sms_name_updated');
      this.smsNameControl.setValue(res.sms_name)
    });
  }

  onPricePresentationPrivateChange(value: any) {
    // this.pricePresentationPrivate = value;

    const payload: _CRM_COY_22 = {
      include_vat_in_price_private: this.pricePresentationPrivate ? 1 : 0,
    }

    this.settingsService.updateCompanyGeneralSettings(payload).subscribe(res => {
      this.pricePresentationPrivate = !!res.include_vat_in_price_private;
      this.toastService.successToast('updated');
    });
  }

  onOperateExVatChange(value: any) {
    const previousValue = !this.operateExVat;

    const modalRef = this.modalService.open(VerifyPopupModal, {
      size: 'md',
      backdrop: 'static'
    });

    modalRef.componentInstance.titleTranslationKey = 'settings.company.operateExVat.change.title';
    modalRef.componentInstance.showBody = true;
    modalRef.componentInstance.bodyBoldTranslationKey = 'settings.company.operateExVat.change.warning';
    modalRef.componentInstance.bodyRegularTranslationKey = 'settings.company.operateExVat.change.description';
    modalRef.componentInstance.yesButtonTranslationKey = 'common.continue';
    modalRef.componentInstance.noButtonTranslationKey = 'common.cancel';

    modalRef.result.then(
      (confirmed) => {
        if (confirmed) {
          const payload: _CRM_COY_22 = {
            operate_ex_vat: this.operateExVat,
          }

          this.settingsService.updateCompanyGeneralSettings(payload).subscribe(res => {
            this.operateExVat = res.operate_ex_vat;
            this.storageService.registerApplicationsAndGeneralSettings();
            this.toastService.successToast('settings.company.operateExVat.change.success');
          });
        } else {
          this.operateExVat = previousValue;
        }
      },
      () => {
        this.operateExVat = previousValue;
      }
    );
  }


  onPricePresentationBusinessChange(value: any) {
    // this.pricePresentationBusiness = value;

    const payload: _CRM_COY_22 = {
      include_vat_in_price_business: this.pricePresentationBusiness ? 1 : 0,
    }

    this.settingsService.updateCompanyGeneralSettings(payload).subscribe(res => {
      this.pricePresentationBusiness = !!res.include_vat_in_price_business;
      this.toastService.successToast('updated');
    });
  }

  enableGeoLockChange() {
    if (!this.enableGeoLock) {
      this.geoLockRangeControl.setValue(null);
      this.settingsService.updateCompanyGeneralSettings({geo_lock_range: null}).subscribe(res => {
        this.toastService.successToast('updated');
      });
    } else {
      this.geoLockRangeControl.setValue(500);
      this.settingsService.updateCompanyGeneralSettings({geo_lock_range: 500}).subscribe(res => {
        this.toastService.successToast('updated');
      });
    }
  }

  // updateManualOrderConfirmationEmail() {
  //   const payload: _CRM_COY_22 = {
  //     manual_order_confirmation_email: this.manualOrderConfirmationEmail ? 1 : 0,
  //   }
  //   this.settingsService.updateCompanyGeneralSettings(payload).subscribe(res => {
  //     this.manualOrderConfirmationEmail = !!res.manual_order_confirmation_email;
  //   });
  // }

  updateCustomerCanOnlyAccept() {
    const payload: _CRM_COY_22 = {
      customer_can_only_accept: this.autoConfirmCustomerAcceptance ? 0 : 1,
    }
    this.settingsService.updateCompanyGeneralSettings(payload).subscribe(res => {
      this.autoConfirmCustomerAcceptance = !res.customer_can_only_accept;
      this.toastService.successToast('updated');
    });
  }

  // updateQuoteValidityDays(): void {
  //   if (!this.quoteValidityDaysControl.valid) {
  //     return;
  //   }
  //
  //   this.settingsService.updateCompanyGeneralSettings({quote_expiry_days: parseInt(this.quoteValidityDaysControl.value)}).subscribe(res => {
  //     this.toastService.successToast('updated');
  //   });
  //



    // const newValue = parseInt(this.quoteValidityDaysControl.value, 10);
    // const payload = {
    //   quote_validity_days: newValue
    // };
    // this.settingsService.updateCompanyGeneralSettings(payload).subscribe(res => {
    //   // Oppdater companySettings med ny verdi
    //   this.companySettings.quote_validity_days = res.quote_validity_days;
    //   this.toastService.successToast('updated');
    // });
  // }

  updateGeoLockRange() {
    if (!this.geoLockRangeControl.valid) {
      return;
    }
    this.settingsService.updateCompanyGeneralSettings({geo_lock_range: parseInt(this.geoLockRangeControl.value)}).subscribe(res => {
      this.toastService.successToast('updated');
    });
  }

  updateshowEmployeeInitialsOnly() {
    const payload: _CRM_COY_22 = {
      show_employee_initials_only: this.showEmployeeInitialsOnly ? 1 : 0,
    }
    this.settingsService.updateCompanyGeneralSettings(payload).subscribe(res => {
      this.showEmployeeInitialsOnly = !!res.show_employee_initials_only;
      this.storageService.registerApplicationsAndGeneralSettings();
      this.toastService.successToast('updated');
    });
  }


  adjustTime() {
    if (!this.calendarViewPortFromControl.valid) {
      this.calendarViewPortFromControl.setValue(this.companySettings.calendar_start_hour);
      return;
    }

    if (!this.calendarViewPortToControl.valid) {
      this.calendarViewPortToControl.setValue(this.companySettings.calendar_end_hour);
      return;
    }

    this.companyService.setCompanyCalendarHours(parseInt(this.calendarViewPortFromControl.value), parseInt(this.calendarViewPortToControl.value)).subscribe(
      res => {
        this.companySettings = res;
        this.calendarViewPortFromControl.setValue(res.calendar_start_hour);
        this.calendarViewPortToControl.setValue(res.calendar_end_hour);
        this.toastService.successToast('updated');
      }
    );
  }

  onQuantityCalculationResolutionChanged(event: any) {
    this.selectedQuantityCalculationResolution = event;
    const payload: _CRM_COY_22 = {
      order_line_quantity_calculation_resolution: event.value,
    }
    this.settingsService.updateCompanyGeneralSettings(payload).subscribe(res => {
      this.selectedQuantityCalculationResolution = this.getCalculationResolutionFromValue(res.order_line_quantity_calculation_resolution);
      this.toastService.successToast('updated');
    });
  }

  getCalculationResolutionFromValue(value: number): { display: string, key: string, value: number } {
    return this.quantityCalculationResolutions.find(resolution => resolution.value === value)!;
  }

  protected readonly getFormControl = getFormControl;

  editModal() {
    const modalRef = this.modalService.open(EditCompanyDetailsModalComponent, {size: 'md'});
    modalRef.componentInstance.companyId = this.companyDetails.company_id
    modalRef.componentInstance.result.subscribe((companyDetails: CompanyResponse) => {
      if (companyDetails) {
        this.companyDetails = companyDetails;
      }
    })
  }
  triggerFileInput() {
    const fileInput = document.getElementById('logo_url_new') as HTMLInputElement;
    fileInput.click();
  }

  updateContractorRelation(rel: ReversedAffiliateResponse | any) {
    let payload: _CRM_AFF_11 = {
      affiliate_id: rel.affiliate_id,
      show_contractor_calendar: rel.show_contractor_calendar,
    }
    this.affiliateService.updateContractorSettingsAsContractor(payload).subscribe(res => {
      this.toastService.successToast('updated');
    });
  }

  toggleRequireCrewWorkOrderConfirmation(event: any): void {
    const newState = this.requireCrewWorkOrderConfirmation;

    const payload: _CRM_COY_22 = {
      require_crew_work_order_confirmation: newState
    };

    this.settingsService.updateCompanyGeneralSettings(payload).subscribe({
      next: (res) => {
        this.storageService.registerApplicationsAndGeneralSettings();
        this.toastService.successToast('updated');
      },
      error: (error) => {
        console.error('Error updating customer cancel work order setting', error);
        this.toastService.errorToast('update_failed');
        this.customerCancelWorkOrder = !newState;
      }
    });
  }

  onToggleQuoteValidityDays(state: boolean): void {
    this.quoteValidityDaysEnabled = state;
    if (!state) {
      this.quoteValidityDaysControl.setValue(null);
      this.updateQuoteValidityDays();
    }
  }

  updateQuoteValidityDays(): void {
    if (!this.quoteValidityDaysControl.valid) {
      return;
    }
    this.settingsService.updateCompanyGeneralSettings({quote_expiry_days: parseInt(this.quoteValidityDaysControl.value)}).subscribe(res => {
      this.toastService.successToast('updated');
    });
  }

  customerCancelWorkOrderChange(event: any): void {
    const newState = this.customerCancelWorkOrder;

    const payload: _CRM_COY_22 = {
      customer_cancel_work_order: newState ? 1 : 0
    };

    this.settingsService.updateCompanyGeneralSettings(payload).subscribe({
      next: (res) => {
        this.toastService.successToast('updated');
      },
      error: (error) => {
        console.error('Error updating customer cancel work order setting', error);
        this.toastService.errorToast('update_failed');
        this.customerCancelWorkOrder = !newState;
      }
    });
  }

}
