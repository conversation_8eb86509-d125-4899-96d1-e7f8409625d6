<!--<app-page-header [pageName]="'settings.company.companyInformation' | translate" [breadcrumb]='[{"name": "Company Information", "routerPath": "company-settings"}]'></app-page-header>-->


<div class="profile-container">
  <!-- Removed page title and more actions dropdown -->
  <div class="row d-flex justify-content-center mt-2 mb-2">
    <div class="col-lg-6">
      <div class="profile-image-card h-100 position-relative">
        <form enctype="multipart/form-data"  [formGroup]="companyForm" *ngIf="companyDetails">
          <div class="wrapper" [style.background-color]="companyDetails.company_color || null"></div>
          <div class="profile-box d-flex justify-content-center align-items-center">
            <div style="cursor: pointer;" (click)="triggerFileInput()">
              <div class="form-group profile-image-container d-flex align-items-center justify-content-center" style="background-color: #f0f0f0;">
                <input
                  type="file"
                  class="form-control d-none"
                  formControlName="logo_url_new"
                  id="logo_url_new"
                  name="logo_url_new"
                  accept="image/*"
                  (change)="onFileSelected($event)">
                <input type="hidden" formControlName="logo_url"/>

                <div class="d-flex align-items-center justify-content-center" *ngIf="companyDetails && companyDetails.logo_url; else dummyImage"
                     [ngStyle]="{'background-color': (selectedLogo || companyDetails.logo_url) ? '#f0f0f0' : 'none','border-radius': '10px'}">
                  <img [src]="selectedLogo ? imagePreview : companyDetails.logo_url" class="img-fluid profile-image"/>
                  <div class="profile-image-overlay">
                    <i class="fa fa-image icon-style fa-2xl"></i>
                  </div>
                </div>

                <!-- Dummy Image Template -->
                <ng-template #dummyImage>
                  <div class="d-flex align-items-center justify-content-center" style="background-color: #f0f0f0; border-radius: 10px;">
                    <img style="border-radius: 10px;" src="../../../../assets/images/yourlogohere.jpg" class="img-fluid"/>
                  </div>
                </ng-template>
              </div>
            </div>
          </div>
          <div class="profile-body-image">
            <h4 class="card-title d-flex justify-content-center mb-2 fs-3"> {{ companyDetails.company_name }}</h4>
            <div class="form-group mb-2">
              <label>{{ "settings.company.registrationNumber" | translate }}</label>
              <p>{{companyDetails.organisation_number || 'N/A'}}</p>
            </div>
            <div class="form-group mb-2">
              <label>{{ "settings.company.address" | translate }}</label>
              <p>{{companyDetails.address.display || 'N/A'}}</p>
            </div>
            <div class="form-group mb-2">
              <label>{{ "settings.company.phoneNumber" | translate }}</label>
              <p>{{companyDetails.phone || 'N/A'}}</p>
            </div>
            <div class="form-group mb-2">
              <label>{{ "settings.company.email" | translate }}</label>
              <p>{{companyDetails.email || 'N/A'}}</p>
            </div>

            <!-- Edit Button -->
            <div class="mt-4 mb-3">
              <button type="button" class="btn btn-primary w-100" (click)="editModal()">
                <i class="fa-regular fa-edit me-2"></i>{{"settings.company.editCompanyDetails" | translate}}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div class="col-lg-6 mt-2 mt-lg-0">
      <app-card
        [labelKey]="'settings.company.pricePresentation.title'"
        [margin]="'mb-2'"
        [backgroundColor]="'rgb(252, 252, 252)'">
        <ng-container cardcontent>
          <div class="row">
          <div class="col">

            <div class="profile-content mb-2">
              <div class="d-flex justify-content-between align-items-center">
                <label class="ms-1 fw-normal" for="operate_ex_vat">{{ "settings.company.pricePresentation.operateExVat" | translate }}</label>
                <div class="form-check form-switch ms-auto custom-switch">
                  <input
                    type="checkbox"
                    [(ngModel)]="operateExVat"
                    (change)="onOperateExVatChange($event)"
                    class="form-check-input"
                    id="operate_ex_vat" />
                </div>
              </div>
            </div>

            <!-- <div class="profile-content mb-2">
              <div class="d-flex justify-content-between align-items-center">
                <label class="ms-1 fw-normal" for="price_presentation_private">{{ "settings.company.pricePresentation.pricePresentationForPrivate" | translate }}</label>
                <div class="form-check form-switch ms-auto custom-switch">
                  <input
                    type="checkbox"
                    [(ngModel)]="pricePresentationPrivate"
                    (change)="onPricePresentationPrivateChange($event)"
                    class="form-check-input"
                    id="price_presentation_private" />
                </div>
              </div>
            </div>

            <div class="profile-content mb-2">
              <div class="d-flex justify-content-between align-items-center">
                <label class="ms-1 fw-normal" for="price_presentation_business">{{ "settings.company.pricePresentation.pricePresentationForBusiness" | translate }}</label>
                <div class="form-check form-switch ms-auto custom-switch">
                  <input
                    type="checkbox"
                    [(ngModel)]="pricePresentationBusiness"
                    (change)="onPricePresentationBusinessChange($event)"
                    class="form-check-input"
                    id="price_presentation_business" />
                </div>
              </div>
            </div> -->

          </div>
        </div>
        </ng-container>
      </app-card>

      <app-card
        [labelKey]="'settings.company.openingHours'"
        [backgroundColor]="'rgb(252, 252, 252)'">
        <ng-container cardcontent>
          <div class="d-flex">
          <div [formGroup]="openingHoursForm" class="me-3">
            <div class="d-flex">
              <!-- Time picker input -->

              <div id="timePickerDivFrom" class="col-7 mb-2 me-2">
                <label class="mb-0 form-label" for="timePickerInputFrom">{{ "settings.company.openingHours.from" | translate }}</label>
                <input id="timePickerInputFrom" type="time" class="form-control form-control-sm" formControlName="start_time" style="height: 36px;"
                (focusout)="updateCompanyOpeningHours()">
              </div>

              <div id="timePickerDivTo" class="col-7 mb-2">
                <label class="mb-0 form-label" for="timePickerInputTo">{{ "settings.company.openingHours.to" | translate }}</label>
                <input id="timePickerInputTo" type="time" class="form-control form-control-sm" formControlName="end_time" style="height: 36px;"
                (focusout)="updateCompanyOpeningHours()">
              </div>

            </div>
          </div>
        </div>
        </ng-container>
      </app-card>

    </div>
  </div>
  <div class="row mb-2 mb-lg-0">
    <div class="col">
      <app-card
        [labelKey]="'settings.company.orders'"
        [margin]="'mb-2'"
        [backgroundColor]="'rgb(252, 252, 252)'">
        <ng-container cardcontent>
          <div class="row">
            <div class="col-12">

              <div class="profile-content mb-2">
                <div class="d-flex justify-content-between align-items-center">
                  <label class="ms-1 fw-normal" for="customer_can_only_accept">{{ "settings.company.customerCanOnlyAccept" | translate }}<i class="fa-regular fa-circle-info ms-1" [ngbTooltip]="'settings.company.customerCanOnlyAccept.tooltip' | translate"></i></label>
                  <div class="form-check form-switch ms-auto custom-switch">
                    <input type="checkbox" [(ngModel)]="autoConfirmCustomerAcceptance" (change)="updateCustomerCanOnlyAccept()" class="form-check-input" id="customer_can_only_accept" />
                  </div>
                </div>
              </div>

              <div class="profile-content mb-2">
                <div class="d-flex justify-content-between align-items-center">
                  <label class="ms-1 fw-normal" for="archive_orders">{{ "settings.company.archiveOnFinished" | translate }}</label>
                  <div class="form-check form-switch ms-auto custom-switch">
                    <input type="checkbox"  [(ngModel)]="archiveOrderWhenFinished" (change)="archiveOrderWhenFinishedChange($event)" class="form-check-input" id="archive_orders" />
                  </div>
                </div>
              </div>

              <div class="profile-content mb-2">
                <div class="d-flex justify-content-between align-items-center">
                  <label class="ms-1 fw-normal" for="auto_finish_work_orders">{{ "settings.company.autoFinishWorkOrders" | translate }}</label>
                  <div class="form-check form-switch ms-auto custom-switch">
                    <input type="checkbox" [disabled]="autoFinishAllWorkOrders" [(ngModel)]="autoFinishWorkOrders" (change)="autoFinishWorkOrdersChange($event)" class="form-check-input" id="auto_finish_work_orders" />
                  </div>
                </div>
              </div>

              <div class="profile-content mb-2">
                <div class="d-flex justify-content-between align-items-center">
                  <label class="ms-1 fw-normal" for="auto_finish_all_work_orders">{{ "settings.company.autoFinishAllWorkOrders" | translate }}</label>
                  <div class="form-check form-switch ms-auto custom-switch">
                    <input type="checkbox" [(ngModel)]="autoFinishAllWorkOrders" (change)="autoFinishAllWorkOrdersChange($event)" class="form-check-input" id="auto_finish_all_work_orders" />
                  </div>
                </div>
              </div>

              <!-- Customer cancel work order -->
              <div class="profile-content mb-2">
                <div class="d-flex justify-content-between align-items-center">
                  <label class="ms-1 fw-normal" for="customer_cancel_work_order">{{ "settings.company.customerCancelWorkOrder" | translate }}</label>
                  <div class="form-check form-switch ms-auto custom-switch">
                    <input type="checkbox" [(ngModel)]="customerCancelWorkOrder" (change)="customerCancelWorkOrderChange($event)" class="form-check-input" id="customer_cancel_work_order" />
                  </div>
                </div>
              </div>

              <!-- Require crew work order confirmation -->
              <div class="profile-content mb-2">
                <div class="d-flex justify-content-between align-items-center">
                  <label class="ms-1 fw-normal" for="require_crew_work_order_confirmation">{{ "settings.company.requireCrewWorkOrderConfirmation" | translate }}</label>
                  <div class="form-check form-switch ms-auto custom-switch">
                    <input type="checkbox" [(ngModel)]="requireCrewWorkOrderConfirmation" (change)="toggleRequireCrewWorkOrderConfirmation($event)" class="form-check-input" id="require_crew_work_order_confirmation" />
                  </div>
                </div>
              </div>

              <div class="profile-content mb-2">
                <div class="d-flex justify-content-between align-items-center">
                  <label class="ms-1 fw-normal" for="default_repeating_order">{{ "settings.company.defaultRepeatingOrders" | translate }}</label>
                  <div class="form-check form-switch ms-auto custom-switch">
                    <input type="checkbox" [(ngModel)]="defaultRepeatingOrders" (change)="defaultRepeatingOrdersChange($event)" class="form-check-input" id="default_repeating_order" />
                  </div>
                </div>
              </div>

<!--              <div class="profile-content mb-2">-->
<!--                <div class="d-flex justify-content-between align-items-center">-->
<!--                  <label class="ms-1 fw-normal" for="quote_validity_days">-->
<!--                    {{ "settings.company.quoteValidityDays" | translate }}:-->
<!--                    <i class="fa-regular fa-circle-info ms-1" [ngbTooltip]="'settings.company.quoteValidityDays.tooltip' | translate"></i>-->
<!--                  </label>-->
<!--                  &lt;!&ndash; Toggle Switch &ndash;&gt;-->
<!--                  <div class="d-flex align-items-center">-->
<!--                    <app-toggle-switch-->
<!--                      [state]="quoteValidityDaysEnabled"-->
<!--                      [bigSwitch]="true"-->
<!--                      (stateChange)="onToggleQuoteValidityDays($event)">-->
<!--                    </app-toggle-switch>-->
<!--                  </div>-->
<!--                </div>-->
<!--                &lt;!&ndash; Input Field only visible when enabled &ndash;&gt;-->
<!--                <div class="d-flex align-items-center col-3 mt-2" *ngIf="quoteValidityDaysEnabled">-->
<!--                  <app-input-->
<!--                    id="quote_validity_days"-->
<!--                    [type]="'number'"-->
<!--                    [control]="quoteValidityDaysControl"-->
<!--                    [editMode]="true"-->
<!--                    [inputSuffix]="'dager'"-->
<!--                    (focusout)="updateQuoteValidityDays()"-->
<!--                    (onEnterPressed)="updateQuoteValidityDays()"-->
<!--                  ></app-input>-->
<!--                </div>-->
<!--              </div>-->

              <div class="mt-3">
                <app-selectorini
                  [labelTranslationKey]="'settings.company.quantityCalculationResolutions.label'"
                  id="quantityCalculationResolutionSelectorini"
                  [predefinedSearchResults]="quantityCalculationResolutions"
                  [searchMainDisplayKeys]="['display']"
                  [selectedItem]="selectedQuantityCalculationResolution"
                  (itemSelectedEmitter)="onQuantityCalculationResolutionChanged($event)"
                  [searchable]="false"
                  [showCrossButton]="false"
                  [enableScrolling]="false"
                ></app-selectorini>
              </div>
            </div>
          </div>
        </ng-container>
      </app-card>

      <app-card
        [labelKey]="'settings.company.employees'"
        [margin]="'mb-2'"
        [backgroundColor]="'rgb(252, 252, 252)'">
        <ng-container cardcontent>
          <div class="row">
            <div class="col-12">
              <div class="profile-content mb-2">
                <div class="d-flex justify-content-between align-items-center">
                  <label class="ms-1 fw-normal" for="geo_lock_range">{{ "settings.company.employees.geo_lock_range_label" | translate }}:</label>
                  <div class="d-flex align-items-center col-3">
                    <app-input
                      id="geo_lock_range"
                      [type]="'number'"
                      [control]="geoLockRangeControl"
                      [editMode]="true"
                      [inputSuffix]="'m'"

                      (focusout)="updateGeoLockRange()"
                      (onEnterPressed)="updateGeoLockRange()"
                    ></app-input>
                  </div>
                </div>
              </div>
              <div class="profile-content">
                <div class="d-flex justify-content-between align-items-center">
                  <label class="ms-1 fw-normal" for="show_employee_initials_only">{{ "settings.company.showEmployeeInitialsOnly" | translate }}<i class="fa-regular fa-circle-info ms-1" [ngbTooltip]="'settings.company.showEmployeeInitialsOnly.tooltip' | translate"></i></label>
                  <div class="form-check form-switch ms-auto custom-switch">
                    <input type="checkbox" [(ngModel)]="showEmployeeInitialsOnly" (change)="updateshowEmployeeInitialsOnly()" class="form-check-input" id="show_employee_initials_only" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-container>
      </app-card>

      <app-card
        [labelKey]="'settings.company.communication'"
        [margin]="'mb-2'"
        [backgroundColor]="'rgb(252, 252, 252)'">
        <ng-container cardcontent>
          <div class="row">
            <div class="col-12 mb-1">
              <app-input
                [type]="'text'"
                [labelKey]="'settings.company.smsName'"
                [control]="smsNameControl"
                [editMode]="true"
                (change)="setSmsName()"
              />
            </div>
            <span class="font-12">{{ "settings.company.smsNameDescription" | translate }}</span>
          </div>
        </ng-container>
      </app-card>

      <app-card
        [labelKey]="'settings.company.contractor.title'"
        [margin]="'mb-2'"
        [backgroundColor]="'rgb(252, 252, 252)'"
        [additionalTitleElement]="true">
        <ng-container additionalTitleElement>
          <i class="fa-regular fa-info-circle ms-1 cursor-pointer" [ngbTooltip]="'settings.company.contractor.tooltip' | translate"></i>
        </ng-container>
        <ng-container cardcontent>
          <app-tablerino
            [disableDrag]="true"
            [disableSort]="true"
            [tableName]="'company-details-contractors'"
            [tableData]="companyContractorRelations"
            [settings]="{}"
            [columnsSubject]="contractorListColumnsSubject"
            [loading]="loading"
            (toggleEmitter)="updateContractorRelation($event)"
          ></app-tablerino>
        </ng-container>
      </app-card>
    </div>
  </div>
</div>
