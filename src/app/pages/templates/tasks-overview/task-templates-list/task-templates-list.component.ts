import {Component, EventEmitter, Input, OnInit, Output, ViewEncapsulation} from "@angular/core";
import {ProductBaseResponse, ProductResponse} from "../../../../@shared/models/product.interfaces";
import {Column} from "../../../../@shared/components/advanced-table/advanced-table.component";
import {SortEvent} from "../../../../@shared/components/advanced-table/sortable.directive";
import {ProductService} from "../../../../@shared/services/product.service";
import {escapeHtml, formatDateDMY, UtilsService} from "../../../../@core/utils/utils.service";
import { TranslateService } from "@ngx-translate/core";
import { Router } from "@angular/router";
import {CustomerQuestionTemplateResponse, TaskTemplateResponse} from "../../../../@shared/models/templates.interfaces";
import {TemplateService} from "../../../../@shared/services/templates.service";
import {TaskTemplateModalComponent} from "../_modals/checklist-modal/task-template-modal.component";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {BehaviorSubject} from "rxjs";
import {TablerinoColumn, TablerinoComponent, TablerinoSettings} from "../../../../@shared/components/tablerino/tablerino.component";
import {StandardImports} from "../../../../@shared/global_import";


@Component({
    selector: 'app-task-templates-list',
    templateUrl: './task-templates-list.template.html',
    encapsulation: ViewEncapsulation.None,
    standalone: true,
  imports: [StandardImports, TablerinoComponent]
})
export class TaskTemplatesListComponent implements OnInit {
  @Input() taskTemplates: Array<TaskTemplateResponse> = [];
  @Output() onSort: EventEmitter<SortEvent> = new EventEmitter<SortEvent>();
  @Input() isLoading: boolean = false;
  @Output() templateUpdated: EventEmitter<TaskTemplateResponse> = new EventEmitter<TaskTemplateResponse>();
  columnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);
  settings: TablerinoSettings = {
    checkboxes: true,
    clickableRows: true
  };

  constructor(
    private templateService: TemplateService,
    public utilsService: UtilsService,
    private translate: TranslateService,
    private router: Router,
    private modalService: NgbModal) {
  }

  deleteTaskTemplate(template: TaskTemplateResponse) {
    this.templateService.deleteTaskTemplate(template.task_template_id).subscribe(res => {
      this.taskTemplates = this.taskTemplates.filter(t => t.task_template_id != template.task_template_id);
    })
  }

  columns: Array<Column> = [];

  ngOnInit() {
    this.columnsSubject.next([
      {
        name: 'template_name',
        labelKey: 'product-list.name',
        formatter: (t: TaskTemplateResponse) => escapeHtml(t.task_template_name),
        sort: true,
        visible: true,
      }]);
  }

  sort(event: SortEvent) {
    this.onSort.emit(event);
  }

  openEditTemplateModal(template: TaskTemplateResponse) {
    let modalRef = this.modalService.open(TaskTemplateModalComponent, {size: 'lg'});
    modalRef.componentInstance.taskTemplate = template;
    modalRef.componentInstance.actionType = 'edit';

    modalRef.result.then((result: TaskTemplateResponse | string) => {
      this.templateUpdated.emit({} as TaskTemplateResponse);
    }).catch((error) => {
    });
  }

}
