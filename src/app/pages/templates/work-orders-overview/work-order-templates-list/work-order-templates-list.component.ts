import {Component, EventEmitter, Input, OnInit, Output, ViewEncapsulation} from "@angular/core";
import {SortEvent} from "../../../../@shared/components/advanced-table/sortable.directive";
import {ProductService} from "../../../../@shared/services/product.service";
import {escapeHtml, formatDateDMY, UtilsService} from "../../../../@core/utils/utils.service";
import { TranslateService } from "@ngx-translate/core";
import { Router } from "@angular/router";
import {CustomerQuestionTemplateResponse, TaskTemplateResponse, WorkOrderTemplateResponse} from "../../../../@shared/models/templates.interfaces";
import {TemplateService} from "../../../../@shared/services/templates.service";
import {WorkOrderTemplateModalComponent} from "../_modals/work-order-modal/work-order-template-modal.component";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {TablerinoColumn, TableRow, TablerinoSettings, TablerinoComponent} from "../../../../@shared/components/tablerino/tablerino.component";
import {_CRM_TMP_25, _CRM_TMP_27, _CRM_TMP_6} from "../../../../@shared/models/input.interfaces";
import {BehaviorSubject, Subject} from "rxjs";
import {StorageService} from "../../../../@core/services/storage.service";
import {StandardImports} from "../../../../@shared/global_import";

export interface WorkOrderTemplateRow extends TableRow, WorkOrderTemplateResponse {
  favourite: boolean;
}

@Component({
    selector: 'app-work-order-templates-list',
    templateUrl: './work-order-templates-list.template.html',
    encapsulation: ViewEncapsulation.None,
    standalone: true,
  imports: [StandardImports, TablerinoComponent]
})
export class WorkOrderTemplatesListComponent implements OnInit {
  workOrderTemplates: WorkOrderTemplateResponse[] = [];
  @Input() loading: boolean = false;
  @Input() updateSubject: BehaviorSubject<void>;

  workOrderRows: WorkOrderTemplateRow[] = [];

  settings: TablerinoSettings = {
    checkboxes: true,
    clickableRows: true
  };

  @Output() onSort: EventEmitter<SortEvent> = new EventEmitter<SortEvent>();
  @Output() templateUpdated: EventEmitter<WorkOrderTemplateResponse> = new EventEmitter<WorkOrderTemplateResponse>();

  constructor(
    private templateService: TemplateService,
    public utilsService: UtilsService,
    private modalService: NgbModal,
    private storageService: StorageService) {
  }

  columnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);

  ngOnInit() {
    this.getTemplates();

    this.updateSubject.subscribe(() => {
      this.getTemplates();
    });

    this.columnsSubject.next([
      {
        name: 'template_name',
        labelKey: 'product-list.name',
        formatter: (w: WorkOrderTemplateResponse) => escapeHtml(w.template_name),
        sort: true,
        visible: true,
      },
      {
        name: 'favourite',
        labelKey: 'templates.favourite',
        formatter: (w: WorkOrderTemplateResponse) => '',
        sort: true,
        visible: true,
        cellTemplateName: 'favourite'
      },
    ]);

  }

  sort(event: SortEvent) {
    this.onSort.emit(event);
  }

  getTemplates() {
    this.templateService.getWorkOrderTemplates().subscribe({
      next: (res) => {
        this.storageService.updateWorkOrderTemplates(res);
        this.workOrderTemplates = res.sort((a, b) => {
          const nameA = a.template_name || '';
          const nameB = b.template_name || '';

          return nameA.localeCompare(nameB)
        });
        this.workOrderRows = this.workOrderTemplates.map((wo: WorkOrderTemplateResponse) => {
          return {
            ...wo,
            favourite: wo.favourite,
            selected: false
          };
        });
      },
      error: (error) => {
        console.error('Error fetching work order templates:', error);
      }
    });
  }

  openEditTemplateModal(template: TaskTemplateResponse) {
    let modalRef = this.modalService.open(WorkOrderTemplateModalComponent, {size: 'lg'});
    modalRef.componentInstance.template = template;
    modalRef.componentInstance.actionType = 'edit';
    modalRef.componentInstance.updateSubject = this.updateSubject;

    modalRef.result.then((result: WorkOrderTemplateResponse) => {
      if (result) {
        this.templateUpdated.emit(result);
      }
    }).catch((error) => {

    });
  }

  favouriteToggled(event: WorkOrderTemplateResponse) {
    let payload: _CRM_TMP_27 = {
      template_id: event.template_id,
      favourite: !event.favourite
    };
    this.templateService.updateWorkOrderTemplate(payload).subscribe({
      next: () => {
        event.favourite = !event.favourite;
      },
      error: (error) => {
        console.error('Error updating favourite:', error);
      },
      complete: () => {
        console.log('Favourite update completed');
      }
    });
  }

}
