import { Component, OnInit } from '@angular/core';
import { FormB<PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastService } from 'src/app/@core/services/toast.service';
import {asyncPhoneNumberValidator, convertCompactAddressToUnitDetails, displayPhone, UtilsService} from 'src/app/@core/utils/utils.service';
import { CompanyResponse } from 'src/app/@shared/models/company.interfaces';
import {CRM_COY_0, CRM_COY_1, CRM_COY_3, CRM_COY_8, UnitDetails} from 'src/app/@shared/models/input.interfaces';
import {UserEntityRelationWithoutUserDataResponse, UserResponse} from 'src/app/@shared/models/user.interfaces';
import { CompanyService } from 'src/app/@shared/services/company.service';
import { BrregCompanyResponse } from 'src/app/@shared/services/companyData.service';
import {StorageService} from "../../../../@core/services/storage.service";
import { PhoneInputComponent } from 'src/app/@shared/components/phone-input/phone-input.component';
import {StandardImports} from "../../../../@shared/global_import";
import {PageHeaderComponent} from "../../../../@shared/components/page-header/page-header.component";
import {CompanySearchComponent} from "../../../../@shared/components/company-search/company-search.component";
import {AddressSearchComponent} from "../../../../@shared/components/address-search/address-search.component";

@Component({
    selector: 'app-add-edit-company',
    templateUrl: './add-edit-company.component.html',
    styleUrls: ['./add-edit-company.component.css'],
    standalone: true,
  imports: [StandardImports, PageHeaderComponent, CompanySearchComponent, PhoneInputComponent, AddressSearchComponent]
})
export class AddEditCompanyComponent implements OnInit {
  companyForm: FormGroup;
  loading = false;
  user?: UserResponse;
  selectedPhoto: File;
  imagePreview: string;
  isEditMode = this.route.snapshot.url[1].path === 'edit';
  companyId = this.isEditMode ? String(this.route.snapshot.paramMap.get('id')) : '';
  companyData : CompanyResponse;
  inviteAdmin = false;
  companyAddress: UnitDetails;
  companyAddressValid = false;
  phoneValid = false;

  constructor(private route: ActivatedRoute,
              private fb: FormBuilder,
              private companyService: CompanyService,
              private router: Router,
              private utilsService: UtilsService,
              private toastService: ToastService,
              private storageService: StorageService
              ) {}

  ngOnInit(): void {
    this.companyForm = new FormGroup({
      company_name: new FormControl(),
      organisation_number: new FormControl(),
      phone: new FormControl(),
      email: new FormControl(),
      logo_url: new FormControl(),
      logo_url_new: new FormControl(),
      invitee_first_name: new FormControl(),
      invitee_last_name: new FormControl(),
      invitee_phone: new FormControl(),
      invitee_email: new FormControl(),
    });
    this.getCompanyData();
  }

  handleCompanySelected(company: BrregCompanyResponse): void {
    // Determine the address to use
    const addressToUse = company.businessAddress.address.length > 0 ? company.businessAddress : company.mailingAddress;

    // Here you map the response to your form structure
    this.companyForm.patchValue({
      company_name: company.name,
      organisation_number: company.organisationNumber,
      phone: '', // Assuming you don't get this from the Brreg response
      email: '', // Assuming you don't get this from the Brreg response
    });

    this.utilsService.getAddressFromBrregResponse(company).subscribe((address) => {
      this.companyAddress = address;
      this.companyAddressValid = true;
    });
  }

  getCompanyData(){
    if(this.isEditMode){
      let params: CRM_COY_1 = {
        company_id: this.companyId,
      };
      this.companyService.getCompanyDataByCompanyId(params).subscribe((res) => {
        this.companyData = res;
        this.companyAddress = convertCompactAddressToUnitDetails(res.address)!;
        this.companyAddressValid = true;
        this.goToCompany()
      });
    }
  }

  goToCompany() {
    let company: UserEntityRelationWithoutUserDataResponse = {
      entity_id: this.companyData.company_id,
      entity_name: this.companyData.company_name,
      role_id: 1,
      role_name: 'Admin',
      identificator: '',
      taskable: 0,
      updated_by: '',
      created_at: new Date(),
      updated_at: null,
      deleted_at: null,
    };
    this.storageService.saveSelectedCompany(company, true).subscribe(() => {
      this.router.navigate(['/dashboard']);
    });
  }

  initCompanyForm(): void {
    this.companyForm = this.fb.group({
      company_name: [this.companyData.company_name, [Validators.required, Validators.maxLength(200)]],
      organisation_number: [this.companyData.organisation_number, [Validators.required, Validators.maxLength(200)]],
      phone: [displayPhone(this.companyData.phone), [Validators.required, asyncPhoneNumberValidator, Validators.maxLength(8), Validators.minLength(8), Validators.min(10000000)]],
      email: [this.companyData.email, [Validators.required, Validators.email, Validators.maxLength(200)]],
      logo_url: [this.companyData.logo_url],
      logo_url_new: [this.companyData.logo_url],
    });
  }

  onFileSelected(event: any) {
    this.selectedPhoto = event.target.files[0];

    const reader = new FileReader();
    reader.onload = (e: any) => {
      this.imagePreview = e.target.result;
    };
    reader.readAsDataURL(this.selectedPhoto);

    if(this.isEditMode){
      this.uploadPhoto(this.companyId)?.subscribe();
    }
    }

    onSubmit() {
    if (this.companyForm.valid) {
        this.loading = true;

        // Update existing company
        if (this.isEditMode) {
          const payload : CRM_COY_3 = {
            company_id : String(this.companyId),
            company_name : this.companyForm.value.company_name,
            address: this.companyAddress,
            phone: this.companyForm.value.phone,
            email: this.companyForm.value.email
          }
          this.companyService.updateCompanyData(payload).subscribe(res => {
            this.loading = false;
            this.uploadPhoto(this.companyData.company_id)?.subscribe();
            this.toastService.successToast("employee_updated")
          });
          }

        // Create new company
        else {
          const payload : CRM_COY_0 = {
            company_name : this.companyForm.value.company_name,
            organisation_number: String(this.companyForm.value.organisation_number),
            address: this.companyAddress,
            phone: String(this.companyForm.value.phone),
            email: this.companyForm.value.email,
            company_type_id: 0,
            invitee: this.inviteAdmin ? {
              email: this.companyForm.value.invitee_email,
              first_name: this.companyForm.value.invitee_first_name,
              last_name: this.companyForm.value.invitee_last_name,
              phone: this.companyForm.value.invitee_phone
            } : null,
          }

          this.companyService.createCompany(payload).subscribe(res => {
            this.loading = false;
            const uploadPhotoObservable = this.uploadPhoto(res.company_id);
            if (uploadPhotoObservable) {
              uploadPhotoObservable.subscribe(() => {
                this.router.navigate(['superadmin/companies/edit/' + res.company_id]);
              });
            } else {
              this.router.navigate(['superadmin/companies/edit/' + res.company_id]);
            }
            this.toastService.successToast("company_created")
          }, (error) => {
            this.toastService.errorToast(error.error.asset_id)
          });
        }
        this.loading = false;
      } else {
        this.companyForm.markAllAsTouched();
        this.loading = false;
      }
    }

    onCompanyDataChange(){
    this.toastService.errorToast("nope")
      // if(this.isEditMode){
      //   const payload : CRM_COY_3 = {
      //     company_id : String(this.companyId),
      //     company_name : this.companyForm.value.company_name,
      //     address: this.companyAddress,
      //     phone: this.companyForm.value.phone,
      //     email: this.companyForm.value.email
      //   }
      //   this.companyService.updateCompanyData(payload).subscribe(res => {
      //     this.uploadPhoto(String(this.companyId))?.subscribe();
      //     this.toastService.successToast("company_updated")
      //   });
      // }
    }

    uploadPhoto(companyId: string = '') {
      if (this.companyForm.value.logo_url_new) {
        const payload: CRM_COY_8 = {
          image: this.selectedPhoto,
          company_id: String(companyId)
        };

        // return the Observable, do not subscribe here
        return this.companyService.uploadCompanyLogo(payload);
      } else {
        return undefined;
      }
    }

  onAddressUpdated(addressWrapper: { id: number, address: UnitDetails }) {
    this.companyAddress = addressWrapper.address;
    this.companyAddressValid = true;
    this.onCompanyDataChange()
  }

  onAddressRemoved() {
    this.toastService.errorToast("nope")
    this.companyAddressValid = false;
  }

  handlePhoneNumberChange(phoneNumber: string | null): void {
    this.companyForm.patchValue({phone: phoneNumber})
  }

  handlePhoneNumberValid(isValid: boolean): void {
    this.phoneValid = isValid;
  }
}
