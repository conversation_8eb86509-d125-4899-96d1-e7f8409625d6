import {Component, OnInit} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Column } from 'src/app/@shared/components/advanced-table/advanced-table.component';
import { SortEvent } from 'src/app/@shared/components/advanced-table/sortable.directive';
import {CompanyResponse, CompanyTypeResponse} from 'src/app/@shared/models/company.interfaces';
import { PaginationResponse } from 'src/app/@shared/models/response.interfaces';
import {CRM_COY_2, _USM_ENT_0, _CRM_ORD_170} from 'src/app/@shared/models/input.interfaces';
import {UserEntityRelationWithoutUserDataResponse, UserEntityRelationWithUserDataResponse} from 'src/app/@shared/models/user.interfaces';
import { CompanyService } from 'src/app/@shared/services/company.service';
import { EmployeeService } from 'src/app/@shared/services/employee.service';
import {environment} from "../../../../../environments/environment";
import {FormControl} from "@angular/forms";
import {BehaviorSubject, forkJoin, pairwise, Subject} from "rxjs";
import {PaginationContainer} from "../../../../@shared/models/global.interfaces";
import {TablerinoColumn, TablerinoSettings} from "../../../../@shared/components/tablerino/tablerino.component";
import {WorkOrderCompactResponse, WorkOrderStatusResponse} from "../../../../@shared/models/order.interfaces";
import {HeaderFilterComponent, HeaderFiltersContainer} from "../../../../@shared/components/tablerino-header/tablerino-header.component";
import {WorkOrderRow} from "../../../work-orders/work-orders-overview/work-orders-overview.component";
import {takeUntil} from "rxjs/operators";
import {formatDateDMY, workOrderBadgeStatus} from "../../../../@core/utils/utils.service";
import {ResourceResponse} from "../../../../@shared/models/resources.interfaces";
import {StorageService} from "../../../../@core/services/storage.service";
import {Router} from "@angular/router";
import {StandardImports} from "../../../../@shared/global_import";
import {PageHeaderComponent} from "../../../../@shared/components/page-header/page-header.component";
import {TablerinoCompleteComponent} from "../../../../@shared/components/tablerino-complete/tablerino-complete.component";

export interface CompanyRow extends CompanyResponse {
  selected: boolean;
}

@Component({
    selector: 'app-companies-overview',
    templateUrl: './companies-overview.component.html',
    styleUrls: ['./companies-overview.component.css'],
    standalone: true,
  imports: [StandardImports, PageHeaderComponent, TablerinoCompleteComponent]
})
export class CompaniesOverviewComponent implements OnInit {
  employees: PaginationResponse<UserEntityRelationWithUserDataResponse[]>;
  companies: PaginationResponse<CompanyResponse[]>
  orderBy: string = '';
  orderByDirection: string = '';
  columns: Array<Column> = [];

  columnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);
  companyRows: CompanyRow[];
  settings: TablerinoSettings = {
    checkboxes: false,
    clickableRows: true,
  }
  loading: boolean = false;
  dataResponse: PaginationResponse<CompanyResponse[]>;
  paginationSubject: BehaviorSubject<PaginationContainer> = new BehaviorSubject<PaginationContainer>({page: 1, limit: 25, paginate: 1, totalPages: 0, totalItems: 0});
  selectedRowsSubject: BehaviorSubject<CompanyResponse[]> = new BehaviorSubject<CompanyResponse[]>([]);
  headerFiltersContainerSubject: BehaviorSubject<HeaderFiltersContainer> = new BehaviorSubject<HeaderFiltersContainer>({filters: [], init: true});
  cancelCurrentRequest$ = new Subject<void>();


  constructor(private companyService: CompanyService, private translate: TranslateService, private storageService: StorageService, protected router: Router) {}


  ngOnInit(): void {
    this.initializeColumns();
    this.initializeHeaderFilters();
    this.getCompanies();
    this.paginationSubject.pipe(pairwise()).subscribe((paginationDetails) => {
      if (paginationDetails[0].page !== paginationDetails[1].page) {
        this.getCompanies();
      }
    });
    this.headerFiltersContainerSubject.subscribe((headerFilters) => {
      if (!headerFilters.init) {
        this.getCompanies();
      }
    });
  }

  getCompanies(searchTerm: string = '') {
    this.cancelCurrentRequest$.next();
    this.loading = true;

    let filters = this.headerFiltersContainerSubject.value.filters;
    let sortColumn = this.columnsSubject.value.find(col => col.sortedAsc || col.sortedDesc) || null;
    let sortKey: string = sortColumn?.name || 'created_at';
    let sortDirection: 'asc' | 'desc' = sortColumn ? (sortColumn.sortedAsc ? 'asc' : 'desc') : 'desc';

    let params: CRM_COY_2 = {
      paginate: 1,
      page: this.paginationSubject.value.page,
      limit: this.paginationSubject.value.limit,
      company_type_id: filters.find(hf => hf.parameterName === 'service_providers_only')!.active ? 0 : null,
      search_string: searchTerm,
      order_by: sortKey,
      order_direction: sortDirection
    }

    this.companyService.getCompanies(params).pipe(takeUntil(this.cancelCurrentRequest$)).subscribe((res) => {
      this.dataResponse = res;
      this.paginationSubject.next({
        ...this.paginationSubject.value,
        totalItems: res.total_items,
        totalPages: res.total_pages
      });
      this.companyRows = res.data.map((wo) => {
        return {
          ...wo,
          selected: false,
        }
      });
      this.loading = false;
    }, error => {
      this.loading = false
    });
  }

    initializeColumns() {
    this.columnsSubject.next([
      {
        name: 'company_id',
        labelKey: this.translate.instant('superadmin.companies.id'),
        formatter: (u: CompanyResponse) => u.company_id,
        sort: true,
        visible: true,
      },
      {
        name: 'company_name',
        labelKey: this.translate.instant('superadmin.companies.companyName'),
        formatter: (u: CompanyResponse) => u.company_name,
        sort: true,
        visible: true,
      },
      {
        name: 'organisation_number',
        labelKey: this.translate.instant('superadmin.companies.orgNumber'),
        formatter: (u: CompanyResponse) => u.organisation_number,
        sort: true,
        visible: true,
      },
      {
        name: 'phone',
        labelKey: this.translate.instant('superadmin.companies.phone'),
        formatter: (u: CompanyResponse) => u.phone,
        sort: false,
        visible: true,
      },
      {
        name: 'email',
        labelKey: this.translate.instant('superadmin.companies.email'),
        formatter: (u: CompanyResponse) => u.email,
        sort: false,
        visible: true,
      },
      {
        name: 'address',
        labelKey: this.translate.instant('superadmin.companies.address'),
        formatter: (u: CompanyResponse) => u.address?.display,
        sort: false,
        visible: true,
      },
      {
        name: 'created_at',
        labelKey: this.translate.instant('superadmin.companies.createdAt'),
        formatter: (u: CompanyResponse) => formatDateDMY(u.created_at).split(' ')[0],
        sort: true,
        visible: true,
      },
    ]);
  }

  initializeHeaderFilters() {
    let headerFilters: HeaderFilterComponent[] = [
      {
        parameterName: 'service_providers_only',
        translationKey: 'Kun tjenesteleverandører',
        active: true,
      },
    ];

    this.headerFiltersContainerSubject.next({filters: headerFilters, init: true});
  }

  goToCompany(company: CompanyResponse) {
    let companyElement: UserEntityRelationWithoutUserDataResponse = {
      entity_id: company.company_id,
      entity_name: company.company_name,
      role_id: 1,
      role_name: 'Admin',
      identificator: '',
      taskable: 0,
      updated_by: '',
      created_at: new Date(),
      updated_at: null,
      deleted_at: null,
    };
    this.storageService.saveSelectedCompany(companyElement, true).subscribe(() => {
      this.router.navigate(['/dashboard']);
    });
  }

  // fetchCompanies(filterOptions?: any) {
  //   if (filterOptions) {
  //     this.searchInput = filterOptions['searchString'];
  //   }
  //   this.loading = true;
  //   const params : CRM_COY_2 = {
  //       paginate: 1,
  //       page: this.paginationSubject.value.page,
  //       limit: this.paginationSubject.value.limit,
  //       order_by: this.orderBy,
  //       order_direction: this.orderByDirection,
  //       search_string: this.searchInput,
  //       company_type_id: this.companyTypeControl.value
  //     };
  //     this.companyService.getCompanies(params).subscribe(res => {
  //       this.paginationSubject.next({
  //         ...this.paginationSubject.value,
  //         totalItems: res.total_items,
  //         totalPages: res.total_pages
  //       });
  //       this.companies = res;
  //       this.loading = false;
  //     })
  // }

  // onSort(event: SortEvent) {
  //   this.orderBy = event.column;
  //   this.orderByDirection = event.direction;
  // }
}
