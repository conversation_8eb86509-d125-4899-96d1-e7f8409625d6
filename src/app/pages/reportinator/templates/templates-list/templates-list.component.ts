import {Component, OnInit} from '@angular/core';
import {displayDate, UtilsService} from 'src/app/@core/utils/utils.service';
import {TranslateService} from "@ngx-translate/core";
import {ReportinatorService} from "../../../../@shared/services/reportinator.service";
import {FormControl, FormGroup} from "@angular/forms";
import {BehaviorSubject, pairwise, Subject} from "rxjs";
import {NgbModal, NgbModalRef} from "@ng-bootstrap/ng-bootstrap";
import {PaginationResponse} from "../../../../@shared/models/response.interfaces";
import {environment} from "../../../../../environments/environment";
import {TemplateResponse} from "../../../../@shared/models/reportinator.interfaces";
import {_REP_TMP_0, _REP_TMP_1} from "../../../../@shared/models/input.interfaces";
import {Column} from "../../../../@shared/components/advanced-table/advanced-table.component";
import {DatePipe} from "@angular/common";
import {SortEvent} from "../../../../@shared/components/advanced-table/sortable.directive";
import {Router} from "@angular/router";
import {NewTemplateModalComponent} from "./_modals/new-template-modal/new-template-modal.component";
import {PaginationContainer} from "../../../../@shared/models/global.interfaces";
import {StandardImports} from "../../../../@shared/global_import";
import {PageHeaderComponent} from "../../../../@shared/components/page-header/page-header.component";
import {AdvanceTableHeaderComponent} from "../../../../@shared/components/advance-table-header/advance-table-header.component";
import {AdvancedTableModule} from "../../../../@shared/components/advanced-table/advanced-table.module";
import {TableFooterComponent} from "../../../../@shared/components/table-footer/table-footer.component";


@Component({
    selector: 'reportinator-templates-list',
    templateUrl: './templates-list.template.html',
    standalone: true,
  imports: [StandardImports, PageHeaderComponent, AdvanceTableHeaderComponent, AdvancedTableModule, TableFooterComponent]
})
export class TemplatesListComponent implements OnInit {

  columns: Array<Column> = [];
  searchForm = new FormGroup({
    searchString: new FormControl(''),
    orderStatus: new FormControl(-1),
    confirmationStatus: new FormControl(-1),
    unpaid: new FormControl(false),
    archived: new FormControl(false),
    accountingUnsynced: new FormControl(false)
  })

  sortFields = [
    {key: 'order_id', text: 'orders.orderNumber'},
    {key: 'execution_at', text: 'orders.orderDate'},
    {key: 'created_at', text: 'orders.createdDate'},
  ]

  private readonly destroy$: Subject<void> = new Subject<void>();
  modalReference: NgbModalRef;
  searchString: string = '';
  paginatedTemplates: PaginationResponse<TemplateResponse[]>;
  isLoading: boolean = false;
  selectedDateFrom: Date | null = null;
  selectedDateTo: Date | null  = null;
  paginationSubject: BehaviorSubject<PaginationContainer> = new BehaviorSubject<PaginationContainer>({page: 1, limit: 25, paginate: 1, totalPages: 0, totalItems: 0});

  orderBy: string = '';
  orderByDirection: string = 'desc';


  constructor(
    private reportinatorService: ReportinatorService,
    public utilsService: UtilsService,
    private translate: TranslateService,
    private router: Router,
    private modalService: NgbModal) {
  }

  ngOnInit() {
    this.reportinatorService.setRevisionElements([]);
    this.fetchTemplates();
    const datePipe = new DatePipe('nb-NO');
    this.columns = [
      {
        name: 'Name',
        label: this.translate.instant('reportinator.common.list.name'),
        formatter: (tmp: TemplateResponse) => `${tmp.template_name}`,
        align: 'left',
        sort: true,
      },
      {
        name: 'Type',
        label: this.translate.instant('reportinator.common.list.type'),
        formatter: (tmp: TemplateResponse) => `${tmp.template_name}`,
        align: 'left',
        sort: true,
      },
      {
        name: 'UpdatedBy',
        label: this.translate.instant('reportinator.common.list.updatedBy'),
        formatter: (tmp: TemplateResponse) => `${tmp.updated_by.full_name}`,
        align: 'left',
        sort: true,
      },
      {
        name: 'CreatedAt',
        label: this.translate.instant('reportinator.common.list.createdAt'),
        formatter: (tmp: TemplateResponse) => `${displayDate(tmp.created_at)}`,
        align: 'left',
        sort: true,
      },
      {
        name: 'CreatedAt',
        label: this.translate.instant('reportinator.common.list.updatedAt'),
        formatter: (tmp: TemplateResponse) => `${displayDate(tmp.updated_at)}`,
        align: 'left',
        sort: true,
      },
    ];

    this.paginationSubject.pipe(pairwise()).subscribe((paginationDetails) => {
      if (JSON.stringify(paginationDetails[0]) !== JSON.stringify(paginationDetails[1])) {
        this.fetchTemplates();
      }
    });

  };

  fetchFilterOptions(filterOptions: any) {
    this.selectedDateFrom       = filterOptions.dateFilters?.dateFrom;
    this.selectedDateTo         = filterOptions.dateFilters?.dateTo;
    this.searchString           = filterOptions.searchString;
    this.orderBy                = filterOptions.orderBy;
    this.orderByDirection       = filterOptions.orderByDirection;
    this.fetchTemplates();
  }

   sort(event: SortEvent) {
    this.orderBy = event.column;
    this.orderByDirection = event.direction;

    this.fetchTemplates();
  }

  fetchTemplates() {
    this.isLoading = true;
    let params: _REP_TMP_1 = {
      page: this.paginationSubject.value.page,
      limit: this.paginationSubject.value.limit,
      search_string: this.searchString,
      order_by: this.orderBy,
      order_direction: this.orderByDirection,
    }
    this.reportinatorService.getTemplates(params).subscribe((res) => {
      this.paginatedTemplates = res;
      this.paginationSubject.next({
        ...this.paginationSubject.value,
        totalItems: res.total_items,
        totalPages: res.total_pages
      });
      this.isLoading = false;
    });
  }

  onAddNewTemplate() {
    let modalRef = this.modalService.open(NewTemplateModalComponent, {});

  }
}
