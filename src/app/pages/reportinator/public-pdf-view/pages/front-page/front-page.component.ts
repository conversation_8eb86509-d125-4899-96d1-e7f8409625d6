import {AfterViewInit, Component, EventEmitter, Input, OnChanges, OnInit, Output, QueryList, ViewChildren, ViewEncapsulation} from '@angular/core';
import {formatDateDMY, formatFullDayAndDate, UtilsService} from 'src/app/@core/utils/utils.service';
import {ReportElementResponse, ReportForHtmlResponse} from "../../../../../@shared/models/reportinator.interfaces";
import {environment} from "../../../../../../environments/environment";
import {CompanyColors, ReportinatorPublicPdfViewService} from "../../reportinator-public-pdf-view.service";
import { HttpClient } from "@angular/common/http";
import {StandardImports} from "../../../../../@shared/global_import";
import {QRCodeComponent} from "angularx-qrcode";


@Component({
    selector: 'reportinator-pdf-front-page',
    templateUrl: './front-page.template.html',
    styleUrls: ['../../reportinator-public-pdf-view.styles.css'],
    encapsulation: ViewEncapsulation.ShadowDom,
    standalone: true,
  imports: [StandardImports, QRCodeComponent]
})
export class FrontPageComponent implements OnInit {
  report: ReportForHtmlResponse;
  qrCodeData: string;
  companyLogo: string;
  frontPageImage: string;
  colors: CompanyColors | undefined;

  today: Date = new Date();
  cadastre: {
    bruksnr: number | undefined;
    festenr: number | undefined;
    seksjonsnr: number | undefined;
    gardsnr: number | undefined;
    kommunenr: number | undefined;
  };

  constructor(public utilsService: UtilsService, private pdfService: ReportinatorPublicPdfViewService, private http: HttpClient) {
  }

  ngOnInit() {
    this.pdfService.report$.subscribe(report => {
      this.report = report;
     this.colors = this.pdfService.mapColors(report);
      if (report.front_page_image_url) {
        this.http.get(report.front_page_image_url, { responseType: 'blob' }).subscribe((blob) => {
          const reader = new FileReader()
          reader.readAsDataURL(blob)
          reader.onloadend = () => {
            this.frontPageImage = reader.result as string
          }
        });
      }
    });
    this.pdfService.companyLogo$.subscribe(logo => {
      this.companyLogo = logo
    });
    this.qrCodeData =  `${environment.coreUrl}reportinator/public/pdf/${this.report.public_key}`
    this.cadastre = {
      bruksnr: this.report.address?.cadastre?.bruksnr,
      festenr: this.report.address?.cadastre?.festenr,
      seksjonsnr: this.report.address?.cadastre?.seksjonsnr,
      gardsnr: this.report.address?.cadastre?.gardsnr,
      kommunenr: this.report.address?.cadastre?.kommunenr,
    }
  }

  protected readonly formatFullDayAndDate = formatFullDayAndDate;
}
