import {AfterViewInit, Component, EventEmitter, Input, OnChanges, OnInit, Output, QueryList, ViewChildren, ViewEncapsulation} from '@angular/core';
import {formatDateDMY, formatFullDayAndDate, UtilsService} from 'src/app/@core/utils/utils.service';
import {ReportElementResponse, ReportForHtmlResponse} from "../../../../../@shared/models/reportinator.interfaces";
import {environment} from "../../../../../../environments/environment";
import {CompanyColors, ReportinatorPublicPdfViewService} from "../../reportinator-public-pdf-view.service";
import {StandardImports} from "../../../../../@shared/global_import";


@Component({
    selector: 'reportinator-pdf-header',
    templateUrl: './header.template.html',
    styleUrls: ['../../reportinator-public-pdf-view.styles.css'],
    encapsulation: ViewEncapsulation.ShadowDom,
    standalone: true,
    imports: [StandardImports]
})
export class HeaderComponent implements OnInit {
  report: ReportForHtmlResponse;
  today: Date = new Date();
  companyLogo: string;
  colors: CompanyColors | undefined;

  constructor(public utilsService: UtilsService, private pdfService: ReportinatorPublicPdfViewService) {}

  ngOnInit() {
    this.pdfService.report$.subscribe(report => {
      this.report = report;
     this.colors = this.pdfService.mapColors(report);
    });
    this.pdfService.companyLogo$.subscribe(logo => {
      this.companyLogo = logo
    });
  }

  protected readonly formatFullDayAndDate = formatFullDayAndDate;
}
