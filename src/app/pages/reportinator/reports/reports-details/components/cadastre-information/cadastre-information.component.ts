import {AfterViewInit, Component, ContentC<PERSON><PERSON>n, ElementRef, OnDestroy, OnInit, QueryList, ViewChild, ViewChildren, ViewContainerRef, ViewEncapsulation} from '@angular/core';
import {ReportResponse} from "../../../../../../@shared/models/reportinator.interfaces";
import {ReportinatorService} from "../../../../../../@shared/services/reportinator.service";
import {FormControl, FormGroup} from "@angular/forms";
import {_REP_REP_2, UnitDetails} from "../../../../../../@shared/models/input.interfaces";
import {ToastService} from "../../../../../../@core/services/toast.service";
import {AddressResponse, OrderLineResponse, OrderResponse, WorkOrderResponse} from "../../../../../../@shared/models/order.interfaces";
import {PropertyOwnershipTypeResponse} from "../../../../../../@shared/models/address.interfaces";
import {AddressService} from "../../../../../../@shared/services/address.service";
import {StandardImports} from "../../../../../../@shared/global_import";
import {ToggleSwitchComponent} from "../../../../../../@shared/components/toggle-switch/toggle-switch.component";
import {SelectoriniComponent} from "../../../../../../@shared/components/selectorini/selectorini.component";
import {OrderService} from "../../../../../../@shared/services/order.service";


@Component({
    selector: 'reportinator-cadastre-information',
    templateUrl: './cadastre-information.template.html',
    styleUrls: ['./cadastre-information.styles.css'],
    standalone: true,
  imports: [StandardImports, ToggleSwitchComponent, SelectoriniComponent]
})
export class CadastreInformationComponent implements OnInit {

  report: ReportResponse;
  order: OrderResponse;
  loading: boolean = false;
  address: UnitDetails;

  workOrders: WorkOrderResponse[] = [];

  leasehold: boolean = false;

  selectedOwnershipType: PropertyOwnershipTypeResponse;

  propertyOwnershipTypes: PropertyOwnershipTypeResponse[] = [];

  kommunenrControl: FormControl = new FormControl();
  gardsnrControl: FormControl = new FormControl();
  bruksnrControl: FormControl = new FormControl();
  seksjonsnrControl: FormControl = new FormControl();
  leaseholdNumberControl: FormControl = new FormControl();
  leaseholdStartYearControl: FormControl = new FormControl();
  leaseholdRegulationYearControl: FormControl = new FormControl();
  leaseholdExpiryYearControl: FormControl = new FormControl();

  businessManagerControl: FormControl = new FormControl();
  homeOwnerControl: FormControl = new FormControl();
  homeOwnerFractionControl: FormControl = new FormControl();
  shareNumberControl: FormControl = new FormControl();
  stockNumberControl: FormControl = new FormControl();
  organisationNumberControl: FormControl = new FormControl();
  organisationNameControl: FormControl = new FormControl();
  apartmentNumberControl: FormControl = new FormControl();
  sectionNumberControl: FormControl = new FormControl();
  plotAreaControl: FormControl = new FormControl();



  constructor(private reportinatorService: ReportinatorService, private toastService: ToastService, private addressService: AddressService, private orderService: OrderService){}


  ngOnInit() {
    this.addressService.getPropertyOwnershipTypes().subscribe((propertyOwnershipTypes) => {
      this.propertyOwnershipTypes = propertyOwnershipTypes;
      if (this.address) {
        this.selectedOwnershipType = propertyOwnershipTypes.find(type => type.ownership_type_id === this.address.ownership_type_id)!;
      }
    });

    this.reportinatorService.report$.subscribe(report => {
      this.report = report;
    });

    this.reportinatorService.reportOrder$.subscribe(order => {
      this.order = order;
    });

    this.orderService.getCompanyWorkOrdersWithFullResponse({order_id: this.order.order_id}).subscribe((workOrders) => {
      let address: UnitDetails = {} as UnitDetails;
      for (const workOrder of workOrders.data) {
        if (workOrder.addresses.length > 0) {
          address = workOrder.addresses[0];
          break;
        }
      }

      if (address.address_id) {
        this.address = address;
        if (this.propertyOwnershipTypes) {
          this.selectedOwnershipType = this.propertyOwnershipTypes.find(type => type.ownership_type_id === address.ownership_type_id)!;
        }
        this.kommunenrControl.setValue(address.cadastre?.kommunenr);
        this.gardsnrControl.setValue(address.cadastre?.gardsnr);
        this.bruksnrControl.setValue(address.cadastre?.bruksnr);
        this.seksjonsnrControl.setValue(address.cadastre?.seksjonsnr);
        this.plotAreaControl.setValue(address.plot_area);
        this.leaseholdNumberControl.setValue(address.leasehold_number);
        this.leaseholdStartYearControl.setValue(address.leasehold_start_year);
        this.leaseholdRegulationYearControl.setValue(address.leasehold_regulation_year);
        this.leaseholdExpiryYearControl.setValue(address.leasehold_expiry_year);


        this.businessManagerControl.setValue(address.business_manager);
        this.homeOwnerControl.setValue(address.homeowners_name);
        this.homeOwnerFractionControl.setValue(address.ownership_fraction);
        this.shareNumberControl.setValue(address.share_number);
        this.stockNumberControl.setValue(address.stock_number);
        this.organisationNumberControl.setValue(address.organisation_number);
        this.organisationNameControl.setValue(address.organisation_name);
        this.apartmentNumberControl.setValue(address.apartment_number);
        this.sectionNumberControl.setValue(address.section_number);

        this.leasehold = !!address.leasehold
      }
    });
  }

  save() {
    this.loading = true;

    this.address.cadastre!.kommunenr = this.kommunenrControl.value;
    this.address.cadastre!.gardsnr = this.gardsnrControl.value;
    this.address.cadastre!.bruksnr = this.bruksnrControl.value;
    this.address.cadastre!.seksjonsnr = this.seksjonsnrControl.value;
    this.address.business_manager = this.businessManagerControl.value;
    this.address.homeowners_name = this.homeOwnerControl.value;
    this.address.ownership_fraction = this.homeOwnerFractionControl.value;
    this.address.share_number = this.shareNumberControl.value;
    this.address.stock_number = this.stockNumberControl.value;
    this.address.organisation_number = this.organisationNumberControl.value;
    this.address.organisation_name = this.organisationNameControl.value;
    this.address.apartment_number = this.apartmentNumberControl.value;
    this.address.section_number = this.sectionNumberControl.value;
    this.address.leasehold = this.leasehold ? 1 : 0;
    this.address.leasehold_number = this.leaseholdNumberControl.value;
    this.address.leasehold_start_year = this.leaseholdStartYearControl.value;
    this.address.leasehold_regulation_year = this.leaseholdRegulationYearControl.value;
    this.address.leasehold_expiry_year = this.leaseholdExpiryYearControl.value;
    this.address.ownership_type_id = this.selectedOwnershipType.ownership_type_id;
    this.address.plot_area = this.plotAreaControl.value;

    let payload: UnitDetails = this.address;

    this.addressService.updateAddress(payload).subscribe((report) => {
      this.toastService.successToast('updated')
      this.reportinatorService.fetchAndUpdateReport(this.report.report_id!);
      this.loading = false;
    }, error => {
      this.loading = false;
    });
  }

  ownershipTypeSelected(event: any) {
    this.selectedOwnershipType = event;
  }

}
