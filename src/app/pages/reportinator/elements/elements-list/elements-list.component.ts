import {Component, OnInit} from '@angular/core';
import {displayDate, UtilsService} from 'src/app/@core/utils/utils.service';
import {TranslateService} from "@ngx-translate/core";
import {ReportinatorService} from "../../../../@shared/services/reportinator.service";
import {FormControl, FormGroup} from "@angular/forms";
import {BehaviorSubject, pairwise, Subject} from "rxjs";
import {NgbModal, NgbModalRef} from "@ng-bootstrap/ng-bootstrap";
import {PaginationResponse} from "../../../../@shared/models/response.interfaces";
import {environment} from "../../../../../environments/environment";
import {ElementResponse, ReportResponse, TemplateResponse} from "../../../../@shared/models/reportinator.interfaces";
import {_REP_ELM_1, _REP_TMP_0, _REP_TMP_1} from "../../../../@shared/models/input.interfaces";
import {Column} from "../../../../@shared/components/advanced-table/advanced-table.component";
import {DatePipe} from "@angular/common";
import {SortEvent} from "../../../../@shared/components/advanced-table/sortable.directive";
import {Router} from "@angular/router";
import {NewElementModalComponent} from "./_modals/new-element-modal/new-element-modal.component";
import {PaginationContainer} from "../../../../@shared/models/global.interfaces";
import {TablerinoColumn, TablerinoSettings} from "../../../../@shared/components/tablerino/tablerino.component";
import {HeaderFiltersContainer} from "../../../../@shared/components/tablerino-header/tablerino-header.component";
import {StandardImports} from "../../../../@shared/global_import";
import {PageHeaderComponent} from "../../../../@shared/components/page-header/page-header.component";
import {TablerinoCompleteComponent} from "../../../../@shared/components/tablerino-complete/tablerino-complete.component";

export interface ElementRow extends ElementResponse{
  selected: boolean;
}

@Component({
    selector: 'reportinator-elements-list',
    templateUrl: './elements-list.template.html',
    styleUrls: [],
    standalone: true,
  imports: [StandardImports, PageHeaderComponent, TablerinoCompleteComponent]
})
export class ElementsListComponent implements OnInit {

  private readonly destroy$: Subject<void> = new Subject<void>();
  modalReference: NgbModalRef;
  loading: boolean = false;

  settings: TablerinoSettings = {
    clickableRows: true
  }
  elementRows: ElementRow[] = [];
  dataResponse: PaginationResponse<ElementResponse[]>;
  columnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);
  paginationSubject: BehaviorSubject<PaginationContainer> = new BehaviorSubject<PaginationContainer>({page: 1, limit: 25, paginate: 1, totalPages: 0, totalItems: 0});
  headerFiltersContainerSubject: BehaviorSubject<HeaderFiltersContainer> = new BehaviorSubject<HeaderFiltersContainer>({filters: [], init: true});
  selectedRowsSubject: BehaviorSubject<ElementRow[]> = new BehaviorSubject<ElementRow[]>([]);
  cancelCurrentRequest$ = new Subject<void>();

  constructor(
    private reportinatorService: ReportinatorService,
    public utilsService: UtilsService,
    private translate: TranslateService,
    private router: Router,
    private modalService: NgbModal) {
  }

  ngOnInit() {
    this.initColumns();
    this.initializeHeaderFilters();
    this.getElements();
    this.paginationSubject.pipe(pairwise()).subscribe((paginationDetails) => {
      if (paginationDetails[0].page !== paginationDetails[1].page) {
        this.getElements();
      }
    });
    this.headerFiltersContainerSubject.subscribe((headerFilters) => {
      if (!headerFilters.init) {
        this.getElements();
      }
    });
  }

  initializeHeaderFilters() {}

  initColumns() {
    this.columnsSubject.next([
      {
        name: 'element_name',
        labelKey: this.translate.instant('reportinator.common.list.name'),
        formatter: (elm: ElementResponse) => `${elm.element_name}`,
        sort: true,
        visible: true,
      },
      {
        name: 'element_type_id',
        labelKey: this.translate.instant('reportinator.common.list.type'),
        formatter: (elm: ElementResponse) => `${elm.element_type_name}`,
        sort: true,
        visible: true,
      },
      {
        name: 'UpdatedBy',
        labelKey: this.translate.instant('reportinator.common.list.updatedBy'),
        formatter: (elm: ElementResponse) => `${elm.updated_by.full_name}`,
        sort: false,
        visible: true,
      },
      {
        name: 'created_at',
        labelKey: this.translate.instant('reportinator.common.list.createdAt'),
        formatter: (elm: ElementResponse) => `${displayDate(elm.created_at)}`,
        sort: true,
        visible: true,
      },
      {
        name: 'updated_at',
        labelKey: this.translate.instant('reportinator.common.list.updatedAt'),
        formatter: (elm: ElementResponse) => `${displayDate(elm.updated_at)}`,
        sort: true,
        visible: true,
      },
    ]);
  };

  getElements(searchTerm: string = '') {
    this.loading = true;
    this.cancelCurrentRequest$.next();

    let filters = this.headerFiltersContainerSubject.value.filters;
    let sortColumn = this.columnsSubject.value.find(col => col.sortedAsc || col.sortedDesc) || null;
    let sortKey: string = sortColumn?.name || 'element_name';
    let sortDirection: 'asc' | 'desc' = sortColumn ? (sortColumn.sortedAsc ? 'asc' : 'desc') : 'asc';

    let params: _REP_ELM_1 = {
      page: this.paginationSubject.value.page,
      limit: this.paginationSubject.value.limit,
      search_string: searchTerm,
      order_by: sortKey,
      order_direction: sortDirection,
    }
    this.reportinatorService.getElements(params).subscribe((res) => {
      this.dataResponse = res;
      this.paginationSubject.next({
        ...this.paginationSubject.value,
        totalItems: res.total_items,
        totalPages: res.total_pages
      });
      this.elementRows = res.data.map((wo) => {
        return {
          ...wo,
          selected: false,
        }
      });
      this.loading = false;
    }, error => {
      this.loading = false
    });
  }

  onAddNewElement() {
    this.modalService.open(NewElementModalComponent, {});
  }

  rowClicked(row: ElementRow) {
    this.router.navigate([`/reportinator/elements/details/${row.element_id}`]);
  }

  deleteElement(element: ElementResponse) {
    this.reportinatorService.deleteElement(element.element_id).subscribe(() => {
      this.getElements();
    });
  }
}
