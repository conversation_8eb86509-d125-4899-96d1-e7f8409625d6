import {AfterViewInit, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild, ViewEncapsulation} from '@angular/core';
import {FormControl} from "@angular/forms";
import {ReportinatorService} from "../../../../../@shared/services/reportinator.service";
import {ReportElementAttributeResponse} from "../../../../../@shared/models/reportinator.interfaces";
import {displayDate, UtilsService} from "../../../../../@core/utils/utils.service";
import {StandardImports} from "../../../../../@shared/global_import";


@Component({
    selector: 'reportinator-element-text-field',
    templateUrl: './element-text-field.template.html',
    styleUrls: ['./element-text-field.styles.css'],
    encapsulation: ViewEncapsulation.None,
    standalone: true,
    imports: [StandardImports]
})
export class ElementTextFieldComponent implements OnInit, AfterViewInit {
  @Input() headerTextKey: string;
  @Input() attribute: ReportElementAttributeResponse;
  @Input() iconAfter: string;
  @Input() status: number = 0;
  @Input() control: FormControl;
  @Input() enableFormatting: boolean = false;
  @Output() elementFocusOut: EventEmitter<{entry_id: number, control_value: string | number, event: FocusEvent}> = new EventEmitter<{entry_id: number, control_value: string | number, event: FocusEvent}>();
  @Output() showPhrasesEmitter: EventEmitter<number | null> = new EventEmitter<number | null>();

  accordionId: string;
  formattedText: string = '';

  // collapsedAttributeIds: number[] = [15,16]; // Add the IDs that should be collapsed by default
  collapsedAttributeIds: number[] = [];

  @ViewChild('mainTextArea') mainTextArea: ElementRef;
  @ViewChild('stylisedTextArea') stylisedTextArea: ElementRef;
  @ViewChild('textAreaContainer') textAreaContainer: ElementRef;

  constructor(public utilsService: UtilsService, private reportinatorService: ReportinatorService, private cdr: ChangeDetectorRef) {
    this.accordionId = Math.random().toString(36).substring(2, 15)
  }

  ngOnInit() {
    this.control.valueChanges.subscribe(() => {
      this.updateFormattedText()
    });
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.updateFormattedText();
    }, 0);
    this.cdr.detectChanges();
  }

  showPhrases() {
    if (this.attribute) {
      this.showPhrasesEmitter.emit(this.attribute.entry_id);
    } else {
      this.showPhrasesEmitter.emit(undefined);
    }
  }

  updateFormattedText() {
    if (this.attribute.relation_id === 2) {
    }
    this.formattedText = this.formatText(this.control.value);
    this.adjustTextareaHeight();
  }

  // Check if the current attribute should be collapsed
  isCollapsed(attributeId: number): boolean {
    const hasValue = this.control && this.control.value && this.control.value.trim().length > 0;
    // Collapse if the attribute ID is in the collapsed list and no value is present in the textarea
    return this.collapsedAttributeIds.includes(attributeId) && !hasValue;
  }

  formatText(value: string): string {
    if (!value) {
      return '';
    }
    return value.replace(/{{\s*([\wæøåÆØÅ\s]+?)\s*:\s*([\wæøåÆØÅ\s]+?)\s*}}/g, '<span class="value-badge">$1</span>');
  }

  saveCursorPosition() {
    const cursorPos = this.mainTextArea.nativeElement.selectionStart;
    this.reportinatorService.setActivePhraseControlCursorPosition(cursorPos);
  }

  textDivFocusOut() {
    this.elementFocusOut.emit({entry_id: this.attribute.entry_id, control_value: this.control.value, event: new FocusEvent('focusout')});
  }

  adjustTextareaHeight(): void {
    // Reset the height to auto to calculate the new height
    this.mainTextArea.nativeElement.style.height = 'auto';
    this.stylisedTextArea.nativeElement.style.height = 'auto';
    this.textAreaContainer.nativeElement.style.height = 'auto';

    // Set the height based on the scroll height to accommodate all text
    this.mainTextArea.nativeElement.style.height = `${this.mainTextArea.nativeElement.scrollHeight}px`;
    this.stylisedTextArea.nativeElement.style.height = `${this.stylisedTextArea.nativeElement.scrollHeight}px`;
    this.textAreaContainer.nativeElement.style.height = `${this.textAreaContainer.nativeElement.scrollHeight}px`;
  }

  protected readonly displayDate = displayDate;
}
