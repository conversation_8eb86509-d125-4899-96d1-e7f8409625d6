<div class="accordion-item">
  <h2
    class="accordion-header"
    [id]="'panelsStayOpen-headingOne' + accordionId">
    <button
      class="accordion-button"
      type="button"
      data-bs-toggle="collapse"
      [attr.data-bs-target]="'#panelsStayOpen-collapseOne' + accordionId"
      [attr.aria-expanded]="isCollapsed(attribute.attribute_id) ? 'false' : 'true'"
      [attr.aria-controls]="'panelsStayOpen-collapseOne' + accordionId">
      <div class="d-flex justify-content-between">
        <div class="d-flex">
          <span>{{headerTextKey | translate}}</span>
          <i class="ms-2 fa-regular"
            [ngClass]="{
              'fa-circle': status === 0,
              'fa-circle-half-stroke': status === 1,
              'fa-circle-check': status === 2
            }"></i>
        </div>
        <i class="fa-solid fa-chevron-down"></i>
      </div>
    </button>
  </h2>
  <div (focusout)="textDivFocusOut()"
    [id]="'panelsStayOpen-collapseOne' + accordionId"
    class="accordion-collapse collapse"
    [ngClass]="{'show': !isCollapsed(attribute.attribute_id)}"
    [attr.aria-labelledby]="'panelsStayOpen-headingOne' + accordionId">
    <div>
      <div #textAreaContainer class="textarea-container">
        <div #stylisedTextArea class="styled-text" [innerHTML]="formattedText"></div>
        <textarea #mainTextArea
                  [formControl]="control"
                  (focus)="showPhrases()"
                  (focusout)="saveCursorPosition()"
                  (input)="updateFormattedText()"
                  class="form-control element-input-field"
        ></textarea>
      </div>
      <div class="d-flex gap-1 px-2 py-1 font-12" style="border-top: 1px solid #ced4da;" *ngIf="attribute.updated_at">
        <span>{{ "reportinator.elementTextField.updated" | translate }}</span>
        <span>{{displayDate(attribute.updated_at)}}</span>
        <span>{{ "reportinator.elementTextField.by" | translate }}</span>
        <span>{{ attribute.updated_by_name }}</span>
      </div>
    </div>
  </div>
</div>
