import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {ActivatedRoute} from "@angular/router";
import {AbstractControl, FormBuilder, FormControl, FormGroup, Validators} from "@angular/forms";
import {NgbActiveModal} from "@ng-bootstrap/ng-bootstrap";
import {AffiliateResponse} from "../../../../../@shared/models/affiliate.interfaces";
import {_CRM_AFF_2, UnitDetails} from "../../../../../@shared/models/input.interfaces";
import {CustomerAggregatedDataResponse} from "../../../../../@shared/models/order.interfaces";
import {convertCompactAddressToUnitDetails, displayPhone, getFormControl, UtilsService} from "../../../../../@core/utils/utils.service";
import {CustomerService} from "../../../../../@shared/services/customer.service";
import {SettingsService} from "../../../../../@shared/services/settings.service";
import {ToastService} from "../../../../../@core/services/toast.service";
import {PaymentService} from "../../../../../@shared/services/payment.service";
import {CustomerAccountingResponse} from "../../../../../@shared/models/customer.interfaces";
import {StorageService} from "../../../../../@core/services/storage.service";
import {StandardImports} from "../../../../../@shared/global_import";
import {AddressSearchComponent} from "../../../../../@shared/components/address-search/address-search.component";
import {SelectoriniComponent} from "../../../../../@shared/components/selectorini/selectorini.component";
import {PhoneInputComponent} from "../../../../../@shared/components/phone-input/phone-input.component";
import {SpinnerComponent} from "../../../../../@shared/components/spinner/spinner.component";

@Component({
    selector: 'app-edit-business-customer-info-modal',
    templateUrl: './edit-affiliate-modal.component.html',
    styleUrls: ['./edit-affiliate-modal.component.css'],
    standalone: true,
  imports: [StandardImports, AddressSearchComponent, SelectoriniComponent, PhoneInputComponent, SpinnerComponent]
})

export class EditAffiliateModalComponent implements OnInit {
  @Input() customerId: number;
  @Output() customerUpdateResult: EventEmitter<AffiliateResponse> = new EventEmitter<AffiliateResponse>();
  customerForm: FormGroup;
  saving: boolean = false;
  affiliate: AffiliateResponse;
  affiliateAddress: UnitDetails;
  affiliateAddressValid: boolean = false;
  affiliateAggregatedData : CustomerAggregatedDataResponse;
  editMode: boolean = true;
  accountingEnabled: boolean = false;
  selectedAccountingCustomer: CustomerAccountingResponse | null = null;
  accountingCustomers: CustomerAccountingResponse[] = [];
  accountingLoading: boolean = false;

  constructor(public utilsService: UtilsService,
              private customerService: CustomerService,
              private route: ActivatedRoute,
              private fb: FormBuilder,
              private settingsService: SettingsService,
              private toastService: ToastService,
              public activeModal: NgbActiveModal,
              private paymentService: PaymentService,
              private storageService: StorageService) {
    this.customerId = parseInt(this.route.snapshot.paramMap.get('id')!);
  }

  ngOnInit() {
    this.storageService.accountingEnabled$.subscribe(res => {
      this.accountingEnabled = res;
    });

    this.customerForm = new FormGroup({
      customer_name: new FormControl<string>('', [Validators.required]),
      organisation_number: new FormControl<string>('', [Validators.required]),
      phone: new FormControl<string | null>(null, [Validators.required]),
      email: new FormControl<string>('', [Validators.required])
    });

    // Get customer details
    this.customerService.getBusinessCustomerById(this.customerId!).subscribe(res => {
      this.affiliate = res;
      this.affiliateAddress = convertCompactAddressToUnitDetails(res.address)!;
      this.affiliateAddressValid = true;
      this.initCompanyForm();
    });

    this.customerService.getCustomerAggregatedData(this.customerId).subscribe(res => {
      this.affiliateAggregatedData = res;
    })

    if (this.accountingEnabled) {
      this.accountingLoading = true;
      this.customerService.getAccountingCustomers().subscribe(res => {
        this.accountingCustomers = res;
        for (const customer of this.accountingCustomers) {
          if (customer.customer_id.toString() === this.affiliate.accounting_id) {
            this.selectedAccountingCustomer = customer;
          }
        }
        this.accountingLoading = false;
      }, error => {
        this.accountingLoading = false;
      });
    }
  }

  initCompanyForm(): void {
    this.customerForm = this.fb.group({
      customer_name: [this.affiliate.name, Validators.required],
      organisation_number: [this.affiliate.organisation_number, Validators.required],
      phone: [displayPhone(this.affiliate.phone)],
      email: [this.affiliate.email, [Validators.email]],
    });
    this.customerForm.controls['customer_name'].disable();
    this.customerForm.controls['organisation_number'].disable();
    this.customerForm.valueChanges.subscribe();
  }

  onAddressUpdated(addressWrapper: { id: number, address: UnitDetails }) {
    this.affiliateAddress = addressWrapper.address;
    this.affiliateAddressValid = true;
  }

  onAddressRemoved() {
    this.affiliateAddressValid = false;
  }

  onSubmit() {
    this.saving = true;

    const payload: _CRM_AFF_2 = {
      affiliate_id: this.customerId!,
      address: this.affiliateAddress,
      _phone: this.customerForm.value.phone,
      _email: this.customerForm.value.email,
      _invoice_email: this.affiliate.invoice_email,
      invoice_send_type_id: this.affiliate.invoice_send_type_id,
      invoice_due_date_days: this.affiliate.invoice_due_date_days,
      accounting_id: this.selectedAccountingCustomer ? this.selectedAccountingCustomer.customer_id : null,
      sub_ledger_account_id: this.selectedAccountingCustomer ? this.selectedAccountingCustomer.sub_ledger_account_id : null,
    }

    this.customerService.updateBusinessCustomer(payload).subscribe(res => {
      this.saving = false;
      this.customerUpdateResult.emit(res);
      this.activeModal.close()
      this.toastService.successToast("company_updated");

    }, (error) => {
      this.saving = false;
      console.log(error);
    })
  }

  accountingCustomerSelected(event: any) {
    this.selectedAccountingCustomer = event;
  }

  accountingCustomerDeselected() {
    this.selectedAccountingCustomer = null;
  }

  handlePhoneNumberChange(phoneNumber: string | null): void {
    this.customerForm.patchValue({ phone: phoneNumber });
  }


  protected readonly getFormControl = getFormControl;
}
