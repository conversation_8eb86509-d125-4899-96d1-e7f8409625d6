import {Component, Input, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {ActivatedRoute, Router, RouterLink} from "@angular/router";
import { NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {OrderService} from "../../../@shared/services/order.service";
import {UtilsService} from "../../../@core/utils/utils.service";
import {TranslateService} from "@ngx-translate/core";
import {AffiliateService} from "../../../@shared/services/affiliate.service";
import {AffiliateResponse} from "../../../@shared/models/affiliate.interfaces";
import {StorageService} from "../../../@core/services/storage.service";
import {EditAffiliateModalComponent} from "./_modals/edit-affiliate-modal/edit-afiiliate-modal.component";
import {_CRM_ADR_10, _CRM_AFF_14, _CRM_AFF_2, _CRM_PAY_41, UnitDetails} from "../../../@shared/models/input.interfaces";
import {ToastService} from "../../../@core/services/toast.service";
import {EditAffiliateInvoiceSettingsModalComponent} from "./_modals/edit-affiliate-invoice-settings-modal/edit-affiliate-invoice-settings-modal.component";
import {PaymentService} from "../../../@shared/services/payment.service";
import {OrderPaymentResponseCompact} from "../../../@shared/models/payment.interfaces";
import {PaymentDetailsComponent} from "../../payments/components/payment-details/payment-details.component";
import {DetailsViewSettings} from "../../../@shared/models/order.interfaces";
import {EditCustomerModalComponent} from "../../../@shared/components/edit-customer-modal/edit-customer-modal.component";
import {StandardImports} from "../../../@shared/global_import";
import {CardComponent} from "../../../@shared/components/layout/card/card.component";
import {AffiliateConsolidatedInvoicePaymentsComponent} from "./affiliate-consolidated-invoice-payments/affiliate-consolidated-invoice-payments.component";
import {AffiliateOrdersComponent} from "./affiliate-orders/affiliate-orders.component";
import {AffiliatePricesComponent} from "./affiliate-prices/affiliate-prices.component";
import {AffiliateContactListComponent} from "./affiliate-contact-list/affiliate-contact-list.component";
import {AddressSearchComponent} from "../../../@shared/components/address-search/address-search.component";
import {AddressService} from "../../../@shared/services/address.service";
import {AffiliateNotes} from "./affiliate-notes/affiliate-notes";
import {CdkDragHandle} from "@angular/cdk/drag-drop";
import {StreetViewModalComponent} from "../../orders/order-details-v2/order-address/_modals/street-view-modal/street-view-modal.component";
import {AddressObject} from "../../orders/order-details-v2/order-address/order-address.component";
import {DomSanitizer} from "@angular/platform-browser";
import {EditAddressModalComponent} from "../../../@shared/components/address-search/edit-address-modal/edit-address-modal.component";
import {TablerinoColumn, TablerinoComponent} from "../../../@shared/components/tablerino/tablerino.component";
import {BehaviorSubject} from "rxjs";
import {ProfiledItemListComponent} from "../../../@shared/components/profiled-item-list/profiled-item-list.component";
import {PaymentDetailsV2Component} from "../../payments/components/payment-details-v2/payment-details-v2.component";

@Component({
    selector: 'app-business-details',
    templateUrl: './affiliates-details.component.html',
    styleUrls: ['./affiliates-details.component.css'],
    providers: [OrderService, NgbModal],
    standalone: true,
  imports: [StandardImports, RouterLink, CardComponent, AffiliateConsolidatedInvoicePaymentsComponent, AffiliateOrdersComponent, AffiliatePricesComponent, AffiliateContactListComponent, AddressSearchComponent, AffiliateNotes, CdkDragHandle, TablerinoComponent, ProfiledItemListComponent]
})

export class AffiliatesDetailsComponent implements OnInit {
  @Input() affiliateId: number;
  @Input() private: boolean = false;
  affiliate: AffiliateResponse;
  isloading = true;
  hasAccounting: boolean = false;
  partnersEnabled: boolean = false;

  isCustomer: boolean = false;
  isPartner: boolean = false;
  isSubContractor: boolean = false;

  hidePrices: boolean = false;
  disableSms: boolean = false;
  disableEmail: boolean = false;

  addAddressActive: boolean = false;
  addressesLoading: boolean = false;
  affiliateAddresses: UnitDetails[] = [];
  activeAddressEditIndex: number | null = null;
  affiliateAddressColumns$: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);

  consolidatedInvoiceSetups: OrderPaymentResponseCompact[] = [];

  @ViewChild('addressActionButtons', { static: true }) addressActionButtons!: TemplateRef<any>;

  constructor(private affiliateService: AffiliateService,
              public utilsService: UtilsService,
              private route: ActivatedRoute,
              private router: Router,
              private addressService: AddressService,
              private translateService: TranslateService,
              private storageService: StorageService,
              private modalService: NgbModal,
              private toastService: ToastService,
              private sanitizer: DomSanitizer,
              private paymentService: PaymentService) {}

  ngOnInit() {
    this.affiliateId = Number(this.route.snapshot.paramMap.get('id'));
    this.storageService.accountingEnabled$.subscribe((enabled: boolean) => {
      this.hasAccounting = enabled;
    });
    this.storageService.partnersEnabled$.subscribe((enabled: boolean) => {
      this.partnersEnabled = enabled;
    });

    this.affiliateAddressColumns$.next([
      { name : 'address_name', labelKey: 'Adresse', formatter: (address: UnitDetails) => `<span class="ms-2"></span>` + address.display, visible: true, hideHeaderName: true},
      { name : 'type', labelKey: 'orders.newOrder.address.type', formatter: (address: UnitDetails) => address.property_type_name, visible: true},
      { name : 'size', labelKey: 'orders.newOrder.address.size', formatter: (address: UnitDetails) => address.livable_area + ' ' + `<span>m<sup>2</sup></span>`, visible: true},
      { name : 'actions', labelKey: 'du', formatter: (address: UnitDetails) => '',  ngTemplate: this.addressActionButtons, visible: true, hideHeaderName: true},
    ]);

    this.isloading = true;
    this.affiliateService.getAffiliateById(this.affiliateId).subscribe((affiliate: AffiliateResponse) => {
      this.setAffiliate(affiliate);
      this.isloading = false;
    }, error => {
      this.isloading = false;
    });
    this.addressesLoading = true;
    this.affiliateService.getAffiliateAddresses(this.affiliateId).subscribe((addresses: UnitDetails[]) => {
      this.affiliateAddresses = addresses;
      this.addressesLoading = false;
    });

    this.getConsolidatedInvoiceSetups();
  }

  getConsolidatedInvoiceSetups() {
    let params: _CRM_PAY_41 = {
      customer_id: this.affiliateId,
      include_consolidated_invoice_parents: true,
    }

    this.paymentService.getCompanyPayments(params).subscribe((payments) => {
      this.consolidatedInvoiceSetups = payments.data;
    });
  }

  setAffiliate(affiliate: AffiliateResponse) {
    this.affiliate = affiliate;
    this.private = this.affiliate.is_private === 1;
    this.isCustomer = !!this.affiliate.is_customer;
    this.isPartner = !!this.affiliate.is_partner;
    this.isSubContractor = !!this.affiliate.is_subcontractor;
    this.hidePrices = !!this.affiliate.hide_payment_data;
    this.disableSms = !!this.affiliate.disable_sms;
    this.disableEmail = !!this.affiliate.disable_email;
  }

  settingsUpdated() {
    let payload: _CRM_AFF_2 = {
      affiliate_id: this.affiliateId,
      is_partner: this.isPartner ? 1 : 0,
      is_customer: this.isCustomer ? 1 : 0,
      is_subcontractor: this.isSubContractor ? 1 : 0,
      hide_payment_data: this.hidePrices ? 1 : 0,
      disable_sms: this.disableSms ? 1 : 0,
      disable_email: this.disableEmail ? 1 : 0,
      sub_contractor_hide_prices: this.affiliate.sub_contractor_hide_prices,
      can_fetch_parent_products_as_contractor: this.affiliate.can_fetch_parent_products_as_contractor,
    };
    this.affiliateService.updateAffiliate(payload).subscribe((affiliate: AffiliateResponse) => {
      this.setAffiliate(affiliate);
      this.toastService.successToast('updated')
    });
  }

  openInvoiceSettingsModal() {
    const modalRef = this.modalService.open(EditAffiliateInvoiceSettingsModalComponent, {size: 'lg'});
    modalRef.componentInstance.affiliate = this.affiliate;
    modalRef.componentInstance.updateEmitter.subscribe((affiliate: AffiliateResponse) => {
      this.affiliate = affiliate;
    });
  }

  paymentUpdated(payment: OrderPaymentResponseCompact) {
    const index = this.consolidatedInvoiceSetups.findIndex(p => p.payment_id === payment.payment_id);
    if (index > -1) {
      this.consolidatedInvoiceSetups[index] = payment;
      this.consolidatedInvoiceSetups.push(payment);
    }
  }

  removeConsInvoice(payment: OrderPaymentResponseCompact) {
    console.log('delete payment', payment);
    const index = this.consolidatedInvoiceSetups.findIndex(p => p.payment_id === payment.payment_id);
    console.log('index', index);
    if (index > -1) {
      this.consolidatedInvoiceSetups.splice(index, 1);
    }
  }

  createConsolidatedInvoice() {
    let modal = this.modalService.open(PaymentDetailsV2Component, { size: 'lg' });
    let viewSettings: DetailsViewSettings = {
      modalView: true,
      createView: true,
      consolidatedInvoiceView: true,
    }
    modal.componentInstance.viewSettings = viewSettings;
    modal.componentInstance.paymentRecipient = this.affiliate;
    modal.componentInstance.paymentUpdated.subscribe((payment: OrderPaymentResponseCompact) => {
      this.consolidatedInvoiceSetups.push(payment);
    });
  }

  createAffiliateAddress(address: UnitDetails) {
    let payload: _CRM_AFF_14 = {
      ...address,
      affiliate_id: this.affiliateId,
    }
    this.affiliateService.createAffiliateAddress(payload).subscribe((res: UnitDetails) => {
      this.affiliateAddresses.push(res);
      this.addAddressActive = false;
      this.toastService.successToast('created');
    });
  }

  deleteAffiliateAddress(addressId: number) {
    this.affiliateService.deleteAffiliateAddress({address_id: addressId, affiliate_id: this.affiliateId}).subscribe(() => {
      this.affiliateAddresses = this.affiliateAddresses.filter(a => a.address_id !== addressId);
      this.toastService.successToast('deleted');
    });
  }

  updateAffiliateAddress(address: UnitDetails) {
    this.addressService.updateAddress(address).subscribe((res: UnitDetails) => {
      this.affiliateAddresses = this.affiliateAddresses.map(a => a.address_id === res.address_id ? res : a);
      this.toastService.successToast('updated');
    });
  }

  openEditModal() {
    if (this.private) {
      let modalRef = this.modalService.open(EditCustomerModalComponent, {size: 'lg'});
      modalRef.componentInstance.accountingEnabled = this.hasAccounting;
      modalRef.componentInstance.affiliateId = this.affiliate.affiliate_id;
      modalRef.result.then((res) => {
        if (res) {
          this.affiliate = res;
        }
      });

    } else {
      const modalRef = this.modalService.open(EditAffiliateModalComponent, {size: 'lg'});
      modalRef.componentInstance.affiliate = this.affiliate;
      modalRef.componentInstance.customerUpdateResult.subscribe((affiliate: AffiliateResponse) => {
        this.affiliate = affiliate;
      });
    }
  }

  async openEditAddressModal(addr: UnitDetails) {
    const modalRef = this.modalService.open(EditAddressModalComponent, {size: "md"})
    modalRef.componentInstance.address = addr;
    modalRef.componentInstance.showChangeAddressButton = true;
    modalRef.result.then((result: UnitDetails) => {
      if (result) {
        this.addressService.updateAddress(result).subscribe(async () => {
          let index = this.affiliateAddresses.findIndex((_addr) => _addr.address_id === addr.address_id)!
          this.affiliateAddresses[index] = result;
        });
      } else {
        return;
    }
  }).catch((error) => {
    return;
  });
  }

  openStreetViewModal(address: UnitDetails) {
    const selectedAddress = {
      address: address,
      addressText: address.display,
      addressUrl: this.sanitizer.bypassSecurityTrustResourceUrl(`https://www.google.com/maps/embed/v1/streetview?key=AIzaSyBICuznrQvIXLH28fI-_Kkw_L4Zmst55sw&location=${address.lat},${address.lng}&heading=300&pitch=0&fov=35`),
      streetValid: true,
      addressName: address.address_name
    };

    const modalRef = this.modalService.open(StreetViewModalComponent, { size: 'lg' });
    modalRef.componentInstance.address = selectedAddress;
  }

}
