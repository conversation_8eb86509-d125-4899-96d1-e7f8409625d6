import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {Ng<PERSON><PERSON>odal, NgbModalRef} from "@ng-bootstrap/ng-bootstrap";
import {Router} from "@angular/router";
import {_CRM_PAY_41, _CRM_PAY_7} from 'src/app/@shared/models/input.interfaces';
import {CustomerService} from 'src/app/@shared/services/customer.service';
import {OrderService} from "../../../../@shared/services/order.service";
import {BehaviorSubject, forkJoin, pairwise} from "rxjs";
import {PaymentService} from "../../../../@shared/services/payment.service";
import {TablerinoColumn, TablerinoComponent, TablerinoSettings} from "../../../../@shared/components/tablerino/tablerino.component";
import {OrderPaymentResponse, OrderPaymentResponseCompact} from 'src/app/@shared/models/payment.interfaces';
import {currencyFormat, displayDate, paymentStatusBadge, UtilsService} from "../../../../@core/utils/utils.service";
import {PaymentDetailsComponent} from "../../../payments/components/payment-details/payment-details.component";
import {DetailsViewSettings} from "../../../../@shared/models/order.interfaces";
import {AffiliateResponse} from "../../../../@shared/models/affiliate.interfaces";
import {StorageService} from "../../../../@core/services/storage.service";
import {PaginationContainer} from "../../../../@shared/models/global.interfaces";
import {StandardImports} from "../../../../@shared/global_import";
import {CardComponent} from "../../../../@shared/components/layout/card/card.component";
import {TableFooterComponent} from "../../../../@shared/components/table-footer/table-footer.component";
import {PaymentDetailsV2Component} from "../../../payments/components/payment-details-v2/payment-details-v2.component";

export interface PaymentRow extends OrderPaymentResponse {
  selected: boolean;
  disableSelect: boolean;
}

@Component({
    selector: 'app-affiliate-consolidated-invoice-payments',
    templateUrl: './affiliate-consolidated-invoice-payments.component.html',
    styleUrls: ['./affiliate-consolidated-invoice-payments.component.css'],
    standalone: true,
  imports: [StandardImports, TablerinoComponent, CardComponent, TableFooterComponent]
})
export class AffiliateConsolidatedInvoicePaymentsComponent implements OnInit {
  @Input() isLoading = false;
  @Input() consolidatedInvoiceSetup: OrderPaymentResponseCompact;
  @Input() paymentRecipient?: AffiliateResponse;

  sendPaymentLoading: boolean = false;
  modalReference: NgbModalRef;
  payments: OrderPaymentResponseCompact[] = [];
  columnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);
  selectedRowsSubject: BehaviorSubject<PaymentRow[]> = new BehaviorSubject<PaymentRow[]>([]);
  paginationSubject: BehaviorSubject<PaginationContainer> = new BehaviorSubject<PaginationContainer>({page: 1, limit: 10, paginate: 1, totalPages: 0, totalItems: 0});
  disableConsolidateButton: boolean = true;
  settings: TablerinoSettings = {
    checkboxes: true,
    clickableRows: true,
  }
  operateExVat: boolean = false;

  @Output() paymentUpdated: EventEmitter<OrderPaymentResponseCompact> = new EventEmitter<OrderPaymentResponseCompact>();
  @Output() consInvoiceDeleted: EventEmitter<OrderPaymentResponseCompact> = new EventEmitter<OrderPaymentResponseCompact>();

  constructor(private storageService: StorageService, private modalService: NgbModal, private orderService: OrderService, private router: Router, private customerService: CustomerService, private paymentService: PaymentService, private utilsService: UtilsService) {
  }

  ngOnInit(): void {
    this.storageService.operateExVat$.subscribe((operateExVat) => {
      this.operateExVat = operateExVat;
    });
    this.initializeColumns();
    this.fetchPayments();
    this.paginationSubject.pipe(pairwise()).subscribe((paginationDetails) => {
      if (paginationDetails[0].page !== paginationDetails[1].page) {
        this.fetchPayments();
      }
    });
    this.paymentService.fetchAndUpdatePaymentMethods();
    this.selectedRowsSubject.subscribe((rows) => {
      this.disableConsolidateButton = rows.length <= 0;
    });
  }

  initializeColumns() {
    this.columnsSubject.next([
      {
        name: 'id',
        labelKey: 'ID',
        formatter: (payment: OrderPaymentResponseCompact) => '#' + payment.payment_number,
        sort: true,
        visible: true,
      },
      {
        name: 'active',
        labelKey: 'common.active',
        formatter: (payment: OrderPaymentResponseCompact) => this.formatActive(payment),
        sort: true,
        visible: true,
      },
      {
        name: 'payment_status_id',
        labelKey: 'payments.list.status',
        formatter: (payment: PaymentRow) => paymentStatusBadge(payment),
        sort: true,
        visible: true,
      },
      {
        name: 'payment_sent_at',
        labelKey: 'payments.list.paymentSentAt',
        formatter: (payment: PaymentRow) => displayDate(payment.payment_sent_at, false),
        sort: true,
        visible: true,
      },
      {
        name: 'auto_send_at',
        labelKey: 'payments.list.autoSendAt',
        formatter: (payment: PaymentRow) => payment.payment_sent_at ? '' : displayDate(payment.auto_send_at, false),
        sort: true,
        visible: true,
      },
      {
        name: 'createdAt',
        labelKey: 'payments.list.createdAt',
        formatter: (payment: OrderPaymentResponseCompact) => displayDate(payment.created_at, false),
        sort: true,
        visible: true,
      },
      {
        name: 'totalAmountIncVat',
        labelKey: 'payments.list.totalAmount',
        formatter: (payment: OrderPaymentResponseCompact) => currencyFormat(this.operateExVat ? payment.total_amount_ex_vat : payment.total_amount_inc_vat),
        sort: true,
        visible: true,
      },
    ]);
  }

  fetchPayments() {
    if (!this.consolidatedInvoiceSetup) return;

    this.isLoading = true;
    let params: _CRM_PAY_41 = {
      paginate: 0,
      parent_payment_id: this.consolidatedInvoiceSetup.payment_id,
    }
    this.paymentService.getCompanyPayments(params).subscribe(res => {
      this.paginationSubject.next({
        ...this.paginationSubject.value,
        totalItems: res.total_items,
        totalPages: res.total_pages
      });
      this.payments = res.data.map(payment => {
        return {
          ...payment,
          selected: false,
          disableSelect: !!payment.payment_sent_at || payment.total_amount_inc_vat === 0,
        }
      });
      this.isLoading = false;
    });
  }

  openPaymentDetails(payment: OrderPaymentResponseCompact) {
    let modalRef = this.modalService.open(PaymentDetailsV2Component, { size: 'lg' });
    modalRef.componentInstance.fetchPayment = true;
    modalRef.componentInstance.payment = payment;
    modalRef.componentInstance.viewSettings = {
      modalView: true,
      paymentView: true,
      paymentStandaloneView: true,
    }
    modalRef.componentInstance.paymentSent.subscribe((payment: OrderPaymentResponseCompact) => {
      this.fetchPayments();
    });
    modalRef.componentInstance.paymentDeleted.subscribe(() => {
      this.fetchPayments();
    });
  }

  editConsolidatedInvoice() {
    let modal = this.modalService.open(PaymentDetailsV2Component, { size: 'lg' });
    modal.componentInstance.viewSettings = {
      modalView: true,
      createView: false,
      consolidatedInvoiceView: true,
    };
    modal.componentInstance.payment = this.consolidatedInvoiceSetup;
    modal.componentInstance.fetchPayment = true;
    modal.componentInstance.paymentUpdated.subscribe((payment: OrderPaymentResponseCompact) => {
      this.consolidatedInvoiceSetup = payment;
    });
    modal.componentInstance.paymentDeleted.subscribe(() => {
      this.consInvoiceDeleted.emit(this.consolidatedInvoiceSetup);
    });
  }

  createConsolidatedInvoice() {
    let modal = this.modalService.open(PaymentDetailsV2Component, { size: 'lg' });
    modal.componentInstance.viewSettings = {
      modalView: true,
      createView: true,
      consolidatedInvoiceView: true,
    };
    modal.componentInstance.paymentRecipient = this.paymentRecipient;
    modal.componentInstance.paymentUpdated.subscribe((payment: OrderPaymentResponseCompact) => {
      this.consolidatedInvoiceSetup = payment;
      this.fetchPayments();
      this.paymentUpdated.emit(payment);
    });
  }

  formatActive(payment: OrderPaymentResponseCompact) {
    return !payment.payment_sent_at ? '<i class="fa-solid fa-check text-success"></i>' : ''
  }

  sendPayments() {
    this.sendPaymentLoading = true;
    let paymentIds = this.selectedRowsSubject.value.map(p => p.payment_id);
    const requests = paymentIds.map(paymentId => {
      let payload: _CRM_PAY_7 = {
        payment_id: paymentId,
      }
      return this.paymentService.sendToPayment(payload);
    });
    forkJoin(requests).subscribe(() => {
      this.sendPaymentLoading = false;
      this.fetchPayments();
      this.selectedRowsSubject.next([]);
    });

  }
}
