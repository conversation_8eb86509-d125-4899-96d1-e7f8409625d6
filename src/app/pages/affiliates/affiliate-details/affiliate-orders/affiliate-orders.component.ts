import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import { Ng<PERSON><PERSON>odal, NgbModalRef } from "@ng-bootstrap/ng-bootstrap";
import {Router} from "@angular/router";
import {_CRM_ORD_2} from 'src/app/@shared/models/input.interfaces';
import {FormControl, FormGroup} from "@angular/forms";
import { CustomerService } from 'src/app/@shared/services/customer.service';
import {ConfirmationStatusResponse, OrderResponse, OrderResponseCompact, OrderStatusResponse} from "../../../../@shared/models/order.interfaces";
import {PaginationResponse} from "../../../../@shared/models/response.interfaces";
import {OrderService} from "../../../../@shared/services/order.service";
import {SortEvent} from "../../../../@shared/components/advanced-table/sortable.directive";
import {BehaviorSubject, pairwise} from "rxjs";
import {PaginationContainer} from "../../../../@shared/models/global.interfaces";
import {TablerinoColumn, TablerinoComponent, TablerinoSettings} from "../../../../@shared/components/tablerino/tablerino.component";
import {Column} from "../../../../@shared/components/advanced-table/advanced-table.component";
import {StorageService} from "../../../../@core/services/storage.service";
import {currencyFormat, displayDate, formatDateDMY, orderBadgeStatus, paymentStatusBadge, UtilsService} from "../../../../@core/utils/utils.service";
import {TranslateService} from "@ngx-translate/core";
import {OrderPaymentResponseCompact} from "../../../../@shared/models/payment.interfaces";
import {PaymentRow} from "../affiliate-consolidated-invoice-payments/affiliate-consolidated-invoice-payments.component";
import {StandardImports} from "../../../../@shared/global_import";
import {CardComponent} from "../../../../@shared/components/layout/card/card.component";
import {TableFooterComponent} from "../../../../@shared/components/table-footer/table-footer.component";

@Component({
    selector: 'app-affiliate-orders',
    templateUrl: './affiliate-orders.component.html',
    styleUrls: ['./affiliate-orders.component.css'],
    standalone: true,
  imports: [StandardImports, CardComponent, TablerinoComponent, TableFooterComponent]
})
export class AffiliateOrdersComponent implements OnInit {
  @Input() paymentRecipientId: number;
  @Input() isLoading = false;
  @Output() onSort: EventEmitter<SortEvent> = new EventEmitter<SortEvent>();
  dataResponse: PaginationResponse<OrderResponse[]>;
  columnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);
  orderRows: OrderResponse[] = []
  operateExVat: boolean = false;
  settings: TablerinoSettings = {
    checkboxes: false,
    clickableRows: true,
  }
  paginationSubject: BehaviorSubject<PaginationContainer> = new BehaviorSubject<PaginationContainer>({page: 1, limit: 10, paginate: 1, totalPages: 0, totalItems: 0});

  constructor(private customerService: CustomerService, private storageService: StorageService, public utilsService: UtilsService, private translate: TranslateService, private router: Router) { }

  ngOnInit() {
    this.storageService.operateExVat$.subscribe((operateExVat) => {
      this.operateExVat = operateExVat;
    });

    this.fetchOrders('');
    this.paginationSubject.pipe(pairwise()).subscribe((paginationDetails) => {
      if (paginationDetails[0].page !== paginationDetails[1].page) {
        this.fetchOrders('');
      }
    });

    this.initializeColumns();
  };

  initializeColumns() {
    this.columnsSubject.next([
      {
        name: 'order_number',
        labelKey: 'orders.orderList.orderID',
        formatter: (order: OrderResponseCompact) => this.formatOrderNumber(order),
        sort: true,
        visible: true,
      },
      {
        name: 'order_title',
        labelKey: 'orders.orderList.title',
        formatter: (order: OrderResponseCompact) => order.order_title,
        sort: true,
        visible: true,
      },
      {
        name: 'execution_at',
        labelKey: 'orders.orderList.executionAt',
        formatter: (order: OrderResponseCompact) => displayDate(order.execution_at),
        sort: true,
        visible: true,
      },
      {
        name: 'address',
        labelKey: 'orders.orderList.address',
        formatter: (order: OrderResponseCompact) => order.display_address,
        sort: false,
        visible: true,
      },
      {
        name: 'order_status_id',
        labelKey: 'orders.orderList.status',
        formatter: (order: OrderResponseCompact) => orderBadgeStatus(order),
        sort: true,
        visible: true,
      },
      {
        name: 'payment_status_id',
        labelKey: 'orders.orderList.payment',
        formatter: (order: OrderResponseCompact) => order.repeating ? '' : paymentStatusBadge(order),
        sort: true,
        visible: true,
      },
      {
        name: 'total_amount_inc_vat',
        labelKey: 'orders.orderList.sum',
        formatter: (order: OrderResponseCompact) => this.formatOrderAmount(order),
        sort: false,
        visible: true,
      },
    ]);
  }

  fetchOrders(searchString: string) {
    this.isLoading = true;

    let params: _CRM_ORD_2 = {
      paginate: 1,
      page: this.paginationSubject.value.page,
      limit: this.paginationSubject.value.limit,
      payment_recipient_id: this.paymentRecipientId,
      search_string: searchString,
      archived: 2,
    }
    this.customerService.getCustomerOrders(params).subscribe(res => {
      this.dataResponse = res;
      this.paginationSubject.next({
        ...this.paginationSubject.value,
        totalItems: res.total_items,
        totalPages: res.total_pages
      });
      this.orderRows = res.data;
      this.isLoading = false;
    });

  }

  setOrderSymbols(order: OrderResponseCompact): string | void {
    let html = '';

    // Subcontracting
    if (order.has_subcontractors == 1 && order.company_id === this.storageService.getSelectedCompanyId()) {
      html += `  <i class="fa-regular fa-user-helmet-safety fa-lg"></i>`;
    }
    if (order.company_id != this.storageService.getSelectedCompanyId()) {
      html += `  <i class="fa-regular fa-user-tie-hair fa-lg"></i>`;
    }
    return html;
  }

  formatOrderNumber(order: OrderResponseCompact): string {
    let html = '<span>#' + order.order_number + '</span>';
    if (order.contains_active_work_order_schedules || order.contains_active_payment_schedules) {
      html += '<i class="fa fa-repeat text-success ms-1"></i>';
    } else if (order.contains_work_order_schedules || order.contains_payment_schedules || order.repeating) {
      html += '<i class="fa fa-repeat ms-1"></i>';
    }

    // Add symbols using setOrderSymbols method
    html += this.setOrderSymbols(order);

    return html;
  }

  formatOrderAmount(order: OrderResponseCompact): string {
    if (this.operateExVat) {
      if (order.repeating) {
        return currencyFormat(order.top_schedule_total_amount_ex_vat);
      } else {
        return currencyFormat(order.total_amount_ex_vat);
      }
    } else {
      if (order.repeating) {
        return currencyFormat(order.top_schedule_total_amount_inc_vat);
      } else {
        return currencyFormat(order.total_amount_inc_vat);
      }
    }
  }

  navigateToOrder(event: any) {
    this.router.navigateByUrl("orders/details/" + event.order_id)
  }

}
