import {Component, EventEmitter, Input, OnInit, Output, SimpleChanges} from '@angular/core';
import {OrderResponse, OrderResponseCompact, OrderScheduleResponse} from "../../../../@shared/models/order.interfaces";
import {SortEvent} from "../../../../@shared/components/advanced-table/sortable.directive";
import {Column} from "../../../../@shared/components/advanced-table/advanced-table.component";
import {currencyFormat, formatDateDMYHHMM, formatDateDMY, UtilsService, displayDate} from "../../../../@core/utils/utils.service";
import {TranslateService} from "@ngx-translate/core";
import {Router} from "@angular/router";
import {AdvancedTableModule} from "../../../../@shared/components/advanced-table/advanced-table.module";
import {DatePipe, NgIf} from "@angular/common";
import {StorageService} from "../../../../@core/services/storage.service";
import {OrderListService} from "../../../../@shared/services/order-list.service";
import {StandardImports} from "../../../../@shared/global_import";



@Component({
  selector: 'app-app-order-reports-list',
  templateUrl: './app-order-reports-list.component.html',
  styleUrl: './app-order-reports-list.component.css',
  standalone: true,
  imports: [StandardImports, AdvancedTableModule]
})
export class AppOrderReportsListComponent implements OnInit {
  @Input() orders: Array<OrderResponse> = [];
  @Input() isLoading: boolean = false;
  @Output() onSort: EventEmitter<SortEvent> = new EventEmitter<SortEvent>();

  accountingEnabled: boolean = false;
  columns: Array<Column> = [];

  constructor(
    public utilsService: UtilsService,
    private translate: TranslateService,
    private router: Router,
    private storageService: StorageService,
    private orderListService: OrderListService,
    private datePipe: DatePipe,
  ) {
  }

  ngOnInit() {
    this.storageService.accountingEnabled$.subscribe((value) => {
      this.accountingEnabled = value;
    });
    this.initializeColumns();
  }

  initializeColumns() {

    this.columns = [
      {
        name: 'order_number',
        label: this.translate.instant('orders.orderList.orderNumber'),
        formatter: (order: OrderResponse) => order.order_number,
        align: 'left',
        sort: false,
      },
      {
        name: 'total_amount_inc_vat',
        label: this.translate.instant('orders.orderList.total_amount_inc_vat'),
        formatter: (order: OrderResponse) => order.total_amount_inc_vat,
        align: 'left',
        sort: false,
      },
      {
        name: 'refunded_amount',
        label: this.translate.instant('orders.orderList.refunded_amount'),
        formatter: (order: OrderResponse) =>  currencyFormat(order.refunded_amount),
        align: 'center',
        sort: false
      },
      {
        name: 'calculated_discount_amount',
        label: this.translate.instant('orders.orderList.calculated_discount_amount'),
        formatter: (order: OrderResponse) => '', //order.calculated_discount_amount,
        align: 'left',
        sort: true
      },
      {
        name: 'sales_price',
        label: this.translate.instant('orders.orderList.sales_price'),
        formatter: (order: OrderResponse) => '', //currencyFormat(order.sales_price),
        sort: false,
      },
      {
        name: 'sales_price_vat_amount',
        label: this.translate.instant('orders.orderList.sales_price_vat_amount'),
        formatter: (order: OrderResponse) => '', //currencyFormat(order.sales_price_vat_amount),
        align: 'left',
        sort: true
      },
      {
        name: 'sales_price_ex_vat',
        label: this.translate.instant('orders.orderList.sales_price_ex_vat'),
        formatter: (order: OrderResponse) => '', //currencyFormat(order.sales_price_ex_vat),
        align: 'left',
        sort: true
      },
      {
        name: 'discount_reason',
        label: this.translate.instant('orders.orderList.discount_reason'),
        formatter: (order: OrderResponse) => '', //order.discount_reason,
        align: 'left',
        sort: true
      },
      {
        name: 'execution_at',
        label: this.translate.instant('orders.orderList.execution_at'),
        formatter: (order: OrderResponse) => displayDate(order.execution_at),
        align: 'left',
        sort: true
      },
      // {
      //   name: 'crew',
      //   label: this.translate.instant('orders.orderList.assignedCrew'),
      //   formatter: (order: OrderResponse) => {
      //     // Check if the order_lines array has a crew and map their full names
      //     return order.order_lines && order.order_lines.length > 0
      //       ? order.order_lines
      //         .map(line => line.crew && line.crew.map(member => member.full_name).join(', '))
      //         .filter(crewNames => crewNames)
      //         .join('; ')
      //       : 'No crew assigned';
      //   },
      //   align: 'left',
      //   sort: true
      // },
      {
        name: 'payment_recipient_name',
        label: this.translate.instant('orders.orderList.payment_recipient_name'),
        formatter: (order: OrderResponse) => order.payment_recipient?.name,
        align: 'left',
        sort: true
      },
      {
        name: 'service_recipient_name',
        label: this.translate.instant('orders.orderList.service_recipient_name'),
        formatter: (order: OrderResponse) => order.service_recipient?.name,
        align: 'left',
        sort: true
      },
      {
        name: 'order_status_name',
        label: this.translate.instant('orders.orderList.order_status_name'),
        formatter: (order: OrderResponse) => order.order_status_name,
        align: 'left',
        sort: true
      },
      {
        name: 'payment_status_name',
        label: this.translate.instant('orders.orderList.payment_status_name'),
        formatter: (order: OrderResponse) => order.payment_status_name,
        align: 'left',
        sort: true
      },
      {
        name: 'payment_method_name',
        label: this.translate.instant('orders.orderList.payment_method_name'),
        formatter: (order: OrderResponse) => '', //order.payment_method_name,
        align: 'left',
        sort: true
      },
      // {
      //   name: 'main_product_name',
      //   label: this.translate.instant('orders.orderList.main_product_name'),
      //   formatter: (order: OrderResponse) => order.main_product_name,
      //   align: 'left',
      //   sort: true
      // },
      {
        name: 'feedback_rating',
        label: this.translate.instant('orders.orderList.feedbackRating'),
        formatter: (order: OrderResponse) => '', //this.formatFeedbackRating(order.feedback_rating),
        align: 'center',
        sort: false
      },
      {
        name: 'quote_sent_at',
        label: this.translate.instant('orders.orderList.quote_sent_at'),
        formatter: (order: OrderResponse) => displayDate(order.quote_sent_at),
        align: 'center',
        sort: false
      },
      {
        name: 'invoice_sent_at',
        label: this.translate.instant('orders.orderList.invoice_sent_at'),
        formatter: (order: OrderResponse) =>  '', //this.utilsService.displayDate(order.invoice_sent_at),
        align: 'center',
        sort: false
      },
      {
        name: 'invoice_send_type_name',
        label: this.translate.instant('orders.orderList.invoice_send_type_name'),
        formatter: (order: OrderResponse) =>  '', //order.invoice_send_type_name,
        align: 'center',
        sort: false
      },
      {
        name: 'invoice_due_date_days',
        label: this.translate.instant('orders.orderList.invoice_due_date_days'),
        formatter: (order: OrderResponse) =>  '', //order.invoice_due_date_days,
        align: 'center',
        sort: false
      },
      {
        name: 'invoice_email',
        label: this.translate.instant('orders.orderList.invoice_email'),
        formatter: (order: OrderResponse) =>  '', //order.invoice_email,
        align: 'center',
        sort: false
      },
      {
        name: 'invoice_reference_text',
        label: this.translate.instant('orders.orderList.invoice_reference_text'),
        formatter: (order: OrderResponse) =>  '', //order.invoice_reference_text,
        align: 'center',
        sort: false
      },
      {
        name: 'captured_at',
        label: this.translate.instant('orders.orderList.captured_at'),
        formatter: (order: OrderResponse) =>  '', //this.utilsService.displayDate(order.captured_at),
        align: 'center',
        sort: false
      },
      {
        name: 'created_at',
        label: this.translate.instant('orders.orderList.created_at'),
        formatter: (order: OrderResponse) =>  displayDate(order.created_at),
        align: 'center',
        sort: false
      },
    ];

    // // Save the columns to orderListService
    this.orderListService.setColumns(this.columns);
  }

  // drop(event: CdkDragDrop<string[]>) {
  //   moveItemInArray(this.columns, event.previousIndex, event.currentIndex);
  //   this.orderListService.setColumns(this.columns);
  // }

  sort(event: SortEvent) {
    if (!event) {
      return;
    }
    this.onSort.emit(event);
  }

  orderCustomerFormatter(order: OrderResponse) {
    let html = '';
    if (order.payment_recipient?.is_private != 1) {
      html += `<i class="fa-regular fa-buildings fa-lg me-1"></i>`;
    }
    html += order.payment_recipient?.name;
    return html;
  }

  setOrderSymbols(order: OrderResponse): string | void {
    let html = '';

    // Subcontracting
    // if (order.has_subcontractors == 1 && order.company_id === this.storageService.getSelectedCompanyId()) {
    //   html += `  <i class="fa-regular fa-user-helmet-safety fa-lg"></i>`;
    // }
    if (order.company_id != this.storageService.getSelectedCompanyId()) {
      html += `  <i class="fa-regular fa-user-tie-hair fa-lg"></i>`;
    }
    return html;
  }

  orderCustomerWithSymbolsFormatter(order: OrderResponse): string {
    let html = '';
    // Add customer name
    html += `${order.payment_recipient?.name}`;

    // Add symbols using setOrderSymbols method
    html += this.setOrderSymbols(order);

    // Add icon for non-private customers
    if (order.payment_recipient?.is_private != 1) {
      html += `  <i class="fa-regular fa-buildings fa-lg me-1"></i>`;
    }

    return html;
  }

  orderServiceRecipientWithSymbolsFormatter(order: OrderResponse): string {
    let html = '';

    if (order.service_recipient) {
      html += `${order.service_recipient.name}`;

      html += this.setOrderSymbols(order);

      if (order.service_recipient.is_private != 1) {
        html += `  <i class="fa-regular fa-buildings fa-lg me-1"></i>`;
      }
    } else {
      html += this.translate.instant('common.noData');
    }

    return html;
  }


  navigateToOrder(event: any) {
    this.router.navigateByUrl("orders/details/" + event.order_id)
  }

  formatFeedbackRating(rating: number): string {
    if (rating === null || rating === undefined) {
      return 'No rating'; // Handle cases where rating is not provided
    }

    // Define the full star and empty star icons with appropriate colors
    const fullStar = '<i class="fa fa-star" style="color: gold;"></i>'; // Filled star icon
    const emptyStar = '<i class="fa fa-star-o" style="color: gray;"></i>'; // Empty star icon

    let stars = '';
    for (let i = 1; i <= 5; i++) {
      // Add a filled star if the current index is less than or equal to the rating
      stars += i <= rating ? fullStar : emptyStar;
    }

    return stars;
  }
}
