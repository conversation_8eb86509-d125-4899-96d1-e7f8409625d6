<!-- Loading Spinner -->
<div class="table-loader" *ngIf="isLoading || isCancellingOrders">
  <div class="spinner-border my-3" role="status">
    <span class="visually-hidden">Loading...</span>
    <span class="spinner-border-sm"></span>
  </div>
</div>

<div *ngIf="!isLoading && !isCancellingOrders" class="table-scroll-container">
  <ng-container *ngIf="(tableData && tableData.length > 0) && (columns && columns.length > 0); else noDataTemplate">

    <div class="table-responsive">
      <!-- Table Header -->
      <div class="table-row table-header">
        <div
          *ngFor="let column of columns; let i = index"
          [ngClass]="{
      'first-visible': isFirstVisible(i),
      'last-visible': isLastVisible(i)}"
          [style.display]="columnVisibility[column.name] ? 'table-cell' : 'none'"
          [style.width.px]="columnWidths[i]"
          class="table-cell header-cell"
        >
          <div class="header-content">
            <span>{{ column.label }}</span>
          </div>
        </div>
      </div>
      <!-- Table Body -->
      <div
        *ngFor="let row of tableData; let rowIndex = index"
        class="table-row">
        <div
          *ngFor="let column of columns; let i = index"
          [ngClass]="{
            'first-visible': isFirstVisible(i),
            'last-visible': isLastVisible(i)
          }"
          [style.display]="columnVisibility[column.name] ? 'table-cell' : 'none'"
          [style.width.px]="columnWidths[i]"
          class="table-cell"
        >
          <!-- Customer Column -->
          <ng-container *ngIf="column.name === 'customer.name'; else serviceCell">
            <div
              class="cell-content"
              (mouseleave)="hideTooltip()">
              <span [innerHTML]="callFormatter(column, row)"></span>
              <i class="fa fa-chevron-down customer-chevron" (click)="toggleTooltip($event, row, 'payment')"></i>
            </div>
          </ng-container>

          <!-- Service Recipient Column -->
          <ng-template #serviceCell>
            <ng-container *ngIf="column.name === 'service_recipient'; else feedbackCell">
              <div
                class="cell-content"
                (mouseleave)="hideTooltip()"
              >
                <span [innerHTML]="callFormatter(column, row)"></span>
                <i class="fa fa-chevron-down customer-chevron" (click)="toggleTooltip($event, row, 'service')"></i>
              </div>
            </ng-container>
          </ng-template>

          <ng-template #feedbackCell>
            <ng-container *ngIf="column.name === 'feedback_rating'; else defaultCell">
              <div
                class="cell-content"
                (click)="toggleTooltip($event, row, 'feedback')"
                (mouseleave)="hideTooltip()">
                <div class="title" *ngIf="row.feedback_rating; else noRating">
                  <span class="star" *ngFor="let star of stars">
                    <i class="fa-regular fa-star" *ngIf="star > row.feedback_rating"></i>
                    <i class="fa-solid fa-star" style="color: #FFD700;" *ngIf="star <= row.feedback_rating"></i>
                  </span>
                  <i class="fa-regular fa-comment ms-1" *ngIf="row.feedback_comment"></i>
                </div>
              </div>
              <!-- Fallback for no rating -->
              <ng-template #noRating>
                <span>{{ "orders.orderList.feedbackRating.tooltip.noRating" | translate }}</span>
              </ng-template>
            </ng-container>

            <ng-template #defaultCell>
              <div [innerHTML]="callFormatter(column, row)"></div>
            </ng-template>
          </ng-template>

        </div>
      </div>

      <!-- Table Footer (Sum Row) -->
      <div class="table-row table-footer">
        <div
          *ngFor="let column of columns; let i = index"
          [ngClass]="{
      'first-visible': isFirstVisible(i),
      'last-visible': isLastVisible(i)
    }"
          [style.display]="columnVisibility[column.name] ? 'table-cell' : 'none'"
          [style.width.px]="columnWidths[i]"
          class="table-cell"
        >
          <div *ngIf="isFirstVisible(i)">
            <span class="fw-bold" style="color: #333;">{{"orders.orderList.total" | translate}}</span>
          </div>
          <div *ngIf="isNumericColumn(column) && !isFirstVisible(i)">
            <span class="fw-bold" style="color: #333;"> {{ calculateColumnSum(column.name) }}</span>
          </div>
        </div>
      </div>
    </div>

  </ng-container>

  <!-- No Data Template -->
  <ng-template #noDataTemplate>
    <div class="text-center d-flex justify-content-center align-items-center no-data-row" style="width: 100%; height: 200px;">
      <p>{{ "orders.orderList.noData" | translate }}</p>
    </div>
  </ng-template>

  <!-- Tooltip -->
  <div
    class="customer-tooltip table-font-size"
    *ngIf="tooltipVisible && (tooltipType !== 'service' || tooltipRow?.service_recipient)"
    [style.top.px]="tooltipPosition.top"
    [style.left.px]="tooltipPosition.left"
    (mouseenter)="keepTooltipVisible()"
    (mouseleave)="hideTooltip()"
  >
    <div *ngIf="tooltipType === 'feedback'">
      <div class="fw-bolder">{{"orders.orderList.feedbackRating.tooltip" | translate}}</div>
      <div class="fs-6 fw-normal text-muted mb-1">
        {{ tooltipRow?.feedback_comment || ("orders.orderList.feedbackRating.tooltip.noComment" | translate) }}
      </div>
    </div>

    <div *ngIf="tooltipType !== 'feedback'">
      <div class="fw-bolder">
        {{ tooltipType === 'payment' ? (tooltipRow?.payment_recipient?.name || 'Unknown') : (tooltipRow?.service_recipient?.name || 'Unknown') }}
      </div>
      <div *ngIf="tooltipType === 'payment' && tooltipRow?.payment_recipient?.organisation_number" class="fs-6 fw-normal text-muted mb-1">
        {{ tooltipRow?.payment_recipient?.organisation_number }}
      </div>
      <div *ngIf="tooltipType === 'service' && tooltipRow?.service_recipient?.organisation_number" class="fs-6 fw-normal text-muted mb-1">
        {{ tooltipRow?.service_recipient?.organisation_number }}
      </div>
      <div class="fw-light">
        {{ tooltipType === 'payment' ? displayPhoneNumber(tooltipRow?.payment_recipient?.phone) || 'Unknown' : displayPhoneNumber(tooltipRow?.service_recipient?.phone) || 'Unknown' }}
      </div>
      <div class="d-flex align-items-center justify-content-between">
        <span class="between-color">{{ tooltipType === 'payment' ? (tooltipRow?.payment_recipient?.email || 'Unknown') : (tooltipRow?.service_recipient?.email || 'Unknown') }}</span>
        <i class="fa-regular fa-copy" style="cursor: pointer;" (click)="copyToClipboard(tooltipType === 'payment' ? tooltipRow?.payment_recipient?.email : tooltipRow?.service_recipient?.email)" title="Copy email"></i>
      </div>
      <app-button [small]="true" [buttonType]="'nude'" [translationKey]="'orders.orderList.customer.viewCustomer'" [customClass]="'mt-3 custom-button w-100'" (buttonClick)="onTooltipButtonClick(tooltipType === 'payment' ? tooltipRow?.payment_recipient : tooltipRow?.service_recipient)"></app-button>
    </div>
  </div>
</div>
