import { Component, OnInit } from '@angular/core';

import { CommonModule, NgForOf, NgIf } from "@angular/common";
import { DateRangePickerComponent } from "../../../@shared/components/date-range-picker/date-range-picker.component";
import { TranslateModule } from "@ngx-translate/core";
import { PaginationResponse } from "../../../@shared/models/response.interfaces";
import { UserEntityRelationWithUserDataResponse } from "../../../@shared/models/user.interfaces";
import {formatDateYMD, UtilsService} from "../../../@core/utils/utils.service";
import moment from "moment";
import { ResourceService } from "../../../@shared/services/resource.service";
import { EmployeeSalesResponse } from "../../../@shared/models/resources.interfaces";
import {StandardImports} from "../../../@shared/global_import";

@Component({
    selector: 'app-employee-reports',
    templateUrl: './employee-reports.component.html',
    styleUrls: ['./employee-reports.component.css'],
    standalone: true,
  imports: [StandardImports, DateRangePickerComponent]
})
export class EmployeeReportsComponent implements OnInit {
  items: any[] = [];
  addModal: boolean = false;
  dateFrom: string;
  dateTo: string;
  dateFromDate: Date;
  dateToDate: Date;
  employees: PaginationResponse<UserEntityRelationWithUserDataResponse[]>;
  employee: UserEntityRelationWithUserDataResponse;
  employeeSales: EmployeeSalesResponse[] = [];
  loading: boolean = false;

  constructor(public utilsService: UtilsService, private resourceService: ResourceService) {}

  ngOnInit(): void {
    // this.setDefaultDateRangeToThisWeek();
    this.dateFrom = formatDateYMD(new Date(moment().startOf('month').format('YYYY-MM-DD')));
    this.dateTo = formatDateYMD(new Date(moment().endOf('month').format('YYYY-MM-DD')));
    this.dateFromDate = new Date(this.dateFrom);
    this.dateToDate = new Date(this.dateTo);
    this.fetchTimeTracking();
  }

  setDefaultDateRangeToThisWeek() {
    const now = new Date();
    const dayOfWeek = now.getDay();
    const diffToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
    const monday = new Date(now.setDate(now.getDate() + diffToMonday));
    const sunday = new Date(monday);
    sunday.setDate(monday.getDate() + 6);

    this.dateFromDate = new Date(monday.setHours(0, 0, 0, 0));
    this.dateToDate = new Date(sunday.setHours(23, 59, 59, 999));
    this.fetchTimeTracking();
  }

  updateDates(event: any) {
    console.log('updateDates called', event);
    this.dateFrom = formatDateYMD(new Date(moment(event.from).format("YYYY-MM-DD")));
    this.dateTo = formatDateYMD(new Date(moment(event.to).format("YYYY-MM-DD")));
    this.dateFromDate = new Date(this.dateFrom);
    this.dateToDate = new Date(this.dateTo);
    this.fetchTimeTracking();
  }

  fetchTimeTracking() {
    this.loading = true;
    const payload = {
      date_from: this.dateFromDate,
      date_to: this.dateToDate
    };

    this.resourceService.getSalesEmoloyees(payload).subscribe(
      (res: EmployeeSalesResponse[]) => {
        this.employeeSales = res;
        this.loading = false;
      },
      (error) => {
        console.error(error);
        this.loading = false;
      }
    );
  }

  getSumExecNumSales(): number {
    return this.employeeSales.reduce((sum, employee) => sum + employee.exec_num_sales, 0);
  }

  getSumExecSalesIncVat(): number {
    return this.employeeSales.reduce((sum, employee) => sum + employee.exec_total_sales_inc_vat, 0);
  }

  getSumExecSalesExVat(): number {
    return this.employeeSales.reduce((sum, employee) => sum + employee.exec_total_sales_ex_vat, 0);
  }

  getSumSentNumSales(): number {
    return this.employeeSales.reduce((sum, employee) => sum + employee.sent_num_sales, 0);
  }

  getSumSentSalesIncVat(): number {
    return this.employeeSales.reduce((sum, employee) => sum + employee.sent_total_sales_inc_vat, 0);
  }

  getSumSentSalesExVat(): number {
    return this.employeeSales.reduce((sum, employee) => sum + employee.sent_total_sales_ex_vat, 0);
  }

  getSumNumSales(): number {
    return this.employeeSales.reduce((sum, employee) => sum + employee.num_sales, 0);
  }

  getSumSalesIncVat(): number {
    return this.employeeSales.reduce((sum, employee) => sum + employee.total_sales_inc_vat, 0);
  }

  getSumSalesExVat(): number {
    return this.employeeSales.reduce((sum, employee) => sum + employee.total_sales_ex_vat, 0);
  }
}
