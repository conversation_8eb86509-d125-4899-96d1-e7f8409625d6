<div *ngIf="timeTracking" class="hidden-id">{{timeTracking.entry_id}}</div>
<div class="modal-header d-flex justify-content-between align-items-center">
  <i class="fa-regular fa-xmark fa-xl" (click)="activeModal.close()" style="font-weight: 400; cursor: pointer"></i>
  <h4 *ngIf="!timeTracking" class="text-center" style="flex-grow: 1;">{{"reports.timeTracking.modal.title" | translate }}</h4>
  <h4 *ngIf="timeTracking" class="text-center" style="flex-grow: 1;">{{"reports.timeTracking.modal.editTitle" | translate }}</h4>
  <i *ngIf="locked" class="fa-solid fa-lock fa-xl cursor-pointer"></i>
</div>

<div class="modal-body p-3">
  <form class="col" [formGroup]="timeTrackingForm">
    <div class="row">
      <!-- Duration -->
      <div class="col col-md-6 mb-2 ">
        <div class="form-group required">
          <label class="form-label mb-0 position-relative">{{ "reports.timeTracking.modal.duration" | translate }}</label>
          <div class="row gx-2">
            <div class="col input-group flex-nowrap">
              <input type="number" formControlName="duration_hours" class="form-control" placeholder="Hours" required>
              <span class="input-group-text" id="basic-addon1">{{"HOUR-ABBREVIATION" | translate}}</span>
            </div>
            <div class="col input-group flex-nowrap">
              <input type="number" formControlName="duration_minutes" class="form-control" placeholder="Minutes" required min="0" max="59">
              <span class="input-group-text" id="basic-addon2">m</span>
            </div>
          </div>
          <div *ngIf="(timeTrackingForm.controls['duration_hours'].invalid && timeTrackingForm.controls['duration_hours'].touched) || (timeTrackingForm.controls['duration_minutes'].invalid && timeTrackingForm.controls['duration_minutes'].touched)" class="text-danger">
            {{ "orders.orderDetails.detailsCard.required" | translate }}
          </div>
        </div>
      </div>

      <!-- Date -->
      <div class="col col-md-6 mb-2">
        <div class="form-group required pb-0">
          <label for="date" class="form-label mb-0 position-relative">{{ "reports.timeTracking.modal.date" | translate }}</label>
          <input type="date" [value]="timeTrackingForm.get('date')!.value | date:'yyyy-MM-dd'" formControlName="date" class="form-control" required="true" id="date" max="2099-01-19">
          <div *ngIf="timeTrackingForm?.get('date')?.errors?.['dateExceeded']" class="text-danger mt-1">
            {{ "orders.orderDetails.detailsCard.date" | translate }}
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <!-- Start Time -->
      <div id="startTimeDiv" class="col col-md-6">
        <label class="mb-0 form-label" for="startTimeInput">{{ "reports.timeTracking.modal.start" | translate }}</label>
        <input id="startTimeInput" type="time" class="form-control form-control-sm" formControlName="start_time" style="height: 36px;">
      </div>

      <!-- Stop Time -->
      <div id="stopTimeDiv" class="col col-md-6">
        <label class="mb-0 form-label" for="stopTimeInput">{{ "reports.timeTracking.modal.stop" | translate }}</label>
        <input id="stopTimeInput" type="time" class="form-control form-control-sm" formControlName="stop_time" style="height: 36px;">
      </div>
    </div>

    <!-- Description -->
    <div class="col col-md-12 mb-2 mt-2">
      <div class="form-group required">
        <label class="form-label mb-0">{{ "reports.timeTracking.modal.description" | translate }}</label>
        <app-input
          [control]="timeTrackingForm.controls['description']"
          [textArea]="true"
          [editMode]="true"
        ></app-input>
      </div>
    </div>

    <!-- Description -->
    <div class="col col-md-12 mb-2 mt-2">
      <div class="form-group required">
        <label class="form-label mb-0">{{ "reports.timeTracking.modal.comment" | translate }}</label>
        <app-input
          [control]="timeTrackingForm.controls['comment']"
          [textArea]="true"
          [editMode]="true"
        ></app-input>
      </div>
    </div>

    <!-- Description -->
    <div class="col col-md-12 mb-2 mt-2">
      <app-toggle-switch
        [isDisabled]="locked"
        [labelKey]="'reports.timeTracking.modal.useInSalary'"
        [control]="timeTrackingForm.controls['use_in_salary']"
      ></app-toggle-switch>
    </div>


    <div class="col-12 d-flex flex-column mt-2">
      <label>{{'reports.timeTracking.modal.employee' | translate}}</label>
      <app-selectorini
        [disabled]="!!preselectedEmployee || locked"
        [zIndex]="50"
        [itemIdKey]="'user_id'"
        [multiSelect]="false"
        [searchMainDisplayKeys]="['full_name']"
        [placeholderTranslationKey]="'addOrder.assign.usersPlaceholder'"
        [initialsKeys]="['full_name']"
        [itemImageKey]="'profile_image_url'"
        [predefinedSearchResults]="employees"
        [predefinedSearchKeys]="['full_name']"
        [selectedItem]="preselectedEmployee"
        [maxWidthPercentage]="20"
        (itemSelectedEmitter)="onSelectedUsersChange($event)"
      ></app-selectorini>
      <div *ngIf="timeTrackingForm.controls['employee'].invalid && timeTrackingForm.controls['employee'].touched" class="text-danger">
        {{ "orders.orderDetails.detailsCard.required" | translate }}
      </div>

      <div class="mt-2">
        <label>{{'reports.timeTracking.modal.job' | translate}}</label>
        <app-selectorini
          [disabled]="locked"
          [multiSelect]="false"
          [selectedItem]="selectedWorkOrder"
          [searchFunction]="searchWorkOrders.bind(this)"
          [predefinedSearchResults]="preloadedWorkOrders"
          [searchMainDisplayKeys]="['work_order_number', 'work_order_title']"
          [searchSubDisplayKeys]="['payment_recipient_name']"
          [placeholderTranslationKey]="'Søk etter tittel eller nummer på jobb'"
          [maxWidthPercentage]="20"
          (itemSelectedEmitter)="onSelectedJobChange($event)"
        ></app-selectorini>
      </div>

      <div class="mt-2">
        <label>{{'reports.timeTracking.modal.activity' | translate}}</label>
        <app-selectorini
          [disabled]="locked"
          [multiSelect]="false"
          [selectedItem]="selectedActivity"
          [directSelection]="true"
          [searchMainDisplayKeys]="['activity_name']"
          [placeholderTranslationKey]="'Velg aktivitet'"
          [predefinedSearchResults]="activities"
          [maxWidthPercentage]="20"
          (itemSelectedEmitter)="onSelectedActivityChange($event)"
        ></app-selectorini>
      </div>

    </div>
  </form>

  <div *ngIf="timeTracking" class="mt-2">
    <h5 class="my-0">Føringstype</h5>
    <div *ngIf="timeTracking.auto_registered" class="">Automatisk registrert fra oppdrag</div>
    <div *ngIf="!timeTracking.auto_registered && timeTracking.entry_id" class="">Manuell føring</div>
    <div *ngIf="!timeTracking.entry_id" class="">Planlagt arbeidstid, ikke ført enda</div>
  </div>

  <h5 *ngIf="children.length > 0" class="mt-2 mb-0">Registrerte føringer</h5>
  <div *ngFor="let tracking of children" class="child-box mt-1">
    <div class="d-flex">
      <span>{{ tracking.started_at | date:'shortTime' }} - {{ tracking.stopped_at | date:'shortTime' }}</span>
      <span class="mx-1">=</span>
      <span>{{utilsService.formatDurationFromHours(tracking.duration_in_seconds)}}</span>
    </div>
  </div>

  <h5 *ngIf="logs.length > 0" class="mt-2 mb-0">Registerte endringer</h5>
  <div *ngFor="let logEntry of logs" class="log-box mt-1">
    <ul class="my-0">
      <li *ngFor="let change of logEntry.changes" class="my-0 py-0" style="white-space: pre-wrap;">
        {{change}}
      </li>
    </ul>
    <div class="d-flex justify-content-end font-12 mt-1">{{logEntry.set_by.full_name}} - {{displayDate(logEntry.set_at)}}</div>
  </div>
</div>


<div class="modal-footer pe-3 justify-content-between">
  <div>
    <app-button
      [themeStyle]="'danger'"
      [translationKey]="'common.delete'"
      *ngIf="timeTracking && !locked"
      (buttonClick)="deleteTimeTracking()"
    ></app-button>
  </div>
  <div class="d-flex gap-1">
    <app-button
      *ngIf="!locked"
      (buttonClick)="saveClick()"
      [disabled]="timeTrackingForm.invalid || !selectedActivity"
      [themeStyle]="'primary'"
      [translationKey]="'common.save'"
      [loading]="loading"
    ></app-button>
    <app-button
      *ngIf="locked"
      (buttonClick)="unlockTimeTracking()"
      [themeStyle]="'primary'"
      [translationKey]="'Lås opp'"
      [loading]="unlockLoading"
    ></app-button>
    <app-button
      (buttonClick)="activeModal.close()"
      [themeStyle]="'secondary'"
      [translationKey]="'common.close'"
      [disabled]="loading"
    ></app-button>
  </div>
</div>
