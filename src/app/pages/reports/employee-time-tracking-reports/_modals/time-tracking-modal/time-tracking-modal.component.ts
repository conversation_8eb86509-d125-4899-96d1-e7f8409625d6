import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {AbstractControl, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators} from "@angular/forms";
import {TrackingService} from "../../../../../@shared/services/tracking.service";
import {
  _CRM_EMP_0, _CRM_ORD_170, _CRM_TTR_0, _CRM_TTR_2,
  _USM_ENT_0,
  CRM_TTR_0,
  CRM_TTR_2,
  CRM_TTR_3
} from "../../../../../@shared/models/input.interfaces";
import {PaginationResponse} from "../../../../../@shared/models/response.interfaces";
import {
  InternalUserResponse,
  UserEntityRelationWithUserDataResponse
} from "../../../../../@shared/models/user.interfaces";
import {displayDate, UtilsService} from "../../../../../@core/utils/utils.service";
import {StorageService} from "../../../../../@core/services/storage.service";
import {EmployeeService} from "../../../../../@shared/services/employee.service";
import {TimeTrackingActivityResponse, TimeTrackingLogResponse, TimeTrackingResponse, TrackingEntryResponse} from "../../../../../@shared/models/timetracking.interfaces";
import moment from "moment";
import {ToastService} from "../../../../../@core/services/toast.service";
import {WorkOrderCompactResponse, WorkOrderResponse} from "../../../../../@shared/models/order.interfaces";
import {OrderService} from "../../../../../@shared/services/order.service";
import {SettingsService} from "../../../../../@shared/services/settings.service";
import {combineLatest} from "rxjs";
import {VerifyPopupModal} from "../../../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {StandardImports} from "../../../../../@shared/global_import";
import {ToggleSwitchComponent} from "../../../../../@shared/components/toggle-switch/toggle-switch.component";
import {SelectoriniComponent} from "../../../../../@shared/components/selectorini/selectorini.component";


@Component({
    selector: 'app-time-tracking-modal',
    templateUrl: './time-tracking-modal.component.html',
    styleUrls: ['./time-tracking-modal.component.css'],
    standalone: true,
  imports: [StandardImports, ToggleSwitchComponent, SelectoriniComponent]
})
export class TimeTrackingModalComponent implements OnInit {
  constructor(
    public activeModal: NgbActiveModal,
    private trackingService: TrackingService,
    protected utilsService: UtilsService,
    private storageService: StorageService,
    private employeeService: EmployeeService,
    private toastService: ToastService,
    public orderService: OrderService,
    private settingsService: SettingsService,
    private modalService: NgbModal
  ) {}

  @Input() preselectedEmployee: InternalUserResponse | null;
  @Input() timeTracking: TimeTrackingResponse;

  employees: InternalUserResponse[];
  employee: InternalUserResponse | null = null;
  registrationDate: Date;
  startTime: string;
  description: string;
  stopTime: string;
  duration: number;
  loading: boolean = false;
  unlockLoading: boolean = false;
  duration_hours: number;
  duration_minutes: number;
  activities: TimeTrackingActivityResponse[] = [];
  locked: boolean = false;
  selectedActivity: TimeTrackingActivityResponse | null = null;
  selectedWorkOrder: WorkOrderResponse | null = null;
  logs: TimeTrackingLogResponse[] = [];
  children: TimeTrackingResponse[] = [];
  preloadedWorkOrders: WorkOrderCompactResponse[] = [];

  @Output() timeTrackingUnlocked: EventEmitter<any> = new EventEmitter();

  timeTrackingForm = new FormGroup(
    {
      date: new FormControl(null as string | null, [this.dateValidator.bind(this)]),
      start_time: new FormControl(),
      stop_time: new FormControl(),
      description: new FormControl(),
      duration_hours: new FormControl(0, [Validators.min(0)]),
      duration_minutes: new FormControl(0, [Validators.min(0), Validators.max(59)]),
      employee: new FormControl<InternalUserResponse | null>(null),
      comment: new FormControl(),
      use_in_salary: new FormControl(false),
    },
    { validators: this.timeValidator.bind(this) }
  );

  ngOnInit(): void {
    if (this.timeTracking && this.timeTracking.entry_id) {
      this.trackingService.getTrackingLogs(this.timeTracking.entry_id).subscribe((res) => {
        this.logs = res;
      });
      this.trackingService.getTimeTrackingChildren(this.timeTracking.entry_id).subscribe((res) => {
        this.children = res;
      });
    }

    this.trackingService.getTimeTrackingActivities().subscribe((activities) => {
      this.activities = activities;
      if (this.timeTracking && this.timeTracking.activity_id) {
        this.selectedActivity = this.activities.find((activity) => activity.activity_id === this.timeTracking.activity_id) || null;
      } else {
        this.selectedActivity = this.activities.find((activity) => activity.is_default) || null;
      }

    });

    if (this.timeTracking?.work_order_id) {
      this.orderService.getWorkOrderById(this.timeTracking.work_order_id).subscribe((res) => {
        this.selectedWorkOrder = res;
      });
    }

    this.locked = this.timeTracking?.locked || false;

    if (this.locked) {
      this.timeTrackingForm.disable();
    }

    if (!this.timeTracking) {
      this.employeeService.getEmployees({}).subscribe((res) => {
        this.employees = res.data;
      });
    }

    this.registrationDate = this.timeTracking?.started_at || new Date();
    this.startTime = this.timeTracking?.started_at ? this.formatTime(this.timeTracking?.started_at) : '08:00';
    this.stopTime = this.timeTracking?.stopped_at ? this.formatTime(this.timeTracking?.stopped_at) : '16:00';
    this.description = this.timeTracking?.description || '';

    if (!this.timeTracking?.work_order_id) {
      this.preloadWorkOrders();
    }

    this.setDurationFromTimes(this.startTime, this.stopTime);

    this.timeTrackingForm.setValue({
      date: this.registrationDate.toISOString().split('T')[0],
      start_time: this.startTime,
      stop_time: this.stopTime,
      description: this.description,
      duration_hours: this.duration_hours,
      duration_minutes: this.duration_minutes,
      employee: this.preselectedEmployee ? this.preselectedEmployee : null,
      comment: this.timeTracking?.comment || '',
      use_in_salary: this.timeTracking?.use_in_salary || false
    });

    if (!this.timeTracking) {
      this.timeTrackingForm.get('employee')?.setValidators([Validators.required]);
    }

    // Add valueChanges subscriptions
    this.timeTrackingForm.get('duration_hours')?.valueChanges.subscribe(() => {
      this.updateStopTime();
    });

    this.timeTrackingForm.get('duration_minutes')?.valueChanges.subscribe(() => {
      this.updateStopTime();
    });

    this.timeTrackingForm.get('start_time')?.valueChanges.subscribe(() => {
      this.updateStopTime();
    });

    this.timeTrackingForm.controls['date'].valueChanges.subscribe((value) => {
      this.registrationDate = new Date(value!);
      this.preloadWorkOrders();
    });
  }

  preloadWorkOrders() {
    this.searchWorkOrders('').subscribe((res) => {
      this.preloadedWorkOrders = res;
    });
  }

  saveClick() {
    if (!this.preselectedEmployee && !this.employee) {
      this.timeTrackingForm.get('employee')?.setErrors({ required: true });
      return
    }
    this.loading = true;
    const date = this.timeTrackingForm.get('date')!.value; // yyyy-MM-dd
    const startTime = this.timeTrackingForm.get('start_time')!.value; // HH:mm
    const stopTime = this.timeTrackingForm.get('stop_time')!.value; // HH:mm

    const startedAt = new Date(`${date}T${startTime}:00`);
    const stoppedAt = new Date(`${date}T${stopTime}:00`);

    if (!this.timeTracking) {
      let payload: _CRM_TTR_0 = {
        description: this.timeTrackingForm.get('description')?.value,
        user_id: (this.preselectedEmployee?.user_id || this.employee?.user_id)!, // Assumes `employee` will be defined when `addModal` is true
        started_at: startedAt,
        stopped_at: stoppedAt,
        work_order_id: this.selectedWorkOrder?.work_order_id || null,
        activity_id: this.selectedActivity?.activity_id!,
        comment: this.timeTrackingForm.get('comment')?.value,
        use_in_salary: this.timeTrackingForm.get('use_in_salary')?.value || false,
        project_id: null,
        department_id: null,
      };

      this.trackingService.createTimeTracking(payload).subscribe({
        next: (res) => {
          this.loading = false;
        },
        error: (error) => {
          this.activeModal.dismiss('cancel');
          this.loading = false;
        },
        complete: () => {
          this.toastService.successToast("timeTracking_created")
          this.activeModal.close('success');
          this.loading = false;
        },
      });
    }

    if (this.timeTracking && this.timeTracking.entry_id !== null) {
      let payload: _CRM_TTR_2 = {
        description: this.timeTrackingForm.get('description')?.value,
        entry_id: this.timeTracking.entry_id,
        started_at: startedAt,
        stopped_at: stoppedAt,
        work_order_id: this.selectedWorkOrder?.work_order_id || null,
        activity_id: this.selectedActivity?.activity_id!,
        comment: this.timeTrackingForm.get('comment')?.value,
        use_in_salary: this.timeTrackingForm.get('use_in_salary')?.value || false
      };

      this.trackingService.editTimeTracking(payload).subscribe({
        next: (res) => {
        },
        error: (error) => {
          this.activeModal.dismiss('cancel');
        },
        complete: () => {
          this.toastService.successToast("timeTracking_updated")
          this.activeModal.close('success');
          this.loading = false;
        },
      });
    }
  }

  dateValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const inputDate = new Date(control.value);
    const maxDate = new Date('2038-01-19');

    if (inputDate > maxDate) {
      return { dateExceeded: true };
    }
    return null;
  }

  timeValidator(group: AbstractControl): ValidationErrors | null {
    const start = group.get('start_time')?.value;
    const stop = group.get('stop_time')?.value;
    if (start && stop && start > stop) {
      return { timeInvalid: true };
    }
    return null;
  }

  onSelectedUsersChange(user: any) {
    this.employee = user;
    this.timeTrackingForm.get('employee')?.setValue(user);
  }

  onSelectedJobChange(job: WorkOrderResponse | any) {
    this.selectedWorkOrder = job;
    if (this.selectedWorkOrder?.activity_id) {
      this.selectedActivity = this.activities.find((activity) => activity.activity_id === this.selectedWorkOrder?.activity_id) || this.selectedActivity;
    }
  }

  onSelectedActivityChange(activity: TimeTrackingActivityResponse | any) {
    this.selectedActivity = activity;
  }

  convertDurationToSeconds(): number {
    const hours = this.timeTrackingForm.get('duration_hours')?.value || 0;
    const minutes = this.timeTrackingForm.get('duration_minutes')?.value || 0;
    return hours * 3600 + minutes * 60;
  }

  setDurationFromTimes(start: string, stop: string) {
    const startMoment = moment(start, 'HH:mm');
    const stopMoment = moment(stop, 'HH:mm');

    const duration = moment.duration(stopMoment.diff(startMoment));
    this.duration_hours = Math.floor(duration.asHours());
    this.duration_minutes = duration.minutes();
  }

  updateStopTime() {
    const start = this.timeTrackingForm.get('start_time')?.value;
    const hours = this.timeTrackingForm.get('duration_hours')?.value || 0;
    const minutes = this.timeTrackingForm.get('duration_minutes')?.value || 0;

    if (start) {
      const [startHours, startMinutes] = start.split(':').map(Number);
      const stopDate = new Date();
      stopDate.setHours(startHours + hours);
      stopDate.setMinutes(startMinutes + minutes);

      const stopHours = stopDate.getHours().toString().padStart(2, '0');
      const stopMinutes = stopDate.getMinutes().toString().padStart(2, '0');
      this.timeTrackingForm.get('stop_time')?.setValue(`${stopHours}:${stopMinutes}`);
    }
  }

  formatTime(date: Date | string): string {
    if (!date) {
      return '';
    }
    const d = new Date(date);
    const hours = d.getHours().toString().padStart(2, '0');
    const minutes = d.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }

  searchWorkOrders(searchString: string) {
    let dateFrom = new Date(this.registrationDate);
    dateFrom.setHours(0, 0, 0, 0);
    let dateTo = new Date(this.registrationDate);
    dateTo.setHours(23, 59, 59, 999);
    let params: _CRM_ORD_170 = {
      search_string: searchString,
      paginate: 1,
      limit: 10,
      page: 1,
      execution_date_from: dateFrom,
      execution_date_to: dateTo
    }
    return this.orderService.selectoriniWorkOrderSearch(params);
  }

  deleteTimeTracking() {
    let modalRef = this.modalService.open(VerifyPopupModal)
    modalRef.result.then(
      (result) => {
        if (result) {
          this.trackingService.deleteTimeTracking(this.timeTracking.entry_id).subscribe(res => {
            this.activeModal.close('success');
          });
        }
      },
      (reason) => { }
    );
  }

  unlockTimeTracking() {
    this.unlockLoading = true;
    this.trackingService.unlockTimeTracking(this.timeTracking!.entry_id).subscribe({
      next: (res) => {
        this.locked = false;
        this.timeTrackingForm.enable();
        this.timeTrackingUnlocked.emit();
        this.trackingService.getTrackingLogs(this.timeTracking!.entry_id).subscribe((res) => {
          this.logs = res;
        });
      },
      error: (error) => {
        this.unlockLoading = false;
      },
      complete: () => {
        this.unlockLoading = false;
      },
    });
  }

  protected readonly displayDate = displayDate;
}
