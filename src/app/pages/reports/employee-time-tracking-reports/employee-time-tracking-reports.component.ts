import {Component, OnInit} from '@angular/core';
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {TrackingService} from "../../../@shared/services/tracking.service";
import {CRM_TTR_9} from "../../../@shared/models/input.interfaces";
import {InternalUserResponse, UserEntityRelationWithUserDataResponse} from "../../../@shared/models/user.interfaces";
import {PaginationResponse} from "../../../@shared/models/response.interfaces";
import {CompanyTimeTrackingResponse, TrackingEntryResponse} from "../../../@shared/models/timetracking.interfaces";
import {formatFullDayAndDate, UtilsService} from "../../../@core/utils/utils.service";
import {StorageService} from "../../../@core/services/storage.service";
import {Router} from "@angular/router";
import {TranslateService} from "@ngx-translate/core";
import {Subject} from "rxjs";
import {takeUntil} from "rxjs/operators";
import {TimeTrackingDetailsModal} from "../../salary/_modals/time-tracking-details-modal/time-tracking-details-modal";
import {StandardImports} from "../../../@shared/global_import";
import {ToggleSwitchComponent} from "../../../@shared/components/toggle-switch/toggle-switch.component";
import {DatepickerinoRangeComponent} from "../../../@shared/components/datepickerino-range/datepickerino-range.component";
import {DeleteButtonComponent} from "../../../@shared/components/delete-button/delete-button.component";

@Component({
    selector: 'app-employee-time-tracking-reports',
    templateUrl: './employee-time-tracking-reports.component.html',
    styleUrls: ['./employee-time-tracking-reports.component.css'],
    standalone: true,
  imports: [StandardImports, ToggleSwitchComponent, DatepickerinoRangeComponent, DeleteButtonComponent]
})
export class EmployeeTimeTrackingReportsComponent implements OnInit {
  items: any[] = [];
  dateFromDate: Date;
  dateToDate: Date;
  expandedRows: boolean[] = [];
  employees: PaginationResponse<UserEntityRelationWithUserDataResponse[]>;
  employee: UserEntityRelationWithUserDataResponse;
  allTrackings: CompanyTimeTrackingResponse[] = [];
  trackingSource: CompanyTimeTrackingResponse[] = [];
  filteredTrackings: CompanyTimeTrackingResponse[] = [];
  exportBtnLoad: boolean = false
  isLoading: boolean = false;
  hideEmployeesWithoutTrackings: boolean = true;
  months: {value: number, display: string, date: Date, active: boolean}[] = [];
  weeks: {weekNo: number, firstDate: Date, lastDate: Date}[] = [];
  selectedMonth: {value: number, display: string, date: Date, active: boolean} | null = null;
  selectedWeek: number | null = null;
  cancelRequest$ = new Subject<void>();

  constructor(
    private modalService: NgbModal,
    private trackingService: TrackingService,
    public utilsService: UtilsService,
    private storageService: StorageService,
    private router: Router,
    private translate: TranslateService
  ) {
  }

  ngOnInit(): void {
    // this.setDefaultDateRangeToThisWeek();
    let currentDateFrom = new Date();
    currentDateFrom.setDate(1);
    let currentDateTo = new Date();
    currentDateTo.setMonth(currentDateTo.getMonth() + 1);
    this.mapMonths();
    this.selectedMonth = this.months.find((month) => month.value === new Date().getMonth() + 1)!;
    this.selectMonth(this.selectedMonth);
    this.mapWeeks();
    this.fetchTimeTracking();
  }

  selectMonth(month: {value: number, display: string, date: Date, active: boolean} | null) {
    if (this.selectedMonth) {
      this.selectedMonth!.active = false;
    }

    if (!month) {
      this.selectedMonth = null;
      return;
    }

    this.selectedMonth = month;
    this.selectedMonth.active = true;
    this.mapWeeks();
    this.dateFromDate = new Date(this.selectedMonth.date.getFullYear(), this.selectedMonth.date.getMonth(), 1);
    this.dateToDate = new Date(this.selectedMonth.date.getFullYear(), this.selectedMonth.date.getMonth() + 1, 0);
    this.fetchTimeTracking();
  }

  mapMonths() {
    this.months = Array.from({length: 12}, (_, i) => {
      let date = new Date(new Date().getFullYear(), 0);
      date.setMonth(date.getMonth() + i);
      return {
        value: date.getMonth() + 1,
        display: `${this.translate.instant('MONTHS.' + (date.getMonth() + 1))}`,
        date: date,
        active: false
      }
    });
    this.months = this.months.sort((a, b) => a.date.getTime() - b.date.getTime());
  }

  getWeekNumberOfDate(date: Date): number {
    let firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    let diff = date.getTime() - firstDayOfYear.getTime();
    let diffDays = Math.ceil(diff / (1000 * 3600 * 24));
    return Math.ceil((diffDays + firstDayOfYear.getDay() + 1) / 7);
  }

  mapWeeks() {
    let firstDayOfMonth = new Date(this.selectedMonth!.date.getFullYear(), this.selectedMonth!.date.getMonth(), 1);
    let dayOfWeekFirstDay = firstDayOfMonth.getDay();
    let diffToMonday = dayOfWeekFirstDay === 0 ? -6 : 1 - dayOfWeekFirstDay;
    let firstMonday = new Date(firstDayOfMonth.setDate(firstDayOfMonth.getDate() + diffToMonday));
    let lastDayOfMonth = new Date(this.selectedMonth!.date.getFullYear(), this.selectedMonth!.date.getMonth() + 1, 0);
    let dayOfWeekLastDay = lastDayOfMonth.getDay();
    let diffToSunday = dayOfWeekLastDay === 0 ? 0 : 7 - dayOfWeekLastDay;
    let lastSunday = new Date(lastDayOfMonth.setDate(lastDayOfMonth.getDate() + diffToSunday));
    let weeks: {weekNo: number, firstDate: Date, lastDate: Date}[] = [];
    // Fill weeks from firstMonday to lastSunday
    let weekNo = this.getWeekNumberOfDate(firstMonday);
    let currentDay = new Date(firstMonday);
    while (currentDay <= lastSunday) {
      let week = {
        weekNo: weekNo,
        firstDate: new Date(currentDay),
        lastDate: new Date(currentDay.setDate(currentDay.getDate() + 6))
      };
      weeks.push(week);
      weekNo++;
      currentDay.setDate(currentDay.getDate() + 1);
    }
    this.weeks = weeks;
  }

  updateDates(dates: Array<Date | null>) {
    this.dateFromDate = dates[0]!;
    this.dateToDate = dates[1]!;
    this.selectMonth(null)
    this.fetchTimeTracking();
  }

  fetchTimeTracking(preservedExpandedRows: boolean[] = []) {
    this.cancelRequest$.next();
    this.isLoading = true; // Start loading
    let dateFrom = this.formatDate(this.dateFromDate);
    let dateTo = this.formatDate(this.dateToDate);
    this.trackingService.getCompanyTimeTracking(dateFrom, dateTo).pipe(takeUntil(this.cancelRequest$)).subscribe({
      next: (res) => {
        this.allTrackings = res;
        this.filteredTrackings = this.allTrackings.filter((tracking) => {
          return tracking.trackings.length > 0;
        });
        this.trackingSource = this.hideEmployeesWithoutTrackings ? this.filteredTrackings : this.allTrackings;
        this.expandedRows = preservedExpandedRows.length ? preservedExpandedRows : new Array(this.allTrackings.length).fill(false);
        this.isLoading = false; // End loading
      },
      error: (error) => {
        console.error(error);
        this.isLoading = false; // End loading on error
      }
    });
  }
  toggleDetailRow(index: number): void {
    this.expandedRows[index] = !this.expandedRows[index];
  }

  isExpanded(index: number): boolean {
    return this.expandedRows[index];
  }

  navigateToOrder(tracking: TrackingEntryResponse) {
    if (tracking.order_id === null) {
      return;
    }
    this.router.navigate(['/orders/details/' + tracking.order_id]);
  }

  openModal(employee: InternalUserResponse | null, timeTracking: TrackingEntryResponse | null) {
    if (timeTracking && !timeTracking.entry_id) {
      return;
    }
    const modalRef = this.modalService.open(TimeTrackingDetailsModal, { size: 'lg' });
    modalRef.componentInstance.user = employee;
    modalRef.componentInstance.timeTracking = timeTracking;

    modalRef.result.then(
      (result) => {
        if (result === 'success') {
          this.fetchTimeTracking(this.expandedRows);
        }
      },
      (reason) => { }
    );
  }

  deleteButtonClick(entry_id: number | null) {
    if (entry_id === null) {
      return;
    }
    this.trackingService.deleteTimeTracking(entry_id).subscribe(res => {
      this.fetchTimeTracking(this.expandedRows);
    });
  }

  formatDate(date: Date): string {
    return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
  }

  toggleHideEmployeesWithoutTrackings() {
    this.hideEmployeesWithoutTrackings = !this.hideEmployeesWithoutTrackings;
    this.trackingSource = this.hideEmployeesWithoutTrackings ? this.filteredTrackings : this.allTrackings;
  }

  exportToExcel() {
    this.exportBtnLoad = true;
    let params: CRM_TTR_9 = {
      date_from: this.formatDate(this.dateFromDate),
      date_to: this.formatDate(this.dateToDate),
      company_id: this.storageService.getSelectedCompanyId()
    };

    this.trackingService.exportToExcel(params).subscribe({
      next: (res) => {
        const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'TimeTrackingReport.xlsx';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        console.error('Error exporting to Excel:', error);
        this.exportBtnLoad = false;
      },
      complete: () => {
        this.exportBtnLoad = false;
        console.log('Excel export completed.');
      }
    });
  }

  protected readonly formatFullDayAndDate = formatFullDayAndDate;
}
