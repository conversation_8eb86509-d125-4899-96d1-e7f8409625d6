<div class="table-container">
  <div class="mt-3">

    <!-- Tool elements -->
    <div class="d-flex justify-content-between align-items-center">
      <h4 class="d-flex align-items-center m-0" style="cursor: pointer;">
        <span class="fw-bolder ms-1">{{ "reports.timeTracking.table.backBtn" | translate }}</span>
      </h4>
      <div class="d-flex align-items-center">
        <div class="me-2">
          <app-toggle-switch
            [labelKey]="'reports.timeTracking.hideToggle'"
            [state]="hideEmployeesWithoutTrackings"
            (stateChange)="toggleHideEmployeesWithoutTrackings()">
          </app-toggle-switch>
        </div>
        <div class="mx-2">
          <app-datepickerino-range
            [startDate]="dateFromDate"
            [endDate]="dateToDate"
            [compact]="true"
            (rangeChanged)="updateDates($event)"
          ></app-datepickerino-range>
        </div>
        <app-button
          class="me-2"
          translationKey="reports.timeTracking.table.addModal"
          (buttonClick)="openModal(null, null)">
        </app-button>
        <app-button
          [loading]="exportBtnLoad"
          translationKey="reports.timeTracking.table.export"
          (buttonClick)="exportToExcel()">
        </app-button>
      </div>
    </div>
  </div>


  <!-- Quick date picker -->
  <div class="position-relative mt-3">
    <!-- Month picker -->
    <div style="display: grid; grid-template-columns: repeat(12, 1fr); max-width: 1178px;">
      <div *ngFor="let month of months" class="month" [ngClass]="{'month-active': month.active}" (click)="selectMonth(month)">{{ month.display}}</div>
    </div>

    <!-- Week picker -->
<!--    <div class="position-absolute" style="display: grid; grid-template-columns: repeat(6, 1fr); max-width: 1178px;">-->
<!--      <div *ngFor="let week of weeks" class="month">Uke {{week.weekNo}}</div>-->
<!--    </div>-->
  </div>

  <div class="report-container mt-2" style="overflow-x: scroll">

    <!--  Column headers  -->
    <div class="tracking-header" style="min-width: 900px;">

      <!--  Employee header -->
      <div class="col-7">
        <span class="fw-bold">{{ "reports.timeTracking.table.employee" | translate }}</span>
      </div>

      <!--  Total tracked time header  -->
      <div class="col-2">
        <span class="fw-bold">{{ "reports.timeTracking.table.totalTrackedTime" | translate }}</span>
      </div>

      <!--  Total assigned time header  -->
      <div class="col-2">
        <span class="fw-bold">{{ "reports.timeTracking.table.totalAssignedTime" | translate }}</span>
      </div>
    </div>

    <!-- Loading spinner -->
    <div *ngIf="isLoading" class="text-center my-3">
      <div class="spinner-border" role="status">
        <span class="sr-only">{{"common.loading" | translate}}...</span>
      </div>
    </div>
    <div *ngIf="!isLoading">
    <!--  Table rows  -->
    <div *ngFor="let entry of trackingSource; let i = index" style="min-width: 900px;">
      <div id="mainRowContainer" [ngStyle]="{'cursor': entry.trackings.length > 0 ? 'pointer': 'default'}">
        <div id="mainRow" class="tracking-row" [ngClass]="{'tracking-row-expanded': isExpanded(i)}" (click)="entry.trackings.length > 0 ? toggleDetailRow(i) : null">
          <!--  Employee  -->
          <div class="d-flex col-7 align-items-center">
            <span class="expander me-2" [ngStyle]="{'visibility': entry.trackings.length > 0 ? 'visible' : 'hidden'}">
              <i *ngIf="isExpanded(i)" class="fa fa-chevron-down"></i>
              <i *ngIf="!isExpanded(i)" class="fa fa-chevron-right"></i>
            </span>
            <span class="me-1">
                <img *ngIf="entry.user.profile_image_url" style="width: 25px; height: 25px;" [src]="entry.user.profile_image_url" class="img-rounded" alt="no Img">
                <img *ngIf="!entry.user.profile_image_url" style="width: 25px; height: 25px;" src="assets/images/users/no-profile-image.jpeg" class="img-rounded" alt="no Img">
            </span>
            <span class="fw-bold"> {{ entry.user.full_name }}</span>
          </div>

          <!--  Total tracked time  -->
          <div class="d-flex col-2 align-items-center">
            <span class="fw-bold">{{ entry.total_tracked_time > 0 ? utilsService.formatDurationFromSeconds(entry.total_tracked_time) : ''}}</span>
          </div>

          <!--  Total tracked time  -->
          <div class="d-flex col-2 align-items-center">
            <span class="fw-bold">{{ entry.total_assigned_time > 0 ? utilsService.formatDurationFromSeconds(entry.total_assigned_time) : ''}}</span>
          </div>
        </div>

        <!-- Expanded row headers -->
        <div *ngIf="isExpanded(i)" id="expandedRowHeaders" class="expanded-row-header align-items-center py-1" style="background-color: white; border-bottom: solid 1px rgb(222, 226, 230);">

          <div></div>

          <div class="">
            <span class="expanded-header">{{"reports.timeTracking.table.order" | translate}}</span>
          </div>

          <div class="">
            <span class="expanded-header">{{"reports.timeTracking.table.date" | translate}}</span>
          </div>

          <div class="">
            <span class="expanded-header">{{"reports.timeTracking.table.description" | translate}}</span>
          </div>

          <div class="">
            <span class="expanded-header">{{"reports.timeTracking.table.startStop" | translate}}</span>
          </div>

          <div class="">
            <span class="expanded-header">{{"reports.timeTracking.table.trackedTime" | translate}}</span>
          </div>

          <div class="">
            <span class="expanded-header">{{"reports.timeTracking.table.assignedTime" | translate}}</span>
          </div>

        </div>

      </div>

      <div id="expandableContent" *ngIf="isExpanded(i)">
        <div class="expanded-row" [ngClass]="{'expanded-tt-row': tracking.entry_id, 'text-muted': !tracking.entry_id}" *ngFor="let tracking of entry.trackings; let j = index" (click)="openModal(entry.user, tracking)">

          <div></div>

          <div class="">
            <span class="order-number" (click)="navigateToOrder(tracking)" *ngIf="tracking.child === 0" [ngStyle]="{'cursor': tracking.order_number !== null ? 'pointer' : 'default'}">{{ tracking.order_number !== null ? '#' + tracking.order_number : '-'}}</span>
            <div class="d-flex justify-content-center">
              <i *ngIf="tracking.child === 1" class="fa-regular fa-arrow-turn-down-right fa-xl"></i>
            </div>
          </div>

          <div class="">
            <span>{{ formatFullDayAndDate(tracking.started_at || tracking.execution_at, true, false)}}</span>
          </div>

          <div class="">
           <span>{{ tracking.description }}</span>
          </div>

          <div class="">
            <span *ngIf="tracking.entry_id">{{ tracking.started_at | date:'shortTime' }} - {{ tracking.stopped_at | date:'shortTime' }}</span>
            <span *ngIf="!tracking.entry_id">Ingen tid ført enda</span>
          </div>

          <div class="">
            <span>{{ utilsService.formatDurationFromSeconds(tracking.duration_in_seconds) || '-' }}</span>
          </div>

          <div class="">
            <span>{{ utilsService.formatDurationFromSeconds(tracking.work_order_duration_in_seconds) || '-' }}</span>
          </div>

          <div class="d-flex align-items-center">
            <div class="col-4" [ngbTooltip]="tracking.planned_time_diff_warning ? 'Denne timesføringen er markert pga forskjell i føringens lengde og planlagt jobbvarighet' : null">
              <i *ngIf="tracking.planned_time_diff_warning" class="fa-regular fa-warning text-warning fa-lg"></i>
            </div>
            <div style="max-width: 25px;">
              <delete-button *ngIf="tracking.entry_id" (click)="$event.stopPropagation()" (delete)="deleteButtonClick(tracking.entry_id)"></delete-button>
            </div>
          </div>


        </div>
      </div>
    </div>
    </div>
    <div *ngIf="!isLoading" style="min-width: 1111px;" class="table-footer"></div>
  </div>
</div>
