import {Component, OnInit, ViewEncapsulation} from '@angular/core';
import {currencyFormat, formatDateYMD, UtilsService} from 'src/app/@core/utils/utils.service';
import { ReportsService } from 'src/app/@shared/services/reports.service';
import moment from 'moment';
import { OrderService } from 'src/app/@shared/services/order.service';
import {_CRM_PAY_41} from 'src/app/@shared/models/input.interfaces';
import {ReportFinancialSummaryResponse} from "../../../@shared/models/report.interfaces";
import {BehaviorSubject, forkJoin, pairwise, Subject} from "rxjs";
import {PaginationContainer} from "../../../@shared/models/global.interfaces";
import {TablerinoColumn, TablerinoComponent} from "../../../@shared/components/tablerino/tablerino.component";
import {TranslateService} from "@ngx-translate/core";
import {OrderPaymentResponse, OrderPaymentResponseCompact} from "../../../@shared/models/payment.interfaces";
import {PaymentService} from "../../../@shared/services/payment.service";
import {PaymentDetailsComponent} from "../../payments/components/payment-details/payment-details.component";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {StandardImports} from "../../../@shared/global_import";
import {PageHeaderComponent} from "../../../@shared/components/page-header/page-header.component";
import {DateRangePickerComponent} from "../../../@shared/components/date-range-picker/date-range-picker.component";
import {PaymentDetailsV2Component} from "../../payments/components/payment-details-v2/payment-details-v2.component";

@Component({
    selector: 'app-financial-reports',
    templateUrl: './financial-reports.component.html',
    styleUrls: ['./financial-reports.component.css',
    ],
    encapsulation: ViewEncapsulation.None,
    standalone: true,
  imports: [StandardImports, PageHeaderComponent, DateRangePickerComponent, TablerinoComponent]
})

export class FinancialReportsComponent implements OnInit {

  searchString: string = '';
  isLoading: boolean = false;
  dateFrom: string
  dateTo: string
  dateFromDate: Date
  dateToDate: Date
  report: ReportFinancialSummaryResponse;
  payments: Array<OrderPaymentResponse | OrderPaymentResponseCompact> = [];
  columnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);

  paginationSubject: BehaviorSubject<PaginationContainer> = new BehaviorSubject<PaginationContainer>({page: 1, limit: 25, paginate: 1, totalPages: 0, totalItems: 0});

  constructor(
    private reportService: ReportsService,
    public utilsService: UtilsService,
    private orderService: OrderService,
    private translate: TranslateService,
    private paymentService: PaymentService,
    private modalService: NgbModal
  ){}

  ngOnInit():void{
    this.initColumns();
    this.dateFrom = formatDateYMD(new Date(moment().startOf('month').format('YYYY-MM-DD')));
    this.dateTo = formatDateYMD(new Date(moment().endOf('month').format('YYYY-MM-DD')));
    this.dateFromDate = new Date(this.dateFrom)
    this.dateToDate = new Date(this.dateTo)
    this.fetchReports();
    this.fetchPayments();
    this.paginationSubject.pipe(pairwise()).subscribe((paginationDetails) => {
      if (JSON.stringify(paginationDetails[0]) !== JSON.stringify(paginationDetails[1])) {
        this.fetchPayments();
      }
    });
  }

  updateDates(event: any) {
    this.dateFrom = formatDateYMD(new Date(moment(event.from).format("YYYY-MM-DD")));
    this.dateTo = formatDateYMD(new Date(moment(event.to).format("YYYY-MM-DD")));
    this.dateFromDate = new Date(this.dateFrom)
    this.dateToDate = new Date(this.dateTo)
    this.fetchReports();
    this.fetchPayments();
  }

  fetchReports(){
    this.reportService.fetchReports(this.dateFrom, this.dateTo).subscribe(res => {
      this.report = res;
    });
  }

  fetchFilterOptions(event: any) {
    this.searchString = event.searchString;
    this.fetchPayments();
  }

  initColumns() {
    this.columnsSubject.next([
      {
        name: 'order_id',
        labelKey: 'ID',
        formatter: (pm: OrderPaymentResponseCompact) => this.colorizeRow(pm, `#${pm.payment_number}`),
        sort: false,
        visible: true,
      },
      {
        name: 'customer_name',
        labelKey: 'reports.orderList.customer',
        formatter: (pm: OrderPaymentResponseCompact) => this.colorizeRow(pm, `${pm.payment_recipient?.name}`),
        sort: false,
        visible: true,
      },
      {
        name: 'captured_at',
        labelKey: 'reports.orderList.captured',
        formatter: (pm: OrderPaymentResponseCompact) => this.colorizeRow(pm, pm.refund ? moment(pm.created_at).format('DD MMMM YYYY HH:mm') : moment(pm.captured_at).format('DD MMMM YYYY HH:mm')),
        sort: false,
        visible: true,
      },
      {
        name: 'payment_method',
        labelKey: 'reports.orderList.paymentMethod',
        formatter: (pm: OrderPaymentResponseCompact) => this.colorizeRow(pm, pm.payment_method_name),
        sort: false,
        visible: true,
      },
      {
        name: 'total_amount_inc_vat',
        labelKey: 'reports.orderList.netSales',
        formatter: (pm: OrderPaymentResponse | OrderPaymentResponseCompact) => this.colorizeRow(pm, currencyFormat(pm.refund ? this.getRefundAmountValue(pm as OrderPaymentResponse, "refund_amount") : pm.total_amount_ex_vat)),
        sort: false,
        visible: true,
      },
      {
        name: 'total_vat_amount',
        labelKey: 'reports.orderList.vat',
        formatter: (pm: OrderPaymentResponseCompact | OrderPaymentResponse) => this.colorizeRow(pm, currencyFormat(pm.total_amount_inc_vat - pm.total_amount_ex_vat)),
        sort: false,
        visible: true,
      },
      {
        name: 'sales_price',
        labelKey: 'reports.orderList.totalSales',
        formatter: (pm: OrderPaymentResponseCompact | OrderPaymentResponse) => this.colorizeRow(pm, currencyFormat(pm.total_amount_inc_vat)),
        sort: false,
        visible: true,
      },
    ]);
  };

  colorizeRow(row: OrderPaymentResponseCompact | OrderPaymentResponse, value: string | undefined | null) {
    if (row.refund) {
      return `<div class="text-danger">${value}</div>`;
    } else {
      return value;
    }
  }

  getRefundAmountValue(payment: OrderPaymentResponse, key: keyof OrderPaymentResponse) {
    return payment[key] as number
  }

  fetchPayments(){
    this.isLoading = true;
    let from: Date = new Date(this.dateFrom);
    from = new Date(from.setHours(0,0,0));
    let to: Date = new Date(this.dateTo);
    to = new Date(to.setHours(23,59,59));
    let searchString = this.searchString;


    const paymentParams: _CRM_PAY_41 = {
      captured_at_from: from,
      captured_at_to: to,
    }

    const refundParams: _CRM_PAY_41 = {
      created_at_from: from,
      created_at_to: to,
      refunds_only: true
    }

    const requests = [
      this.paymentService.getCompanyPayments(paymentParams),
      this.paymentService.getCompanyPaymentsCompleteResponse({...refundParams, refunds_only: true})
    ];

    this.isLoading = true;
    forkJoin(requests).subscribe(([payments, refunds]) => {
      let tempPayments = payments.data.concat(refunds.data);
      this.payments = tempPayments.sort((a, b) => {
        // Determine the date to sort by for each payment
        const dateA = a.refund ? new Date(a.created_at) : new Date(a.captured_at!);
        const dateB = b.refund ? new Date(b.created_at) : new Date(b.captured_at!);

        // Compare the dates
        return dateA.getTime() - dateB.getTime();
      });
      this.isLoading = false;
    });

    // this.paymentService.getCompanyPayments(params).subscribe(res => {
    //   this.dataResponse = res;
    //   this.payments = res.data;
    //   this.isLoading = false;
    // })
    //
    // this.paymentService.getCompanyPayments({...params, refunds_only: true}).subscribe(res => {
    //   this.payments = this.payments.concat(res.data);
    //   this.isLoading = false;
    // });
  }

  getSalesReportPDF(){
    // console.log(this.dateFrom)
    this.reportService.getSalesReportPDF(this.dateFrom, this.dateTo).subscribe((response: Blob) => {
      const fileUrl = URL.createObjectURL(response);
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = 'sales_report.pdf';
      link.click();
      URL.revokeObjectURL(fileUrl);
      link.remove();
    });
  }

  getTransactionReportPDF(){
    this.reportService.getTransactionReportPDF(this.dateFrom, this.dateTo).subscribe((response: Blob) => {
      const fileUrl = URL.createObjectURL(response);
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = 'transaction_report.pdf';
      link.click();
      URL.revokeObjectURL(fileUrl);
      link.remove();
    });
  }

  getTransactionReportXLSX(){
    this.reportService.getTransactionReportXLSX(this.dateFrom, this.dateTo).subscribe((response: Blob) => {
      const fileUrl = URL.createObjectURL(response);
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = 'transaction_report.xlsx';
      link.click();
      URL.revokeObjectURL(fileUrl);
      link.remove();
    });
  }

  rowClicked(event: OrderPaymentResponseCompact) {
    let modalRef = this.modalService.open(PaymentDetailsV2Component, {size: 'lg'});
    modalRef.componentInstance.payment = event;
    modalRef.componentInstance.fetchPayment = true;
    modalRef.componentInstance.viewSettings = {modalView: true, paymentView: true, paymentStandaloneView: true};
  }

  protected readonly currencyFormat = currencyFormat;
}
