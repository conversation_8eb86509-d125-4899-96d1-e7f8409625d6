import {ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import {_CRM_EMP_12, _CRM_EMP_19, _CRM_EMP_5, _USM_ENT_0, _USM_USR_17,} from 'src/app/@shared/models/input.interfaces';
import {RoleResponse, UserEntityRelationWithUserDataResponse} from 'src/app/@shared/models/user.interfaces';
import { EmployeeService } from 'src/app/@shared/services/employee.service';
import {ActivatedRoute, Router} from '@angular/router';
import {asyncPhoneNumberValidator, currencyFormat, displayPhone, formatDateDMY, getFormControl, resizeAndConvertToJpeg, UtilsService} from 'src/app/@core/utils/utils.service';
import {EmployeeAttributesResponse, EmployeeSalaryResponse, EmployeeVacationDaysResponse} from "../../../@shared/models/employee.interfaces";
import {ToastService} from "../../../@core/services/toast.service";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {ImageProcessorComponent} from "../../../@shared/components/image-processor/image-processor.component";
import {formatCurrency} from "@angular/common";
import {EmployeeSalaryModalComponent} from "./_modals/employee-salary-modal/employee-salary-modal.component";
import {EditEmployeeModalComponent} from "./_modals/edit-employee-modal/edit-employee-modal.component";
import {StorageService} from "../../../@core/services/storage.service";
import {StandardImports} from "../../../@shared/global_import";
import {CardComponent} from "../../../@shared/components/layout/card/card.component";
import {DeleteButtonComponent} from "../../../@shared/components/delete-button/delete-button.component";
import {EmployeeVacationModalComponent} from "./_modals/employee-vacation-modal/employee-vacation-modal.component";


@Component({
    selector: 'app-edit-employee',
    templateUrl: './edit-employee.component.html',
    styleUrls: ['./edit-employee.component.css'],
    standalone: true,
    imports: [StandardImports, CardComponent, DeleteButtonComponent]
})
export class EditEmployeeComponent implements OnInit {

  @ViewChild('acc') accordion: any;  // Reference to the ngbAccordion
  @ViewChild('acc', {static: true}) acc: any;

  employeeForm: FormGroup;
  notificationForm: FormGroup;
  availabilityForm: FormGroup;
  accessControlForm: FormGroup;
  loading = false;
  selectedPhoto: File;
  imagePreview: string;
  companyRoles: RoleResponse[];
  editMode = false
  user_id: string;
  userData?: UserEntityRelationWithUserDataResponse;
  attributeData: EmployeeAttributesResponse;
  originalFormValues: any;
  success: boolean;
  failureMessage: boolean;
  isBackOfficeUser: boolean = false;
  selectedRole: RoleResponse;
  isReadonly: boolean = true;
  salaries: EmployeeSalaryResponse[] = [];
  isAccordionOneExpanded = true;
  isAccordionTwoExpanded = false;
  isAccordionThreeExpanded = false;
  timeTrackingEnabled: boolean = false;
  vacationDays: EmployeeVacationDaysResponse[] = [];

  // Notification section expanded states
  notificationSections: { [key: string]: boolean } = {
    ntf_emp_assign_job: false,
    ntf_internal_note: false,
    ntf_order_accepted: false,
    ntf_order_finished: false,
    ntf_upcoming_order_not_accepted: false,
    ntf_order_rating: false,
    ntf_embed_order: false,
    ntf_customer_email_error: false,
    ntf_new_customer_msg: false,
    ntf_accounting_integration_fail: false,
    ntf_sub_contractor_order: false,
    ntf_customer_cancel_work_order: false
  };

  settingsForm: FormGroup;

  constructor(private route: ActivatedRoute,
              private router: Router,
              private fb: FormBuilder,
              private employeeService: EmployeeService,
              private utilsService: UtilsService,
              private toastService: ToastService,
              private modalService: NgbModal,
              private cd: ChangeDetectorRef,
              private storageService: StorageService) {
  }

  goBack() {
    // Check if we came from the profile link or settings
    if (this.router.url.includes('/profile/')) {
      this.router.navigate(['/dashboard']);
    } else {
      this.router.navigate(['/settings/employees']);
    }
  }

  changes: { [key: string]: boolean } = {
    first_name: false,
    last_name: false,
    phone: false,
    email: false,
    role_id: false,
  };

  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      this.user_id = params.get('id')!;
      this.employeeService.getEmployeeSalaries(this.user_id).subscribe(res => {
        this.salaries = res.sort((a, b) => a.created_at > b.created_at ? -1 : 1);
      });
      this.employeeService.getUserVacationDays(this.user_id).subscribe(res => {
        this.vacationDays = res.sort((a, b) => a.year > b.year ? -1 : 1);
      });
    });

    this.storageService.timeTrackingEnabled$.subscribe((enabled) => {
      this.timeTrackingEnabled = enabled;
    });

    this.notificationForm = this.fb.group({
      add_product: [this.attributeData?.add_product || false],
      edit_quantity: [this.attributeData?.edit_quantity || false],
      make_external_notes: [this.attributeData?.make_external_notes || false],
      make_external_reports: [this.attributeData?.make_external_reports || false],
      set_payment_to_external: [this.attributeData?.set_payment_to_external || false],
      unpaid_order_notification: [this.attributeData?.unpaid_order_notification || false],
      failed_schedule_order_creation_notification: [this.attributeData?.failed_schedule_order_creation_notification || false],
    });

    // Employee form - Creation
    this.employeeForm = new FormGroup({
      first_name: new FormControl(),
      last_name: new FormControl(),
      phone: new FormControl(),
      email: new FormControl(),
      role_id: new FormControl(),
      profile_image_url: new FormControl(),
      profile_image_url_new: new FormControl(),
    });

    // Initialize the settings form with a nested FormGroup for internal note notifications
    this.settingsForm = this.fb.group({
      order_scope: [0], // Default to "Only created orders"
      ntf_internal_note: this.fb.group({
        email: [{ value: false, disabled: true }],
        sms: [{ value: false, disabled: true }],
        core_push: [false],
        crew_push: [false]
      }),
      ntf_order_accepted: this.fb.group({email: [false], sms: [false], core_push: [false], crew_push: [false]}),
      ntf_order_finished: this.fb.group({email: [{ value: false, disabled: true }], sms: [false], core_push: [false], crew_push: [false]}),
      ntf_upcoming_order_not_accepted: this.fb.group({email: [false], sms: [{ value: false, disabled: true }], core_push: [false], crew_push: [false]}),
      ntf_order_rating: this.fb.group({email: [false], sms: [{ value: false, disabled: true }], core_push: [false], crew_push: [false]}),
      ntf_embed_order: this.fb.group({email: [false], sms: [{ value: false, disabled: true }], core_push: [false], crew_push: [false]}),
      ntf_customer_email_error: this.fb.group({email: [false], sms: [{ value: false, disabled: true }], core_push: [{ value: false, disabled: true }], crew_push: [{ value: false, disabled: true }]}),
      ntf_new_customer_msg: this.fb.group({email: [false], sms: [false], core_push: [false], crew_push: [false]}),
      ntf_accounting_integration_fail: this.fb.group({email: [false], sms: [false], core_push: [false], crew_push: [false]}),
      ntf_sub_contractor_order: this.fb.group({email: [false], sms: [{ value: false, disabled: true }], core_push: [false], crew_push: [false]}),
      ntf_emp_assign_job: this.fb.group({email: [{ value: false, disabled: true }], sms: [{ value: false, disabled: true }], core_push: [false], crew_push: [false]}),
      ntf_customer_cancel_work_order: this.fb.group({email: [false], sms: [{ value: false, disabled: true }], core_push: [false], crew_push: [false]})
    });


    // Availability form - Creation
    this.availabilityForm = new FormGroup({
      taskable: new FormControl(),
    })

    // Access control form - Creation
    this.accessControlForm = new FormGroup({
      view_all_events_as_crew: new FormControl(),
      view_all_orders_as_crew: new FormControl(),
      view_prices: new FormControl(),
      view_order_lines: new FormControl(),
      send_to_payment: new FormControl(),
      checkin_geo_lock: new FormControl(),
      edit_discount: new FormControl(),
      confirm_order: new FormControl(),
      edit_work_order_execution_time: new FormControl(),
      edit_order_lines: new FormControl(),
      complete_job: new FormControl(),
      cancel_order: new FormControl(),
      create_order: new FormControl(),
      view_unconfirmed_orders: new FormControl(),
      create_event: new FormControl(),
      edit_company_events: new FormControl(),
      edit_own_event: new FormControl(),
      edit_time_tracking: new FormControl(),
      create_manual_time_tracking: new FormControl(),
      edit_employees: new FormControl(),
      add_payment_recipient: new FormControl(),
      edit_resources: new FormControl(),
      checkout_geo_lock: new FormControl(),
      start_work_order_outside_execution_date: new FormControl(),

    });

    // Get employee data
    const employeeParams: _USM_ENT_0 = {
      user_id: this.user_id!,
    };
    this.employeeService.getEmployeesInCompany(employeeParams).subscribe(res => {
      const emp = res.find(emp => emp.user_id === this.user_id);
      if (emp) {
        this.userData = emp;
        this.isBackOfficeUser = [0, 1, 2].includes(emp.role_id);
        this.initEmployeeForm();
        this.fetchCompanyRoles();
        this.loadNotificationSettings();
        const isCrew = emp.role_id === 3 || emp.role_id === 4;
        const ntfEmpAssignJobGroup = this.settingsForm.get('ntf_emp_assign_job') as FormGroup;
        if (isCrew) {
          ntfEmpAssignJobGroup.get('core_push')?.disable();
        } else {
          ntfEmpAssignJobGroup.get('core_push')?.enable();
        }
        const ntfInternalNoteGroup = this.settingsForm.get('ntf_internal_note') as FormGroup;
        if (isCrew) {
          ntfInternalNoteGroup.get('core_push')?.disable();
        } else {
          ntfInternalNoteGroup.get('core_push')?.enable();
        }
      }

      // Get employee attributes
        this.employeeService.getEmployeeAttributes({ user_id: this.user_id }).subscribe(res => {
          this.attributeData = res;
          // this.initNotificationForm();

          this.initAvailabilityForm();
          this.initAccessControlForm();
        });
      this.originalFormValues = this.employeeForm.getRawValue();
    })

    this.failureMessage = false
    this.success = false
  }

  fetchCompanyRoles() {
    // Fetching company roles
    this.employeeService.getCompanyRoles().subscribe(res => {
      this.companyRoles = res;
        this.selectedRole = this.companyRoles.find(role => role.role_id === this.userData!.role_id)!;
    });
  }


  initEmployeeForm(): void {
    this.employeeForm = this.fb.group({
      first_name: [this.userData?.first_name, [Validators.required]],
      last_name: [this.userData?.last_name, [Validators.required]],
      phone: [displayPhone(this.userData?.phone), [Validators.required, asyncPhoneNumberValidator, Validators.maxLength(8), Validators.minLength(8), Validators.min(10000000)]],
      email: [this.userData?.email, [Validators.required, Validators.email, Validators.maxLength(200)]],
      role_id: [this.userData?.role_id, Validators.required],
      profile_image_url: [this.userData?.profile_image_url],
      profile_image_url_new: [],
    });
    this.originalFormValues = this.employeeForm.getRawValue();
  }

  updateSettings(): void {
    // Get the form values
    let internalNoteSettings = this.settingsForm.value.ntf_internal_note;

    // For users with role_id 3 and above, force the scope to be 'my_orders'
    if (this.userData && this.userData.role_id > 2 && internalNoteSettings) {
      internalNoteSettings.scope = 'my_orders';
    }

    const payload: _CRM_EMP_5 = {
      user_id: this.userData?.user_id!,
      add_product: this.attributeData.add_product,
      edit_quantity: this.attributeData.edit_quantity,
      make_external_notes: this.attributeData.make_external_notes,
      make_external_reports: this.attributeData.make_external_reports,
      send_to_payment: this.attributeData.send_to_payment,
      edit_discount: this.attributeData.edit_discount,
      view_prices: this.attributeData.view_prices,
      view_order_lines: this.attributeData.view_order_lines,
      payment_page_access: this.attributeData.payment_page_access,
      set_payment_to_external: this.attributeData.set_payment_to_external,
      ntf_accounting_integration: this.attributeData.ntf_accounting_integration,
      view_all_events_as_crew: this.attributeData.view_all_events_as_crew,
      view_all_orders_as_crew: this.attributeData.view_all_orders_as_crew,
      unpaid_order_notification: this.attributeData.unpaid_order_notification,
      failed_schedule_order_creation_notification: this.attributeData.failed_schedule_order_creation_notification,
      checkin_geo_lock: this.attributeData.checkin_geo_lock,
      edit_own_event: this.attributeData.edit_own_event,
      confirm_order: this.attributeData.confirm_order,
      edit_work_order_execution_time: this.attributeData.edit_work_order_execution_time,
      edit_order_lines: this.attributeData.edit_order_lines,
      complete_job: this.attributeData.complete_job,
      cancel_order: this.attributeData.cancel_order,
      create_order: this.attributeData.create_order,
      view_unconfirmed_orders: this.attributeData.view_unconfirmed_orders,
      create_event: this.attributeData.create_event,
      edit_company_events: this.attributeData.edit_company_events,
      edit_time_tracking: this.attributeData.edit_time_tracking,
      create_manual_time_tracking: this.attributeData.create_manual_time_tracking,
      edit_employees: this.attributeData.edit_employees,
      add_payment_recipient: this.attributeData.add_payment_recipient,
      edit_resources: this.attributeData.edit_resources,
      start_work_order_outside_execution_date: this.attributeData.start_work_order_outside_execution_date,
      checkout_geo_lock: this.attributeData.checkout_geo_lock,

      ntf_order_accepted: this.settingsForm.value.ntf_order_accepted,
      ntf_internal_note: internalNoteSettings,
      ntf_order_finished:  this.settingsForm.value.ntf_order_finished,
      ntf_upcoming_order_not_accepted: this.settingsForm.value.ntf_upcoming_order_not_accepted,
      ntf_order_rating: this.settingsForm.value.ntf_order_rating,
      ntf_embed_order: this.settingsForm.value.ntf_embed_order,
      ntf_customer_email_error: this.settingsForm.value.ntf_customer_email_error,
      ntf_new_customer_msg: this.settingsForm.value.ntf_new_customer_msg,
      ntf_accounting_integration_fail: this.settingsForm.value.ntf_accounting_integration_fail,
      ntf_sub_contractor_order: this.settingsForm.value.ntf_sub_contractor_order,
      ntf_emp_assign_job: this.settingsForm.value.ntf_emp_assign_job,
      ntf_customer_cancel_work_order: this.settingsForm.value.ntf_customer_cancel_work_order,
      order_scope: this.settingsForm.value.order_scope
    };

    this.employeeService.updateEmployeeAttributes(payload).subscribe({
      next: () => {
        console.log('Settings updated successfully');
      },
      error: (error) => {
        console.error("Error updating settings", error);
      }
    });
  }


  loadNotificationSettings(): void {
    this.employeeService.getEmployeeAttributes({ user_id: this.userData?.user_id! }).subscribe({
      next: (attributes) => {
        this.attributeData = attributes;

        // Set the internal note notification with scope
        let internalNote: any = attributes.ntf_internal_note || { email: false, sms: false, core_push: false, crew_push: false, scope: 'all_orders' };

        // For users with role_id 3 and above, force the scope to be 'my_orders'
        if (this.userData && this.userData.role_id > 2) {
          if (typeof internalNote === 'object') {
            internalNote.scope = 'my_orders';
          }
        }

        this.settingsForm.patchValue({
          order_scope: attributes.order_scope ?? 0,
          ntf_internal_note: internalNote,
          ntf_order_accepted: attributes.ntf_order_accepted,
          ntf_order_finished: attributes.ntf_order_finished,
          ntf_upcoming_order_not_accepted: attributes.ntf_upcoming_order_not_accepted,
          ntf_order_rating: attributes.ntf_order_rating,
          ntf_embed_order: attributes.ntf_embed_order,
          ntf_customer_email_error: attributes.ntf_customer_email_error,
          ntf_new_customer_msg: attributes.ntf_new_customer_msg,
          ntf_sub_contractor_order: attributes.ntf_sub_contractor_order,
          ntf_emp_assign_job: attributes.ntf_emp_assign_job,
          ntf_customer_cancel_work_order: attributes.ntf_customer_cancel_work_order
        });
        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error fetching employee attributes', error);
      }
    });
  }

  initAvailabilityForm(): void {
    let taskable = false;
    if (this.userData) {
      taskable = this.userData.taskable == 1;
    }

    this.availabilityForm = this.fb.group({
      taskable: [taskable, Validators.required],
    });
  }

  initAccessControlForm(): void {
    let view_all_events_as_crew = false;
    let view_all_orders_as_crew = false;

    if (this.attributeData) {
      view_all_events_as_crew = this.attributeData.view_all_events_as_crew == 1;
      view_all_orders_as_crew = this.attributeData.view_all_orders_as_crew == 1;
    }

    if (view_all_events_as_crew) {
      this.accessControlForm = this.fb.group({
        view_all_events_as_crew: [view_all_events_as_crew, Validators.required],
        view_all_orders_as_crew: [view_all_orders_as_crew, Validators.required]
      });
      this.accessControlForm.setControl('view_all_orders_as_crew', new FormControl({value: view_all_orders_as_crew, disabled: true}));
    }
    else {
      this.accessControlForm = this.fb.group({
        view_all_events_as_crew: [view_all_events_as_crew, Validators.required],
        view_all_orders_as_crew: [view_all_orders_as_crew, Validators.required]
      });
    }

    const viewOrderLinesEnabled = this.attributeData.view_order_lines == 1;

    this.accessControlForm = this.fb.group({
      view_all_events_as_crew: [this.attributeData.view_all_events_as_crew == 1],
      view_all_orders_as_crew: [{value: this.attributeData.view_all_orders_as_crew == 1, disabled: this.attributeData.view_all_events_as_crew == 1}],
      view_prices: [{value: this.attributeData.view_prices == 1, disabled: !viewOrderLinesEnabled}],
      view_order_lines: [viewOrderLinesEnabled],
      send_to_payment: [{value: this.attributeData.send_to_payment == 1, disabled: !viewOrderLinesEnabled}],
      checkin_geo_lock: [this.attributeData.checkin_geo_lock == 1],
      edit_discount: [this.attributeData.edit_discount == 1],
      confirm_order: [this.attributeData.confirm_order == 1],
      edit_work_order_execution_time: [this.attributeData.edit_work_order_execution_time == 1],
      edit_order_lines: [{value: this.attributeData.edit_order_lines == 1, disabled: !viewOrderLinesEnabled}],
      complete_job: [this.attributeData.complete_job == 1],
      cancel_order: [this.attributeData.cancel_order == 1],
      create_order: [this.attributeData.create_order == 1],
      view_unconfirmed_orders: [this.attributeData.view_unconfirmed_orders == 1],
      create_event: [this.attributeData.create_event == 1],
      edit_company_events: [this.attributeData.edit_company_events == 1],
      edit_own_event: [this.attributeData.edit_own_event == 1],
      edit_time_tracking: [this.attributeData.edit_time_tracking == 1],
      create_manual_time_tracking: [this.attributeData.create_manual_time_tracking == 1],
      edit_employees: [this.attributeData.edit_employees == 1],
      add_payment_recipient: [this.attributeData.add_payment_recipient == 1],
      edit_resources: [this.attributeData.edit_resources == 1],
      checkout_geo_lock: [this.attributeData.checkout_geo_lock == 1],
      start_work_order_outside_execution_date: [this.attributeData.start_work_order_outside_execution_date == 1],
    });
  }

  onAttributeChange() {
    // Calendar Section
    if (this.isAllSelectedInAccordionTwo()) {
      this.accessControlForm.patchValue({ select_all_calendar: true });
    } else {
      this.accessControlForm.patchValue({ select_all_calendar: false });
    }

    // Timetracking Section
    if (this.isAllSelectedInAccordionThree()) {
      this.accessControlForm.patchValue({ select_all_timetracking: true });
    } else {
      this.accessControlForm.patchValue({ select_all_timetracking: false });
    }

    // Orders Section
    if (this.isAllSelected()) {
      this.accessControlForm.patchValue({ select_all: true });
    } else {
      this.accessControlForm.patchValue({ select_all: false });
    }

    // Handle view_all_events_as_crew dependency
    if (this.accessControlForm.value.view_all_events_as_crew) {
      this.accessControlForm.patchValue({ view_all_orders_as_crew: true });
      this.accessControlForm.controls['view_all_orders_as_crew'].disable();
    } else if (!this.accessControlForm.value.view_all_events_as_crew) {
      this.accessControlForm.controls['view_all_orders_as_crew'].enable();
    }

    // Handle view_order_lines dependency
    if (!this.accessControlForm.value.view_order_lines) {
      // If view_order_lines is unchecked, uncheck all dependent options
      this.accessControlForm.patchValue({
        view_prices: false,
        send_to_payment: false,
        edit_order_lines: false
      });
    }

    this.updateEmployeeNotifications();
  }

  triggerFileInput() {
    const fileInput = document.getElementById('profile_image_url_new') as HTMLInputElement;
    fileInput.click();
  }

  updateEmployeeNotifications(): void {
    // Get the form values
    let internalNoteSettings = this.settingsForm.value.ntf_internal_note;

    // For users with role_id 3 and above, force the scope to be 'my_orders'
    if (this.userData && this.userData.role_id > 2 && internalNoteSettings) {
      internalNoteSettings.scope = 'my_orders';
    }

    const attributePayload: _CRM_EMP_5 = {
      user_id: this.userData?.user_id!,
      add_product: this.notificationForm.value.add_product ? 1 : 0,
      edit_quantity: this.notificationForm.value.edit_quantity ? 1 : 0,
      make_external_notes: this.notificationForm.value.make_external_notes ? 1 : 0,
      make_external_reports: this.notificationForm.value.make_external_reports ? 1 : 0,
      set_payment_to_external: this.notificationForm.value.set_payment_to_external ? 1 : 0,
      payment_page_access: 1,
      unpaid_order_notification: this.notificationForm.value.unpaid_order_notification ? 1 : 0,
      failed_schedule_order_creation_notification: this.notificationForm.value.failed_schedule_order_creation_notification ? 1 : 0,
      view_all_orders_as_crew: this.accessControlForm.getRawValue().view_all_orders_as_crew ? 1 : 0,
      send_to_payment: this.accessControlForm.getRawValue().send_to_payment ? 1 : 0,
      view_all_events_as_crew: this.accessControlForm.value.view_all_events_as_crew ? 1 : 0,
      view_prices: this.accessControlForm.getRawValue().view_prices ? 1 : 0,
      view_order_lines: this.accessControlForm.value.view_order_lines ? 1 : 0,
      checkin_geo_lock: this.accessControlForm.value.checkin_geo_lock ? 1 : 0,
      edit_own_event: this.accessControlForm.value.edit_own_event ? 1 : 0,
      edit_discount: this.accessControlForm.value.edit_discount ? 1 : 0,
      confirm_order: this.accessControlForm.value.confirm_order ? 1 : 0,
      edit_work_order_execution_time: this.accessControlForm.value.edit_work_order_execution_time ? 1 : 0,
      edit_order_lines: this.accessControlForm.getRawValue().edit_order_lines ? 1 : 0,
      complete_job: this.accessControlForm.value.complete_job ? 1 : 0,
      cancel_order: this.accessControlForm.value.cancel_order ? 1 : 0,
      create_order: this.accessControlForm.value.create_order ? 1 : 0,
      view_unconfirmed_orders: this.accessControlForm.value.view_unconfirmed_orders ? 1 : 0,
      create_event: this.accessControlForm.value.create_event ? 1 : 0,
      edit_company_events: this.accessControlForm.value.edit_company_events ? 1 : 0,
      edit_time_tracking: this.accessControlForm.value.edit_time_tracking ? 1 : 0,
      create_manual_time_tracking: this.accessControlForm.value.create_manual_time_tracking ? 1 : 0,
      edit_employees: this.accessControlForm.value.edit_employees ? 1 : 0,
      add_payment_recipient: this.accessControlForm.value.add_payment_recipient ? 1 : 0,
      edit_resources: this.accessControlForm.value.edit_resources ? 1 : 0,
      start_work_order_outside_execution_date: this.accessControlForm.value.start_work_order_outside_execution_date ? 1 : 0,
      checkout_geo_lock: this.accessControlForm.value.checkout_geo_lock ? 1 : 0,

      ntf_order_accepted: this.settingsForm.value.ntf_order_accepted,
      ntf_internal_note: internalNoteSettings,
      ntf_order_finished:  this.settingsForm.value.ntf_order_finished,
      ntf_upcoming_order_not_accepted: this.settingsForm.value.ntf_upcoming_order_not_accepted,
      ntf_order_rating: this.settingsForm.value.ntf_order_rating,
      ntf_embed_order: this.settingsForm.value.ntf_embed_order,
      ntf_customer_email_error: this.settingsForm.value.ntf_customer_email_error,
      ntf_new_customer_msg: this.settingsForm.value.ntf_new_customer_msg,
      ntf_accounting_integration_fail: this.settingsForm.value.ntf_accounting_integration_fail,
      ntf_sub_contractor_order: this.settingsForm.value.ntf_sub_contractor_order,
      ntf_emp_assign_job: this.settingsForm.value.ntf_emp_assign_job,
      ntf_accounting_integration: this.attributeData.ntf_accounting_integration,
      order_scope: this.settingsForm.value.order_scope,
      ntf_customer_cancel_work_order: this.settingsForm.value.ntf_customer_cancel_work_order
    }
    this.employeeService.updateEmployeeAttributes(attributePayload).subscribe({
      next: () => {
        this.toastService.successToast("employee_updated")
      },
      error: (error) => {
        console.error("Error updating employee notifications", error);
        this.toastService.errorToast("update_failed");
      }
    });
  }

  uploadPhoto(userId: string = '') {
    if (this.employeeForm.value.profile_image_url_new) {
      const payload: _USM_USR_17 = {
        'image': this.selectedPhoto,
        'user_id': userId
      };
      // return the Observable, do not subscribe here
      return this.employeeService.uploadProfileImage(payload);
    } else {
      return undefined;
    }
  }

  sendUserInvitation() {
    this.employeeService.resendUserInvitation(this.userData?.user_id!).subscribe({
      next: () => {
        this.toastService.successToast("invitation_sent")
      },
      error: () => {
        this.toastService.errorToast("invitation_failed")
      }
    });
  }


  onFileSelected(event: Event): void {
    const input = event?.target as HTMLInputElement;
    if (input && input.files && input.files.length > 0) {
      const file = input.files[0];
      if (file) {
        resizeAndConvertToJpeg(file, 0.8).then((compressedFile: Blob) => {
          const compressedImageFile = new File([compressedFile], file.name, {
            type: 'image/jpeg',
          });
          this.openImageProcessorModal(compressedImageFile);
          input.value = '';

        }).catch(() => {
          this.toastService.errorToast('error_compressing_logo');
        });
      }
    }
  }

  openImageProcessorModal(file: File) {
    const modalRef = this.modalService.open(ImageProcessorComponent, {size: 'md' });
    modalRef.componentInstance.imageFile = file;
    modalRef.componentInstance.ratio = 1;
    modalRef.result.then((croppedResult) => {
      const blob = croppedResult.blob;
      const croppedFile = new File([blob], file.name, {type: blob.type});
      this.selectedPhoto = croppedFile;
      this.setImagePreviewAndUpload(croppedFile);
    }, (reason) => {
      console.log(reason);
    });
  }

  setImagePreviewAndUpload(file: File) {
    const reader = new FileReader();
    reader.onload = (e: any) => {this.imagePreview = e.target.result;};
    reader.readAsDataURL(file);
    this.uploadPhoto(this.userData?.user_id)?.subscribe();
  }

  editModal() {
    const modalRef = this.modalService.open(EditEmployeeModalComponent, {size: 'md'});
    modalRef.componentInstance.employeeId = this.user_id;
    modalRef.result.then((userUpdated: UserEntityRelationWithUserDataResponse) => {
      if (userUpdated) {
        this.userData = userUpdated;
        this.selectedRole = this.companyRoles.find(role => role.role_id === userUpdated.role_id)!;
        this.employeeService.getEmployeeAttributes({ user_id: this.userData.user_id }).subscribe(res => {
          this.attributeData = res;
          this.initEmployeeForm();
          this.initAvailabilityForm();
          this.initAccessControlForm();
          this.fetchCompanyRoles();
          this.loadNotificationSettings();
        });
      }
    })
  }

// Function to check if all checkboxes are selected
  isAllSelected(): boolean {
    const controlsToCheck = [
      'edit_work_order_execution_time',
      'edit_discount',
      'confirm_order',
      'send_to_payment',
      'edit_order_lines',
      'complete_job',
      'view_prices',
      'view_order_lines',
      'edit_employees',
      'create_order',
      'checkin_geo_lock',
      'add_payment_recipient',
      'edit_resources'
    ];

    return controlsToCheck.every(key => this.accessControlForm.controls[key].value === true);
  }

// Method to check if all checkboxes in the Calendar accordion are selected
  isAllSelectedInAccordionTwo(): boolean {
    const calendarControls = [
      'view_all_orders_as_crew',
      'view_all_events_as_crew',
      'create_event',
      'edit_company_events',
      'view_unconfirmed_orders',
    ];

    return calendarControls.every(key => this.accessControlForm.controls[key]?.value === true);
  }


// Check if all Timetracking checkboxes are selected
  isAllSelectedInAccordionThree(): boolean {
    const timetrackingControls = [
      'edit_time_tracking',
      'create_manual_time_tracking',
    ];
    return timetrackingControls.every(key => this.accessControlForm.controls[key].value === true);
  }



  getSelectedAndTotal(form: FormGroup, controlKeys: string[]): string {
    const total = controlKeys.length;
    const selected = controlKeys.filter(key => form.controls[key]?.value === true).length;
    return `${selected}/${total}`;
  }

// Accordion 1 (Orders) Controls
  getSelectedAndTotalOrders(): string {
    const controls = [
      'edit_work_order_execution_time',
      'edit_discount',
      'confirm_order',
      'send_to_payment',
      'edit_order_lines',
      'complete_job',
      'view_prices',
      'view_order_lines',
      'edit_employees',
      'create_order',
      'checkin_geo_lock',
      'add_payment_recipient',
      'edit_resources',
    ];
    return this.getSelectedAndTotal(this.accessControlForm, controls);
  }

// Accordion 2 (Calendar) Controls
  getSelectedAndTotalCalendar(): string {
    const controls = [
      'view_all_orders_as_crew',
      'view_all_events_as_crew',
      'create_event',
      'edit_company_events',
      'view_unconfirmed_orders',
    ];
    return this.getSelectedAndTotal(this.accessControlForm, controls);
  }

// Accordion 3 (Time Tracking) Controls
  getSelectedAndTotalTimetracking(): string {
    const controls = [
      'edit_time_tracking',
      'create_manual_time_tracking',
    ];
    return this.getSelectedAndTotal(this.accessControlForm, controls);
  }

// Toggle all checkboxes for Orders
  toggleAllCheckboxes(event: Event): void {
    const checked = (event.target as HTMLInputElement).checked;
    const controls = [
      'edit_work_order_execution_time',
      'edit_discount',
      'confirm_order',
      'send_to_payment',
      'edit_order_lines',
      'complete_job',
      'view_prices',
      'view_order_lines',
      'edit_employees',
      'create_order',
      'checkin_geo_lock',
      'add_payment_recipient',
      'edit_resources'
    ];
    const newValues: { [key: string]: boolean } = {};
    controls.forEach(key => newValues[key] = checked);
    this.accessControlForm.patchValue(newValues);
    this.updateEmployeeNotifications();
  }

// Toggle all checkboxes for Calendar
  toggleAllCheckboxesInAccordionTwo(event: Event): void {
    const checked = (event.target as HTMLInputElement).checked;
    const controls = [
      'view_all_orders_as_crew',
      'view_all_events_as_crew',
      'create_event',
      'edit_company_events',
      'view_unconfirmed_orders',
    ];
    const newValues: { [key: string]: boolean } = {};
    controls.forEach(key => newValues[key] = checked);
    this.accessControlForm.patchValue(newValues);
    this.updateEmployeeNotifications();
  }

// Toggle all checkboxes for Timetracking
  toggleAllCheckboxesInAccordionThree(event: Event): void {
    const checked = (event.target as HTMLInputElement).checked;
    const controls = [
      'edit_time_tracking',
      'create_manual_time_tracking',
    ];
    const newValues: { [key: string]: boolean } = {};
    controls.forEach(key => newValues[key] = checked);
    this.accessControlForm.patchValue(newValues);
    this.updateEmployeeNotifications();
  }

  toggleAccordion(accordion: string) {
    if (accordion === 'one') {
      this.isAccordionOneExpanded = !this.isAccordionOneExpanded;
    } else if (accordion === 'two') {
      this.isAccordionTwoExpanded = !this.isAccordionTwoExpanded;
    } else if (accordion === 'three') {
      this.isAccordionThreeExpanded = !this.isAccordionThreeExpanded;
    }
  }

  // Toggle notification section expanded state
  toggleNotificationSection(sectionKey: string) {
    this.notificationSections[sectionKey] = !this.notificationSections[sectionKey];
  }

  // Handle view_order_lines checkbox change
  onViewOrderLinesChange(): void {
    const viewOrderLinesChecked = this.accessControlForm.value.view_order_lines;

    // Enable or disable dependent controls based on view_order_lines state
    if (viewOrderLinesChecked) {
      this.accessControlForm.get('view_prices')?.enable();
      this.accessControlForm.get('send_to_payment')?.enable();
      this.accessControlForm.get('edit_order_lines')?.enable();
    } else {
      // If view_order_lines is unchecked, disable and uncheck all dependent options
      this.accessControlForm.get('view_prices')?.disable();
      this.accessControlForm.get('send_to_payment')?.disable();
      this.accessControlForm.get('edit_order_lines')?.disable();

      this.accessControlForm.patchValue({
        view_prices: false,
        send_to_payment: false,
        edit_order_lines: false
      });
    }

    // Call the general attribute change handler
    this.onAttributeChange();
  }

  expandAll() {
    this.isAccordionOneExpanded = true;
    this.isAccordionTwoExpanded = true;
    this.isAccordionThreeExpanded = true;
  }

  compactAll() {
    this.isAccordionOneExpanded = false;
    this.isAccordionTwoExpanded = false;
    this.isAccordionThreeExpanded = false;
  }

  isAccordionPermissionsExpanded = false;
  // Permissions (mock data, add actual form control names)
  permissionsAccordionOne = [
    'edit_work_order_execution_time',
    'edit_discount',
    'confirm_order',
    'send_to_payment',
    'edit_order_lines',
    'complete_job',
    'view_prices',
    'view_order_lines',
    'edit_employees',
    'create_order',
    'checkin_geo_lock',
    'add_payment_recipient',
    'edit_resources'
  ];

  permissionsAccordionTwo = [
    'view_all_orders_as_crew',
    'view_all_events_as_crew',
    'create_event',
    'edit_company_events',
    'view_unconfirmed_orders',
  ];

  permissionsAccordionThree = [
    'edit_time_tracking',
    'create_manual_time_tracking',
  ];


  // Check if all permissions across Accordion 1, 2, and 3 are selected
  areAllAccordionsSelected(): boolean {
    return [...this.permissionsAccordionOne, ...this.permissionsAccordionTwo, ...this.permissionsAccordionThree]
      .every(permission => this.accessControlForm.controls[permission].value);
  }

  // Toggle all permissions when "Select All Permissions" is clicked
  toggleAllAccordions(event: Event): void {
    const checked = (event.target as HTMLInputElement).checked;
    const newValues: { [key: string]: boolean } = {};

    // Toggle all checkboxes in Accordion 1, 2, and 3
    [...this.permissionsAccordionOne, ...this.permissionsAccordionTwo, ...this.permissionsAccordionThree]
      .forEach(permission => newValues[permission] = checked);

    this.accessControlForm.patchValue(newValues);
    this.updateEmployeeNotifications();
  }

  addSalary() {
    let modalRef = this.modalService.open(EmployeeSalaryModalComponent, {size: 'md'});
    modalRef.componentInstance.userId = this.user_id;
    modalRef.result.then((res: EmployeeSalaryResponse) => {
      if (res) {
        this.salaries.push(res);
      }
    });
  }

  addVacationDays() {
    let modalRef = this.modalService.open(EmployeeVacationModalComponent, {size: 'md'});
    modalRef.componentInstance.userId = this.user_id;
    modalRef.result.then((res: EmployeeVacationDaysResponse) => {
      if (res) {
        this.vacationDays.push(res);
      }
    });
  }

  openSalaryModal(salary: EmployeeSalaryResponse) {
    let modalRef = this.modalService.open(EmployeeSalaryModalComponent, {size: 'md'});
    modalRef.componentInstance.salary = salary;
    modalRef.componentInstance.userId = this.user_id;
    modalRef.result.then((res: EmployeeSalaryResponse) => {
      if (res) {
        this.salaries = this.salaries.map(s => s.entry_id === res.entry_id ? res : s);
      }
    });
  }

  openVacationModal(vacationDays: EmployeeVacationDaysResponse) {
    let modalRef = this.modalService.open(EmployeeVacationModalComponent, {size: 'md'});
    modalRef.componentInstance.vacationDays = vacationDays;
    modalRef.componentInstance.userId = this.user_id;
    modalRef.result.then((res: EmployeeVacationDaysResponse) => {
      if (res) {
        this.vacationDays = this.vacationDays.map(s => s.entry_id === res.entry_id ? res : s);
      }
    });
  }

  deleteSalary(salary: EmployeeSalaryResponse) {
    let params: _CRM_EMP_12 = {
      entry_id: salary.entry_id,
      user_id: this.user_id
    }
    this.employeeService.deleteEmployeeSalary(params).subscribe(() => {
      this.salaries = this.salaries.filter(s => s.entry_id !== salary.entry_id)
    });
  }

  deleteVacationDays(vacationDays: EmployeeVacationDaysResponse) {
    let params: _CRM_EMP_19 = {
      entry_id: vacationDays.entry_id,
      user_id: this.user_id
    }
    this.employeeService.deleteUserVacationDays(params).subscribe(() => {
      this.vacationDays = this.vacationDays.filter(s => s.entry_id !== vacationDays.entry_id)
    });
  }

  protected readonly getFormControl = getFormControl;
  protected readonly formatTimeYMD = formatDateDMY;
  protected readonly formatCurrency = formatCurrency;
  protected readonly currencyFormat = currencyFormat;
}
