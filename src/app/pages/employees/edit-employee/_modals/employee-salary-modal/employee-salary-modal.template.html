<div class="modal-header d-flex justify-content-between align-items-center">
  <h4 class="text-center" style="flex-grow: 1;">{{ salary ? '<PERSON>iger lønn' : '<PERSON><PERSON><PERSON><PERSON> lønn' }}</h4>
</div>
<div class="modal-body row p-3">

  <div *ngIf="!salary" class="mb-2">
    <app-button-double
      [leftButtonTranslationKey]="'Timeslønn'"
      [rightButtonTranslationKey]="'Månedslønn'"
      [leftButtonActive]="hourly"
      [rightButtonActive]="!hourly"
      (leftButtonClick)="toggleHourly(true)"
      (rightButtonClick)="toggleHourly(false)"
    ></app-button-double>

  </div>

  <div>
    <label>{{hourly ? 'Timeslønn' : 'Månedslønn'}}</label>
    <app-input
      [editMode]="true"
      [control]="rateControl"
      [type]="'number'"
      [inputSuffix]="'kr'"
    ></app-input>
  </div>

  <div class="d-flex gap-4">
    <div class="mt-2 position-relative">
      <label>Aktiv fra</label>
      <div *ngIf="!activeFrom" class="clickable-text cursor-pointer" (click)="pickerinoFrom.toggle($event)">Velg dato</div>
      <div *ngIf="activeFrom" class="clickable-text cursor-pointer" (click)="pickerinoFrom.toggle($event)" (mouseenter)="activeFromHovered = true;" (mouseleave)="activeFromHovered = false;">
        {{displayDate(activeFrom, false, false)}}
        <i *ngIf="activeFrom !== null && this.activeFromHovered" class="fa-regular fa-xmark cursor-pointer px-1" (click)="activeFromChanged(null)"></i>
      </div>
       <app-datepickerino
        #pickerinoFrom
        [popup]="true"
        [selectedDates]="[activeFrom!]"
        [disableAfterDate]="getDisabledTo()"
        [referenceDate]="salary?.active_from"
        (datesSelectedEmitter)="activeFromChanged($event[0])"
      ></app-datepickerino>
    </div>

    <div class="mt-2 position-relative">
      <label>Aktiv til</label>
      <div *ngIf="!activeTo" class="clickable-text cursor-pointer" (click)="pickerinoTo.toggle($event)">Velg dato</div>
      <div *ngIf="activeTo" class="clickable-text cursor-pointer" (click)="pickerinoTo.toggle($event)" (mouseenter)="activeToHovered = true;" (mouseleave)="activeToHovered = false;">
        {{displayDate(activeTo, false, false)}}
        <i *ngIf="activeTo !== null && this.activeToHovered" class="fa-regular fa-xmark cursor-pointer px-1" (click)="activeToChanged(null)"></i>
      </div>
       <app-datepickerino
        #pickerinoTo
        [popup]="true"
        [selectedDates]="[activeTo!]"
        [disableBeforeDate]="getDisabledFrom()"
        [referenceDate]="salary?.active_to"
        (datesSelectedEmitter)="activeToChanged($event[0])"
      ></app-datepickerino>
    </div>
  </div>


</div>

<div class="modal-footer justify-content-end pe-2 gap-2">
  <app-button
    [ngbTooltip]="periodCrash ? 'Perioden overlapper med en annen periode' : null"
    [translationKey]="'common.save'"
    [loading]="loading"
    [disabled]="this.rateControl.invalid || this.periodCrash"
    (buttonClick)="save()"
  ></app-button>
  <app-button
    [translationKey]="'common.cancel'"
    (buttonClick)="cancel()"
  ></app-button>
</div>
