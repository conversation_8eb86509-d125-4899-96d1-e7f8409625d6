import {Component, HostListener, Input, OnInit} from '@angular/core';
import {NgbActiveModal, NgbModal, NgbTooltip} from '@ng-bootstrap/ng-bootstrap';
import {CompanySalaryTypeResponse, TimeTrackingActivityResponse, TimeTrackingActivityTypeResponse} from "../../../../../@shared/models/timetracking.interfaces";
import {TranslateModule} from "@ngx-translate/core";
import {NgIf} from "@angular/common";
import {TrackingService} from "../../../../../@shared/services/tracking.service";

import {InputComponent} from "../../../../../@shared/components/input/input.component";
import {FormControl, Validators} from "@angular/forms";
import {_CRM_EMP_11, _CRM_EMP_13, _CRM_TTR_17, _CRM_TTR_18, _CRM_TTR_31, _CRM_TTR_32} from "../../../../../@shared/models/input.interfaces";
import {ButtonComponent} from "../../../../../@shared/components/button/button.component";
import {EmployeeSalaryResponse} from "../../../../../@shared/models/employee.interfaces";
import {EmployeeService} from "../../../../../@shared/services/employee.service";
import {DatepickerinoComponent} from "../../../../../@shared/components/datepickerino/datepickerino.component";
import {displayDate, UtilsService} from "../../../../../@core/utils/utils.service";
import {addDays} from "date-fns";
import {ButtonDoubleComponent} from "../../../../../@shared/components/button-double/button-double.component";
import {StandardImports} from "../../../../../@shared/global_import";

@Component({
    selector: 'app-employee-salary-modal',
    templateUrl: './employee-salary-modal.template.html',
    standalone: true,
  imports: [StandardImports, ButtonDoubleComponent, DatepickerinoComponent]
})

export class EmployeeSalaryModalComponent implements OnInit {

  @Input() salary: EmployeeSalaryResponse | null = null;
  @Input() userId: string;
  rateControl: FormControl = new FormControl(0, [Validators.required, Validators.min(0)]);
  loading: boolean = false;
  activeFrom: Date | null = null;
  activeTo: Date | null = null;
  activeFromHovered: boolean = false;
  activeToHovered: boolean = false;
  existingSalariesHourly: EmployeeSalaryResponse[] = [];
  existingSalariesMonthly: EmployeeSalaryResponse[] = [];
  periodCrash: boolean = false;
  hourly: boolean = true;

  constructor(public activeModal: NgbActiveModal, private employeeService: EmployeeService, public utilsService: UtilsService) { }

  ngOnInit(): void {

    if (this.salary) {
      this.hourly = !!this.salary.hourly_rate;
    }

    this.employeeService.getEmployeeSalaries(this.userId).subscribe((salaries) => {
      this.existingSalariesHourly = salaries.filter((salary) => {
        return (salary.entry_id !== this.salary?.entry_id) && !!salary.hourly_rate;
      });
      this.existingSalariesMonthly = salaries.filter((salary) => {
        return (salary.entry_id !== this.salary?.entry_id) && !!salary.monthly_rate;
      });
      this.periodCrash = this.crashesWithExistingSalaries();
    });

    if (this.salary) {
      this.rateControl.setValue(this.salary.hourly_rate);
      this.activeFrom = this.salary.active_from;
      this.activeTo = this.salary.active_to;
    }
  }

  activeFromChanged(date: Date | null) {
    this.periodCrash = false;
    this.activeFrom = date;
    this.periodCrash = this.crashesWithExistingSalaries();
  }

  activeToChanged(date: Date | null) {
    this.periodCrash = false;
    this.activeTo = date;
    this.periodCrash = this.crashesWithExistingSalaries();
  }

  toggleHourly(hourly: boolean) {
    this.hourly = hourly;
    this.periodCrash = this.crashesWithExistingSalaries();
  }

  crashesWithExistingSalaries() {
    let salarySource = this.hourly ? this.existingSalariesHourly : this.existingSalariesMonthly;
    if (!this.activeFrom && !this.activeTo && salarySource.length > 0) {
      return true;
    }


    if (salarySource.some((salary) => { return !salary.active_from && !salary.active_to })) {
      return true;
    }

    if (this.activeFrom && !this.activeTo) {
      for (let salary of salarySource) {
        if (!salary.active_to || salary.active_to > this.activeFrom) {
          return true;
        }
      }
    }

    if (!this.activeFrom && this.activeTo) {
      for (let salary of salarySource) {
        if (!salary.active_from || salary.active_from < this.activeTo) {
          return true;
        }
      }
    }

    if (this.activeFrom && this.activeTo) {
      for (let salary of salarySource) {
        if (salary.active_from && !salary.active_to) {
          if (salary.active_from < this.activeTo) {
            return true;
          }
        }
        if (!salary.active_from && salary.active_to) {
          if (salary.active_to > this.activeFrom) {
            return true;
          }
        }
        if (salary.active_from && salary.active_to) {
          if (salary.active_from > this.activeFrom && salary.active_to < this.activeTo) {
            return true;
          }
          if (salary.active_to > this.activeFrom && salary.active_to < this.activeTo) {
            return true;
          }
          if (salary.active_from > this.activeFrom && salary.active_from < this.activeTo) {
            return true;
          }
          if (salary.active_from < this.activeFrom && salary.active_to > this.activeTo) {
            return true;
          }
        }
      }
    }
    return false;
  }

  getDisabledFrom() {
    return this.activeFrom ? addDays(this.activeFrom, 1) : null;
  }

  getDisabledTo() {
    return this.activeTo ? addDays(this.activeTo, -1) : null;
  }


  save() {
    if (this.rateControl.invalid) {
      return;
    }
    this.loading = true;
    // Put
    if (this.salary) {
      let payload: _CRM_EMP_13 = {
        user_id: this.userId,
        entry_id: this.salary.entry_id,
        active_from: this.activeFrom,
        active_to: this.activeTo,
      }

      if (this.hourly) {
        payload.hourly_rate = this.rateControl.value;
      } else {
        payload.monthly_rate = this.rateControl.value;
      }

      this.employeeService.updateEmployeeSalary(payload).subscribe((salary) => {
        this.activeModal.close(salary);
        this.loading = false;
      }, error => {
        this.loading = false;
      });
    } else {
      // Post
      let payload: _CRM_EMP_11 = {
        user_id: this.userId,
        active_from: this.activeFrom,
        active_to: this.activeTo,
        salary_type: this.hourly ? 'hourly' : 'monthly'
      }

      if (this.hourly) {
        payload.hourly_rate = this.rateControl.value;
      } else {
        payload.monthly_rate = this.rateControl.value;
      }

      this.employeeService.createEmployeeSalary(payload).subscribe((salary) => {
        this.activeModal.close(salary);
        this.loading = false;
      }, error => {
        this.loading = false;
      });
    }
  }

  cancel() {
    this.activeModal.close(false)
  }

  protected readonly displayDate = displayDate;
}
