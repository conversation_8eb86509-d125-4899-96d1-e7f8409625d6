import {AfterViewInit, Component, forwardRef, OnChanges, OnInit, Optional, SimpleChanges} from '@angular/core';
import {NgbActiveModal, NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {BehaviorSubject, debounceTime, forkJoin, pairwise, Subject} from "rxjs";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {FormControl, FormsModule, ReactiveFormsModule} from "@angular/forms";
import {OrderResponseCompact, OrderStatusResponse} from "../../../@shared/models/order.interfaces";
import {currencyFormat, displayDate, orderBadgeStatus, paymentStatusBadge, UtilsService} from "../../../@core/utils/utils.service";
import {OrderService} from "../../../@shared/services/order.service";
import {TablerinoColumn, TablerinoSettings} from "../../../@shared/components/tablerino/tablerino.component";
import {_CRM_ORD_165, _CRM_ORD_2} from "../../../@shared/models/input.interfaces";
import {takeUntil} from "rxjs/operators";

import {PaginationResponse} from "../../../@shared/models/response.interfaces";
import {PaginationContainer} from "../../../@shared/models/global.interfaces";
import {CustomActionButton, HeaderFilterComponent, HeaderFiltersContainer, SortContainer} from "../../../@shared/components/tablerino-header/tablerino-header.component";
import {EmployeeService} from "../../../@shared/services/employee.service";
import {UserEntityRelationWithUserDataResponse} from "../../../@shared/models/user.interfaces";
import {StorageService} from "../../../@core/services/storage.service";
import {Router} from "@angular/router";
import {ProductService} from "../../../@shared/services/product.service";
import {ProductCompactResponse} from "../../../@shared/models/product.interfaces";
import {AdvancedOrderSearchModal} from "../../calendar/resource-calendar/v1/components/search-events-modal/advanced-order-search-modal.component";
import {VerifyPopupModal} from "../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {StandardImports} from "../../../@shared/global_import";
import {PageHeaderComponent} from "../../../@shared/components/page-header/page-header.component";
import {TablerinoCompleteComponent} from "../../../@shared/components/tablerino-complete/tablerino-complete.component";
import {NewWorkOrderComponent} from "../../work-orders/components/new-work-order/new-work-order.component";

export interface OrderRow extends OrderResponseCompact {
  selected: boolean;
}


@Component({
    selector: 'app-orders-overview',
    templateUrl: './orders-overview.component.html',
    styleUrls: ['./orders-overview.component.css'],
    standalone: true,
    imports: [StandardImports, PageHeaderComponent, TablerinoCompleteComponent]
})
export class OrdersOverviewV2Component implements OnInit, OnChanges, AfterViewInit {
  columnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);
  orderRows: OrderRow[];
  settings: TablerinoSettings = {
    checkboxes: true,
    clickableRows: true,
  }
  loading: boolean = false;
  dataResponse: PaginationResponse<OrderResponseCompact[]>;
  paginationSubject: BehaviorSubject<PaginationContainer> = new BehaviorSubject<PaginationContainer>({page: 1, limit: 25, paginate: 1, totalPages: 0, totalItems: 0});
  selectedRowsSubject: BehaviorSubject<OrderRow[]> = new BehaviorSubject<OrderRow[]>([]);
  headerFiltersContainerSubject: BehaviorSubject<HeaderFiltersContainer> = new BehaviorSubject<HeaderFiltersContainer>({filters: [], init: true});
  sortingSubject: BehaviorSubject<SortContainer> = new BehaviorSubject<SortContainer>({sortDirection: 'desc', sortOptions: []});
  actionButtonsSubject: BehaviorSubject<CustomActionButton[]> = new BehaviorSubject<CustomActionButton[]>([]);
  advancedSearchActiveSubject: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  advancedSearchParams: _CRM_ORD_165 = {};

  cancelCurrentRequest$ = new Subject<void>();
  ready = false;

  operateExVat: boolean = false;

  constructor(public utilsService: UtilsService,
              @Optional() public activeModal: NgbActiveModal,
              private orderService: OrderService,
              private modalService: NgbModal,
              private translate: TranslateService,
              private employeeService: EmployeeService,
              private storageService: StorageService,
              private productService: ProductService,
              private router: Router) {
  }

  ngOnInit() {
    this.initializeColumns();
    this.initializeHeaderFilters();
    this.initialiseSortOptions();
    this.initialiseActionButtons();
    this.getOrders();

    this.storageService.operateExVat$.subscribe((value) => {
      this.operateExVat = value;
    });

    this.paginationSubject.pipe(pairwise()).subscribe((paginationDetails) => {
      if (paginationDetails[0].page !== paginationDetails[1].page) {
        if (this.advancedSearchActiveSubject.value) {
          this.executeAdvancedSearch()
        } else {
          if (this.ready) {
            this.getOrders();
          }
        }
      }
    });
    this.headerFiltersContainerSubject.subscribe((headerFilters) => {
      if (!headerFilters.init) {
        this.getOrders();
      }
    });
    this.selectedRowsSubject.subscribe((selectedRows) => {
      if (selectedRows.length > 0) {
        this.actionButtonsSubject.value.forEach((button) => {
          if (button.functionName === 'archiveOrders' || button.functionName === 'cancelOrders') {
            button.disabled = false;
          }
        });
      } else {
        this.actionButtonsSubject.value.forEach((button) => {
          if (button.functionName === 'archiveOrders' || button.functionName === 'cancelOrders') {
            button.disabled = true;
          }
        });
      }
    });
  }

  ngAfterViewInit() {
    this.ready = true;
  }

  ngOnChanges(simpleChanges: SimpleChanges) {
  }

  getOrders(searchTerm: string = '') {
    this.advancedSearchActiveSubject.next(false);
    this.cancelCurrentRequest$.next();
    this.loading = true;

    let filters = this.headerFiltersContainerSubject.value.filters;
    let sortColumn = this.columnsSubject.value.find(col => col.sortedAsc || col.sortedDesc) || null;
    let sortKey: string = sortColumn?.name || 'created_at';
    let sortDirection: 'asc' | 'desc' = sortColumn ? (sortColumn.sortedAsc ? 'asc' : 'desc') : 'desc';

    let quoteSent = filters.find(hf => hf.parameterName === 'quote_sent')!.dropDownOptions!.find(ddo => ddo.active)?.value || null
    let containsSchedules = filters.find(hf => hf.parameterName === 'contains_schedules')!.dropDownOptions!.find(ddo => ddo.active)?.value || null

    let params: _CRM_ORD_2 = {
      paginate: 1,
      page: this.paginationSubject.value.page,
      limit: this.paginationSubject.value.limit,
      archived: filters.find(hf => hf.parameterName === 'archived')!.active ? 1 : 0,
      order_status_ids: filters.find(hf => hf.parameterName === 'order_status_ids')!.dropDownOptions!.filter(ddo => ddo.active).map(ddo => Number(ddo.value!)),
      product_ids: filters.find(hf => hf.parameterName === 'product_ids')!.dropDownOptions!.filter(ddo => ddo.active).map(ddo => Number(ddo.value!)),
      user_ids: filters.find(hf => hf.parameterName === 'user_ids')!.dropDownOptions!.filter(ddo => ddo.active).map(ddo => ddo.value) as string[],
      quote_sent: quoteSent === null ? null : Number(quoteSent),
      contains_schedules: containsSchedules === null ? null : containsSchedules === 'true',
      order_by: sortKey,
      order_direction: sortDirection,
      execution_date_from: filters.find(hf => hf.parameterName === 'execution_at')!.dateRangeFromControl!.value,
      execution_date_to: filters.find(hf => hf.parameterName === 'execution_at')!.dateRangeToControl!.value,
      search_string: searchTerm,
    }

    this.orderService.getAllOrdersCompact(params).pipe(takeUntil(this.cancelCurrentRequest$)).subscribe((res) => {
      this.dataResponse = res;
      this.paginationSubject.next({
        ...this.paginationSubject.value,
        totalItems: res.total_items,
        totalPages: res.total_pages
      });
      this.orderRows = res.data.map((order) => {
        return {
          ...order,
          selected: false,
        }
      });
      this.loading = false;
    }, error => {
      this.loading = false
    });
  }

  initializeColumns() {
    this.columnsSubject.next([
      {
        name: 'order_number',
        labelKey: 'orders.orderList.orderID',
        formatter: (order: OrderResponseCompact) => this.formatOrderNumber(order),
        sort: true,
        visible: true,
      },
      {
        name: 'order_title',
        labelKey: 'orders.orderList.title',
        formatter: (order: OrderResponseCompact) => order.order_title,
        sort: true,
        visible: true,
      },
      {
        name: 'service_recipient',
        labelKey: 'orders.orderList.serviceRecipient',
        formatter: (order: OrderResponseCompact) => this.orderServiceRecipientWithSymbolsFormatter(order),
        sort: false,
        visible: true,
      },
      {
        name: 'customerName',
        labelKey: 'orders.orderList.customer',
        formatter: (order: OrderResponseCompact) => this.orderCustomerWithSymbolsFormatter(order),
        sort: false,
        visible: true,
      },
      {
        name: 'order_source',
        labelKey: 'orders.orderList.source',
        formatter: (order: OrderResponseCompact) => order.order_source_name,
        sort: false,
        visible: true,
      },
      {
        name: 'order_status_id',
        labelKey: 'orders.orderList.status',
        formatter: (order: OrderResponseCompact) => orderBadgeStatus(order),
        sort: true,
        visible: true,
      },
      {
        name: 'payment_status_id',
        labelKey: 'orders.orderList.payment',
        formatter: (order: OrderResponseCompact) => order.repeating ? '' : paymentStatusBadge(order),
        sort: true,
        visible: true,
      },
      {
        name: 'total_amount_inc_vat',
        labelKey: 'orders.orderList.sum',
        formatter: (order: OrderResponseCompact) => this.formatOrderAmount(order),
        sort: false,
        visible: true,
      },
      {
        name: 'execution_at',
        labelKey: 'orders.orderList.executionAt',
        formatter: (order: OrderResponseCompact) => displayDate(order.execution_at),
        sort: true,
        visible: true,
      },
      // {
      //   name: 'comment',
      //   labelKey: 'orders.orderList.comment',
      //   formatter: (order: OrderResponseCompact) => order.comment,
      //   align: 'left',
      //   sort: false,
      //   visible: true,
      // },
      {
        name: 'address',
        labelKey: 'orders.orderList.address',
        formatter: (order: OrderResponseCompact) => order.display_address,
        sort: false,
        visible: true,
      },
      {
        name: 'feedback_rating',
        labelKey: 'orders.orderList.feedbackRating',
        formatter: (order: OrderResponseCompact) => this.formatFeedbackRating(order.feedback_rating),
        sort: false,
        visible: true,
      }
    ]);
  }

  initializeHeaderFilters() {
    let headerFilters: HeaderFilterComponent[] = [
      {
        parameterName: 'archived',
        translationKey: 'orders.archivedBtn',
        active: false,
      },
      {
        parameterName: 'order_status_ids',
        translationKey: 'orders.filterStatus',
        multiSelect: true,
        dropDownOptions: [],
        active: false,
      },
      {
        parameterName: 'product_ids',
        translationKey: 'orders.filterProducts',
        multiSelect: true,
        dropDownOptions: [],
        active: false,
      },
      {
        parameterName: 'user_ids',
        translationKey: 'workOrder.list.filter.employees',
        multiSelect: true,
        dropDownOptions: [],
        active: false,
      },
      {
        parameterName: 'quote_sent',
        translationKey: 'orders.filterQuote',
        multiSelect: false,
        dropDownOptions: [
          {
            value: 1,
            translationKey: 'orders.quoteSent',
            active: false,
          },
          {
            value: 0,
            translationKey: 'orders.quoteNotSent',
            active: false,
          }
        ],
        active: false,
      },
      {
        parameterName: 'contains_schedules',
        translationKey: 'orders.filterSchedules',
        multiSelect: false,
        dropDownOptions: [
          {
            value: 'false',
            translationKey: 'orders.filterSchedules.single',
            active: false,
          },
          {
            value: 'true',
            translationKey: 'orders.filterSchedules.repeating',
            active: false,

          }
        ],
        active: false,
      },
      {
        parameterName: 'execution_at',
        translationKey: 'workOrder.list.filter.execution',
        active: false,
        dateRange: true,
        dateRangeFromControl: new FormControl(),
        dateRangeToControl: new FormControl(),
        dateRangeFromParamKey: 'execution_date_from',
        dateRangeToParamKey: 'execution_date_to',
      },
    ];

    const requests = [
      this.orderService.getOrderStatuses(),
      this.productService.getCompactProducts(),
      this.employeeService.getEmployeesInCompany({}),
    ];

    forkJoin(requests).subscribe({
      next: (res) => {
        let orderStatuses = res[0] as OrderStatusResponse[];
        let products = res[1] as ProductCompactResponse[];
        let employees = res[2] as UserEntityRelationWithUserDataResponse[];


        // Map order statuses
        headerFilters.find(hf => hf.parameterName === 'order_status_ids')!.dropDownOptions = orderStatuses.sort
        ((a, b) => a.order_status_id - b.order_status_id).map
        ((status: OrderStatusResponse) => {
          return {
            value: status.order_status_id,
            translationKey: status.order_status_name,
            active: false,
          }
        });

        // Map products
        headerFilters.find(hf => hf.parameterName === 'product_ids')!.dropDownOptions = products.map
        ((product: ProductCompactResponse) => {
          return {
            value: product.product_id,
            translationKey: product.product_name,
            active: false,
          }
        });

        // Map employees
        headerFilters.find(hf => hf.parameterName === 'user_ids')!.dropDownOptions = employees.map
        ((employee: UserEntityRelationWithUserDataResponse) => {
          return {
            value: employee.user_id,
            translationKey: employee.full_name,
            active: false,
          }
        });

        this.headerFiltersContainerSubject.next({filters: headerFilters, init: true});
      }
    });

    this.headerFiltersContainerSubject.next({filters: headerFilters, init: true});
  }

  initialiseSortOptions() {
    this.sortingSubject.next(
      {
        sortOptions:
          [
            {
              value: 'execution_at',
              translationKey: 'workOrder.list.filter.execution',
              active: false,
            },
            {
              value: 'created_at',
              translationKey: 'workOrder.list.sorting.created',
              active: false,
            },
          ],
        sortDirection: 'desc'
      }
    );
  }

  initialiseActionButtons() {
    this.actionButtonsSubject.next([
      {
        translationKey: 'orders.overview.archiveSelected',
        iconClass: 'fa-regular fa-inbox-in me-1',
        functionName: 'archiveOrders',
        placeInDropdown: true,
        disabled: true
      },
      {
        translationKey: 'orders.overview.cancelSelected',
        iconClass: 'fa-regular fa-delete-right me-1',
        functionName: 'cancelOrders',
        placeInDropdown: true,
        disabled: true
      }
    ]);
  }

  rowClicked(row: OrderRow) {
    this.navigateToOrder(row);
  }


  setOrderSymbols(order: OrderResponseCompact): string | void {
    let html = '';

    // Subcontracting
    if (order.has_subcontractors == 1 && order.company_id === this.storageService.getSelectedCompanyId()) {
      html += `  <i class="fa-regular fa-user-helmet-safety fa-lg"></i>`;
    }
    if (order.company_id != this.storageService.getSelectedCompanyId()) {
      html += `  <i class="fa-regular fa-user-tie-hair fa-lg"></i>`;
    }
    return html;
  }

  orderCustomerWithSymbolsFormatter(order: OrderResponseCompact): string {
    let html = '';
    if (order.payment_recipient) {
      // Add customer name
      html += `${order.payment_recipient.name}`;

      // Add icon for non-private customers
      if (order.payment_recipient.is_private != 1) {
        html += `<i class="fa-regular fa-buildings fa-lg mx-1"></i>`;
      }
    }

    return html;
  }

  orderServiceRecipientWithSymbolsFormatter(order: OrderResponseCompact): string {
    let html = '';
    if (order.service_recipient) {
      // Add customer name
      if (order.service_recipient) {
        html += `${order.service_recipient.name}`;

        // Add icon for non-private customers
        if (order.service_recipient.is_private != 1) {
          html += `<i class="fa-regular fa-buildings fa-lg mx-1"></i>`;
        }
      } else {
        html += this.translate.instant('common.noData');
      }

    }
    return html;
  }


  navigateToOrder(event: any) {
    this.router.navigateByUrl("orders/details/" + event.order_id)
  }

  formatOrderAmount(order: OrderResponseCompact): string {
    if (this.operateExVat) {
      if (order.repeating) {
        return currencyFormat(order.top_schedule_total_amount_ex_vat);
      } else {
        return currencyFormat(order.total_amount_ex_vat);
      }
    } else {
      if (order.repeating) {
        return currencyFormat(order.top_schedule_total_amount_inc_vat);
      } else {
        return currencyFormat(order.total_amount_inc_vat);
      }
    }
  }

  formatFeedbackRating(rating: number): string {
    if (rating === null || rating === undefined) {
      return 'No rating'; // Handle cases where rating is not provided
    }

    // Define the full star and empty star icons with appropriate colors
    const fullStar = '<i class="fa fa-star" style="color: gold;"></i>'; // Filled star icon
    const emptyStar = '<i class="fa fa-star-o" style="color: gray;"></i>'; // Empty star icon

    let stars = '';
    for (let i = 1; i <= 5; i++) {
      // Add a filled star if the current index is less than or equal to the rating
      stars += i <= rating ? fullStar : emptyStar;
    }

    return stars;
  }

  formatOrderNumber(order: OrderResponseCompact): string {
    let html = '<span>#' + order.order_number + '</span>';
    if (order.contains_active_work_order_schedules || order.contains_active_payment_schedules) {
      html += '<i class="fa fa-repeat text-success ms-1"></i>';
    } else if (order.contains_work_order_schedules || order.contains_payment_schedules || order.repeating) {
      html += '<i class="fa fa-repeat ms-1"></i>';
    }

    // Add symbols using setOrderSymbols method
    html += this.setOrderSymbols(order);

    return html;
  }

  executeAdvancedSearch() {
    this.advancedSearchActiveSubject.next(true);
    this.loading = true;
    this.cancelCurrentRequest$.next();

    let params: _CRM_ORD_165 = {
      ...this.paginationSubject.value,
      ...this.advancedSearchParams
    }

    this.orderService.advancedSearch(params).pipe(debounceTime(1), takeUntil(this.cancelCurrentRequest$)).subscribe((res) => {
      this.dataResponse = res;
      this.orderRows = res.data.map((order) => {
        return {
          ...order,
          selected: false,
        }
      });
      this.paginationSubject.next({
        ...this.paginationSubject.value,
        totalItems: res.total_items,
        totalPages: res.total_pages
      });
      this.loading = false;
    }, (error) => {
      console.log(error);
      this.loading = false;
    });
  }

  initiateAdvancedSearch() {
    const modalRef = this.modalService.open(AdvancedOrderSearchModal, {windowClass: 'fixed-modal', size: 'xl'});
      modalRef.result.then((params: _CRM_ORD_165) => {
        if (params) {
          this.advancedSearchActiveSubject.next(true);
          this.paginationSubject.next({...this.paginationSubject.value, page: 1});
          this.advancedSearchParams = params;
          this.executeAdvancedSearch();
        }
      })
  }

  archiveOrders() {
    let modalRef = this.modalService.open(VerifyPopupModal)
    modalRef.result.then((result) => {
      if (result) {
        const requests = this.selectedRowsSubject.value.map((row) => {
          return this.orderService.archiveOrder(row.order_id);
        });
        this.loading = true;
        forkJoin(requests).subscribe(() => {
          this.getOrders();
        }, error => {
          console.log(error);
          this.getOrders();
        });
      }
    });
  }

  cancelOrders() {
    let modalRef = this.modalService.open(VerifyPopupModal)
    modalRef.result.then((result) => {
      if (result) {
        const requests = this.selectedRowsSubject.value.map((row) => {
          return this.orderService.cancelOrder(0, row.order_id);
        });
        this.loading = true;
        forkJoin(requests).subscribe(() => {
          this.getOrders();
        }, error => {
          console.log(error);
          this.getOrders();
        });
      }
    });
  }

  async onAddNewOrder() {
    let modalRef = this.modalService.open(NewWorkOrderComponent)
  }

  actionButtonClicked(functionName: string) {
    if (functionName == 'advancedSearch') {
      this.initiateAdvancedSearch()
    }
    else if (functionName == 'archiveOrders') {
      this.archiveOrders();
    } else if (functionName == 'cancelOrders') {
      this.cancelOrders();
    }
  }
}
