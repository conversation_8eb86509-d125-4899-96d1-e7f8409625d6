<app-card
[labelKey]="'order.orderDetails.customer.title'"
[buttonHeader]="!!order.payment_recipient || !!order.service_recipient"
[padding]="'0'">
  <ng-container buttonHeader>
    <app-button
      *ngIf="order.payment_recipient || order.service_recipient"
      customClass="p-0"
      [translationKey]="order.payment_recipient ? 'order.orderDetails.customer.changeCustomer' : 'order.orderDetails.customer.setCustomer'"
      [small]="true"
      [buttonType]="'link'"
      [disabled]="hasSentPayments"
      [ngbTooltip]="hasSentPayments ? 'Denne ordren har sendte fakturaer, du kan derfor ikke endre kunde' : null"
      (buttonClick)="openChangeCustomerModal()"
    ></app-button>
  </ng-container>

  <ng-container cardcontent *ngIf="order && !order.payment_recipient">
    <div class="p-3">
      <app-selectorini
        [zIndex]="100"
        [searchFunction]="customerService.search.bind(customerService)"
        [searchMainDisplayKeys]="['name']"
        [selectedDisplaySeparator]="', '"
        [selectedMainDisplayKeys]="['name']"
        [searchSubDisplayKeys]="['phone', 'email', 'organisation_number']"
        [searchMainParenthesisedDisplayKeys]="['company_name']"
        [selectedMainParenthesisedDisplayKeys]="['company_name']"
        [fallBackDisplayKeys]="['phone', 'email', 'organisation_number']"
        [placeholderTranslationKey]="'order.orderDetails.customer.setCustomer'"
        [createItemTranslationKey]="'addOrder.customer.createNewCustomerButton'"
        [showIcons]="true"
        [icons]="customerIcons"
        [disableOnSelect]="true"
        [showCreateItem]="true"
        (itemSelectedEmitter)="handleCustomerSelected($event)"
        (createItemEmitter)="showNewCustomer()"
      ></app-selectorini>
    </div>
  </ng-container>

  <ng-container cardcontent *ngIf="order && order.payment_recipient">
    <!--  Payment recipient  -->
    <div class="customer-card-padding" [ngClass]="{'border-bottom': !order.contractorView}">
      <div class="d-flex justify-content-between" *ngIf="order.single_affiliate == 0">
        <label class="mb-2 inner-card-title">{{ "order.orderDetails.customer.paymentRecipient" | translate }}</label>
      </div>
      <div class="d-flex align-items-center mb-2">
        <div class="initials-box" (click)="navigateToPaymentRecipient()" style="cursor: pointer">
          <span *ngIf="order.payment_recipient.is_private == 1">{{paymentRecipientInitials}}</span>
          <i *ngIf="order.payment_recipient.is_private == 0" class="fa-regular fa-buildings fa-xl"></i>
        </div>
        <div class="ps-2 fw-bold" (click)="navigateToPaymentRecipient()" style="cursor: pointer">{{ order.payment_recipient.name }}</div>
      </div>

       <div class="mb-2 cursor-pointer" *ngIf="order.affiliate_contact" style="overflow: hidden;" (click)="openAffiliateContactModal()">
        <i class="fa-regular fa-building-user"></i>
        {{ order.affiliate_contact.name}}
      </div>

      <div *ngIf="!order.payment_recipient.is_private && !order.affiliate_contact" class="mb-2 clickable-text" [ngbTooltip]="'For at kunden skal få tilgang til kundeportalen må du legge til en bedriftskontakt som representerer kunden. Klikk for å legge til.'" (click)="openAddAffiliateContactModal()">
        <i class="fa-regular fa-warning text-warning me-1"></i>
        <span class="text-muted">Denne ordren har ingen bedriftskontakt</span>
      </div>

      <div class="mb-2" style="overflow: hidden;">
        <i class="fa-regular fa-phone"></i>
        {{displayPhoneNumber(order.affiliate_contact ? order.affiliate_contact.phone : order.payment_recipient.phone)}}
        <i *ngIf="order.affiliate_contact ? order.affiliate_contact.phone : order.payment_recipient.phone" class="fa-regular fa-copy" style="float: right; cursor: pointer;" (click)="copyValue(displayPhoneNumber(order.affiliate_contact ? order.affiliate_contact.phone : order.payment_recipient.phone))"></i>
        <span *ngIf="!(order.affiliate_contact ? order.affiliate_contact.phone : order.payment_recipient.phone)" class="me-1 text-muted"><i class="fa-regular fa-warning text-warning me-1"></i>{{"common.noPhone" | translate}}</span>
      </div>
      <div class="mb-2" style="overflow: hidden;">
        <i class="fa-regular fa-envelope"></i>
        {{ order.affiliate_contact ? order.affiliate_contact.email : order.payment_recipient.email }}
        <i *ngIf="order.affiliate_contact ? order.affiliate_contact.email : order.payment_recipient.email" class="fa-regular fa-copy" style="float: right; cursor: pointer;" (click)="copyValue(order.affiliate_contact ? order.affiliate_contact.email : order.payment_recipient.email)"></i>
        <span *ngIf="!(order.affiliate_contact ? order.affiliate_contact.email : order.payment_recipient.email)" class="me-1 text-muted"><i class="fa-regular fa-warning text-warning me-1"></i>{{"common.noEmail" | translate}}</span>
      </div>
      <div *ngIf="order.payment_recipient.organisation_number" style="overflow: hidden;">
        <i class="fa-regular fa-building"></i>
        {{ order.payment_recipient.organisation_number }}
        <i class="fa-regular fa-copy" style="float: right; cursor: pointer;" (click)="copyValue(order.payment_recipient.organisation_number)"></i>
      </div>

      <div *ngIf="order.affiliate_contact" class="mt-2">
        <app-button
          customClass="p-0"
          [translationKey]="(!order.affiliate_contact ? 'order.orderDetails.customer.addAffiliateContact' : 'order.orderDetails.customer.changeAffiliateContact')"
          [small]="true"
          [buttonType]="'link'"
          [iconClass]="'fa-regular fa-plus me-1'"
          [iconPlacement]="'left'"
          [disabled]="hasSentPayments"
          [ngbTooltip]="hasSentPayments ? 'Denne ordren har sendte fakturaer, du kan derfor ikke endre eller legge til bedriftskontakt' : null"
          (buttonClick)="openAddAffiliateContactModal()"
        ></app-button>
      </div>

    </div>

    <!--  Service recipient  -->
    <div class="customer-card-padding" *ngIf="order.single_affiliate == 0" [ngClass]="{'border-bottom': !order.contractorView}">
      <div class="d-flex justify-content-between">
        <label class="mb-2 inner-card-title">{{ "order.orderDetails.customer.serviceRecipient" | translate }}</label>
      </div>

      <div *ngIf="order.service_recipient">
        <div class="d-flex align-items-center mb-2">
          <div class="initials-box" (click)="navigateToServiceRecipient()" style="cursor: pointer">
            <span *ngIf="order.service_recipient.is_private == 1">{{serviceRecipientInitials}}</span>
            <i *ngIf="order.service_recipient.is_private == 0" class="fa-regular fa-buildings fa-xl"></i>
          </div>
          <div class="ps-2 fw-bold" (click)="navigateToServiceRecipient()" style="cursor: pointer">{{ order.service_recipient.name }}</div>
        </div>

        <div class="mb-2" style="overflow: hidden;">
          <i class="fa-regular fa-phone "></i>
          {{displayPhoneNumber(order.service_recipient.phone)}}
          <i *ngIf="order.service_recipient.phone" class="fa-regular fa-copy" style="float: right; cursor: pointer;" (click)="copyValue(displayPhoneNumber(order.service_recipient.phone))"></i>
          <span *ngIf="!(order.service_recipient.phone)" class="ms-1 text-muted"><i class="fa-regular fa-warning text-warning me-1"></i>{{"common.noPhone" | translate}}</span>
        </div>
        <div style="overflow: hidden;">
          <i class="fa-regular fa-envelope"></i>
          {{ order.service_recipient.email }}
          <i *ngIf="order.service_recipient.email" class="fa-regular fa-copy" style="float: right; cursor: pointer;" (click)="copyValue(order.service_recipient.email)"></i>
          <span *ngIf="!(order.service_recipient.email)" class="ms-1 text-muted"><i class="fa-regular fa-warning text-warning me-1"></i>{{"common.noEmail" | translate}}</span>
        </div>
        <div class="mt-2" *ngIf="order.service_recipient.organisation_number" style="overflow: hidden;">
          <i class="fa-regular fa-building"></i>
          {{ order.service_recipient.organisation_number }}
          <i class="fa-regular fa-copy" style="float: right; cursor: pointer;" (click)="copyValue(order.service_recipient.organisation_number)"></i>
        </div>
      </div>

      <div *ngIf="!order.service_recipient">{{"order.orderDetails.customer.noServiceRecipient" | translate}}</div>

    </div>

    <!--  Partner  -->
    <div *ngIf="!order.partner && partnersEnabled && !order.contractorView && order.locked != 1 " class="customer-card-padding border-bottom">
      <div class="d-flex justify-content-between mb-1 ">
        <div class="inner-card-title">{{ "order.orderDetails.customer.partner" | translate }}</div>
        <app-button
          *ngIf="order.locked != 1"
          customClass="p-0"
          [translationKey]="'common.add'"
          [buttonType]="'link'"
          [disabled]="hasSentPayments"
          [ngbTooltip]="hasSentPayments ? 'Denne ordren har sendte fakturaer, du kan derfor ikke legge til partner' : null"
          (buttonClick)="openPartnerModal()" [small]="true"
        ></app-button>
      </div>
      <div>{{ "order.orderDetails.customer.partner.selectPartner" | translate }}</div>
    </div>

    <!--  Partner info  -->
    <div *ngIf="order.partner && !order.contractorView">
      <div class="customer-card-padding border-bottom" [ngClass]="{'border-bottom': !order.contractorView}">
        <div class="d-flex justify-content-between mb-1">
          <div class="inner-card-title">{{ "order.orderDetails.customer.partner" | translate }}</div>
          <div *ngIf="order.locked != 1" class="m-0 cursor-pointer" style="color: var(--primary-color);" (click)="openPartnerModal()"> {{ "common.remove" | translate }} </div>
        </div>
        <div *ngIf="!order.partner"><i class="fa-regular fa-handshake"></i>{{ "order.orderDetails.customer.partner.selectPartner" | translate }}</div>
        <div><i class="fa-regular fa-handshake me-1"></i> <span class="fw-bold">{{ order.partner.company_name }}</span> {{ "order.orderDetails.customer.partner.partnerChosen" | translate }}</div>
        <div *ngIf="order.partner_contact"> <span class="fw-bold">{{ "order.orderDetails.customer.partner.contact" | translate }}:</span> {{ order.partner_contact.full_name }}</div>
      </div>
    </div>

    <div class="customer-card-padding">
      <div class="fw-bold mb-1"> {{ "orders.orderDetails.customerRating" | translate }}</div>
      <div class="title" *ngIf="order?.feedback_rating">
      <span class="star" *ngFor="let star of stars">
        <i class="fa-regular fa-star fa-xl" *ngIf="star > order.feedback_rating!"></i>
        <i class="fa-solid fa-star fa-xl" style="color: #5377e6;" *ngIf="star <= order.feedback_rating!"></i>
      </span>
      </div>
      <div *ngIf="order.feedback_comment">{{ order.feedback_comment }}</div>
      <p class="mb-0" *ngIf="!order?.feedback_rating">{{ "orderDetails.customerFeedback.CustomerRating.customerHasNotRatedYet" | translate }}</p>
    </div>

  </ng-container>
</app-card>


