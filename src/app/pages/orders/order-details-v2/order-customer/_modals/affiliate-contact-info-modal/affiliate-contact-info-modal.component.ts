import {Component, Input} from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {displayPhone, UtilsService} from "../../../../../../@core/utils/utils.service";
import {AffiliateContactResponse} from "../../../../../../@shared/models/affiliate.interfaces";
import {ToastService} from "../../../../../../@core/services/toast.service";
import {StandardImports} from "../../../../../../@shared/global_import";

@Component({
    selector: 'order-verify-external-payment-modal',
    templateUrl: './affiliate-contact-info-modal.component.html',
    standalone: true,
    imports: [StandardImports]
})
export class AffiliateContactInfoModalComponent {
  @Input() affiliateContact: AffiliateContactResponse

  constructor(public activeModal: NgbActiveModal, public utilsService: UtilsService, private toastService: ToastService) { }

  copyValue(value: string | null): void {
    if (!value) {
      return;
    }
    navigator.clipboard.writeText(value).then(
      () => {
        this.toastService.successToast('copy')
      },
      (err) => console.error('Could not copy text: ', err)
    );
  }

  protected readonly displayPhoneNumber = displayPhone;
}
