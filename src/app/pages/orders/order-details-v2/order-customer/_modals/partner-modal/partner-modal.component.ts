import {Component, Input, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {OrderResponse} from "../../../../../../@shared/models/order.interfaces";
import {FormControl, FormGroup} from "@angular/forms";
import {NgbActiveModal, <PERSON><PERSON><PERSON><PERSON><PERSON>, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {OrderService} from "../../../../../../@shared/services/order.service";
import {debounceTime, firstValueFrom, merge, Observable, of, Subject} from "rxjs";
import {catchError, distinctUntilChanged, switchMap, takeUntil, tap} from "rxjs/operators";
import {_CRM_ORD_56} from "../../../../../../@shared/models/input.interfaces";
import {AffiliateService} from "../../../../../../@shared/services/affiliate.service";
import {AffiliateSearchResponse} from "../../../../../../@shared/models/affiliate.interfaces";
import {VerifyPopupModal} from "../../../../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {StandardImports} from "../../../../../../@shared/global_import";
import {SelectoriniComponent} from "../../../../../../@shared/components/selectorini/selectorini.component";


@Component({
    selector: 'order-partner-modal',
    templateUrl: './partner-modal.component.html',
    styleUrls: ['./partner-modal.component.css'],
    standalone: true,
  imports: [StandardImports, NgbHighlight, SelectoriniComponent]
})
export class PartnerModalComponent implements OnInit, OnDestroy {
  @Input() order: OrderResponse;
  partnerForm: FormGroup;
  searching = false;
  searchFailed = false;
  predefinedSearchResults: AffiliateSearchResponse[] = [];
  destroy$ = new Subject<void>();
  partnerIcons = {
    0: 'fa-building',
    1: 'fa-user',
  }

  constructor(public activeModal: NgbActiveModal,
              private orderService: OrderService,
              private affiliateService: AffiliateService,
              private modalService: NgbModal
  ) {
  }

  ngOnInit(): void {
    this.partnerForm = new FormGroup({
      company_name: new FormControl(),
      organisation_number: new FormControl(),
      phone: new FormControl(),
      email: new FormControl(),
      address: new FormControl(),
      city: new FormControl(),
      postal_code: new FormControl(),
      country: new FormControl(),
    });

    this.affiliateService.searchForPartnerAffiliatesAndContacts().subscribe(res => {
      this.predefinedSearchResults = res;
    })
  }

ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  search = (text$: Observable<string>) =>
    merge(
      of(''),
      text$).pipe(
      debounceTime(300),
      distinctUntilChanged(),
      tap(() => (this.searching = true)),
      switchMap(term => this.affiliateService.searchForPartnerAffiliatesAndContacts(term).pipe(
        tap(() => {
          this.searchFailed = false
        }),
        catchError(() => {
          this.searchFailed = true;
          return of([]);
        }))
      ),
      tap(() => (this.searching = false)),
    );

  formatter = (partner: {
    first_name: string,
    last_name: string,
    company_name: string
  }) => partner.first_name + " " + partner.last_name + " " + " (" + partner.company_name + ")";

  async onSelectCompany(partner: { [key: string]: any }) {
    let updatePricing = false;
    const orderLines = await firstValueFrom(this.orderService.orderLines$.pipe(takeUntil(this.destroy$)))
    let orderProductIds = orderLines.filter(line => line.product_id !== null).map(line => line.product_id!);
    const products = await firstValueFrom(this.affiliateService.getProductsWithAffiliatePricing(partner['affiliate_id']));
    let productMatches = products.filter(product => orderProductIds.includes(product.product_id));

    if (productMatches.length > 0) {
      let modalRef = this.modalService.open(VerifyPopupModal, {size: 'md'});
      modalRef.componentInstance.titleTranslationKey = 'orders.orderDetails.partner.refreshPrices.title';
      modalRef.componentInstance.showBody = true;
      modalRef.componentInstance.bodyBoldTranslationKey = 'orders.orderDetails.partner.refreshPrices.bold';
      modalRef.componentInstance.bodyRegularTranslationKey = 'orders.orderDetails.refreshPrices.regular';
      updatePricing = await modalRef.result;
    }

    const payload: _CRM_ORD_56 = {
      order_id: this.order.order_id,
      partner_id: partner['affiliate_id'],
      partner_contact_id: partner['affiliate_contact_id'],
      refresh_affiliate_prices: updatePricing
    };

    this.orderService.attachPartnerToOrder(payload).subscribe(res => {
      this.orderService.refreshOrder(res, 'attachPartnerToOrder(companySelect)');
      this.activeModal.close();
    });
  }

  deletePartner() {
    if (this.order.order_status_id > 5) {
      return;
    }
    const payload: _CRM_ORD_56 = {
      order_id: this.order.order_id,
      partner_id: null,
      partner_contact_id: null,
      refresh_affiliate_prices: false
    };

    this.orderService.attachPartnerToOrder(payload).subscribe(res => {
      this.orderService.refreshOrder(res, 'deletePartner2');
      this.activeModal.close();
    });
  }
}
