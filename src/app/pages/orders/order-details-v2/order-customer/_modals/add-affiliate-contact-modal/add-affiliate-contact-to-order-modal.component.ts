import {Component, Input, OnInit} from '@angular/core';
import {OrderResponse, OrderScheduleResponse} from "../../../../../../@shared/models/order.interfaces";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {CustomerService} from "../../../../../../@shared/services/customer.service";
import {OrderService} from "../../../../../../@shared/services/order.service";
import {AffiliateContactResponse, AffiliateSearchResponse} from "../../../../../../@shared/models/affiliate.interfaces";
import {AffiliateService} from "../../../../../../@shared/services/affiliate.service";
import {CreateAffiliateContactModalComponent} from "../create-affiliate-contact-modal/create-affiliate-contact-modal.component";
import {StandardImports} from "../../../../../../@shared/global_import";
import {SelectoriniComponent} from "../../../../../../@shared/components/selectorini/selectorini.component";

@Component({
    selector: 'app-add-affiliate-contact-to-order-modal',
    templateUrl: './add-affiliate-contact-to-order-modal.component.html',
    styleUrls: ['./add-affiliate-contact-to-order-modal.component.css'],
    standalone: true,
  imports: [StandardImports, SelectoriniComponent]
})
export class AddAffiliateContactToOrderModalComponent implements OnInit {
  @Input() order: OrderResponse;
  selectedContact: AffiliateContactResponse | null;
  affiliateContacts: AffiliateContactResponse[] = [];
  loading: boolean = false;
  constructor(
    private orderService: OrderService,
    private modalService: NgbModal,
    public customerService: CustomerService,
    public activeModal: NgbActiveModal,
    public affiliateService: AffiliateService) {
  }

  ngOnInit(){
    this.affiliateService.getAffiliateContacts(this.order.payment_recipient!.affiliate_id).subscribe((res) => {
      this.affiliateContacts = res;
      if (this.order.affiliate_contact) {
        this.selectedContact = this.affiliateContacts.find(contact => contact.affiliate_contact_id === this.order.affiliate_contact!.affiliate_contact_id)!
      }
    });
  }

  handleContactSelected(customer: any): void {
    this.selectedContact = customer
  }

  contactRemoved(){
    this.selectedContact = null;
  }

  showNewContact() {
    let modalRef = this.modalService.open(CreateAffiliateContactModalComponent)
    modalRef.componentInstance.createNewEmployee = true;
    modalRef.componentInstance.affiliate = this.order.payment_recipient
    modalRef.componentInstance.contacts = this.affiliateContacts
    modalRef.componentInstance.contactAdded.subscribe((result: boolean) => {
      if (result) {
        this.handleContactSelected(result)
      }
    });
  }

  onSubmit(){
    this.loading = true;
    this.orderService.setAffiliateContactForOrder(this.order.order_id, this.selectedContact?.affiliate_contact_id || -1).subscribe((res: OrderResponse) => {
      this.orderService.refreshOrder(res, 'changeOrderAffiliateContact(modal)');
      this.orderService.fetchAndRefreshOrderPayments(this.order.order_id);
      this.activeModal.close();
      this.loading = false;
    }, (err) => {
      this.loading = false;
    });
  }
}

