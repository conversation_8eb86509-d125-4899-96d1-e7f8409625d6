import {Component, Input, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {OrderResponse, OrderScheduleResponse, PaymentRecipientResponse, ServiceRecipientResponse} from "../../../../../../@shared/models/order.interfaces";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {CustomerService} from "../../../../../../@shared/services/customer.service";
import {
  NewCustomerModalComponent
} from "../../../../../../@shared/components/new-customer-modal/new-customer-modal.component";
import {_CRM_ORD_103, _CRM_ORD_80, _CRM_ORD_86} from "../../../../../../@shared/models/input.interfaces";
import {OrderService} from "../../../../../../@shared/services/order.service";
import {CustomerResponse} from "../../../../../../@shared/models/customer.interfaces";
import {AffiliateContactResponse, AffiliateSearchResponse} from "../../../../../../@shared/models/affiliate.interfaces";
import {AffiliateService} from "../../../../../../@shared/services/affiliate.service";
import {CreateAffiliateContactModalComponent} from "../create-affiliate-contact-modal/create-affiliate-contact-modal.component";
import {firstValueFrom, Subject} from "rxjs";
import {StandardImports} from "../../../../../../@shared/global_import";
import {SelectoriniComponent} from "../../../../../../@shared/components/selectorini/selectorini.component";
import {ToggleSwitchComponent} from "../../../../../../@shared/components/toggle-switch/toggle-switch.component";
import {takeUntil} from "rxjs/operators";

@Component({
    selector: 'app-change-customer-modal',
    templateUrl: './change-customer-modal.component.html',
    styleUrls: ['./change-customer-modal.component.css'],
    standalone: true,
  imports: [StandardImports, SelectoriniComponent, ToggleSwitchComponent]
})
export class ChangeCustomerModalComponent implements OnInit, OnDestroy {
  @Input() order: OrderResponse;
  @Input() create: boolean = false;
  @Input() changePaymentRecipientOnly: boolean = false;
  selectedCustomer: CustomerResponse | null;
  customerHasBeenSelected: boolean = false;
  selectedPaymentRecipient: CustomerResponse | null;
  paymentRecipientHasBeenSelected: boolean = false;
  selectedServiceRecipient: CustomerResponse | null;
  loading: boolean = false;
  customer?: CustomerResponse | null;
  showAffiliateContactsCustomer: boolean = false;
  showAffiliateContactsPaymentRecipient: boolean = false;
  affiliateContacts: AffiliateContactResponse[] = [];
  selectedContact: AffiliateContactResponse | null;
  refreshAffiliatePrices: boolean = false;
  showRefreshPricesToggle: boolean = false;
  affiliateProductsLoading: boolean = false;
  showCustomer: boolean = true;

  customerIsRemoved: boolean = false;
  contactIsRemoved: boolean = false;
  serviceRecipientIsRemoved: boolean = false;
  paymentRecipientIsRemoved: boolean = false;
  destroy$ = new Subject<void>();

  customerIcons = {
    0: 'fa-user',
    1: 'fa-building',
    2: 'fa-building-user',
  }
  constructor(private orderService: OrderService, private modalService: NgbModal, public customerService: CustomerService, public activeModal: NgbActiveModal, private affiliateService: AffiliateService) {
  }

  ngOnInit(){
    if (this.order.payment_recipient && this.order.service_recipient && this.order.payment_recipient.affiliate_id === this.order.service_recipient.affiliate_id) {
      this.selectedCustomer = this.convertToCustomer(this.order.payment_recipient)
    } else {
      if (this.order.payment_recipient) {
        this.selectedPaymentRecipient = this.convertToCustomer(this.order.payment_recipient)
      }
      if (this.order.service_recipient) {
        this.selectedServiceRecipient = this.convertToCustomer(this.order.service_recipient)
      }
    }

    if (this.order.affiliate_contact) {
      this.selectedContact = this.order.affiliate_contact
      if (this.selectedCustomer) {
        this.showAffiliateContactsCustomer = true;
      } else {
        this.showAffiliateContactsPaymentRecipient = true;
      }
    }
  }

ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  convertToCustomer(recipient: ServiceRecipientResponse | PaymentRecipientResponse): CustomerResponse {
    return {
      affiliate_id: recipient.affiliate_id,
      name: recipient.name,
      email: recipient.email,
      phone: recipient.phone,
      user_id: recipient.user_id,
      is_private: recipient.is_private,
      organisation_number: recipient.organisation_number,
      company_name: '',
      affiliate_contact_id: null,
      icon_id: recipient.is_private === 0 ? 1 : 0,
    }
  }

  async handleCustomerSelected(customer: CustomerResponse | any) {
    this.customerHasBeenSelected = true;
    this.selectedCustomer = customer
    this.customerIsRemoved = false;
    this.serviceRecipientIsRemoved = false;
    this.paymentRecipientIsRemoved = false;
    this.showAffiliateContactsCustomer = false;

    this.affiliateProductsLoading = true;
    const orderLines = await firstValueFrom(this.orderService.orderLines$.pipe(takeUntil(this.destroy$)))
    let orderProductIds = orderLines.filter(line => line.product_id !== null).map(line => line.product_id!);
    this.affiliateService.getProductsWithAffiliatePricing(customer.affiliate_id).subscribe((data) => {
      let productMatches = data.filter(product => orderProductIds.includes(product.product_id));
      if (productMatches.length > 0) {
        this.showRefreshPricesToggle = true;
      }
      this.affiliateProductsLoading = false;
    }, error => {
      this.affiliateProductsLoading = false;
    });

    if (customer.is_private === 0) {
      this.showAffiliateContactsCustomer = true;
      this.affiliateService.getAffiliateContacts(customer.affiliate_id).subscribe((res) => {
      this.affiliateContacts = res;
      if (res.length === 1) {
        this.selectedContact = res[0]
      }
    });
    }
  }

  async handlePaymentRecipientSelected(customer: CustomerResponse | any) {
    this.showCustomer = false;
    this.customerRemoved();
    this.paymentRecipientHasBeenSelected = true;
    this.selectedPaymentRecipient = customer
    this.paymentRecipientIsRemoved = false;
    this.showAffiliateContactsPaymentRecipient = false;

    this.affiliateProductsLoading = true;
    const orderLines = await firstValueFrom(this.orderService.orderLines$.pipe(takeUntil(this.destroy$)))
    let orderProductIds = orderLines.filter(line => line.product_id !== null).map(line => line.product_id!);
    const products = this.affiliateService.getProductsWithAffiliatePricing(customer.affiliate_id).subscribe((data) => {
      let productMatches = data.filter(product => orderProductIds.includes(product.product_id));
      if (productMatches.length > 0) {
        this.showRefreshPricesToggle = true;
      }
      this.affiliateProductsLoading = false;
    }, error => {
      this.affiliateProductsLoading = false;
    });

    if (customer.is_private === 0) {
      this.showAffiliateContactsPaymentRecipient = true;
      this.affiliateService.getAffiliateContacts(customer.affiliate_id).subscribe((res) => {
      this.affiliateContacts = res;
      if (res.length === 1) {
        this.selectedContact = res[0]
      }
    });
    }
  }

  handleServiceRecipientSelected(customer: CustomerResponse | any): void {
    this.selectedServiceRecipient = customer;
    this.serviceRecipientIsRemoved = false;
  }

  handleContactSelected(contact: AffiliateContactResponse | any): void {
    this.selectedContact = contact
  }

  contactRemoved(){
    this.selectedContact = null;
    this.contactIsRemoved = true;
  }

  customerRemoved(){
    this.contactRemoved();
    this.showAffiliateContactsCustomer = false;
    this.customerHasBeenSelected = false;
    this.selectedCustomer = null;
    this.customerIsRemoved = true;
  }

  paymentRecipientRemoved(){
    this.paymentRecipientHasBeenSelected = false;
    this.selectedPaymentRecipient = null;
    this.paymentRecipientIsRemoved = true;
  }

  serviceRecipientRemoved(){
    this.selectedServiceRecipient = null;
    this.serviceRecipientIsRemoved = true;
  }

  showNewContact() {
    let affiliate;
    if (this.selectedCustomer) {
      affiliate = this.selectedCustomer;
    } else {
      affiliate = this.selectedPaymentRecipient;
    }
    let modalRef = this.modalService.open(CreateAffiliateContactModalComponent)
    modalRef.componentInstance.createNewEmployee = true;
    modalRef.componentInstance.affiliate = affiliate;
    modalRef.componentInstance.contacts = this.affiliateContacts
    modalRef.componentInstance.contactAdded.subscribe((result: boolean) => {
      if (result) {
        this.handleContactSelected(result)
      }
    });
  }

  showNewCustomer(source: 'customer' | 'paymentRecipient' | 'serviceRecipient') {
    this.modalService.open(NewCustomerModalComponent).result.then((result) => {
      if (result) {
        if (source === 'customer') {
          this.handleCustomerSelected(result)
        } else if (source === 'paymentRecipient') {
          this.handlePaymentRecipientSelected(result)
        } else if (source === 'serviceRecipient') {
          this.handleServiceRecipientSelected(result)
        }
      }
    });
  }

  onSubmit(){
    if (!this.selectedCustomer && !this.selectedPaymentRecipient && !this.selectedServiceRecipient) {
      return;
    }

    this.loading = true;
    let paymentRecipientId: number;
    let serviceRecipientId: number;
    let contactId: number;

    if (this.customerIsRemoved) {
      paymentRecipientId = -1;
      serviceRecipientId = -1;
    }

    if (this.paymentRecipientIsRemoved) {
      paymentRecipientId = -1;
    }

    if (this.serviceRecipientIsRemoved) {
      serviceRecipientId = -1;
    }

    if (this.contactIsRemoved) {
      contactId = -1;
    }

    if (this.selectedCustomer) {
      paymentRecipientId = this.selectedCustomer.affiliate_id;
      serviceRecipientId = this.selectedCustomer.affiliate_id;
    }

    if (this.selectedPaymentRecipient) {
      paymentRecipientId = this.selectedPaymentRecipient.affiliate_id;
    }

    if (this.selectedServiceRecipient) {
      serviceRecipientId = this.selectedServiceRecipient.affiliate_id;
    }

    if (this.selectedContact) {
      contactId = this.selectedContact.affiliate_contact_id
    }

    let payload: _CRM_ORD_103 = {
      order_id: this.order.order_id,
      payment_recipient_id: paymentRecipientId!,
      service_recipient_id: serviceRecipientId!,
      affiliate_contact_id: contactId!,
      refresh_affiliate_prices: this.refreshAffiliatePrices,
    }

    if (this.selectedContact) {
      payload.affiliate_contact_id = this.selectedContact.affiliate_contact_id;
    }

    this.orderService.changeOrderCustomerSetup(payload).subscribe((res: OrderResponse) => {
      this.orderService.refreshOrder(res, 'changeOrderPaymentRecipient(modal)')
      this.orderService.fetchAndRefreshOrderPayments(this.order.order_id);
      this.loading = false;
      this.activeModal.close()
    }, (error) => {
      this.loading = false;
    });
  }
}

