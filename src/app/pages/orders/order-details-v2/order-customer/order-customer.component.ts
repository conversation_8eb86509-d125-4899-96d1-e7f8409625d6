import {Component, ElementRef, Input, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {OrderResponse, OrderSubContractorResponse} from "../../../../@shared/models/order.interfaces";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {PaymentService} from "../../../../@shared/services/payment.service";
import {OrderService} from "../../../../@shared/services/order.service";
import {PartnerModalComponent} from "./_modals/partner-modal/partner-modal.component";
import {StorageService} from "../../../../@core/services/storage.service";
import {displayPhone, UtilsService} from "../../../../@core/utils/utils.service";
import {ToastService} from "../../../../@core/services/toast.service";
import {ChangeCustomerModalComponent} from "./_modals/change-customer-modal/change-customer-modal.component";
import {CompanyPaymentMethodResponse} from "../../../../@shared/models/integrations.interfaces";
import {CompanyService} from "../../../../@shared/services/company.service";
import {Router} from "@angular/router";
import {_CRM_ORD_103, _CRM_ORD_56, CRM_ORD_56} from "../../../../@shared/models/input.interfaces";
import {VerifyPopupModal} from "../../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {TranslateService} from "@ngx-translate/core";
import {AffiliateContactInfoModalComponent} from "./_modals/affiliate-contact-info-modal/affiliate-contact-info-modal.component";
import {AddAffiliateContactToOrderModalComponent} from "./_modals/add-affiliate-contact-modal/add-affiliate-contact-to-order-modal.component";
import {CustomerService} from "../../../../@shared/services/customer.service";
import {CustomerResponse} from "../../../../@shared/models/customer.interfaces";
import {NewCustomerModalComponent} from "../../../../@shared/components/new-customer-modal/new-customer-modal.component";
import {AffiliateService} from "../../../../@shared/services/affiliate.service";
import {firstValueFrom, Subject} from "rxjs";
import {StandardImports} from "../../../../@shared/global_import";
import {SelectoriniComponent} from "../../../../@shared/components/selectorini/selectorini.component";
import {CardComponent} from "../../../../@shared/components/layout/card/card.component";
import {takeUntil} from "rxjs/operators";

@Component({
    selector: 'app-order-customer',
    templateUrl: './order-customer.component.html',
    styleUrls: ['./order-customer.component.css'],
    standalone: true,
  imports: [StandardImports, SelectoriniComponent, CardComponent]
})
export class OrderCustomerComponent implements OnInit, OnDestroy {
  @ViewChild('dropdownRef') dropdownRef: ElementRef;
  order: OrderResponse
  paymentRecipientInitials: string;
  serviceRecipientInitials: string;
  sendInvoiceLoading: boolean = false;
  accountingRefreshLoading: boolean = false;
  orderScheduleLink: string;
  paymentMethods: CompanyPaymentMethodResponse[] = [];
  customerPortalPaymentMethods: CompanyPaymentMethodResponse[] = [];
  paymentMethodString: string = '';
  paymentMethodsLoaded: boolean = false;
  partnersEnabled: boolean = false;
  accountingEnabled: boolean = false;
  stars: number[] = [1, 2, 3, 4, 5];
  hasSentPayments: boolean = false;
  accountingPaymentStatusColorMap = {
    0: 'text-success',
    1: 'text-danger',
    4: '',
    5: '',
    6: 'text-success',
    7: 'text-warning',
  }
  customerIcons = {
    0: 'fa-user',
    1: 'fa-building',
    2: 'fa-building-user',
  }

  destroy$ = new Subject<void>();

  constructor(private paymentService: PaymentService,
              private modalService: NgbModal,
              private storageService: StorageService,
              public utilsService: UtilsService,
              private orderService: OrderService,
              private toastService: ToastService,
              private companyService: CompanyService,
              private router: Router,
              private translate: TranslateService,
              public customerService: CustomerService,
              private affiliateService: AffiliateService) {}

  ngOnInit() {
    this.orderService.order$.pipe(takeUntil(this.destroy$)).subscribe(order => {
      this.order = order;
      if (this.order.order_id) {

        if (this.order.payment_recipient?.name) {
          const nameParts = this.order.payment_recipient.name.split(' ');
          this.paymentRecipientInitials = nameParts.map(part => part[0]).join('').toUpperCase();
        } else {
          this.paymentRecipientInitials = 'XX';
        }

        if (this.order.service_recipient) {
          if (this.order.service_recipient.name) {
            const nameParts = this.order.service_recipient.name.split(' ');
            this.serviceRecipientInitials = nameParts.map(part => part[0]).join('').toUpperCase();
          } else {
            this.serviceRecipientInitials = 'XX';
          }
        }
      }
    });
    this.storageService.partnersEnabled$.subscribe(enabled => {
      this.partnersEnabled = enabled;
    });
    this.storageService.accountingEnabled$.subscribe(enabled => {
      this.accountingEnabled = enabled;
    });

    this.orderService.orderPayments$.pipe(takeUntil(this.destroy$)).subscribe((payments) => {
      payments.forEach(payment => {
        if (payment.payment_sent_at && payment.payment_status_id != 12) {
          this.hasSentPayments = true;
        }
      })
    });

    this.companyService.getCompanyPaymentMethods().subscribe(res => {
      this.customerPortalPaymentMethods = [];
      this.paymentMethodsLoaded = false;
      this.paymentMethods = res.map(method => {
        if (method.payment_method_id === 4 && !this.accountingEnabled) {
          method.payment_method_name += ' / ' + this.translate.instant('common.invoice');
        }
        return method;
      });
      let count = 0;
      for (let method of this.paymentMethods) {
        if (method.active) {
          if (this.order.payment_recipient?.is_private == 1 && method.private_customer_available != 1) {
            continue
          }
          else if (this.order.payment_recipient?.is_private == 0 && method.business_customer_available != 1) {
            continue
          }

          if (count !== 0) {
            this.paymentMethodString += ", "
          }
          this.paymentMethodString += method.payment_method_name;
          this.customerPortalPaymentMethods.push(method);
          count++;
        }
      }
      this.paymentMethodsLoaded = true;
    })
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  copyValue(value: string | null): void {
    if (!value) return;

    navigator.clipboard.writeText(value).then(
      () => {
        this.toastService.successToast('copy')
      },
      (err) => console.error('Could not copy text: ', err)
    );
  }

  openAffiliateContactModal() {
    const modalRef = this.modalService.open(AffiliateContactInfoModalComponent, {size: 'md'});
    modalRef.componentInstance.affiliateContact = this.order.affiliate_contact;
  }

  openAddAffiliateContactModal() {
    const modalRef = this.modalService.open(AddAffiliateContactToOrderModalComponent);
    modalRef.componentInstance.order = this.order;
  }

  openPartnerModal(){
    if (this.order.order_status_id > 5) {
      return
    }

    if (this.order.partner !== null) {
      let modalRef = this.modalService.open(VerifyPopupModal, {size: 'md'});
      modalRef.result.then((result) => {
        if (result) {
          const payload: _CRM_ORD_56 = {
            order_id: this.order.order_id,
            partner_id: null,
            partner_contact_id: null,
            refresh_affiliate_prices: false
          };

          this.orderService.attachPartnerToOrder(payload).subscribe(res => {
            this.orderService.refreshOrder(res, 'attachPartnerToOrder(customer)');
          });
        }
      });
    } else {
      const modalRef = this.modalService.open(PartnerModalComponent, { size: 'md'});
      modalRef.componentInstance.order = this.order;
    }


  }

  openChangeCustomerModal() {
    // If service recipient change, just auto-insert the payment recipient - T.I. wants it like this for now
    const modalRef = this.modalService.open(ChangeCustomerModalComponent, {size: 'lg'});
    modalRef.componentInstance.order = this.order;
  }

  navigateToPaymentRecipient() {
    if (this.order.payment_recipient) {
      this.router.navigateByUrl(`affiliates/details/${this.order.payment_recipient.affiliate_id}`);
    }
  }

  showNewCustomer() {
    this.modalService.open(NewCustomerModalComponent).result.then((result) => {
      if (result) {
        this.handleCustomerSelected(result)
      }
    });
  }

  async handleCustomerSelected(customer: CustomerResponse | any) {
    let updatePricing = false;
    const orderLines = await firstValueFrom(this.orderService.orderLines$.pipe(takeUntil(this.destroy$)))
    let orderProductIds = orderLines.filter(line => line.product_id !== null).map(line => line.product_id!);
    const products = await firstValueFrom(this.affiliateService.getProductsWithAffiliatePricing(customer.affiliate_id));
    let productMatches = products.filter(product => orderProductIds.includes(product.product_id));

    if (productMatches.length > 0) {
      let modalRef = this.modalService.open(VerifyPopupModal, {size: 'md'});
      modalRef.componentInstance.titleTranslationKey = 'orders.orderDetails.customer.refreshPrices.title';
      modalRef.componentInstance.showBody = true;
      modalRef.componentInstance.bodyBoldTranslationKey = 'orders.orderDetails.customer.refreshPrices.bold';
      modalRef.componentInstance.bodyRegularTranslationKey = 'orders.orderDetails.refreshPrices.regular';
      updatePricing = await modalRef.result;
    }

    let payload: _CRM_ORD_103 = {
      order_id: this.order.order_id,
      payment_recipient_id: customer.affiliate_id,
      service_recipient_id: customer.affiliate_id,
      affiliate_contact_id: customer.affiliate_contact_id,
      refresh_affiliate_prices: updatePricing
    }
    this.orderService.changeOrderCustomerSetup(payload).subscribe((res: OrderResponse) => {
      this.orderService.refreshOrder(res, 'changeOrderPaymentRecipient(modal)')
    });
  }

  navigateToServiceRecipient() {
    if (this.order.service_recipient!.is_private == 1) {
      this.router.navigateByUrl(`customers/private/${this.order.service_recipient!.affiliate_id}`);
    }
    else {
      this.router.navigateByUrl(`affiliates/details/${this.order.service_recipient!.affiliate_id}`);
    }
  }

  removeSubContractor(contractor: OrderSubContractorResponse) {
    this.orderService.removeSubContractorFromOrder(this.order.order_id, contractor.affiliate_id).subscribe(res => {
      this.orderService.fetchAndRefreshOrder(this.order.order_id, 'removeSubContractor');
      this.toastService.successToast('deleted');
    })
  }

  protected readonly displayPhoneNumber = displayPhone;
}
