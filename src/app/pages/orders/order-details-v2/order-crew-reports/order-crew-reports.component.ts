import {Component, ElementRef, Input, OnInit, ViewChild} from '@angular/core';
import {
  IncidentResponse,
  OrderResponse, WorkOrderResponse
} from "../../../../@shared/models/order.interfaces";
import {ModalDismissReasons, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {ReportsService} from "../../../../@shared/services/reports.service";
import {OrderService} from "../../../../@shared/services/order.service";
import {CommonModule} from "@angular/common";
import {CardComponent} from "../../../../@shared/components/layout/card/card.component";
import {TranslateModule} from "@ngx-translate/core";
import {Subject} from "rxjs";
import {takeUntil} from "rxjs/operators";
import {StandardImports} from "../../../../@shared/global_import";
import {DeleteButtonComponent} from "../../../../@shared/components/delete-button/delete-button.component";
@Component({
  selector: 'app-order-crew-reports',
  templateUrl: './order-crew-reports.component.html',
  styleUrls: ['./order-crew-reports.component.css'],
  standalone: true,
  imports: [StandardImports, CardComponent, DeleteButtonComponent]
})
export class OrderCrewReportsComponent implements OnInit {
  @Input() workOrderView: boolean = false;
  workOrder?: WorkOrderResponse;
  order?: OrderResponse
  workOrders: WorkOrderResponse[] = [];

  allIncidents: IncidentResponse[] = [];
  images: string[] = [];
  currentImageIndex: number = 0;
  currentImageUrl: string = '';
  closeResult: string | undefined;

  destroy$ = new Subject<void>();

  constructor(private modalService: NgbModal,
              public orderService: OrderService,
              private reportService: ReportsService) {}

  ngOnInit() {
    if (!this.workOrderView) {
      this.orderService.workOrders$.pipe(takeUntil(this.destroy$)).subscribe(workOrders => {
        this.workOrders = workOrders;
        this.updateIncidentsFromOrder();
      });
      this.orderService.order$.pipe(takeUntil(this.destroy$)).subscribe(order => {
        this.order = order;
      });
    } else {
      this.orderService.workOrder$.subscribe(workOrder => {
        this.destroy$.next();
        this.workOrder = workOrder;
        this.updateIncidentsFromOrder();
      })
    }
  }

  private updateIncidentsFromOrder() {
    this.allIncidents = [];
    let workOrders: WorkOrderResponse[] = this.workOrderView ? [this.workOrder!] : this.workOrders;
    for (const wo of workOrders) {
      for (const incident of wo.incidents) {
        this.allIncidents.push(incident);
      }
    }
  }

  openImageModal(content: any, images: string[], startIndex: number = 0) {
    this.images = images;
    this.currentImageIndex = startIndex;
    this.currentImageUrl = images[startIndex];

    const modalRef = this.modalService.open(content, {
      size: 'xl',
      windowClass: 'image-modal',
      centered: true
    });
    modalRef.result.then(
      (result) => {
        this.closeResult = `Closed with: ${result}`;
      },
      (reason) => {
        this.closeResult = `Dismissed ${this.getDismissReason(reason)}`;
      }
    );
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  formatDate(date: any){
    const inputDate = new Date(date);
    const year = inputDate.getFullYear();
    const month = String(inputDate.getMonth() + 1).padStart(2, '0');
    const day = String(inputDate.getDate()).padStart(2, '0');
    const hours = String(inputDate.getHours()).padStart(2, '0');
    const minutes = String(inputDate.getMinutes()).padStart(2, '0');

    const formattedDate = `${day}-${month}-${year} ${hours}:${minutes}`;

    return formattedDate.toString();
  }

  deleteReport(report: IncidentResponse) {
    this.reportService.deleteIncidentReport(report.incident_id).subscribe((res) => {
      this.allIncidents = this.allIncidents.filter(incident => incident.incident_id !== report.incident_id);
    });
  }

  nextImage() {
    if (this.currentImageIndex < this.images.length - 1) {
      this.currentImageIndex++;
      this.currentImageUrl = this.images[this.currentImageIndex];
    }
  }

  previousImage() {
    if (this.currentImageIndex > 0) {
      this.currentImageIndex--;
      this.currentImageUrl = this.images[this.currentImageIndex];
    }
  }

}
