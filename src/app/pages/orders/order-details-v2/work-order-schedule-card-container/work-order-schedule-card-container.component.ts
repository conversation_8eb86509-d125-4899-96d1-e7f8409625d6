import {ChangeDetector<PERSON>ef, Component, EventEmitter, Input, OnChanges, OnInit, Output, Renderer2, SimpleChanges} from '@angular/core';
import {DetailsViewSettings, OrderResponse, OrderSubContractorResponse, WorkOrderResponse} from "../../../../@shared/models/order.interfaces";
import {paymentStatusBadge, UtilsService, workOrderBadgeStatus} from "../../../../@core/utils/utils.service";
import {NgbDateStruct, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {OrderService} from "../../../../@shared/services/order.service";
import {WorkOrderDetailsComponent} from "../../../work-orders/components/work-order-details/work-order-details.component";
import {NgClass, NgForOf, NgIf} from "@angular/common";
import {TranslateModule, TranslateService} from "@ngx-translate/core";

import {ButtonComponent} from "../../../../@shared/components/button/button.component";





import {FormControl, FormsModule, ReactiveFormsModule} from "@angular/forms";
import {_CRM_ORD_114, _CRM_ORD_116, _CRM_ORD_117, _CRM_ORD_118, _CRM_ORD_119} from "../../../../@shared/models/input.interfaces";
import {ToastService} from "../../../../@core/services/toast.service";
import {InternalUserResponse} from "../../../../@shared/models/user.interfaces";
import {ResourceResponse} from "../../../../@shared/models/resources.interfaces";
import {BehaviorSubject} from "rxjs";

import {WorkOrderDuplicateModal} from "../../../work-orders/components/work-order-details/_modals/duplicate-modal/work-order-duplicate-modal";
import {VerifyPopupModal} from "../../../../@shared/components/verify-popup-modal/verify-popup-modal";


import {CardComponent} from "../../../../@shared/components/layout/card/card.component";

import {WorkOrderScheduleCardComponent} from "../work-order-schedule-card/work-order-schedule-card.component";
import {StandardImports} from "../../../../@shared/global_import";
import {NewWorkOrderComponent} from "../../../work-orders/components/new-work-order/new-work-order.component";

@Component({
  selector: 'app-work-order-schedule-card-container',
  templateUrl: './work-order-schedule-card-container.component.html',
  styleUrls: ['./work-order-schedule-card-container.component.css'],
  standalone: true,
  imports: [StandardImports, CardComponent, WorkOrderScheduleCardComponent]
})
export class WorkOrderScheduleCardContainerComponent implements OnInit, OnChanges {

  @Input() workOrderScheduleTemplates: WorkOrderResponse[] = [];
  @Input() viewSettings: DetailsViewSettings = {};

  loading = false;

  constructor(public utilsService: UtilsService, private modalService: NgbModal, private orderService: OrderService, private renderer: Renderer2, private cdr: ChangeDetectorRef, private toastService: ToastService, private translate: TranslateService) {}

  ngOnInit(): void {
  }

  ngOnChanges(changes: SimpleChanges) {
  }

  openCreateWorkOrderModal() {
    let modalRef = this.modalService.open(NewWorkOrderComponent);
  }
}

