import {Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges} from '@angular/core';
import {ActivatedRoute, Router, RouterLink} from "@angular/router";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {OrderService} from "../../../@shared/services/order.service";
import {DetailsViewSettings, OrderLineRow, OrderResponse, OrderStatusResponse, WorkOrderResponse} from "../../../@shared/models/order.interfaces";
import {displayDate, paymentStatusBadge, UtilsService} from "../../../@core/utils/utils.service";
import {TranslateService} from "@ngx-translate/core";
import {_CRM_ORD_97} from "../../../@shared/models/input.interfaces";
import {Location} from "@angular/common";
import {OverlayService} from "../../../@shared/services/overlay.service";
import {PaymentService} from "../../../@shared/services/payment.service";
import {PaymentDetailsComponent} from "../../payments/components/payment-details/payment-details.component";
import {BehaviorSubject, Subject} from "rxjs";
import {OrderPaymentResponse} from "../../../@shared/models/payment.interfaces";
import {FormControl} from "@angular/forms";
import {ChangeCustomerModalComponent} from "./order-customer/_modals/change-customer-modal/change-customer-modal.component";
import {SendQuoteModalComponent} from "./components/action-button-group/_modals/send-quote-modal/send-quote-modal.component";
import {StorageService} from "../../../@core/services/storage.service";
import {
  NoPaymentWarningModalComponent
} from "./components/action-button-group/_modals/no-payment-warning-modal/no-payment-warning-modal.component";
import {StandardImports} from "../../../@shared/global_import";
import {ActionButtonGroupComponent} from "./components/action-button-group/action-button-group.component";
import {WorkOrderScheduleCardContainerComponent} from "./work-order-schedule-card-container/work-order-schedule-card-container.component";
import {WorkOrderCardContainerComponent} from "./work-order-card-container/work-order-card-container.component";
import {OrderLinesComponent} from "./order-lines/order-lines.component";
import {PaymentScheduleComponent} from "./order-payment-schedule/payment-schedule.component";
import {OrderNotesComponent} from "./order-notes/order-notes.component";
import {OrderTimelineComponent} from "./order-timeline/order-timeline.component";
import {OrderCustomerComponent} from "./order-customer/order-customer.component";
import {OrderRefundPaymentComponent} from "./order-refund-payment/order-refund-payment.component";
import {OrderAddressComponent} from "./order-address/order-address.component";
import {OrderCustomerQuestionsComponent} from "./order-customer-questions/order-customer-questions.component";
import {ChecklistComponent} from "./order-checklist/checklist.component";
import {OrderTimetrackingComponent} from "./order-timetracking/order-timetracking.component";
import {OrderAttachmentsComponent} from "./order-attachments/order-attachments.component";
import {OrderCrewReportsComponent} from "./order-crew-reports/order-crew-reports.component";
import {OrderProjectAndDepartmentComponent} from "./order-project-and-department/order-project-and-department.component";
import {OrderPaymentsComponent} from "./order-payments/order-payments.component";
import {OrderPaymentComponent} from "./order-payment/order-payment.component";
import {takeUntil} from "rxjs/operators";
import {WorkOrderContainerComponent} from "./work-order-container/work-order-container.component";

@Component({
    selector: 'app-order-details-v2',
    templateUrl: './order-details-v2.component.html',
    styleUrls: ['./order-details-v2.component.css'],
    providers: [OrderService, NgbModal],
    standalone: true,
  imports: [StandardImports, RouterLink, ActionButtonGroupComponent, OrderPaymentsComponent, WorkOrderScheduleCardContainerComponent, WorkOrderCardContainerComponent, OrderLinesComponent, PaymentDetailsComponent, PaymentScheduleComponent, OrderNotesComponent, OrderTimelineComponent, OrderCustomerComponent, OrderPaymentComponent, OrderRefundPaymentComponent, OrderAddressComponent, OrderCustomerQuestionsComponent, ChecklistComponent, OrderTimetrackingComponent, OrderAttachmentsComponent, OrderCrewReportsComponent, OrderProjectAndDepartmentComponent, WorkOrderContainerComponent]
})

export class OrderDetailsV2Component implements OnInit, OnChanges, OnDestroy{
  @Input() orderId: number;
  @Input() overlay: boolean = false;
  order: OrderResponse;
  unfilteredOrderStatuses: OrderStatusResponse[] = [];
  isloading = true;
  orderLinesLoading = false;
  orderStatuses: OrderStatusResponse[] = [];
  paymentSchedules: OrderPaymentResponse[] = [];
  projectsEnabled: boolean = false;
  departmentsEnabled: boolean = false;
  orderDetailsOrderLinesViewSettings: DetailsViewSettings = {
    orderDetailsOrderLinesView: true
  }

  customerCanOnlyAccept: boolean;

  noWorkOrderRepeatingPayment = false;

  workOrders: WorkOrderResponse[] = [];
  workOrderSchedules: WorkOrderResponse[] = [];

  orderLinesSubject: BehaviorSubject<OrderLineRow[]> = new BehaviorSubject<OrderLineRow[]>([]);

  orderHasPayments: boolean = false;
  orderHasRefundPayments: boolean = false;

  editOrderTitleActive: boolean = false;
  orderTitleControl: FormControl = new FormControl();

  destroy$ = new Subject<void>();

  @Output() updateCalendar: EventEmitter<string> = new EventEmitter<string>();

  constructor(private orderService: OrderService,
              public utilsService: UtilsService,
              private route: ActivatedRoute,
              private router: Router,
              private location: Location,
              private translateService: TranslateService,
              private modalService: NgbModal,
              private overlayService: OverlayService,
              private paymentService: PaymentService,
              private storageService: StorageService
              ) {}

  ngOnInit() {
    this.storageService.customerCanOnlyAccept$.pipe(takeUntil(this.destroy$)).subscribe((value) => {
      this.customerCanOnlyAccept = value;
    });

    this.storageService.projectsEnabled$.pipe(takeUntil(this.destroy$)).subscribe((enabled) => {
      this.projectsEnabled = enabled;
    });

    this.storageService.departmentsEnabled$.pipe(takeUntil(this.destroy$)).subscribe((enabled) => {
      this.departmentsEnabled = enabled;
    });

    this.orderService.getOrderStatuses().pipe(takeUntil(this.destroy$)).subscribe((statuses) => {
      this.unfilteredOrderStatuses = statuses;
      this.mapOrderStatuses();
    });

    this.orderService.workOrders$.pipe(takeUntil(this.destroy$)).subscribe((workOrders) => {
      this.workOrders = workOrders;
      this.mapOrderStatuses();
    });

    this.orderService.workOrderSchedules$.pipe(takeUntil(this.destroy$)).subscribe((workOrderSchedules) => {
      this.workOrderSchedules = workOrderSchedules;
      this.noWorkOrderRepeatingPayment = true;
        for (let wo of workOrderSchedules) {
          if (wo.payment) {
            this.noWorkOrderRepeatingPayment = false;
          }
        }
    });

    if (!this.overlay) {
      this.route.paramMap.subscribe(params => {
        this.orderId = Number(params.get('id'));
        this.initializeOrder();
        this.isloading = false;
      });
    }
    else {
      this.initializeOrder();
      this.mapOrderStatuses();
      this.isloading = false;
    }

    // Subscribe to order lines
    this.orderService.orderLines$.pipe(takeUntil(this.destroy$)).subscribe((orderLines) => {
      this.orderLinesSubject.next(orderLines.sort((a, b) => {
        return a.index - b.index}).map(orderLine => {
          return {
            ...orderLine,
            checked: false,
          }
        }
      ));
    });

    this.paymentService.fetchAndUpdatePaymentMethods();
    this.scrollToTop();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getActiveOrderStatusIndex(): number {
    return this.orderStatuses.findIndex(status => status.order_status_id === this.order.order_status_id);
  }

  scrollToTop(): void {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['orderId'] && changes['orderId'].currentValue !== changes['orderId'].previousValue) {
      this.initializeOrder();
    }
  }

  mapOrderStatuses() {
    if (this.order && this.unfilteredOrderStatuses.length > 0) {
      this.orderStatuses = [];
      let orderedStatuses = this.unfilteredOrderStatuses.sort((a, b) => a.order_status_id - b.order_status_id)
      let exemptOrderStatuses: number[] = [];

      // Do not show "Accepted by customer" if customer acceptance confirms order
      if (!this.customerCanOnlyAccept) {
        exemptOrderStatuses.push(1);
      }

      if (this.order.order_status_id !== 8) {
        exemptOrderStatuses.push(8)
      }

      exemptOrderStatuses.push(6);

      if (this.order.order_status_id > 1 && !this.order.quote_sent_at) {
        exemptOrderStatuses.push(0);
      }

      if (this.order.repeating && this.order.order_status_id !== 7) {
        exemptOrderStatuses.push(7);
      }

      // If no payment exists, remove inapplicable statuses
      if (!this.orderHasPayments) {
        exemptOrderStatuses.push(6);
      }

      // If no work orders exist, remove inapplicable statuses
      if (this.workOrders.length === 0) {
        exemptOrderStatuses.push(4, 5);
      }

      for (let status of orderedStatuses) {
        if (!exemptOrderStatuses.includes(status.order_status_id)) {
          this.orderStatuses.push(status);
        }
      }
    }
  }

  private initializeOrder() {
    this.orderService.getOrderById(this.orderId).pipe(takeUntil(this.destroy$)).subscribe((order) => {
      this.order = order;
      this.orderService.fetchAndRefreshOrderLines(this.order.order_id, 'orderDetailsInit')
      this.mapOrderStatuses();
      this.orderService.fetchAndRefreshOrderPayments(order.order_id);
      this.orderService.fetchAndRefreshOrderRefundPayments(order.order_id);
      this.orderService.fetchAndRefreshOrderPaymentSchedules(order.order_id);

      this.orderService.refreshOrder(order, 'initializeOrder');
      this.orderService.refreshOrderLogs(order.order_id)
    });

    this.orderService.order$.pipe(takeUntil(this.destroy$)).subscribe((order) => {
        this.order = order;
        if (order) {
          this.orderTitleControl.setValue(order.order_title);
          this.mapOrderStatuses();
          this.orderHasPayments = order.payment_status_id !== -2;
          if (!['ODv2OnInit', 'initializeOrder'].includes(order.source_tag)){
            this.updateCalendar.emit(order.source_tag);
          }
        }
    });

    this.orderService.orderPayments$.pipe(takeUntil(this.destroy$)).subscribe((payments) => {
      this.orderHasPayments = payments.length > 0;
      this.mapOrderStatuses();
    });

    this.orderService.orderRefundPayments$.pipe(takeUntil(this.destroy$)).subscribe((payments) => {
      this.orderHasRefundPayments = payments.length > 0;
    });

    this.orderService.orderPaymentSchedules$.pipe(takeUntil(this.destroy$)).subscribe(paymentSchedules => {
      this.paymentSchedules = paymentSchedules;
    });
  }

  lowerCaseFirstCharacter(value: string): string {
    return value.charAt(0).toLowerCase() + value.slice(1);
  }

  goBack() {
    this.location.back();
  }

  editOrderTitle() {
    this.editOrderTitleActive = true;
  }

  updateTitle() {
    let payload: _CRM_ORD_97 = {
      order_id: this.order.order_id,
      order_title: this.orderTitleControl.value
    }
    this.orderService.patchOrder(payload).pipe(takeUntil(this.destroy$)).subscribe((order) => {
      this.orderService.refreshOrder(order, 'updateTitle');
      this.editOrderTitleActive = false;
    });
  }

  openCustomerModal() {
    let modalRef = this.modalService.open(ChangeCustomerModalComponent)
    modalRef.componentInstance.order = this.order;
    modalRef.componentInstance.create = true;
  }

  sendQuote() {
    if (this.order.repeating && this.noWorkOrderRepeatingPayment) {
      const modalRef = this.modalService.open(NoPaymentWarningModalComponent, {});
      modalRef.componentInstance.order = this.order;

      modalRef.result.then(
        (result) => {
          if (result === 'proceed') {
            this.openSendQuoteModal();
          }
        },
        (reason) => {

        }
      );
    } else {
      this.openSendQuoteModal();
    }
  }

  private openSendQuoteModal(): void {
    const quoteModalRef = this.modalService.open(SendQuoteModalComponent, {});
    quoteModalRef.componentInstance.order = this.order;
  }


  onAcceptOrder(sendConfirmation: boolean) {
    if (this.isloading) {
      return;
    }
    this.isloading = true;
    this.orderService.acceptOrderAsCompany(this.order.order_id, sendConfirmation).pipe(takeUntil(this.destroy$)).subscribe( {
      next: (res) => {
        this.orderService.refreshOrder(res, 'onAcceptOrder');
        this.orderService.fetchAndRefreshOrderPaymentSchedules(res.order_id);
        this.orderService.fetchAndRefreshOrderPayments(res.order_id);
        this.orderService.refreshOrderLogs(this.order.order_id);
        this.isloading = false;
      },
      error: (error) => {
        this.isloading = false;
      }});
  }

  protected readonly Math = Math;
  protected readonly orderBadgePayment = paymentStatusBadge;
  protected readonly displayDate = displayDate;
}
