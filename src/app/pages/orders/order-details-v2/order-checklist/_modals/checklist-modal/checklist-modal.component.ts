import {ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, ViewChild} from '@angular/core';
import {NgbActiveModal} from "@ng-bootstrap/ng-bootstrap";
import {
  DetailsViewSettings,
  OrderResponse,
  WorkOrderResponse,
  WorkOrderTaskGroupResponse,
  WorkOrderTaskResponse
} from "../../../../../../@shared/models/order.interfaces";
import {displayDate, UtilsService} from "../../../../../../@core/utils/utils.service";
import {TaskTemplateResponse} from "../../../../../../@shared/models/templates.interfaces";
import {TemplateService} from "../../../../../../@shared/services/templates.service";
import {OrderService} from "../../../../../../@shared/services/order.service";
import {_CRM_ORD_126, _CRM_ORD_127, _CRM_ORD_128, _CRM_ORD_129, _CRM_ORD_130, _CRM_ORD_131, _CRM_ORD_132} from "../../../../../../@shared/models/input.interfaces";
import {SelectoriniComponent} from "../../../../../../@shared/components/selectorini/selectorini.component";
import {ToastService} from "../../../../../../@core/services/toast.service";
import {FormControl} from "@angular/forms";
import {StorageService} from "../../../../../../@core/services/storage.service";
import {forkJoin, lastValueFrom} from "rxjs";
import {StandardImports} from "../../../../../../@shared/global_import";
import {SpinnerComponent} from "../../../../../../@shared/components/spinner/spinner.component";

@Component({
    selector: 'app-checklist-modal',
    templateUrl: './checklist-modal.component.html',
    styleUrls: ['./checklist-modal.component.css'],
    standalone: true,
  imports: [StandardImports, SpinnerComponent, SelectoriniComponent]
})
export class ChecklistModalComponent implements OnInit {
  @Input() order?: OrderResponse;
  @Input() workOrder?: WorkOrderResponse;
  @Input() viewSettings?: DetailsViewSettings;
  @Input() workOrderView: boolean = false;
  @Input() orderDetailsScheduleView: boolean = false;
  workOrders: WorkOrderResponse[] = [];
  workOrderSchedules: WorkOrderResponse[] = [];
  taskGroups: WorkOrderTaskGroupResponse[] = [];
  activeTab: string = 'Upcoming';
  finishedWorkOrders: WorkOrderResponse[] = [];
  upcomingWorkOrder: WorkOrderResponse[] = [];
  selectedWorkOrder?: WorkOrderResponse;
  taskTemplates: TaskTemplateResponse[] = [];
  repeatingView: boolean = false;
  repeatingOrderDetailsView: boolean = false;
  loading = false;
  @Output() checklistsUpdated = new EventEmitter<true>();


  newTaskNameControl: FormControl = new FormControl();
  newTaskTaskGroupId: number | null = null;

  newTaskGroupNameControl: FormControl = new FormControl();
  showNewTaskGroupInput: boolean = false;

  editTaskGroupNameId: number | null = null;
  editTaskGroupNameControl: FormControl = new FormControl();

  editTaskNameControl: FormControl = new FormControl();
  editTaskCommentControl: FormControl = new FormControl();
  editTaskId: number | null = null;

  @ViewChild('taskTemplateSelectorini') taskTemplateSelectorini: SelectoriniComponent;

  constructor(
    public activeModal: NgbActiveModal,
    public utilsService: UtilsService,
    private templateService: TemplateService,
    private orderService: OrderService,
    private toastService: ToastService,
    private storageService: StorageService
  ){}

  ngOnInit() {
    if (!this.workOrderView) {
      this.orderService.workOrders$.subscribe((workOrders) => {
        this.workOrders = workOrders;
        if (this.workOrders.length === 1) {
          this.workOrder = this.workOrders[0];
        }
        if (this.workOrders.length > 0) {
          this.initTasks();
          this.setInitialSelection();
        }
      });
      this.orderService.workOrderSchedules$.subscribe((workOrders) => {
        this.workOrderSchedules = workOrders;
        if (this.workOrderSchedules.length > 0) {
          this.initTasks();
          this.setInitialSelection();
        }
      });
      this.orderService.order$.subscribe((order) => {
        this.order = order;
        this.repeatingOrderDetailsView = order.repeating;
      });
    } else {
      this.repeatingView = this.workOrder?.schedule_template!;
    }

    this.initTasks();
    this.setInitialSelection();

    this.templateService.getTaskTemplates({}).subscribe((response) => {
      this.taskTemplates = response;
    });
  }

  setInitialSelection() {
    if (this.workOrder) {
      this.selectedWorkOrder = this.workOrder;
    } else {
      if (this.finishedWorkOrders.length > 0) {
        this.selectedWorkOrder = this.finishedWorkOrders[0];
        this.activeTab = 'Finished';
      } else if (this.upcomingWorkOrder.length > 0) {
        this.selectedWorkOrder = this.upcomingWorkOrder[0];
        this.activeTab = 'Upcoming';
      }
    }

  }

  async initTasks() {
    this.taskGroups = [];
    let workOrders: WorkOrderResponse[] = this.repeatingOrderDetailsView ? this.workOrderSchedules : this.workOrders;
    if (!this.workOrderView) {
      this.finishedWorkOrders = workOrders.filter(
        (workOrder) => workOrder.work_order_status_id === 2
      );

      this.upcomingWorkOrder = workOrders!.filter(
        (workOrder) => workOrder.work_order_status_id != 2
      );

      workOrders = this.workOrder ? [this.workOrder] : workOrders;
      for (const wo of workOrders) {
        for (const taskGroup of wo.task_groups) {
          this.taskGroups.push(taskGroup);
        }
      }
    }

    if (this.workOrder && this.selectedWorkOrder) {
      this.orderService.refreshSingleWorkOrder(this.selectedWorkOrder!, 'initTasksChecklistModal');
    }
  }

  setActiveTab(tab: string) {
    this.activeTab = tab;
    if (this.activeTab === 'Upcoming') {
       if (this.upcomingWorkOrder.length > 0) {
          this.selectedWorkOrder = this.upcomingWorkOrder[0];
       } else {
          this.selectedWorkOrder = undefined;
       }
    } else if (this.activeTab === 'Finished') {
      if (this.finishedWorkOrders.length > 0) {
        this.selectedWorkOrder = this.finishedWorkOrders[0];
      } else {
        this.selectedWorkOrder = undefined;
      }
    }
  }

  selectWorkOrder(workOrder: WorkOrderResponse) {
    this.selectedWorkOrder = workOrder;
  }

  templateSelected(template: TaskTemplateResponse | any) {
    if (!this.selectedWorkOrder) return;
    let payload: _CRM_ORD_126 = {
      work_order_id: this.selectedWorkOrder!.work_order_id,
      task_template_id: template.task_template_id
    }
    this.orderService.assignTaskTemplateToWorkOrder(payload).subscribe((response) => {
      this.checklistsUpdated.emit(true);
      this.taskTemplateSelectorini.deselectItem(false);
      if (!this.workOrder || this.storageService.ownedByCompany(this.workOrder.company_id)) {
        this.orderService.refreshSingleWorkOrderInWorkOrders(response, 'taskTemplateSelected');
        this.workOrder = response;
        this.selectedWorkOrder = response;
        this.initTasks();
      }
      this.selectedWorkOrder = response;
      this.workOrder = response;
      this.initTasks();
    });
  }

  toggleTaskStatus(task: WorkOrderTaskResponse, event: MouseEvent) {
    event.stopPropagation();
    if (!this.selectedWorkOrder || this.repeatingView) return;
    let status: number;

    switch (task.task_status) {
      case 0:
        status = 1;
        break;
      case 1:
        status = 2;
        break;
      case 2:
        status = 0;
        break;
      default:
        status = 0;
        break;
    }

    let payload: _CRM_ORD_128 = {
      work_order_id: this.selectedWorkOrder?.work_order_id,
      task_id: task.task_id,
      task_status: status
    }
    this.orderService.editWorkOrderTask(payload).subscribe((response) => {
      task.task_status = payload.task_status!;
      if (this.order?.order_id) {
        this.orderService.refreshSingleWorkOrderInWorkOrders(response, 'toggleTaskStatus');
      }
      this.selectedWorkOrder = response;
      this.workOrder = response;
      this.initTasks();
    });
  }

  saveNewTask() {
    if (!this.selectedWorkOrder || !this.newTaskTaskGroupId || !this.newTaskNameControl.value) return;
    let payload: _CRM_ORD_127 = {
      work_order_id: this.selectedWorkOrder.work_order_id,
      task_group_id: this.newTaskTaskGroupId,
      task_name: this.newTaskNameControl.value,
      index: this.selectedWorkOrder.task_groups.find((group) => group.task_group_id === this.newTaskTaskGroupId)?.tasks.length
    }
    this.orderService.createWorkOrderTask(payload).subscribe((response) => {
      this.checklistsUpdated.emit(true);
      this.newTaskTaskGroupId = null;
      this.newTaskNameControl.reset();
      if (this.order?.order_id) {
        this.orderService.refreshSingleWorkOrderInWorkOrders(response, 'saveNewTask');
      }
      this.selectedWorkOrder = response;
      this.workOrder = response;
      this.initTasks();
    });
  }

  toggleEditTaskGroupName(taskGroup: WorkOrderTaskGroupResponse) {
    this.editTaskGroupNameId = taskGroup.task_group_id;
    this.editTaskGroupNameControl.setValue(taskGroup.task_group_name);
  }

  updateTaskGroupName() {
    if (!this.editTaskGroupNameId) return;
    let payload: _CRM_ORD_131 = {
      work_order_id: this.selectedWorkOrder!.work_order_id,
      task_group_id: this.editTaskGroupNameId,
      task_group_name: this.editTaskGroupNameControl.value
    }
    this.orderService.editWorkOrderTaskGroup(payload).subscribe((response) => {
      this.checklistsUpdated.emit(true);
      this.editTaskGroupNameId = null;
      this.editTaskGroupNameControl.reset();
      if (this.order?.order_id) {
        this.orderService.refreshSingleWorkOrderInWorkOrders(response, 'updateTaskGroupName');
      }
      this.selectedWorkOrder = response;
      this.workOrder = response;
      this.initTasks();
    });
  }

  deleteTaskGroup(taskGroup: WorkOrderTaskGroupResponse) {
    let params: _CRM_ORD_132 = {
      work_order_id: this.selectedWorkOrder!.work_order_id,
      task_group_id: taskGroup.task_group_id
    }
    this.orderService.deleteTaskGroup(params).subscribe((response) => {
      this.checklistsUpdated.emit(true);
      if (this.order?.order_id) {
        this.orderService.refreshSingleWorkOrderInWorkOrders(response, 'deleteTaskGroup');
      }
      this.selectedWorkOrder = response;
      this.workOrder = response;
      this.initTasks();
    });
  }

  toggleTaskEdit(task: WorkOrderTaskResponse) {
    this.editTaskId = task.task_id;
    this.editTaskNameControl.setValue(task.task_name);
    this.editTaskCommentControl.setValue(task.comment);
  }

  saveEditTask(event?: MouseEvent) {
    if (!this.editTaskId) return;
    if (event) {
      event.stopPropagation();
    }
    let payload: _CRM_ORD_128 = {
      work_order_id: this.selectedWorkOrder!.work_order_id,
      task_id: this.editTaskId,
      task_name: this.editTaskNameControl.value,
      comment: this.editTaskCommentControl.value
    }
    this.orderService.editWorkOrderTask(payload).subscribe((response) => {
      this.checklistsUpdated.emit(true);
      this.editTaskId = null;
      this.editTaskNameControl.reset();
      this.editTaskCommentControl.reset();
      if (this.order?.order_id) {
        this.orderService.refreshSingleWorkOrderInWorkOrders(response, 'saveEditTask');
      }
      this.selectedWorkOrder = response;
      this.workOrder = response;
      this.initTasks();
    });
  }

  deleteTask(task: WorkOrderTaskResponse, event: MouseEvent) {
    event.stopPropagation();
    if (!this.selectedWorkOrder) return;
    let params: _CRM_ORD_129 = {
      work_order_id: this.selectedWorkOrder.work_order_id,
      task_id: task.task_id
    }
    this.orderService.deleteWorkOrderTask(params).subscribe((response) => {
      this.checklistsUpdated.emit(true);
      if (this.order?.order_id) {
        this.orderService.refreshSingleWorkOrderInWorkOrders(response, 'deleteTask');
      }
      this.selectedWorkOrder = response;
      this.workOrder = response;
      this.initTasks();
    });
  }

  createNewTaskGroup() {
    if (!this.newTaskGroupNameControl) return;

    let payload: _CRM_ORD_130 = {
      work_order_id: this.selectedWorkOrder!.work_order_id,
      task_group_name: this.newTaskGroupNameControl.value,
      index: this.taskGroups.length
    }
    this.orderService.createWorkOrderTaskGroup(payload).subscribe((response) => {
      this.checklistsUpdated.emit(true);
      this.showNewTaskGroupInput = false;
      this.newTaskGroupNameControl.reset();
      if (this.order?.order_id) {
        this.orderService.refreshSingleWorkOrderInWorkOrders(response, 'createNewTaskGroup');
      }
      this.selectedWorkOrder = response;
      this.initTasks();

    });
  }

  protected readonly displayDate = displayDate;
}
