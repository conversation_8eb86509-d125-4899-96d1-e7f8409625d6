<div class="modal-header d-flex justify-content-between align-items-center">
  <i class="fa-regular fa-xmark fa-xl" (click)="activeModal.close()" style="cursor: pointer"></i>
  <h4 class="text-center mx-auto"> {{ "orderDetails.crewChecklist.title" | translate }} </h4>
</div>

<div class="modal-body p-0">
  <div class="d-flex">


    <div *ngIf="!workOrderView" class="col-md-5 left-side-col p-2 px-3">

      <div *ngIf="!repeatingView" class="btn-group w-100" role="group">
        <button type="button" class="btn"
                [ngClass]="{ 'btn-primary': activeTab === 'Upcoming', 'btn-outline-primary': activeTab !== 'Upcoming' }"
                (click)="setActiveTab('Upcoming')">
          {{ 'orderDetails.crewChecklist.upcoming' | translate }}
        </button>
        <button type="button" class="btn"
                [ngClass]="{ 'btn-primary': activeTab === 'Finished', 'btn-outline-primary': activeTab !== 'Finished' }"
                (click)="setActiveTab('Finished')">
          {{ 'orderDetails.crewChecklist.finished' | translate }}
        </button>
      </div>

      <div class="mt-3">

        <ng-container *ngIf="activeTab === 'Upcoming'">
          <div *ngIf="upcomingWorkOrder.length == 0 && !loading">
            <p class="text-center">{{ 'orderDetails.crewChecklist.noUpcomingWorkOrders' | translate }}</p>
          </div>
          <div *ngIf="loading" class="d-flex align-items-center justify-content-center">
            <app-spinner></app-spinner>
          </div>

          <div *ngFor="let _workOrder of upcomingWorkOrder" class="work-order mb-2"
               [ngClass]="{ 'selected-work-order': selectedWorkOrder?.work_order_id === _workOrder.work_order_id }"
               (click)="selectWorkOrder(_workOrder)">
            <div class="fw-bold"
                 style="color: unset;">{{ '#' + _workOrder.work_order_number}}{{!!_workOrder.work_order_title ? (' - ' + _workOrder.work_order_title) : '' }}
            </div>
            <div *ngIf="!repeatingView">{{ displayDate(_workOrder.execution_at) }}</div>
          </div>
        </ng-container>

        <ng-container *ngIf="activeTab === 'Finished'">

          <div *ngIf="finishedWorkOrders.length == 0 && !loading">
            <p class="text-center">{{ 'orderDetails.crewChecklist.noFinishedWorkOrders' | translate }}</p>
          </div>
          <div *ngIf="loading" class="d-flex align-items-center justify-content-center">
            <app-spinner></app-spinner>
          </div>

          <div *ngFor="let _workOrder of finishedWorkOrders" class="work-order mb-2"
               [ngClass]="{ 'selected-work-order': selectedWorkOrder === workOrder }"
               (click)="selectWorkOrder(_workOrder)">
            <div class="fw-bold"
                 style="color: unset;">{{ '#' + _workOrder.work_order_number}}{{!!_workOrder.work_order_title ? (' - ' + _workOrder.work_order_title) : '' }}
            </div>
            <div *ngIf="!repeatingView">{{ displayDate(_workOrder.execution_at) }}</div>
          </div>
        </ng-container>
      </div>

    </div>

    <div class="col">
      <div class="me-0 pe-0" id="task-list-col" style="min-height: 100%">
        <div class="" style="min-height: 100%;" id="task-list-card">
          <div class="px-2" id="task-list-card-body">
            <ng-container *ngIf="selectedWorkOrder">
              <div *ngFor="let taskGroup of selectedWorkOrder.task_groups" class="py-3">

                <div class="d-flex justify-content-between">
                  <div class="mb-1 px-2 cursor-pointer" (click)="toggleEditTaskGroupName(taskGroup)">
                    <label *ngIf="editTaskGroupNameId !== taskGroup.task_group_id" class="fw-bold cursor-pointer">{{ taskGroup.task_group_name }}</label>
                     <app-input
                       *ngIf="editTaskGroupNameId === taskGroup.task_group_id"
                       [editMode]="true"
                       [control]="editTaskGroupNameControl"
                       (onEnterPressed)="updateTaskGroupName()"
                    ></app-input>
                  </div>
                  <!-- Task Group Bucket -->
                  <div class="d-flex px-2 align-items-center cursor-pointer me-1" (click)="deleteTaskGroup(taskGroup)">
                    <i class="fa-regular fa-trash-can fa-lg"></i>
                  </div>
                </div>

                <hr class="my-0 mx-2">
                <div class="ps-0">
                  <div *ngFor="let task of taskGroup.tasks" class="task-button px-2 py-2 cursor-pointer" [ngClass]="{'task-edit': editTaskId == task.task_id}" (click)="toggleTaskEdit(task)">

                    <div *ngIf="editTaskId !== task.task_id" class="d-flex align-items-center justify-content-between">
                      <div class="d-flex align-items-center">
                        <!-- Checkbox -->
                        <input
                          *ngIf="task.task_status !== 2"
                          type="checkbox"
                          class="form-check-input me-2"
                          [checked]="task.task_status === 1"
                          (click)="toggleTaskStatus(task, $event)"
                        />
                        <!-- Deviation checkbox -->
                        <div *ngIf="task.task_status === 2" class="me-2 custom-x-mark" style="cursor: default !important;" (click)="toggleTaskStatus(task, $event)">
                          <i class="fa-solid fa-xmark"></i>
                        </div>

                        <!-- Task Name -->
                        <span class="" [ngClass]="{'line-through': task.task_status === 2}">{{ task.task_name }}</span>

                      </div>

                      <!-- Task Bucket -->
                      <div class="d-flex px-2 align-items-center cursor-pointer" (click)="deleteTask(task, $event)">
                        <i class="fa-regular fa-trash-can"></i>
                      </div>

                    </div>

                    <!-- Task Comment -->
                    <div *ngIf="task.comment && editTaskId !== task.task_id" class="ms-3">
                      <div class="text-muted" style="font-style: italic;">{{ task.comment }}</div>
                      <div class="font-12 text-muted"> - {{task.updated_by_name}}</div>
                    </div>



                    <div *ngIf="editTaskId === task.task_id" class="d-flex gap-1">
                      <!-- Input fields -->
                      <div class="col">
                        <!-- Edit Task Name -->
                        <div class="mb-1">
                          <app-input
                            [editMode]="true"
                            [textArea]="true"
                            [placeholderKey]="'orderDetails.crewChecklist.newTask.taskName.placeholder'"
                            [control]="editTaskNameControl"
                            (onEnterPressed)="saveEditTask()"
                          ></app-input>
                        </div>

                        <!-- Edit Task Comment -->
                        <div class="">
                          <app-input
                            [editMode]="true"
                            [textArea]="true"
                            [placeholderKey]="'orderDetails.crewChecklist.newTask.taskComment.placeholder'"
                            [control]="editTaskCommentControl"
                            (onEnterPressed)="saveEditTask()"
                          ></app-input>
                        </div>
                      </div>

                      <!-- Buttons -->
                      <div class="d-flex align-items-center">
                        <div>
                          <div class="mb-1">
                            <app-button
                              [translationKey]="'common.save'"
                              [buttonType]="'nude'"
                              [buttonWidth]="68"
                              (buttonClick)="saveEditTask($event)"
                            ></app-button>
                          </div>
                          <app-button
                            [translationKey]="'common.cancel'"
                            [buttonType]="'nude'"
                            [buttonWidth]="68"
                            (buttonClick)="editTaskId = null; $event.stopPropagation();"
                          ></app-button>
                        </div>
                      </div>

                    </div>

                  </div>

                  <!-- New task input -->
                  <div *ngIf="newTaskTaskGroupId === taskGroup.task_group_id" class="mt-2 px-2">
                    <label>{{"orderDetails.crewChecklist.newTask.label" | translate}}</label>
                    <app-input
                      [editMode]="true"
                      [placeholderKey]="'orderDetails.crewChecklist.newTask.taskName.placeholder'"
                      [control]="newTaskNameControl"
                      [showInputButtons]="true"
                      (onEnterPressed)="saveNewTask()"
                      (crossButtonClicked)="newTaskTaskGroupId = null"
                      (checkButtonClicked)="saveNewTask()"
                      (onEscapePressed)="newTaskTaskGroupId = null"
                    ></app-input>

                  </div>

                  <!-- New task button -->
                  <div *ngIf="newTaskTaskGroupId !== taskGroup.task_group_id" class="mt-2 ms-2">
                    <app-button
                      [buttonType]="'nude'"
                      [small]="true"
                      [disabled]="newTaskTaskGroupId !== null"
                      [iconClass]="'fa-regular fa-plus me-1 grey-text'"
                      [translationKey]="'orderDetails.crewChecklist.newTask.button'"
                      (buttonClick)="newTaskTaskGroupId = taskGroup.task_group_id"
                    ></app-button>
                  </div>

                </div>

              </div>

              <!-- New Task Group -->
              <div *ngIf="showNewTaskGroupInput" class="px-2 mt-2">
                <label>{{"orderDetails.crewChecklist.newTaskGroup.label" | translate}}</label>
                <app-input
                  [editMode]="true"
                  [control]="newTaskGroupNameControl"
                  [placeholderKey]="'orderDetails.crewChecklist.newTaskGroup.placeholder'"
                  [showInputButtons]="true"
                  (onEnterPressed)="createNewTaskGroup()"
                  (crossButtonClicked)="showNewTaskGroupInput = false"
                  (checkButtonClicked)="createNewTaskGroup()"
                  (onEscapePressed)="showNewTaskGroupInput = false"
                ></app-input>
              </div>

              <div class="py-3 px-2 d-flex justify-content-between w-100">
                <div class="col pe-2">
                  <app-selectorini
                    #taskTemplateSelectorini
                    [minWidthPercentage]="100"
                    [directSelection]="true"
                    [disabled]="newTaskTaskGroupId !== null || showNewTaskGroupInput"
                    [predefinedSearchResults]="taskTemplates"
                    [searchMainDisplayKeys]="['task_template_name']"
                    [placeholderTranslationKey]="'orderDetails.crewChecklist.fromTemplate.placeholder'"
                    (itemSelectedEmitter)="templateSelected($event)"
                  ></app-selectorini>
                </div>
                <div class="">
                  <app-button
                    [disabled]="newTaskTaskGroupId !== null || showNewTaskGroupInput"
                    [translationKey]="'orderDetails.crewChecklist.newTaskGroup.button'"
                    [buttonType]="'nude'"
                    (buttonClick)="showNewTaskGroupInput = true"
                  ></app-button>
                </div>
              </div>
            </ng-container>
            <p *ngIf="!selectedWorkOrder" class="text-center mt-2">
              {{ 'orderDetails.crewChecklist.selectWorkOrder' | translate }}
            </p>
          </div>
        </div>
      </div>
    </div>



  </div>
</div>
