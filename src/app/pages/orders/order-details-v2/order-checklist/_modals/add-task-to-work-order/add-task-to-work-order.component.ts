import {Component, Input, OnInit} from '@angular/core';
import {CdkDrag, CdkDragDrop, CdkDragHandle, CdkDropList, moveItemInArray} from "@angular/cdk/drag-drop";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON>orOf, NgIf} from "@angular/common";
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {TranslateModule} from "@ngx-translate/core";
import {NgbActiveModal} from "@ng-bootstrap/ng-bootstrap";
import {ButtonComponent} from "../../../../../../@shared/components/button/button.component";
import {WorkOrderResponse, WorkOrderTaskGroupResponse, WorkOrderTaskResponse} from "../../../../../../@shared/models/order.interfaces";
import {OrderService} from "../../../../../../@shared/services/order.service";
import {
  _CRM_ORD_126,
  _CRM_ORD_130,
  _CRM_ORD_131,
  _CRM_ORD_132,
} from "../../../../../../@shared/models/input.interfaces";
import {forkJoin, of} from "rxjs";
import {TaskTemplateResponse} from "../../../../../../@shared/models/templates.interfaces";
import {SelectoriniComponent} from "../../../../../../@shared/components/selectorini/selectorini.component";
import {StandardImports} from "../../../../../../@shared/global_import";
import {DeleteButtonComponent} from "../../../../../../@shared/components/delete-button/delete-button.component";


@Component({
    selector: 'app-add-task-to-work-order',
    templateUrl: './add-task-to-work-order.component.html',
    styleUrl: './add-task-to-work-order.component.css',
    standalone: true,
  imports: [StandardImports, CdkDropList, DeleteButtonComponent, CdkDragHandle, CdkDrag, SelectoriniComponent]
})
export class AddTaskToWorkOrderComponent implements OnInit{
  @Input() workOrder: WorkOrderResponse;
  @Input() taskGroups: WorkOrderTaskGroupResponse[];
  @Input() availableTaskTemplates: TaskTemplateResponse[];

  addTaskForm: FormGroup;
  addTaskGroupForm: FormGroup;
  originalTaskGroups: WorkOrderTaskGroupResponse[] = [];
  selectedTemplateId: number;

  private tempTaskIdCounter = -1;
  private tempTaskGroupIdCounter = -1;

  isLoadingTaskGroup: boolean = false;
  isGrouped: boolean = false;
  originalTaskGroupIndices: Map<number, number> = new Map();
  taskGroupsToDelete: number[] = [];

  constructor(
    public activeModal: NgbActiveModal,
    private fb: FormBuilder,
    private orderService: OrderService
  ) {}


  ngOnInit() {
    this.initializeForms();

    this.taskGroups = JSON.parse(JSON.stringify(this.taskGroups));
    this.isGrouped = true; // Always group the tasks

    if (!this.taskGroups || this.taskGroups.length === 0) {
      this.initializeDefaultTaskGroup();
    }

    this.originalTaskGroups = this.taskGroups.map(group => ({
      ...group,
      tasks: group.tasks.map(task => ({ ...task })) // Deep copy of tasks
    }));

    // Add `isCollapsed` property dynamically to each group
    this.taskGroups = this.taskGroups.map(group => ({
      ...group,
      isCollapsed: false // Default: All groups expanded
    }));
  }


  toggleGroup(index: number): void {
    const group = this.taskGroups[index] as WorkOrderTaskGroupResponse & { isCollapsed?: boolean };
    group.isCollapsed = !group.isCollapsed;
  }

  initializeDefaultTaskGroup() {
    const newTaskGroupPayload: _CRM_ORD_130 = {
      work_order_id: this.workOrder.work_order_id,
      task_group_name: 'Sjekkliste',
      index: 0,
      tasks: [
        {
          task_name: 'Nytt punkt',
          index: 0,
        },
        {
          task_name: 'Nytt punkt',
          index: 1,
        },
      ],
    };

    this.orderService.createWorkOrderTaskGroup(newTaskGroupPayload).subscribe(
      (updatedWorkOrder: WorkOrderResponse) => {
        this.taskGroups = updatedWorkOrder.task_groups;
      },
      (err) => {
        console.error(err);
      }
    );
  }


  private initializeForms() {
    this.addTaskForm = this.fb.group({
      taskId: [null, Validators.required],
      task_name: ['Nytt punkt'],
      comment: [''],
    });

    this.addTaskGroupForm = this.fb.group({
      taskGroupName: ['', Validators.required],
    });
  }





  // handleGroupToggle() {
  //   this.isGrouped = !this.isGrouped;
  //   if (!this.isGrouped) {
  //     this.mergeAllTaskGroups();
  //   } else {
  //     this.restoreTaskGroups();
  //   }
  // }
  //
  // restoreTaskGroups() {
  //   if (this.originalTaskGroups && this.originalTaskGroups.length > 0) {
  //     this.taskGroups = JSON.parse(JSON.stringify(this.originalTaskGroups));
  //   }
  // }

  onAddTask(group: WorkOrderTaskGroupResponse) {
    const taskName = 'Nytt Punkt';
    const tempTaskId = this.tempTaskIdCounter--;

    const newTask: WorkOrderTaskResponse = {
      work_order_task_id: -1, // Temporary ID
      task_id: tempTaskId,    // Unique temporary ID
      task_name: taskName,
      task_status: 0,
      comment: '',
      index: group.tasks.length,
      deleted: false,
      updated_by_name: null,
    };

    group.tasks.push(newTask);
  }



  onDeleteTask(task: WorkOrderTaskResponse, group: WorkOrderTaskGroupResponse) {
    task.deleted = true;
  }

  onAddTaskGroup() {
    this.isLoadingTaskGroup = true;
    const taskGroupName = 'Sjekkliste';
    const iconId = this.addTaskGroupForm.value.iconId || 0;
    const tempTaskGroupId = this.tempTaskGroupIdCounter--;

    const newTaskGroup: WorkOrderTaskGroupResponse = {
      task_group_id: tempTaskGroupId, // Temporary ID
      task_group_name: taskGroupName,
      icon_id: iconId,
      index: this.taskGroups.length,
      tasks: []
    };

    this.taskGroups.push(newTaskGroup);
  }


  onDeleteTaskGroup(group: WorkOrderTaskGroupResponse) {
    this.taskGroups = this.taskGroups.filter((tg) => tg.task_group_id !== group.task_group_id)
    if (group.task_group_id > 0) {
      this.taskGroupsToDelete.push(group.task_group_id);
    }
  }


  drop(event: CdkDragDrop<WorkOrderTaskResponse[]>, targetGroup: WorkOrderTaskGroupResponse) {
    if (event.previousContainer === event.container) {
      moveItemInArray(targetGroup.tasks, event.previousIndex, event.currentIndex);
    } else {
      const sourceGroupId = this.taskGroups.findIndex(g => g.task_group_id === +event.previousContainer.id.replace('tasks-', ''));
      const targetGroupId = this.taskGroups.findIndex(g => g.task_group_id === +event.container.id.replace('tasks-', ''));

      if (sourceGroupId === -1 || targetGroupId === -1) {
        return;
      }

      const sourceGroup = this.taskGroups[sourceGroupId];
      const targetGroup = this.taskGroups[targetGroupId];

      const task = sourceGroup.tasks[event.previousIndex];
      sourceGroup.tasks.splice(event.previousIndex, 1);
      targetGroup.tasks.splice(event.currentIndex, 0, task);
    }

    // Update indices for all groups and tasks
    this.updateIndices();
  }

  dropGroup(event: CdkDragDrop<WorkOrderTaskGroupResponse[]>) {
    moveItemInArray(this.taskGroups, event.previousIndex, event.currentIndex);
    // Update indices for task groups
    this.updateGroupIndices();
  }

  private updateIndices() {
    this.taskGroups.forEach((group, groupIndex) => {
      group.index = groupIndex;
      group.tasks.forEach((task, taskIndex) => {
        task.index = taskIndex;
      });
    });
  }


  private updateGroupIndices() {
    this.taskGroups.forEach((group, index) => {
      group.index = index;
    });
  }


  addSelectedTemplate(event: any) {
    let _payload: _CRM_ORD_126 = {
      work_order_id: this.workOrder.work_order_id,
      task_template_id: event.task_template_id,
    };

    this.orderService.assignTaskTemplateToWorkOrder(_payload).subscribe((updatedWorkOrder: WorkOrderResponse) => {
          this.taskGroups = updatedWorkOrder.task_groups;
        });
  }




onSave() {
  this.isLoadingTaskGroup = true;

  let deletePayloads: _CRM_ORD_132[] = [];
  let postPayloads: _CRM_ORD_130[] = [];
  let putPayloads: _CRM_ORD_131[] = [];

  // Prepare payloads
  this.taskGroupsToDelete.forEach((tgId) => {
    deletePayloads.push({
      work_order_id: this.workOrder.work_order_id,
      task_group_id: tgId,
    });
  });

  this.taskGroups.forEach((tg) => {
    if (tg.task_group_id < 0) {
      postPayloads.push({
        work_order_id: this.workOrder.work_order_id,
        task_group_name: tg.task_group_name,
        icon_id: tg.icon_id,
        index: tg.index,
        tasks: tg.tasks.map((task) => ({
          task_name: task.task_name,
          index: task.index,
        })),
      });
    } else {
      putPayloads.push({
        work_order_id: this.workOrder.work_order_id,
        task_group_id: tg.task_group_id,
        task_group_name: tg.task_group_name,
        icon_id: tg.icon_id,
        index: tg.index,
        tasks: tg.tasks.map((task) => ({
          task_id: task.task_id < 0 ? null : task.task_id,
          task_name: task.task_name,
          index: task.index,
          deleted: task.deleted || false,
        })),
      });
    }
  });

  // Convert payloads into requests
  const deleteRequests = deletePayloads.length
    ? forkJoin(deletePayloads.map((payload) => this.orderService.deleteTaskGroup(payload)))
    : of([]); // Return an Observable of an empty array if no deletes

  const postRequests = postPayloads.length
    ? forkJoin(postPayloads.map((payload) => this.orderService.createWorkOrderTaskGroup(payload)))
    : of([]); // Return an Observable of an empty array if no posts

  const putRequests = putPayloads.length
    ? forkJoin(putPayloads.map((payload) => this.orderService.editWorkOrderTaskGroup(payload)))
    : of([]); // Return an Observable of an empty array if no puts

  // Execute requests sequentially
  deleteRequests.subscribe({
    next: () => {
      postRequests.subscribe({
        next: () => {
          putRequests.subscribe({
            next: () => {
              // Final step: Fetch updated work order and close modal
              this.orderService.fetchAndRefreshSingleWorkOrder(this.workOrder.work_order_id, 'addTaskToWorkOrderSave');
              this.isLoadingTaskGroup = false;
              this.activeModal.close(this.taskGroups);
            },
            error: (err) => {
              console.error("Error in PUT requests", err);
              this.isLoadingTaskGroup = false;
            },
          });
        },
        error: (err) => {
          console.error("Error in POST requests", err);
          this.isLoadingTaskGroup = false;
        },
      });
    },
    error: (err) => {
      console.error("Error in DELETE requests", err);
      this.isLoadingTaskGroup = false;
    },
  });
}



  getFilteredTasks(group: any): any[] {
    return group.tasks.filter((task: WorkOrderTaskResponse) => !task.deleted);
  }

}
