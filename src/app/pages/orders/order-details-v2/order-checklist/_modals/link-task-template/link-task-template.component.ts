import {Component, EventEmitter, Input, Output} from '@angular/core';
import {TabsMenuModel} from "../../../../../../@shared/models/calendar.interfaces";
import {TaskTemplateResponse} from "../../../../../../@shared/models/templates.interfaces";
import {NgbActiveModal, NgbModal, NgbModalRef} from "@ng-bootstrap/ng-bootstrap";
import {environment} from "../../../../../../../environments/environment";
import {TemplateService} from "../../../../../../@shared/services/templates.service";
import {SortEvent} from "../../../../../../@shared/components/advanced-table/sortable.directive";
import {
  TaskTemplateModalComponent
} from "../../../../../templates/tasks-overview/_modals/checklist-modal/task-template-modal.component";
import {TranslateModule} from "@ngx-translate/core";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "@angular/common";
import {ButtonComponent} from "../../../../../../@shared/components/button/button.component";
import {OrderService} from "../../../../../../@shared/services/order.service";
import {_CRM_ORD_126, _CRM_ORD_128, _CRM_ORD_132, CRM_ORD_128} from "../../../../../../@shared/models/input.interfaces";
import {forkJoin, from, lastValueFrom} from "rxjs";
import {
  DetailsViewSettings,
  OrderResponse,
  WorkOrderResponse,
  WorkOrderTaskGroupResponse, WorkOrderTaskResponse
} from "../../../../../../@shared/models/order.interfaces";
import {FormsModule} from "@angular/forms";
import {EditWorkOrderTasksModalComponent} from "../edit-work-order-tasks-modal/edit-work-order-tasks-modal.component";
import {AddTaskToWorkOrderComponent} from "../add-task-to-work-order/add-task-to-work-order.component";
import {concatMap} from "rxjs/operators";
import {Router} from "@angular/router";
import {StandardImports} from "../../../../../../@shared/global_import";

@Component({
    selector: 'app-link-task-template',
    templateUrl: './link-task-template.component.html',
    styleUrl: './link-task-template.component.css',
    standalone: true,
    imports: [StandardImports]
})
export class LinkTaskTemplateComponent {
  @Input() workOrder: WorkOrderResponse;
  @Input() order?: OrderResponse;
  @Input() workOrderView: boolean = false;
  @Input() viewSettings: DetailsViewSettings;

  workOrders: WorkOrderResponse[] = [];
  workOrderSchedules: WorkOrderResponse[] = [];

  activeTab!: number | string;
  taskTemplates: TaskTemplateResponse[] = [];
  modalReference: NgbModalRef;
  searchString: string | null = null;
  paginationDetails = {
    page: 1,
    limit: environment.standardPageSize,
  };
  selectedTemplateId: number | null = null;
  orderBy: string = '';
  orderByDirection: string = '';
  availableTaskTemplates: TaskTemplateResponse[] = [];

  loading: boolean = false;
  taskGroups: WorkOrderTaskGroupResponse[] = [];
  totalTasks: number = 0;
  // Add this property to track selected templates
  selectedTemplates: TaskTemplateResponse[] = [];

  constructor(
    public activeModal: NgbActiveModal,
    public templateService: TemplateService,
    private modalService: NgbModal,
    private orderService: OrderService,
    private router: Router
  ) {
  }

  ngOnInit() {
    this.fetchTaskTemplates();
    this.orderService.workOrders$.subscribe((workOrders) => {
      this.workOrders = workOrders;
      this.initTasks();
    });
    this.orderService.workOrderSchedules$.subscribe((workOrders) => {
      this.workOrderSchedules = workOrders;
      this.initTasks();
    });
  }

  initTasks() {
    if (!this.order && !this.workOrder) {
      console.error('Order or WorkOrder is not available.');
      this.taskGroups = [];
      return;
    }

    if (this.viewSettings.workOrderView) {
      // Use workOrder if available, else fallback to order.work_orders
      if (this.workOrder) {
        this.taskGroups = this.workOrder.task_groups;
      } else if (this.order) {
        this.taskGroups = this.workOrders.flatMap((wo) => wo.task_groups);
      } else {
        this.taskGroups = [];
      }
    } else if (this.viewSettings.repeatingView) {
      // Use repeating schedule templates
      if (this.order) {
        this.taskGroups = this.workOrderSchedules.flatMap((template) => template.task_groups);
      } else {
        this.taskGroups = [];
      }
    } else {
      console.error('No valid viewSettings found.');
      this.taskGroups = [];
    }

    // Calculate total tasks
    this.totalTasks = this.taskGroups.reduce((total, group) => total + group.tasks.length, 0);
  }

  handleActiveTab(tabValue: number | string) {
    this.activeTab = tabValue;
  }

  fetchFilterOptions(filterOptions: any) {
    this.searchString = filterOptions.searchString;
    this.fetchTaskTemplates();
  }

  fetchTaskTemplates(paginationDetails?: any) {
    this.loading = true;
    if (!paginationDetails) {
      paginationDetails = this.paginationDetails;
    } else {
      this.paginationDetails = paginationDetails;
    }

    this.templateService.getTaskTemplates({}).subscribe({
      next: (res) => {
        this.taskTemplates = res;
        this.loading = false;
      },
      error: (error) => {
        this.loading = false;
      },
    });
  }


  // getTaskStatusClass(task: WorkOrderTaskResponse): string {
  //   const className = 'task-status-' + task.task_status;
  //   return className;
  // }

  getTaskStatusClass(task: any): string {
    if (task.task_status === 0) {
      return 'task-status-0';
    } else if (task.task_status === 1) {
      return 'task-status-1';
    } else if (task.task_status === 2) {
      return 'task-status-2';
    }
    return '';
  }

  openCreateTaskTemplateModal() {
    let modalRef = this.modalService.open(TaskTemplateModalComponent, {size: 'lg',});
    modalRef.result.then((result: TaskTemplateResponse) => {
      if (result) {
        this.taskTemplates.push(result);
      }
    })
      .catch((error) => {
      });
  }


  toggleTemplateSelection(taskTemplate: any): void {
    const index = this.selectedTemplates.findIndex(t => t.task_template_id === taskTemplate.task_template_id);
    if (index === -1) {
      this.selectedTemplates.push(taskTemplate);
    } else {
      this.selectedTemplates.splice(index, 1);
    }
  }

  isSelected(taskTemplate: any): boolean {
    return this.selectedTemplates.some(t => t.task_template_id === taskTemplate.task_template_id);
  }

  addSelectedTemplates() {
    const selectedTemplatesPayload = this.selectedTemplates.map((st) => ({
      work_order_id: this.workOrder.work_order_id,
      task_template_id: st.task_template_id,
    }));

    from(selectedTemplatesPayload)
      .pipe(
        concatMap((payload) => this.orderService.assignTaskTemplateToWorkOrder(payload))
      )
      .subscribe({
        complete: () => {
          this.orderService.getWorkOrderById(this.workOrder.work_order_id).subscribe((updatedWorkOrder) => {
            this.taskGroups = updatedWorkOrder.task_groups;

            // Recalculate totalTasks dynamically
            this.totalTasks = this.taskGroups.reduce(
              (total, group) => total + group.tasks.length,
              0
            );
          });
          this.selectedTemplates = [];
        },
      });
  }


  toggleTaskStatus(task: WorkOrderTaskResponse): void {
    if (!this.order || !this.order.company_id) {
      console.error('Order or company ID is missing.');
      return;
    }

    const originalStatus = task.task_status;
    task.task_status = task.task_status === 1 ? 0 : 1;

    const payload: CRM_ORD_128 = {
      company_id: this.order.company_id,
      work_order_id: this.workOrder.work_order_id,
      task_id: task.task_id,
      task_status: task.task_status,
    };

    this.orderService.editWorkOrderTask(payload).subscribe(
      () => {
        console.log(`Task ${task.task_id} status updated to ${task.task_status}.`);
      },
      (err) => {
        console.error(err);
        task.task_status = originalStatus;
      }
    );
  }

  markTaskAsCompleted(task: WorkOrderTaskResponse): void {
    if (!this.order || !this.order.company_id) {
      console.error('Order or company ID is missing.');
      return;
    }

    const originalStatus = task.task_status;
    task.task_status = task.task_status === 2 ? 0 : 2;

    const payload: CRM_ORD_128 = {
      company_id: this.order.company_id,
      work_order_id: this.workOrder.work_order_id,
      task_id: task.task_id,
      task_status: task.task_status,
    };

    this.orderService.editWorkOrderTask(payload).subscribe(
      () => {
        console.log(`Task ${task.task_id} marked as completed.`);
      },
      (err) => {
        console.error(err);
        task.task_status = originalStatus; // Revert on error
      }
    );
  }

  addChecklistToOrder(taskGroups?: WorkOrderTaskGroupResponse[]): void {
    let modalRef = this.modalService.open(AddTaskToWorkOrderComponent, {size: 'lg',});
    modalRef.componentInstance.workOrder = this.workOrder;
    modalRef.componentInstance.taskGroups = this.taskGroups;
    modalRef.componentInstance.availableTaskTemplates = this.taskTemplates;
    modalRef.result
      .then((updatedTaskGroups: WorkOrderTaskGroupResponse[]) => {
        if (updatedTaskGroups) {

          this.orderService.getWorkOrderById(this.workOrder.work_order_id).subscribe((updatedWorkOrder) => {
            this.taskGroups = updatedWorkOrder.task_groups;

            // Recalculate totalTasks dynamically
            this.totalTasks = this.taskGroups.reduce(
              (total, group) => total + group.tasks.length,
              0
            );
          });
        }
      })
      .catch((error) => {
        // Optional: Handle modal dismissal or errors gracefully
        console.error('Modal dismissed or encountered an error:', error);
      });
  }

  routeToCreateTemplate() {
    this.modalService.dismissAll();
    this.router.navigate(['/templates/tasks']);
  }

}
