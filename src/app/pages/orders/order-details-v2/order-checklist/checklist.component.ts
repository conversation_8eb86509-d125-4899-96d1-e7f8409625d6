import {Component, Input, OnInit} from '@angular/core';
import {
  DetailsViewSettings,
  OrderLineResponse,
  OrderLineStageResponse,
  OrderResponse,
  WorkOrderResponse
} from "../../../../@shared/models/order.interfaces";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {ChecklistModalComponent} from "./_modals/checklist-modal/checklist-modal.component";
import {OrderService} from "../../../../@shared/services/order.service";
import {CommonModule} from "@angular/common";
import {CardComponent} from "../../../../@shared/components/layout/card/card.component";
import {ButtonComponent} from "../../../../@shared/components/button/button.component";
import {TranslateModule} from "@ngx-translate/core";
import {Subject} from "rxjs";
import {takeUntil} from "rxjs/operators";
import {LinkTaskTemplateComponent} from "./_modals/link-task-template/link-task-template.component";
import {StorageService} from "../../../../@core/services/storage.service";
import {VerifyPopupModal} from "../../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {StandardImports} from "../../../../@shared/global_import";

@Component({
  selector: 'app-order-checklist',
  templateUrl: './checklist.component.html',
  styleUrls: ['./checklist.component.css'],
  standalone: true,
  imports: [StandardImports, CardComponent]
})
export class ChecklistComponent implements OnInit {
  constructor(private modalService: NgbModal, private orderService: OrderService, private storageService: StorageService) {}
  @Input() workOrderView: boolean = false;
  @Input() workOrder?: WorkOrderResponse;
  @Input() order?: OrderResponse;
  @Input() viewSettings?: DetailsViewSettings

  workOrders: WorkOrderResponse[] = [];
  workOrderSchedules: WorkOrderResponse[] = [];
  totalTasks: number = 0;
  totalCheckedTasks: number = 0;
  totalPercentageTaskChecked: number = 0;

  destroy$ = new Subject<void>();


  ngOnInit() {
    if (!this.workOrderView) {
      this.orderService.workOrders$.pipe(takeUntil(this.destroy$)).subscribe(workOrders => {
        this.workOrders = workOrders;
        if (this.workOrders.length > 0) {
          this.initTasks();
        }
      });
      this.orderService.workOrderSchedules$.pipe(takeUntil(this.destroy$)).subscribe(workOrders => {
        this.workOrderSchedules = workOrders;
        if (this.workOrderSchedules.length > 0) {
          this.initTasks();
        }
      });
      this.orderService.order$.pipe(takeUntil(this.destroy$)).subscribe(order => {
        this.order = order;
      });
    } else {
      this.initTasks();
      this.orderService.workOrder$.pipe(takeUntil(this.destroy$)).subscribe(workOrder => {
        this.workOrder = workOrder;
        if (!this.workOrder || this.storageService.ownedByCompany(this.workOrder.company_id)) {
          this.orderService.order$.pipe(takeUntil(this.destroy$)).subscribe(order => {
            this.order = order;
          });
        }
        this.initTasks();
      });
    }
  }


  initTasks() {
    let workOrders: WorkOrderResponse[] = this.workOrderView ? [this.workOrder!] : this.order?.repeating ? this.workOrderSchedules : this.workOrders!;
    this.totalTasks = 0;
    this.totalCheckedTasks = 0;
    this.totalPercentageTaskChecked = 0;

    for (const wo of workOrders) {

      for (const group of wo.task_groups) {
        for (const task of group.tasks) {
          this.totalTasks += 1;
          if (task.task_status === 1) {
            this.totalCheckedTasks += 1;
          }
        }
      }
    }
    this.totalPercentageTaskChecked = Math.round((this.totalCheckedTasks / this.totalTasks) * 100);
  }

  openModal(){
    const modalRef= this.modalService.open(ChecklistModalComponent, {size: this.workOrder || this.workOrders.length === 1 ? 'md' : 'lg'});
    let updated = false;
    modalRef.componentInstance.checklistsUpdated.subscribe(() => {
      updated = true;
    });
    modalRef.componentInstance.order = this.order;
    modalRef.componentInstance.workOrder = this.workOrderView ? this.workOrder : this.workOrders.length === 1 ? this.workOrders[0] : null;
    modalRef.componentInstance.viewSettings = this.viewSettings;
    modalRef.componentInstance.workOrderView = this.workOrderView || this.workOrders.length === 1;
    modalRef.result.then((result) => {
      if (updated) {
        this.onModalClosed();
      }
    }).catch((error) => {
      if (updated) {
        this.onModalClosed();
      }
    });
  }

  onModalClosed() {
    if (this.workOrder?.schedule?.num_unstarted_work_orders && this.workOrder.schedule.num_unstarted_work_orders > 1) {
      let modalRef = this.modalService.open(VerifyPopupModal, {backdrop: 'static'});
      modalRef.componentInstance.showBody = true;
      modalRef.componentInstance.titleTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.title';
      modalRef.componentInstance.bodyBoldTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyBoldTranslationKey';
      modalRef.componentInstance.bodyMutedTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyMutedTranslationKey';
      modalRef.componentInstance.bodyRegularTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.checkListUpdate';
      modalRef.result.then((result) => {
        if (result) {
          this.orderService.updateWorkOrderScheduleFutureChildrenChecklists(this.workOrder?.work_order_id!).subscribe(() => {
            this.orderService.refreshWorkOrders$.next();
          });
        }
      });
    }
  }
}
