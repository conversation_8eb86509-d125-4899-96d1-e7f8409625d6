<div class="mb-2">
  <app-card
    [labelKey]="'orderDetails.payments.title'"
    [additionalTitleText]="payments && payments.length > 1 ? '(' + payments.length.toString() + ')' : ''"
    [padding]="'0'"
    [buttonHeader]="true">
    <ng-container buttonHeader><i class="fa-regular fa-arrow-up-right-from-square fa-lg cursor-pointer" style="color: rgb(108, 117, 125) !important;" (click)="openPaymentsList()"></i></ng-container>
    <ng-container cardcontent>
      <div class="p-3" *ngIf="loading">
<!--        <div *ngIf="!loading" class="text-muted">{{"orderDetails.payments.noPayments" | translate}}</div>-->
        <div class="d-flex justify-content-center">
          <app-spinner *ngIf="loading"></app-spinner>
        </div>
      </div>

      <div *ngIf="!loading" style="overflow-y: auto; max-height: 250px;">
        <div class="accordion-item payment-container" *ngFor="let payment of payments">
          <div class="d-flex justify-content-between" type="button" data-bs-toggle="collapse" [attr.data-bs-target]="'#payment' + payment.payment_id" (click)="toggleExpand(payment.payment_id)">
            <div class="col">
              <div class="d-flex col">
                <div class="mb-1 d-flex justify-content-between">
                  <span class="me-1 fw-bold">{{payment.payment_method_name}}</span>
                  <div class="d-flex align-items-end" style="padding-bottom: 4px;">
                    <i *ngIf="payment.payment_method_id === 14 && !payment.subscription_active" class="fa-regular fa-warning text-warning" [ngbTooltip]="'orderDetails.paymentSchedules.noSubscriptionActive' | translate"></i>
                  </div>
                  <app-spinner *ngIf="paymentIdLoading == payment.payment_id" class="ms-2"></app-spinner>
                </div>
                <div *ngIf="payment.work_order_title" class="text-muted mb-1 d-flex align-items-end ms-auto">
                  <span *ngIf="payment.work_order_title">{{payment.work_order_title}} (#{{ payment.work_order_number }})</span>
                </div>
                <div *ngIf="payment.comment" class="text-muted">{{payment.comment}}</div>
              </div>
              <div class="d-flex">
                <div class="col-6" [ngClass]="getPaymentStatusColor(payment)"><i *ngIf="payment.auto_send_at" class="fa-regular fa-clock me-1" [ngClass]="{'text-success': payment.payment_sent_at}" [ngbTooltip]="getPaymentAutoSendToolTip(payment)"></i>{{payment.payment_status_id === 0 && payment.payment_method_id === 10 ? ('orderDetails.payments.paymentStatusInvoiceNotSent' | translate) : payment.payment_status_name}}</div>
                <div class="col-6">{{currencyFormat( payment.total_amount_inc_vat)}}</div>
              </div>
            </div>
            <div class="d-flex justify-content-between align-items-center">
              <i class="fa-regular fa-chevron-down" [ngClass]="{'rotate': isExpanded(payment.payment_id)}"></i>
            </div>
          </div>
          <div [id]="'payment' + payment.payment_id" class="accordion-collapse collapse pt-2">
            <div class="accordion-body payment-content font-14">
              <div *ngIf="payment.payment_sent_at && ![10, 15].includes(payment.payment_method_id) && !payment.captured_at"><span class="fw-bold">{{"orderDetails.payments.nextPaymentReminder" | translate}}</span>: {{displayDate(payment.payment_reminder_status === 0 ? payment.payment_reminder_1_scheduled_at : payment.payment_reminder_2_scheduled_at)}}</div>
              <div *ngIf="![10, 15].includes(payment.payment_method_id)"><span class="fw-bold">{{"orderDetails.payments.sentAt" | translate}}</span>: {{payment.payment_sent_at ? displayDate(payment.payment_sent_at) : ''}}</div>
              <div *ngIf="[10, 15].includes(payment.payment_method_id)"><span class="fw-bold">{{"orderDetails.payments.invoiceDate" | translate}}</span>: {{payment.invoice_date ? formatTimeYMD(payment.invoice_date) : ("orderDetails.payments.invoiceNotSent" | translate)}}</div>
              <div *ngIf="[10, 15].includes(payment.payment_method_id)"><span class="fw-bold">{{"orderDetails.payments.dueDate" | translate}}</span>:
                <span *ngIf="!payment.invoice_sent_at || payment.payment_status_id == 3">{{payment.invoice_due_date_days}} {{"common.days" | translate}}</span>
                <span *ngIf="payment.invoice_sent_at && payment.payment_status_id !== 3" [ngClass]="{'text-danger': payment.actual_invoice_due_date_days !== null && payment.actual_invoice_due_date_days < 1}">{{payment.actual_invoice_due_date_text}}</span>
              </div>
              <div *ngIf="[10, 15].includes(payment.payment_method_id)"><span class="fw-bold">{{"orderDetails.payments.invoiceEmail" | translate}}</span>: {{payment.invoice_email}}</div>
              <div *ngIf="[10, 15].includes(payment.payment_method_id)"><span class="fw-bold">{{"orderDetails.payments.invoiceReference" | translate}}</span>: {{payment.invoice_reference_text}}</div>
              <div *ngIf="[10, 15].includes(payment.payment_method_id)"><span class="fw-bold">{{"orderDetails.payments.invoiceSendType" | translate}}</span>: {{payment.invoice_send_type_name}}</div>
              <div><span class="fw-bold">{{"orderDetails.payments.paidAt" | translate}}</span>: {{payment.captured_at ? displayDate(payment.captured_at) : ("orderDetails.payments.notPaidYet" | translate)}}</div>
              <div *ngIf="[10, 15].includes(payment.payment_method_id) && tripletexEnabled"><span class="fw-bold">{{"orderDetails.payments.invoicePDF" | translate}}</span>: <a *ngIf="payment.invoice_sent_at" class="between-color cursor-pointer" (click)="downloadPdf(payment)">{{"orderDetails.payments.download" | translate}}</a> <span *ngIf="!payment.invoice_sent_at">{{"orderDetails.payments.invoiceNotSent" | translate}}</span></div>
              <div>
                <span class="fw-bold">{{"orderDetails.payments.accountingStatus" | translate}}</span>:
                <span [ngClass]="accountingStatusColorMap[payment.accounting_transfer_status_id]!">{{payment.accounting_transfer_status_name}}</span>
                <span *ngIf="[1, 3].includes(payment.accounting_transfer_status_id) && payment.payment_sent_at" class="ms-2 align-items-center" (click)="retryAccountingSync(payment)"></span>
              </div>
<!--              <div>-->
<!--                <span class="fw-bold">{{"orderDetails.payments.paymentAccountingStatus" | translate}}</span>:-->
<!--                <span [ngClass]="accountingPaymentStatusColorMap[payment.accounting_payment_transfer_status_id]!">{{payment.accounting_payment_transfer_status_name}}</span>-->
<!--                <span *ngIf="payment.accounting_transfer_status_id == 2 && payment.payment_status_id == 3 && payment.accounting_payment_transfer_status_id != 0 && payment.payment_method_id != 4" class="ms-2 align-items-center" (click)="retryAccountingSync(payment)"></span>-->
<!--                <a class="ms-1 cursor-pointer" *ngIf="paymentIdLoading !== payment.payment_id && payment.payment_method_id == 10 && payment.payment_status_id != 6" (click)="sendPaymentAsInvoice(payment)">({{payment.invoice_sent_at === null ? ("common.send" | translate) : ("orders.orderDetails.invoiceSendAgain" | translate)}})</a>-->
<!--              </div>-->
              <div>
                <div class="cursor-pointer clickable-text" style="color: var(--primary-color) !important;" *ngIf="[10, 15].includes(payment.payment_method_id) && payment.payment_sent_at && payment.accounting_transfer_at != null" (click)="checkPaymentStatus(payment)">{{"orders.orderDetails.accountingStatus.checkPayment" | translate}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </ng-container>
  </app-card>
</div>
