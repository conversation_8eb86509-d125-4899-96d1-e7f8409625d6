<div class="accordion-item payment-list-item-container p-2" [ngClass]="{'expanded': expanded}">
  <div class="" style="display: grid; grid-template-columns: 14fr 6fr 8fr 8fr 8fr 1fr;" type="button" data-bs-toggle="collapse" [attr.data-bs-target]="'#paymentDetails' + payment.payment_id" (click)="toggleExpand()">

    <div class="col">
      <div class="fw-bold mb-1"><span class="me-1">{{payment.payment_method_name}}</span></div>
      <div class="" [ngClass]="getPaymentStatusColor(payment)">{{payment.payment_status_name}}</div>
      <div *ngIf="!payment.payment_sent_at && payment.auto_send_at" class="font-12">
        {{"orderDetails.payments.list.autoSendAt" | translate}} {{displayDate(payment.auto_send_at, false)}}
      </div>
    </div>

    <div class="d-flex align-items-center">{{currencyFormat( payment.total_amount_inc_vat)}}</div>

    <div class="d-flex align-items-center justify-content-center">
      <i *ngIf="payment.payment_sent_at" class="fa-regular fa-check fa-xl text-success"></i>
      <i *ngIf="!payment.payment_sent_at && payment.auto_send_at" class="fa-regular fa-clock"></i>
    </div>

    <div class="d-flex align-items-center justify-content-center">
      <i *ngIf="payment.payment_status_id === 3" class="fa-regular fa-xl fa-check text-success"></i>
    </div>

    <div class="d-flex align-items-center justify-content-center">{{utilsService.formatDateWdDYM(payment.captured_at, false, false)}}</div>

    <div class="d-flex justify-content-center align-items-center">
      <i *ngIf="!loading" class="fa-regular fa-chevron-down" [ngClass]="{'rotate': expanded}"></i>
      <app-spinner *ngIf="loading"></app-spinner>
    </div>

  </div>
  <div [id]="'paymentDetails' + payment.payment_id" class="accordion-collapse collapse">
    <hr>
    <app-payment-details [payment]="payment" [viewSettings]="viewSettings" (paymentDeleted)="paymentDeleted.emit($event)" (paymentUpdated)="payment = $event;" (loadingEmitter)="loading = $event"></app-payment-details>
  </div>
</div>
