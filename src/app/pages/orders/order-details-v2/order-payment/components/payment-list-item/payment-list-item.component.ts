import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {FormControl} from "@angular/forms";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {OrderService} from "../../../../../../@shared/services/order.service";
import {PaymentService} from "../../../../../../@shared/services/payment.service";
import {currencyFormat, displayDate, getPaymentStatusColor, UtilsService} from "../../../../../../@core/utils/utils.service";
import {ToastService} from "../../../../../../@core/services/toast.service";
import {DetailsViewSettings, OrderResponse} from "../../../../../../@shared/models/order.interfaces";
import {OrderPaymentResponse} from "../../../../../../@shared/models/payment.interfaces";
import {AffiliateResponse} from "../../../../../../@shared/models/affiliate.interfaces";
import {StorageService} from "../../../../../../@core/services/storage.service";
import {StandardImports} from "../../../../../../@shared/global_import";
import {PaymentDetailsComponent} from "../../../../../payments/components/payment-details/payment-details.component";
import {SpinnerComponent} from "../../../../../../@shared/components/spinner/spinner.component";

@Component({
    selector: 'order-payment-list-item',
    templateUrl: './payment-list-item.component.html',
    styleUrl: './payment-list-item.component.css',
    standalone: true,
    imports: [StandardImports, PaymentDetailsComponent, SpinnerComponent]
})
export class PaymentListItemComponent implements OnInit {
  constructor(
    private orderService: OrderService,
    private paymentService: PaymentService,
    public utilsService: UtilsService,
    private toastService: ToastService,
    private modalService: NgbModal,
    private storageService: StorageService
  ) {}

  @Input() payment: OrderPaymentResponse;
  @Input() paymentRecipient: AffiliateResponse;
  loading = false;
  expanded: boolean = false;
  operateExVat: boolean = false;
  viewSettings: DetailsViewSettings = {
    listView: true,
    paymentView: true,
  };
  @Output() paymentDeleted = new EventEmitter<number>();


  ngOnInit() {
    this.storageService.operateExVat$.subscribe((operateExVat) => {
      this.operateExVat = operateExVat;
    });
  }

  toggleExpand() {
    this.expanded = !this.expanded;
  }

  protected readonly currencyFormat = currencyFormat;
  protected readonly getPaymentStatusColor = getPaymentStatusColor;
  protected readonly displayDate = displayDate;
}
