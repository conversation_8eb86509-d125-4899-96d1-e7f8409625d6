import {Component, OnInit} from '@angular/core';
import {OrderService} from "../../../../@shared/services/order.service";
import {OrderPaymentResponse} from "../../../../@shared/models/payment.interfaces";
import {PaymentService} from "../../../../@shared/services/payment.service";
import {currencyFormat, displayDate, formatDateDMY, getPaymentStatusColor, UtilsService} from "../../../../@core/utils/utils.service";
import {ToastService} from "../../../../@core/services/toast.service";
import {PaymentListModalComponent} from "./_modals/payment-list-modal/payment-list-modal.component";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {PaymentDetailsComponent} from "../../../payments/components/payment-details/payment-details.component";
import {TranslateService} from "@ngx-translate/core";
import {StorageService} from "../../../../@core/services/storage.service";
import {StandardImports} from "../../../../@shared/global_import";
import {CardComponent} from "../../../../@shared/components/layout/card/card.component";
import {SpinnerComponent} from "../../../../@shared/components/spinner/spinner.component";
import {PaymentDetailsV2Component} from "../../../payments/components/payment-details-v2/payment-details-v2.component";

@Component({
    selector: 'app-order-payments-original',
    templateUrl: './order-payment.component.html',
    styleUrl: './order-payment.component.css',
    standalone: true,
  imports: [StandardImports, CardComponent, SpinnerComponent]
})
export class OrderPaymentComponent implements OnInit {
  constructor(
    private orderService: OrderService,
    private paymentService: PaymentService,
    public utilsService: UtilsService,
    private toastService: ToastService,
    private modalService: NgbModal,
    private translateService: TranslateService,
    private storageService: StorageService
  ) {}

  // order: OrderResponse;
  payments: OrderPaymentResponse[];
  loading = true;
  paymentIdLoading: number | null = null;
  expandedIds: number[] = [];
  accountingStatusColorMap: {[key: number]: string} = {
    1: '',
    2: 'text-success',
    3: 'text-warning',
    4: '',
    5: '',
  }
  accountingPaymentStatusColorMap: {[key: number]: string} = {
    0: 'text-success',
    1: '',
    4: '',
    5: '',
    6: 'text-success',
    7: 'text-warning',
  }
  tripletexEnabled: boolean = false;
  operateExVat: boolean = false;

  ngOnInit() {
    this.storageService.tripletexEnabled$.subscribe((enabled) => {
      this.tripletexEnabled = enabled;
    });

    this.storageService.operateExVat$.subscribe((operateExVat) => {
      this.operateExVat = operateExVat;
    });

    this.orderService.orderPayments$.subscribe((payments) => {
      this.loading = false;
      if (!payments) return;
      this.payments = payments.sort((a, b) => b.payment_id - a.payment_id);
    });
  }

  toggleExpand(paymentId: number) {
    const index = this.expandedIds.indexOf(paymentId);
    if (index === -1) {
      this.expandedIds.push(paymentId);
    } else {
      this.expandedIds.splice(index, 1);
    }
  }

  isExpanded(paymentId: number) {
    return this.expandedIds.includes(paymentId);
  }

  downloadPdf(payment: OrderPaymentResponse) {
    if (this.paymentIdLoading === payment.payment_id) return;
    this.paymentIdLoading = payment.payment_id;
    this.paymentService.getPaymentInvoicePdfFromTripletex(payment.payment_id).subscribe((response: Blob) => {
      this.paymentIdLoading = null;
      const fileUrl = URL.createObjectURL(response);
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = 'invoice_' + payment.order_number + '.pdf';
      link.click();
      URL.revokeObjectURL(fileUrl);
      link.remove();
    }, error => {
      this.paymentIdLoading = null;
    });
  }

  retryAccountingSync(payment: OrderPaymentResponse) {
    if (this.paymentIdLoading === payment.payment_id) return;
    this.paymentIdLoading = payment.payment_id;
    this.paymentService.retryAccountingSynchronisation(payment.payment_id).subscribe((res) => {
      this.paymentIdLoading = null;
      this.payments = this.payments.map(p => p.payment_id === res.payment_id ? res : p);
      this.toastService.successToast('updated')
      this.orderService.refreshOrderLogs(payment.order_id!)
    }, (error) => {
      this.paymentIdLoading = null;
    })
  }


  checkPaymentStatus(payment: OrderPaymentResponse) {
    this.paymentIdLoading = payment.payment_id;
    this.paymentService.checkAccountingPaymentStatus(payment.payment_id).subscribe((res) => {
      if (res.payment_status_id === 3) {
        this.orderService.fetchAndRefreshOrder(payment.order_id!, 'checkPaymentStatus', false);
      }
      this.paymentIdLoading = null;
    }, error => {
      this.paymentIdLoading = null;
    });
  }

  openPaymentsList() {
    if (this.payments.length === 0) return;
    if (this.payments.length === 1) {
     let modalRef = this.modalService.open(PaymentDetailsV2Component, {size: 'lg'});
     modalRef.componentInstance.payment = this.payments[0];
     modalRef.componentInstance.viewSettings = {
        modalView: true,
        paymentView: true,
        listView: true
     };
    } else {
      let modalRef = this.modalService.open(PaymentListModalComponent, {size: 'lg'});
    }
  }

  getPaymentAutoSendToolTip(payment: OrderPaymentResponse) {
    if (payment.payment_sent_at) {
      return this.translateService.instant('orderDetails.payments.list.autoSendAtButSent') + ' ' + displayDate(payment.auto_send_at!, false)
    } else {
      return this.translateService.instant('orderDetails.payments.list.autoSendAt') + ' ' + displayDate(payment.auto_send_at!, false)
    }
  }

  protected readonly currencyFormat = currencyFormat;
  protected readonly getPaymentStatusColor = getPaymentStatusColor;
  protected readonly formatDateDMY = formatDateDMY;
  protected readonly formatTimeYMD = formatDateDMY;
  protected readonly displayDate = displayDate;
}
