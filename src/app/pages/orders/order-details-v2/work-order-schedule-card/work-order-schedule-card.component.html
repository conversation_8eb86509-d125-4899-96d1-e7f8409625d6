<div class="card order-details-card mb-0">
  <div class="card-body pt-2 px-2 pb-2">
    <div class="schedule-overlay" [ngClass]="{'warning': !workOrder.schedule?.active}" [style.background-color]="!this.workOrder.execution_at ? 'grey' : null" [ngbTooltip]="!this.workOrder.execution_at ? ('orderDetails.workOrderScheduleCard.noExecutionTooltip' | translate) : null"><i class="fa-regular fa-refresh fa-xl overlay-icon cursor-pointer" [ngClass]="{'spin-animation': creationLoading}" (click)="createWorkOrders()"></i></div>
    <div class="row justify-content-between px-2 pt-2 mb-1">
      <div class="col-8">
        <h5 class="card-title mb-1 color-header" *ngIf="workOrder.work_order_status_id == 1">{{"orderDetails.dateTime.repeats" | translate}} {{workOrder.schedule!.schedule_description}}</h5>
        <h4 class="m-0" style="color: #6C757D;">{{workOrder.work_order_title}}</h4>
        <div *ngIf="executionTimeStartControl.value" class="d-flex color-header" style="max-height: 36px !important;">
          <app-input
            type="time"
            (click)="executionTimeStartEdit = true;"
            [editMode]="executionTimeStartEdit"
            [control]="executionTimeStartControl"

            [emitChangeOnBlurOnly]="true"
            [hideTypeIcon]="true"
            (valueChange)="updateStartTime()"
            [readInputClasses]="['h3-mimic']"
            [editInputClasses]="['h3-mimic']"
            [noYPadding]="true"
            [centerWithNoPadding]="true"
          ></app-input>
          <div class="px-1 h3-mimic" style="padding-bottom: 10px;">-</div>
          <app-input
            type="time"
            (click)="executionTimeEndEdit = true;"
            [editMode]="executionTimeEndEdit"
            [control]="executionTimeEndControl"

            [emitChangeOnBlurOnly]="true"
            [hideTypeIcon]="true"
            (valueChange)="updateEndTime()"
            [readInputClasses]="['h3-mimic']"
            [editInputClasses]="['h3-mimic']"
            [noYPadding]="true"
            [centerWithNoPadding]="true"
          ></app-input>
        </div>
        <div *ngIf="workOrder.arrival_from"> {{ "addOrder.schedule.arrivalWindow" | translate }}: {{workOrder.arrival_from}} - {{workOrder.arrival_to}} </div>
        <div [ngClass]="{'text-muted': workOrder.work_order_status_id != 1}">{{ "orders.orderDetails.detailsCard.nextExecution" | translate }}: {{workOrder.work_order_status_id == 1 ? nextExecution : ('orders.orderDetails.detailsCard.nextExecution.notPlanned' | translate)}}</div>
        <hr *ngIf="workOrder.work_order_description" class="my-1">
        <div *ngIf="workOrder.work_order_description" #descriptionContainer [class.expanded]="expandDescription" [class.overflow]="descriptionOverflow" class="position-relative text-muted description-container" style="font-style: italic;">{{workOrder.work_order_description}}</div>
        <div *ngIf="descriptionOverflow && !expandDescription" class="clickable-text between-color pt-1" (click)="expandDescription = true;">{{"workOrderDetails.workOrderDescription.showMore" | translate}}</div>
        <div *ngIf="descriptionOverflow && expandDescription" class="clickable-text between-color pt-1" (click)="expandDescription = false;">{{"workOrderDetails.workOrderDescription.showLess" | translate}}</div>
      </div>
      <div class="col-4 pe-4 d-flex justify-content-end align-items-center" *ngIf="!order.contractorView">
        <div>
          <div class="d-flex">
            <div *ngIf="workOrder.work_order_status_id == 1" class="me-2">
              <app-button
                *ngIf="!workOrder.schedule?.active"
                [buttonType]="'outline'"
                [translationKey]="'Aktiver'"
                (buttonClick)="toggleScheduleActiveStatus()"
              ></app-button>
              <app-button
                *ngIf="workOrder.schedule?.active && order.order_status_id >= 2"
                [iconClass]="'fa-regular fa-pause me-1'"
                [buttonType]="'outline'"
                [themeStyle]="'warning'"
                [translationKey]="'Pause'"
                (buttonClick)="toggleScheduleActiveStatus()"
              ></app-button>
            </div>
            <app-button
              [translationKey]="workOrder.work_order_status_id == 0 ? 'workOrderDetails.repeatingView.scheduleButton' : 'common.open'"
              [buttonType]="'nude'"
              (buttonClick)="openScheduleModal()"
            ></app-button>
          </div>

          <div *ngIf="compactView" class="d-flex justify-content-end mt-2">
            <app-button
              [translationKey]="'workOrderDetails.repeatingView.showAllJobs'"
              [buttonType]="'nude'"
              [small]="true"
              (buttonClick)="openAllJobs()"
            ></app-button>
          </div>
        </div>


      </div>
    </div>

    <div *ngIf="!compactView" class="mb-2">
      <app-work-order-list *ngIf="workOrder.schedule?.num_work_orders! > 0" [workOrderTemplate]="workOrder" [showFiveNext]="true"></app-work-order-list>
    </div>

    <div *ngIf="orderLinesExistsOnWorkOrder" class="pe-2">
      <app-order-lines [workOrder]="workOrder" [orderLinesSubject]="orderLinesSubject" [expanded]="false" [viewSettings]="{compactView: true}"></app-order-lines>
    </div>

    <!--   Repeating payment   -->
    <div *ngIf="workOrder.payment?.template" class="pe-2" [ngClass]="{'mt-2': orderLinesExistsOnWorkOrder}">
      <order-payment-schedule-summary [workOrder]="workOrder" [payment]="workOrder.payment!" [orderLinesSubject]="orderLinesSubject"></order-payment-schedule-summary>
    </div>


  </div>
</div>
