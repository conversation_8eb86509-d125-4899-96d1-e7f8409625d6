import {AfterViewInit, Component, ElementRef, Input, OnInit, ViewChild} from '@angular/core';
import {DetailsViewSettings, OrderLineRow, OrderResponse, WorkOrderResponse} from "../../../../@shared/models/order.interfaces";
import {formatFullDayAndDate, formatTimeHM, UtilsService} from "../../../../@core/utils/utils.service";
import {OrderService} from "../../../../@shared/services/order.service";
import {EventService} from "../../../../@shared/services/event.service";
import {ToastService} from "../../../../@core/services/toast.service";
import {NgbModal, NgbTooltip} from "@ng-bootstrap/ng-bootstrap";

import {WorkOrderDetailsComponent} from "../../../work-orders/components/work-order-details/work-order-details.component";
import {WorkOrderListComponent} from "../../../work-orders/components/work-order-list/work-order-list.component";
import {_CRM_ORD_118, _CRM_ORD_123} from "../../../../@shared/models/input.interfaces";
import {FormControl} from "@angular/forms";
import {VerifyPopupModal} from "../../../../@shared/components/verify-popup-modal/verify-popup-modal";

import {BehaviorSubject} from "rxjs";
import {PaymentScheduleSummaryComponent} from "../order-payment-schedule/components/payment-schedule-summary.component";
import {StandardImports} from "../../../../@shared/global_import";
import {OrderLinesComponent} from "../order-lines/order-lines.component";

@Component({
  selector: 'app-work-order-schedule-card',
  templateUrl: './work-order-schedule-card.component.html',
  styleUrls: ['./work-order-schedule-card.component.css'],
  standalone: true,
  imports: [StandardImports, WorkOrderListComponent, PaymentScheduleSummaryComponent, OrderLinesComponent]
})
export class WorkOrderScheduleCardComponent implements OnInit, AfterViewInit {

  constructor(public utilsService: UtilsService, private modalService: NgbModal, private orderService: OrderService, private eventService: EventService, private toastService: ToastService) {}

  @Input() workOrder: WorkOrderResponse;
  @Input() viewSettings: DetailsViewSettings = {repeatingView: true};
  @Input() compactView: boolean = false;
  @Input() showJobs: boolean = false;
  executionDate: Date;
  fullDate: string;
  formattedDurationHours: string;
  nextExecution: string;
  creationLoading: boolean = false;

  workOrderTitleControl = new FormControl();
  workOrderDescriptionControl = new FormControl();

  executionTimeStartEdit = false;
  executionTimeEndEdit = false;
  executionTimeStartControl = new FormControl();
  executionTimeEndControl = new FormControl();

  orderLinesSubject: BehaviorSubject<OrderLineRow[]> = new BehaviorSubject<OrderLineRow[]>([]);
  orderLinesExistsOnWorkOrder: boolean = false;

  order: OrderResponse;
  descriptionOverflow = false;
  expandDescription = false;

  @ViewChild('descriptionContainer') descriptionContainer: ElementRef<HTMLElement>;

  ngOnInit(): void {
    this.orderService.order$.subscribe((order) => {
      this.order = order;
      this.initWorkOrder();
    });
  }

  ngAfterViewInit() {
    this.checkDescriptionOverflow();
  }

  initWorkOrder() {
    this.workOrderTitleControl.setValue(this.workOrder.work_order_title);
    this.workOrderDescriptionControl.setValue(this.workOrder.work_order_description);
    this.executionDate = this.workOrder.execution_at!;
    this.executionTimeStartControl.setValue(formatTimeHM(this.workOrder.execution_at));
    this.executionTimeEndControl.setValue(formatTimeHM(this.workOrder.execution_to));
    this.fullDate = formatFullDayAndDate(this.workOrder.execution_at, false);
    this.nextExecution = formatFullDayAndDate(this.workOrder.next_execution_at, false);
    const duration = this.workOrder.execution_to?.getTime()! - this.workOrder.execution_at?.getTime()!;
    const durationHours = duration / 1000 / 60 / 60;
    this.formattedDurationHours = this.utilsService.formatDurationFromHours(durationHours)
    this.orderLinesExistsOnWorkOrder = false;
    if (this.workOrder.payment && this.workOrder.payment.order_lines.length > 0) {
      this.orderLinesSubject.next(this.workOrder.payment.order_lines as OrderLineRow[]);
    } else if (this.workOrder.order_lines && this.workOrder.order_lines.length > 0) {
      this.orderLinesExistsOnWorkOrder = true;
      this.orderLinesSubject.next(this.workOrder.order_lines as OrderLineRow[]);
    }
    this.checkDescriptionOverflow();
  }

  openScheduleModal(){
    let modalRef = this.modalService.open(WorkOrderDetailsComponent, {size: 'xl'});
    let viewSettings: DetailsViewSettings = {
      modalView: true,
      collapsedOrderLines: true,
      repeatingView: true,
      workOrderView: true,
    }
    modalRef.componentInstance.workOrderId = this.workOrder.work_order_id;
    modalRef.componentInstance.viewSettings = viewSettings;
  }

  toggleScheduleActiveStatus(){
    if (!this.workOrder.execution_at) return;
    let payload: _CRM_ORD_123 = {
      work_order_schedule_id: this.workOrder.schedule!.work_order_schedule_id!,
      active: !this.workOrder.schedule!.active
    }

    this.orderService.updateWorkOrderSchedule(payload).subscribe((data) => {
      this.orderService.fetchAndRefreshOrder(this.order.order_id, 'updateWorkOrderSchedule');
    });
  }

  async updateStartTime() {
    if (this.executionTimeStartControl.value === formatTimeHM(this.workOrder.execution_at)) {
      this.executionTimeStartEdit = false;
      return;
    }
    this.executionTimeStartEdit = false;
    this.updateWorkOrderExecution();
  }

  async updateEndTime() {
    if (this.executionTimeEndControl.value === formatTimeHM(this.workOrder.execution_to)) {
      this.executionTimeEndEdit = false;
      return;
    }
    this.executionTimeEndEdit = false;
    this.updateWorkOrderExecution();
  }

  calculateExecutionTimes(): Date[] {
    let executionAt = new Date(this.executionDate!);
    if (this.executionTimeStartControl.value) {
      executionAt.setHours(parseInt(this.executionTimeStartControl.value.split(':')[0]), parseInt(this.executionTimeStartControl.value.split(':')[1]));
    }

    let executionTo = new Date(this.executionDate!);
    if (this.executionTimeEndControl.value) {
      executionTo.setHours(parseInt(this.executionTimeEndControl.value.split(':')[0]), parseInt(this.executionTimeEndControl.value.split(':')[1]));
    }

    if (executionAt.getTime() >= executionTo.getTime()) {
      executionTo = new Date(executionAt);
      this.executionTimeEndControl.setValue(formatTimeHM(executionTo));
    }

    return [executionAt, executionTo];
  }

  updateWorkOrderTitleAndDescription(){
    const payload: _CRM_ORD_118 = {
      work_order_id: this.workOrder.work_order_id,
      work_order_title: this.workOrderTitleControl.value,
      work_order_description: this.workOrderDescriptionControl.value,
    }

    this.orderService.patchWorkOrder(payload).subscribe((res) => {
      this.orderService.refreshSingleWorkOrderInWorkOrders(res, 'updateWorkOrderTitleAndDescription');
    }, error => {
    })
  }

  async updateWorkOrderExecution(){
    if (this.viewSettings.createView) return;
    let [executionAt, executionTo] = this.calculateExecutionTimes();

    let payload: _CRM_ORD_118 = {
      work_order_id: this.workOrder?.work_order_id!,
      execution_at: executionAt,
      execution_to: executionTo,
    }

    if (this.workOrder.schedule?.num_unstarted_work_orders! > 1) {
      let modalRef = this.modalService.open(VerifyPopupModal, {size: 'xl', backdrop: 'static'});
      modalRef.componentInstance.showBody = true;
      modalRef.componentInstance.titleTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.title';
      modalRef.componentInstance.bodyBoldTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyBoldTranslationKey';
      modalRef.componentInstance.bodyRegularTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey';
      try {
        payload.update_children_executions = await modalRef.result;
      } catch (e) {
      }
    }

    this.orderService.patchWorkOrder(payload).subscribe((workOrder) => {
      this.workOrder = workOrder;
      this.orderService.fetchAndRefreshOrder(workOrder.order_id, 'updateWorkOrder');
    });
  }

  createWorkOrders(){
    if (!this.workOrder.execution_at) return;
    this.creationLoading = true;
    this.orderService.initiateWorkOrderCreationForSchedule(this.workOrder.schedule?.work_order_schedule_id!).subscribe((res) => {
      this.workOrder = res;
      this.orderService.fetchAndRefreshOrder(this.order.order_id, 'scheduleWorkOrderCreation');
      this.toastService.successToast('global_response_created');
      this.creationLoading = false;
    }, (error) => {
      this.creationLoading = false;
    });
  }

  async openAllJobs() {
    const { WorkOrderListModal } = await import("../../../work-orders/components/work-order-list-modal/work-order-list-modal");
    let modalRef = this.modalService.open(WorkOrderListModal, {size: 'xl'});
    modalRef.componentInstance.workOrderTemplate = this.workOrder;
  }

  checkDescriptionOverflow() {
    if (!this.descriptionContainer) return;
    setTimeout(() => {
      this.descriptionOverflow = this.descriptionContainer.nativeElement.scrollHeight > this.descriptionContainer.nativeElement.clientHeight;
    }, 0);
  }

}

