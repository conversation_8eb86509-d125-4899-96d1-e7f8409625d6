import {Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild} from '@angular/core';
import { CompanyResponse } from 'src/app/@shared/models/company.interfaces';
import {OrderCustomerQuestionResponse, OrderLineResponse, OrderNoteResponse, OrderResponse, WorkOrderResponse} from 'src/app/@shared/models/order.interfaces';
import {currencyFormat, UtilsService} from 'src/app/@core/utils/utils.service';
import { TranslateService } from '@ngx-translate/core';
import { UnitDetails } from 'src/app/@shared/models/input.interfaces';
import { AddressObject } from '../../order-address/order-address.component';
import { EventResponse } from 'src/app/@shared/models/events.interfaces';
import {BehaviorSubject} from "rxjs";
import {StandardImports} from "../../../../../@shared/global_import";
import {PdfWorkOrder, WorkOrderPdfSetup} from "../action-button-group/_modals/init-work-order-pdf-modal/init-work-order-pdf-modal.component";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import {ToastService} from "../../../../../@core/services/toast.service";
import {OrderService} from "../../../../../@shared/services/order.service";

@Component({
    selector: 'app-work-order-pdf',
    templateUrl: './work-order-pdf.component.html',
    styleUrl: './work-order-pdf.component.css',
    standalone: true,
    imports: [StandardImports]
})
export class WorkOrderPdfComponent implements OnInit {

  @Input() order: OrderResponse;
  @Input() company: CompanyResponse;
  @Input() setup: WorkOrderPdfSetup;
  workOrders: PdfWorkOrder[] = [];
  address: UnitDetails;
  orderCustomerQuestions: OrderCustomerQuestionResponse[] = []
  events: EventResponse[] = [];
  orderLines: OrderLineResponse[] = [];
  addresses: AddressObject[] = [];
  current_user_id: string = '';
  totalDiscountAmount: number = 0;
  totalAmountExVat: number = 0;
  totalAmountIncVat: number = 0;
  customerNotes: OrderNoteResponse[] = [];
  internalNotes: OrderNoteResponse[] = [];
  logoLoaded: boolean = false;
  orderLinesLoaded: boolean = false;
  pdfReadySubject: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  @Output() pdfDownloaded: EventEmitter<boolean> = new EventEmitter<boolean>(false);

  @ViewChild('content', {static: false}) contentElementRef: ElementRef;

  constructor(public utilsService: UtilsService,
    public translateService: TranslateService,
    private toastService: ToastService,
    private orderService: OrderService,
  ) {}

  ngOnInit() {
    this.workOrders = this.setup.workOrders.filter((workOrder) => workOrder.include);
    this.orderLines = [];
    for (const wo of this.workOrders) {
      this.orderLines = this.orderLines.concat(wo.order_lines);
    }
    this.orderLines.map((orderLine) => {
      this.totalDiscountAmount += orderLine.discount_amount;
      this.totalAmountExVat += orderLine.gross_total_price_ex_vat;
      this.totalAmountIncVat += orderLine.gross_total_price_inc_vat;
    });
    this.orderLinesLoaded = true;
    if (this.logoLoaded) {
      this.pdfReadySubject.next(true);
    }

    this.customerNotes = this.order.order_notes.filter((note) => note.internal !== 1);
    this.internalNotes = this.order.order_notes.filter((note) => note.internal === 1);

    this.orderCustomerQuestions = this.order.customer_questions;
    this.pdfReadySubject.subscribe((ready) => {
      if (ready) {
        this.createPdf();
      }
    })
  }

  createPdf() {
   html2canvas(this.contentElementRef.nativeElement, {scale: 2, useCORS: true}).then((canvas) => {
      const imgData = canvas.toDataURL('image/jpeg', 1.0);

      const pdf = new jsPDF('p', 'mm', 'a4');
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();

      const imgWidth = pdfWidth;
      const imgHeight = (canvas.height * pdfWidth) / canvas.width;

      let position = 0;

      // If content height is taller than one page
      if (imgHeight < pdfHeight) {
        pdf.addImage(imgData, 'JPEG', 0, 0, imgWidth, imgHeight);
      } else {
        // multipage logic
        const canvasHeightLeft = imgHeight;
        let heightLeft = imgHeight;

        pdf.addImage(imgData, 'JPEG', 0, position, imgWidth, imgHeight);

        heightLeft -= pdfHeight;

        while (heightLeft > 0) {
          position = heightLeft - imgHeight;
          pdf.addPage();
          pdf.addImage(imgData, 'JPEG', 0, position, imgWidth, imgHeight);
          heightLeft -= pdfHeight;
        }
      }

      this.translateService.get('pdf-workOrder.filename', {
        name: this.order.payment_recipient?.name || '',
        orderNumber: this.order.order_number
      }).subscribe((res: string) => {
        pdf.save(`${res}.pdf`);
        this.toastService.successToast('pdf_generated_successfully');
        this.pdfDownloaded.emit(true);
      });
    });
  }

  onLogoLoaded(loaded: boolean) {
    this.logoLoaded = true;
    if (this.orderLinesLoaded) {
      this.pdfReadySubject.next(true);
    }
  }

  verifyValue(value: number | null): string {
    if (value === 1) {
      return this.translateService.instant('yes');
    }
    else if (value === 0) {
      return this.translateService.instant('no');
    }
    else {
    return this.translateService.instant('unknown');
    }
  }

  displayPhoneNumber(phoneNumber: string | null): string {
    if (!phoneNumber) {
      return '';
    }
    //add +47 to the phone number if it is not there, and the format should be +47 12 34 56 78 for all phone numbers
    const countryCode = '+47';
    if (phoneNumber.startsWith(countryCode)) {
      phoneNumber = phoneNumber.slice(countryCode.length);
    }
    const pattern = /^(\d{3})(\d{2})(\d{3})$/;
    const match = phoneNumber.trim().match(pattern);
    if (match) {
      return `${countryCode} ${match[1]} ${match[2]} ${match[3]}`;
    }

    return phoneNumber;
  }

  formatNote(note: OrderNoteResponse): string {
    let text = note.deleted_at ? "" : note.note_text;
    return note.internal ? `${text}` : text;
  }

  protected readonly currencyFormat = currencyFormat;
}
