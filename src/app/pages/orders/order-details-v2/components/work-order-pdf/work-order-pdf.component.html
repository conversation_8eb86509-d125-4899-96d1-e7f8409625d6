<div class="work-order-pdf-content" #content>
  <!-- Header Section -->
  <div class="header">
    <div class="title">{{ "workorder.title" | translate }}</div>
    <div class="logo">
      <img
        id="company-logo-image"
        src="{{ company.logo_url }}"
        alt="{{ 'pdf-invoice.s3ImageAlt' | translate }}"
        (load)="onLogoLoaded(true)"
        (error)="onLogoLoaded(false)"
      >
    </div>
  </div>

  <!-- Information section -->
  <div class="information-section">
    <!-- Workorder to section-->
    <div class="section billed-to">
    <div class="heading">{{ "workorder.toSection.heading" | translate }}</div>
    <h4><strong>{{ order.service_recipient?.name }}</strong></h4>
    <div class="text">{{ displayPhoneNumber(order.service_recipient?.phone ?? null) }}</div>
    <div class="text mb-2">{{ order.service_recipient?.email }}</div>
    </div>

    <!-- Work order details Section -->
    <div class="section details">
      <div class="heading">{{ "workorder.detailsHeading" | translate }}</div>
      <strong>Arbeidstidspunkter:</strong>
      <div class="text-capitalize" *ngFor="let wo of workOrders; let j = index">
        {{ wo.execution_at | date: 'EEEE d. MMM' }}  <i class="fa-regular fa-clock"></i> {{ wo.execution_at | date: 'HH:mm' }} - {{ wo.execution_to | date: 'HH:mm' }}
      </div>
    </div>
  </div>

  <div class="ms-4 d-flex align-items-center" *ngIf="order.comment">
    <h6  class="m-0 pe-1">{{ "workorder.orderComment" | translate }}</h6> <p>   {{order.comment}}</p>
  </div>

  <!-- Work order address section -->
  <div *ngFor="let wo of workOrders" class="row px-4 my-4">
    <div class="p-0 d-flex justify-content-between"><h5>{{wo.work_order_title}}</h5><h5 *ngIf="wo.execution_at" class="text-capitalize">{{ wo.execution_at | date: 'EEEE d. MMM' }}  <i class="fa-regular fa-clock ms-2"></i> {{ wo.execution_at | date: 'HH:mm' }} - {{ wo.execution_to | date: 'HH:mm' }}</h5></div>
    <hr>
    <div *ngIf="wo.work_order_description && setup.showDescription" class="mb-2 text-muted">
      {{ wo.work_order_description }}
    </div>
    <div *ngIf="setup.showAddresses" class="">
      <div class="d-flex justify-content-between gap-2" *ngFor="let chunk of wo.chunkedAddresses">
        <div class="col-6" *ngFor="let addrObj of chunk">
          <h6 class="p-0 mb-1" *ngIf="addrObj.address_name">{{ addrObj.address_name }}</h6>
          <h6 class="p-0 mb-2">{{ addrObj.display }}</h6>
          <div *ngIf="setup.showAddressInformation" class="card mb-2 p-2" style="background-color: #e4f4f1;">
            <div class="row p-2" style="background-color: #fff; color: black;">

              <div class="col-sm-6 p-0">
                <div class="mb-1">
                  <i class="font-14 fa-regular fa-house"></i>
                  {{ "orders.newOrder.address.type" | translate }}: {{ addrObj.property_type_name }}
                </div>
                <div class="mb-1">
                  <i class="fa-regular fa-bed-front"></i>
                  {{ "orders.newOrder.address.bedrooms" | translate }}: {{ addrObj.number_of_bedrooms }}
                </div>
                <div class="mb-1">
                  <i class="fa-regular fa-door-open"></i>
                  {{ "orders.newOrder.address.garage" | translate }}: {{ verifyValue(addrObj.has_garage) }}
                </div>
                <div class="mb-1">
                  <i class="mdi mdi-garage-open-variant"></i>
                  {{ "orders.newOrder.address.totalRooms" | translate }}: {{ addrObj.number_of_rooms }}
                </div>
                <div *ngIf="addrObj.property_type_id === 2 || addrObj.property_type_id === 3">
                  <i class="fa-regular fa-building"></i>
                  {{ "orders.newOrder.address.sectionId" | translate }}: {{ addrObj.section_id }}
                </div>
              </div>

              <div class="col-sm-6 p-0">
                <div class="mb-1">
                  <i class="fa-regular fa-draw-square"></i>
                  {{ "orders.newOrder.address.size" | translate }}:
                  <span *ngIf="addrObj.livable_area">{{ addrObj.livable_area }} m<sup>2</sup></span>
                  <span *ngIf="!addrObj.livable_area">{{ "unknown" | translate }}</span>
                </div>
                <div class="mb-1">
                  <i class="fa-regular fa-bath"></i>
                  {{ "orders.newOrder.address.bathrooms" | translate }}: {{ addrObj.number_of_bathrooms }}
                </div>
                <div class="mb-1">
                  <i class="fa-regular fa-elevator"></i>
                  {{ "orders.newOrder.address.elevator" | translate }}: {{ verifyValue(addrObj.has_elevator) }}
                </div>
                <div>
                  <i class="fa-regular fa-apartment"></i>
                  {{ "orders.newOrder.address.floors" | translate }}: {{ addrObj.number_of_floors }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div *ngIf="wo.users.length > 0 || wo.resources.length > 0" class="d-flex justify-content-between gap-2">
      <!-- Crew persons -->
      <div class="col-6">
        <div class="seller-card" *ngIf="wo.users.length > 0" style="min-width: 200px;">
          <h6 class="text">{{ "workorder.crewTitle" | translate }}</h6>
          <div class="card" style="background-color: #e4f4f1;">
            <div class="p-2">
              <ul class="list-group list-group-flush">
                <li class="list-group-item rounded-2 p-2 mb-1" *ngFor="let user of wo.users">
                  <p class="mb-0"><strong>{{ user.first_name }} {{ user.last_name }}</strong> <span *ngIf="user.phone"> ({{ user.phone }})</span></p>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Resource list -->
      <div class="col-6">
        <div class="seller-card" *ngIf="wo.resources.length > 0" style="min-width: 350px;">
          <h6 class="text">{{ "workorder.resourceTitle" | translate }}</h6>
          <div class="card" style="background-color: #e4f4f1;">
            <div class="p-2">
              <ul class="list-group list-group-flush">
                <li class="list-group-item rounded-2 p-2 mb-1" *ngFor="let resource of wo.resources">
                  <p class="mb-0"><strong>{{ resource.resource_name }} <span *ngIf="resource.resource_data['registration_number']" class="ms-1"> ({{resource.resource_data['registration_number']}})</span></strong></p>
                  <p class="mb-0">{{ resource.resource_description }}</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

    </div>

    <!-- Checklist Section -->
    <div class="" *ngIf="wo.task_groups.length != 0 && setup.showTasks">
      <div class="component-container pe-4">
        <h5>Sjekkliste</h5>
        <div class="card-body" id="task-list-card-body">
          <div *ngIf="wo.task_groups.length > 0" class="job-checklist-container">
            <div class="row"></div>
            <div *ngFor="let taskGroup of wo.task_groups" class="preview-task-group mb-2">
              <label class="fw-bold">
                <span class="" *ngIf="wo.task_groups.length > 1">{{ taskGroup.task_group_name }}</span>
              </label>
              <ul class="list-group mt-1 ms-1">
                <li *ngFor="let task of taskGroup.tasks" class="list-group-item p-2 d-flex align-items-center">
                  <input type="checkbox" class="form-check-input me-2" id="{{task.task_id}}-1" [checked]="task.task_status == 1"/>
                  <label class="form-check-label fw-bold ms-1" for="{{task.task_id}}-1"
                         [ngStyle]="{'text-decoration': task.task_status == 2 ? 'line-through' : 'none'}">
                    {{ task.task_name }}
                  </label>
                  <input *ngIf="task.task_status == 1" type="checkbox" class="form-check-input ms-3 m-0 red-checkbox" checked>
                  <span *ngIf="task.comment" class="ms-1">({{task.comment}})</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>



  <!-- Items Table -->
  <table class="items-table" *ngIf="setup.showOrderLines">
    <!-- Table Head -->
    <thead class="table-header">
      <tr>
        <th>{{ "pdf-invoice.productName" | translate }}</th>
        <th class="text-right">{{ "pdf-invoice.qty" | translate }}</th>
        <th class="text-right">{{ "pdf-invoice.unitPrice" | translate }}</th>
        <th class="text-right">{{ "pdf-invoice.amount" | translate }}</th>
      </tr>
    </thead>
    <!-- Table Body Order lines -->
    <tbody>
      <tr *ngFor="let orderLine of orderLines">
        <td style="max-width: 300px;">{{ orderLine.order_line_name }} <br> <span style="font-style: italic;">{{ orderLine.comment }} </span> </td>
        <td class="text-right">{{ orderLine.quantity }} {{ orderLine.unit_abbreviation }}</td>
        <td *ngIf="order.show_prices_inc_vat === 1" class="text-right">{{ setup.showPrices ? currencyFormat(orderLine.unit_price_inc_vat) : '-' }}</td>
        <td *ngIf="order.show_prices_inc_vat === 1" class="text-right">{{ setup.showPrices ? currencyFormat(orderLine.calculated_total_price_inc_vat) : '-' }}</td>
        <td *ngIf="order.show_prices_inc_vat === 0" class="text-right">{{ setup.showPrices ? currencyFormat(orderLine.unit_price_ex_vat): '-' }}</td>
        <td *ngIf="order.show_prices_inc_vat === 0" class="text-right">{{ setup.showPrices ? currencyFormat(orderLine.calculated_total_price_ex_vat): '-' }}</td>
      </tr>
    </tbody>
  </table>


  <div *ngIf="setup.showPrices" class="container-fluid pe-4">
    <div class="row justify-content-end">
      <div class="col-auto">
        <div *ngIf="totalDiscountAmount">{{ "pdf-invoice.totalExVAT" | translate }}</div>
        <div *ngIf="totalDiscountAmount">{{ "pdf-invoice.discount" | translate }}</div>
        <div><strong>{{ "pdf-invoice.totalEx" | translate }}</strong></div>
        <div>{{ "pdf-invoice.vat" | translate }}</div>
        <div><strong>{{ "pdf-invoice.total" | translate }}</strong></div>
      </div>
      <div class="col-auto">
        <div *ngIf="totalDiscountAmount" class="text-right">{{ currencyFormat(totalAmountIncVat) }}</div>
        <div class="text-right" *ngIf="totalDiscountAmount">-{{ currencyFormat(totalDiscountAmount) }}</div>
        <div><strong>{{ currencyFormat(totalAmountExVat) }}</strong></div>
        <div class="text-right">{{ currencyFormat(totalAmountIncVat - totalAmountExVat) }}</div>
        <div><strong>{{ currencyFormat(totalAmountIncVat) }}</strong></div>
      </div>
    </div>
  </div>

  <!-- Notes, Tasks and CQ -->
  <div class="m-3">
    <div class="row">

      <!-- Notes -->
      <div class="col-6" *ngIf="customerNotes.length > 0 && setup.showCustomerNotes">
        <div class="component-container" >
          <h5 class="mb-2">{{"Kundemeldinger" | translate}}</h5>
          <div id="written-message-customer" class="row custom-message-line mx-1" *ngFor="let note of customerNotes">
            <div class="col justify-content-start ps-0 pe-0" *ngIf="!note.deleted_at">
              <h6 style="color: #313A46;">{{ note.updated_by_name }}</h6>
              <div class="written-message-background-other mt-1">
                <p class="mb-0 mt-0 font-14" [innerHTML]="formatNote(note)"></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-6" *ngIf="internalNotes.length > 0 && setup.showInternalNotes">
        <div class="component-container" >
          <h5 class="mb-2">{{"Interne meldinger" | translate}}</h5>
          <div id="written-message-internal" class="row custom-message-line mx-1" *ngFor="let note of internalNotes">
            <div class="col justify-content-start ps-0 pe-0" *ngIf="!note.deleted_at">
              <h6 style="color: #313A46;">{{ note.updated_by_name }}</h6>
              <div class="written-message-background-other mt-1">
                <p class="mb-0 mt-0 font-14" [innerHTML]="formatNote(note)"></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- CQ Section -->
      <div class="col-6 mt-4" *ngIf="orderCustomerQuestions.length > 0 && setup.showCustomerQuestions">
        <!-- Specifications -->
        <h5 class="mb-2">{{ "workOrder.spesifications.heading" | translate }}</h5>

        <ng-container *ngFor="let question of orderCustomerQuestions">
          <p class="fw-bold mt-2 mb-1">{{ question?.question_text }}</p>
          <ng-container *ngFor="let choice of question?.choices; let i = index">

            <!-- Radio button -->
            <div *ngIf="question?.radio_selection === 1" class="form-check">
              <input type="radio" class="form-check-input" id="radio{{i}}" [checked]="choice?.value === 1" disabled>
              <label class="form-check-label" for="radio{{i}}">{{ choice?.choice_name }}</label>
              <p class="mb-0 ms-4" *ngIf="choice?.input">{{ translateService.instant('orders.orderDetails.orderNotes.customerInput') + ": " + choice?.input }}</p>
            </div>

            <!-- Checkbox -->
            <div *ngIf="question?.radio_selection === 0" class="form-check">
              <input type="checkbox" class="form-check-input" id="checkbox{{i}}" [checked]="choice?.value === 1" disabled>
              <label class="form-check-label" for="checkbox{{i}}">{{ choice?.choice_name }}</label>
              <p class="mb-0 ms-4" *ngIf="choice?.input">{{ translateService.instant('orders.orderDetails.orderNotes.customerInput') + ": " + choice?.input }}</p>
            </div>
          </ng-container>
        </ng-container>
      </div>
    </div>
  </div>

  <div *ngIf="setup.showSignature" class="my-4 px-4">
    <h4>Signatur</h4>
    <hr class="mt-1">
  </div>


  <div class="row footer-section" #footer>
    <!-- Terms and Conditions Section -->
    <div class="col section terms"></div>


    <!-- Additional Notes Section -->
    <div class="col section notes">
      <div class="company-information">
        <h5 class="mb-0 text-right" *ngIf="company" >{{company.company_name}}</h5>
        <p class="m-0 text-right" *ngIf="company" >{{company.address.display}}</p>
        <p class="m-0 text-right" *ngIf="company" >{{ "pdf-invoice.phone" | translate }}: {{displayPhoneNumber(company.phone)}}</p>
        <p class="m-0 text-right" *ngIf="company" >{{ "pdf-invoice.email" | translate }}: {{company.email}}</p>
      </div>
    </div>
    <div class= "ps-2 generated-by mb-2"> {{ "pdf-invoice.generated-by" | translate }}</div>
  </div>

</div>
