import {Component, Input, OnInit} from '@angular/core';
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {NgbActiveModal} from "@ng-bootstrap/ng-bootstrap";
import {OrderResponse, WorkOrderResponse} from "../../../../../../../@shared/models/order.interfaces";
import {OrderService} from "../../../../../../../@shared/services/order.service";
import {_CRM_ORD_101, _CRM_PAY_34} from "../../../../../../../@shared/models/input.interfaces";
import {AffiliateResponse} from "../../../../../../../@shared/models/affiliate.interfaces";
import {AffiliateService} from "../../../../../../../@shared/services/affiliate.service";
import {SelectoriniComponent} from "../../../../../../../@shared/components/selectorini/selectorini.component";
import {NgIf} from "@angular/common";
import {StandardImports} from "../../../../../../../@shared/global_import";

@Component({
    selector: 'app-send-to-sub-contractor-modal',
    templateUrl: './send-to-sub-contractor-modal.component.html',
    styleUrls: ['./send-to-sub-contractor-modal.component.scss'],
    standalone: true,
  imports: [StandardImports, SelectoriniComponent]
})
export class SendToSubContractorModalComponent implements OnInit {
  @Input() workOrder: WorkOrderResponse;

  selectedContractor: AffiliateResponse | null;
  loading: boolean = false;
  subContractors: AffiliateResponse[] = [];

  constructor(private translate: TranslateService,
              private orderService: OrderService,
              public activeModal: NgbActiveModal,
              public affiliateService: AffiliateService
              ) {
  }

  ngOnInit() {
    this.affiliateService.searchForSubContractors().subscribe((res) => {
      this.subContractors = res;
    });
  }

  onSubContractorSelected(contractor: any) {
    this.selectedContractor = contractor;
  }

  onDeselectSubContractor() {
    this.selectedContractor = null;
  }

  sendWorkOrder() {
    this.loading = true;
    let params: _CRM_ORD_101 = {
      work_order_id: this.workOrder.work_order_id,
      affiliate_ids: [this.selectedContractor!.affiliate_id],
      accept: false,
    }
    this.orderService.sendOrderToSubContractors(params).subscribe((res) => {
      this.orderService.refreshSingleWorkOrderInWorkOrders(res, 'send-to-sub-contractor-modal');
      this.activeModal.close(res);
    }, () => {
      this.loading = false;
    });
  };

  closeModal() {
    this.activeModal.close(false);
  }



}
