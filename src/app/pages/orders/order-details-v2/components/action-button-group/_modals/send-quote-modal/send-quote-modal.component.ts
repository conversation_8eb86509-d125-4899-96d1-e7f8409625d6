import { Component, Input, OnInit } from '@angular/core';
import { TranslateService } from "@ngx-translate/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { OrderResponse} from "../../../../../../../@shared/models/order.interfaces";
import { OrderService } from "../../../../../../../@shared/services/order.service";
import { ToastService } from 'src/app/@core/services/toast.service';
import {StandardImports} from "../../../../../../../@shared/global_import";
import {ToggleSwitchComponent} from "../../../../../../../@shared/components/toggle-switch/toggle-switch.component";

@Component({
    selector: 'app-send-quote-modal',
    templateUrl: './send-quote-modal.component.html',
    styleUrls: ['./send-quote-modal.component.scss'],
    standalone: true,
  imports: [StandardImports, ToggleSwitchComponent]
})
export class SendQuoteModalComponent implements OnInit {
  @Input() order: OrderResponse;
  sms: boolean = true;
  smsDisabled: boolean = false;
  email: boolean = false;
  emailDisabled: boolean = false;
  sendQuoteInProgress: boolean = false;
  useAffiliateContact: boolean = false;
  phoneErrorMessageKey: string;
  validRecipient: boolean = false;

  constructor(private translate: TranslateService,
              private orderService: OrderService,
              public activeModal: NgbActiveModal,
              private toastService: ToastService,
  ) {
  }

  ngOnInit() {
    // If the recipient is a private person
    if (this.order.payment_recipient?.is_private === 1) {
      this.validRecipient = true;

      if (!this.order.payment_recipient?.email) {
        this.email = false;
        this.emailDisabled = true;
      }

      // Check if the phone exists and starts with '+47'
      if (!this.order.payment_recipient?.phone || !this.order.payment_recipient.phone.startsWith('+47')) {
        this.sms = false;
        this.smsDisabled = true;
        this.phoneErrorMessageKey = 'orders.orderDetails.sendQuote.foreignPhone';
      }
    }
    // If the recipient is a business (using affiliate_contact)
    else {
      if (!this.order.affiliate_contact) {
        this.sms = false;
        this.smsDisabled = true;
        this.email = false;
        this.emailDisabled = true;
      } else {
        this.validRecipient = true;
        if (!this.order.affiliate_contact.email) {
          this.email = false;
          this.emailDisabled = true;
        }
        if (!this.order.affiliate_contact.phone) {
          this.sms = false;
          this.smsDisabled = true;
          this.phoneErrorMessageKey = 'orders.orderDetails.sendQuote.noPhone';
        }
      }
    }
  }

  sendQuote() {
    this.sendQuoteInProgress = true;
    const sendViaAffiliate = this.useAffiliateContact;

    this.orderService.sendQuote(this.order.order_id, this.sms, this.email).subscribe(() => {
      this.sendQuoteInProgress = false;
      this.orderService.fetchAndRefreshOrder(this.order.order_id, 'sendQuote');
      this.orderService.refreshOrderLogs(this.order.order_id);
      this.closeModal();
      this.toastService.successToast('quote_sent_successfully');
    });
  };

  smsChange(event: any) {
    this.sms = event;
  }

  emailChange(event: any) {
    this.email = event;
  }

  closeModal() {
    this.activeModal.close(false);
  }
}

