import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {OrderResponse, WorkOrderResponse} from "../../../../../@shared/models/order.interfaces";
import {NgbModal, NgbModalRef} from "@ng-bootstrap/ng-bootstrap";
import {OrderService} from "../../../../../@shared/services/order.service";
import {Router} from "@angular/router";
import {ToastService} from "../../../../../@core/services/toast.service";
import {TranslateService} from "@ngx-translate/core";
import {VerifyPopupModal} from "../../../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {_CRM_ORD_55, _CRM_ORD_97} from "../../../../../@shared/models/input.interfaces";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import {PaymentService} from "../../../../../@shared/services/payment.service";
import {RefundOrderModalComponent} from "./_modals/refund-order-modal/refund-order-modal.component";
import {CancelOrderModalComponent} from "./_modals/cancel-order-modal/cancel-order-modal.component";
import {SendQuoteModalComponent} from "./_modals/send-quote-modal/send-quote-modal.component";
import {StorageService} from "../../../../../@core/services/storage.service";
import {ReportinatorService} from "../../../../../@shared/services/reportinator.service";
import {NewReportModalComponent} from "../../../../reportinator/reports/reports-list/_modals/new-report-modal/new-report-modal.component";
import {WorkOrderDetailsComponent} from "../../../../work-orders/components/work-order-details/work-order-details.component";
import {PaymentDetailsComponent} from "../../../../payments/components/payment-details/payment-details.component";
import {StandardImports} from "../../../../../@shared/global_import";
import {WorkOrderPdfComponent} from "../work-order-pdf/work-order-pdf.component";
import {SpinnerComponent} from "../../../../../@shared/components/spinner/spinner.component";
import {QuotePdfComponent} from "../quote-pdf/quote-pdf.component";
import {InitWorkOrderPdfModalComponent} from "./_modals/init-work-order-pdf-modal/init-work-order-pdf-modal.component";
import {PaymentDetailsV2Component} from "../../../../payments/components/payment-details-v2/payment-details-v2.component";
import {NewWorkOrderComponent} from "../../../../work-orders/components/new-work-order/new-work-order.component";
import {OrderSettingsModalComponent} from "./_modals/order-settings-modal/order-settings-modal.component";

@Component({
    selector: 'app-action-button-group',
    templateUrl: './action-button-group.component.html',
    styleUrls: ['./action-button-group.component.css'],
    standalone: true,
  imports: [StandardImports, WorkOrderPdfComponent, SpinnerComponent, QuotePdfComponent]
})
export class ActionButtonGroupComponent implements OnInit {
  order: OrderResponse;
  acceptOrderLoading: boolean = false;
  sendQuoteLoading: boolean = false;
  orderLoading: boolean = false;
  isDropdownOpen: boolean = false;
  orderId: any;
  reportinatorEnabled: boolean = false;
  reportinatorReportId: number | null = null;


  constructor(private orderService: OrderService,
              private modalService: NgbModal,
              private router: Router,
              private toastService: ToastService,
              private translate: TranslateService,
              private paymentService: PaymentService,
              private storageService: StorageService,
              private reportinatorService: ReportinatorService
              ) {
  }

  ngOnInit(): void {
    this.orderService.order$.subscribe((order) => {
      this.order = order;
    });

    this.storageService.reportinatorEnabled$.subscribe((enabled) => {
      this.reportinatorEnabled = enabled;
      if (enabled) {
        this.reportinatorService.getReportByOrderId(this.order.order_id).subscribe((report) => {
          if (report) {
            this.reportinatorReportId = report.report_id;
          }
        });
      }
    });
  }

  onSendQuote() {
    const quoteModalRef = this.modalService.open(SendQuoteModalComponent, {});
    quoteModalRef.componentInstance.order = this.order;
  }


  onAcceptOrder() {
    if (this.acceptOrderLoading) {
      return;
    }
    this.acceptOrderLoading = true;
    this.orderLoading = true;
    this.orderService.acceptOrderAsCompany(this.order.order_id).subscribe( {
      next: (res) => {
        this.orderService.refreshOrder(res, 'onAcceptOrder');
        this.orderService.refreshOrderLogs(this.order.order_id);
        this.acceptOrderLoading = false;
        this.orderLoading = false;
      },
      error: (error) => {
        this.acceptOrderLoading = false;
        this.orderLoading = false;
      }});
  }


  onRefundClick() {
    const modalRef = this.modalService.open(RefundOrderModalComponent, { size: 'xl'});
    modalRef.componentInstance.order = this.order;
    modalRef.componentInstance.orderRefunded.subscribe((res: OrderResponse) => {
      this.orderService.refreshOrder(res, 'onRefundClick');
      modalRef.close();
    });
  }

  sendFinishJob() {
    this.orderLoading = true;
    let payload: _CRM_ORD_55 = {
      order_id: this.order.order_id,
      order_status_id: 5
    }
    this.orderService.updateOrderStatus(payload).subscribe({
      next: (res) => {
        this.orderService.refreshOrder(res, 'sendFinishJob');
        this.orderService.refreshOrderLogs(this.order.order_id);
        this.orderLoading = false;
      },
      error: (error) => {
        this.orderLoading = false;
      }});
  }


  archiveOrder() {
    if (this.order.archived_at != null || this.order.order_status_id != 7) {
      return;
    }
    this.orderLoading = true;
    this.orderService.archiveOrder(this.order.order_id).subscribe({
      next: (res) => {
        this.orderService.refreshOrder(res, 'archiveOrder');
        this.orderLoading = false;
      },
      error: (error) => {
        this.orderLoading = false;
      }});
  }

  removeOrderFromArchive() {
    if (this.order.archived_at == null || this.order.order_status_id == 8) {
      return;
    }
    this.orderLoading = true;
    this.orderService.removeFromArchive(this.order.order_id).subscribe({
      next: (res) => {
        this.orderService.refreshOrder(res, 'removeOrderFromArchive');
        this.orderLoading = false;
      },
      error: (error) => {
        this.orderLoading = false;
      }});
  }

  cancelOrder() {
    if (this.order.order_status_id == 7) {
      return;
    }

    const modalRef = this.modalService.open(CancelOrderModalComponent, { size: 'lg' });
    modalRef.result.then((result) => {
      if (result.deleteConfirmed) {
        this.orderLoading = true;
        let notify_customer = result.notifyCustomer ? 1 : null
        this.orderService.cancelOrder(notify_customer, this.order.order_id).subscribe((res) => {
          this.orderService.refreshOrder(res, 'cancelOrder');
          this.orderService.refreshOrderLogs(this.order.order_id);
          this.orderService.fetchAndRefreshOrderPaymentSchedules(this.order.order_id);
          this.orderLoading = false;
        });
      }
    }).catch((reason) => {
      this.orderLoading = false;
    });
  }

  enableProject() {
    this.orderLoading = true;
    let payload: _CRM_ORD_97 = {
      order_id: this.order.order_id,
      is_project: true
    }
    this.orderService.patchOrder(payload).subscribe({
      next: (res) => {
        this.orderService.refreshOrder(res, 'enableProject');
        this.orderLoading = false;
      },
      error: (error) => {
        this.orderLoading = false;
      }});
  }

  disableProject() {
    this.orderLoading = true;
    let payload: _CRM_ORD_97 = {
      order_id: this.order.order_id,
      is_project: false
    }
    this.orderService.patchOrder(payload).subscribe({
      next: (res) => {
        this.orderService.refreshOrder(res, 'disableProject');
        this.orderLoading = false;
      },
      error: (error) => {
        this.orderLoading = false;
      }});
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  sendOrderConfirmationEmail() {
    this.orderLoading = true;
    this.orderService.sendOrderConfirmation(this.order.order_id).subscribe({
      next: (res) => {
        this.orderService.refreshOrder(res, 'sendOrderConfirmationEmailo');
        this.orderService.refreshOrderLogs(this.order.order_id);
        this.toastService.successToast('order_confirmation_email_sent');
        this.orderLoading = false;
      },
    });
  }

  generatePdf(elementId: string, translationKey: string): void {
    const element = document.getElementById(elementId);
    if (!element) {
      console.error(`Unable to find the element for PDF content with id: ${elementId}.`);
      return;
    }

    html2canvas(element, { scale: 3, useCORS: true, logging: true }).then(canvas => {
      const imgData = canvas.toDataURL('image/jpeg', 1); // High quality
      const pdfWidth = 595.28; // A4 width in pixels at 72 DPI
      const pdfHeight = Math.max(841.89, canvas.height * pdfWidth / canvas.width); // A4 height in pixels at 72 DPI or higher based on content

      const doc = new jsPDF({
        orientation: 'p',
        unit: 'px',
        format: [pdfWidth, pdfHeight],
      });

      try {
        doc.addImage(imgData, 'JPEG', 0, 0, pdfWidth, pdfHeight);
      } catch (error) {
        console.error('Error adding image to PDF:', error);
      }

      // Save the PDF
      this.translate.get(translationKey, { name: this.order.payment_recipient?.name || '', orderNumber: this.order.order_number }).subscribe(
        (res: string) => {
          const fileName = `${res}.pdf`;
          doc.save(fileName);
          this.toastService.successToast('pdf_generated_successfully');
        },
        (error) => {
          console.error('Error generating PDF:', error);
        }
      );
    }).catch(error => {
      console.error('Error during HTML to Canvas conversion:', error);
    });
  }

  generateOrderPdf(): void {
    this.generatePdf('quotePdfContent', 'pdf-invoice.filename');
  }

  openWorkOrderPdfModal(): void {
    let modalRef = this.modalService.open(InitWorkOrderPdfModalComponent);
    modalRef.componentInstance.order = this.order;
  }

  goToReport() {
    if (this.reportinatorReportId) {
      this.router.navigate(['/reportinator/reports/details/', this.reportinatorReportId]);
    } else {
      return;
    }
  }

  createReport() {
    let modalRef: NgbModalRef = this.modalService.open(NewReportModalComponent);
    modalRef.componentInstance.order_id = this.order.order_id;
  }

  initiateOrderDraft() {
    this.orderService.initiateOrderDraft(this.order.order_id).subscribe({
      next: (res) => {
        this.orderService.refreshOrder(res, 'initiateOrderDraft');
        this.orderService.refreshOrderLogs(this.order.order_id);
        this.toastService.successToast('updated')
      }
    });
  }

  openCreateWorkOrderModal() {
    let modalRef = this.modalService.open(NewWorkOrderComponent);
    modalRef.componentInstance.orderId = this.order.order_id;
    modalRef.componentInstance.viewSettings = {};
  }

  openCreateScheduleModal() {
    let modalRef = this.modalService.open(NewWorkOrderComponent);
    modalRef.componentInstance.orderId = this.order.order_id;
    modalRef.componentInstance.viewSettings = {
      repeatingView: true,
    }
  }

  async closeOrder() {
    let modalRef = this.modalService.open(VerifyPopupModal, {});
    modalRef.componentInstance.showBody = true;
    modalRef.componentInstance.bodyBoldTranslationKey = 'order.orderDetails.closeOrderVerifyModal.boldText'
    modalRef.componentInstance.bodyRegularTranslationKey = 'order.orderDetails.closeOrderVerifyModal.regularText'
    await modalRef.result.then((proceed) => {
      if (proceed) {
        this.orderLoading = true;
        let payload: _CRM_ORD_55 = {
          order_id: this.order.order_id,
          order_status_id: 7
        }
        this.orderService.updateOrderStatus(payload).subscribe({
          next: (res) => {
            this.orderService.refreshOrder(res, 'sendFinishJob');
            this.orderService.refreshOrderLogs(this.order.order_id);
            this.orderLoading = false;
          },
          error: (error) => {
            this.orderLoading = false;
          }});
        }
      });
  }

  openRepeatingPayment() {
    let modalRef = this.modalService.open(PaymentDetailsV2Component, {size: 'lg', centered: true});
    modalRef.componentInstance.order = this.order;
    modalRef.componentInstance.viewSettings = {
      createView: true,
      modalView: true,
      repeatingView: true,
    };
  }

  openSettingsModal() {
    let modalRef = this.modalService.open(OrderSettingsModalComponent);
    modalRef.componentInstance.order = this.order;
  }

  openPayment() {
    let modalRef = this.modalService.open(PaymentDetailsV2Component, {size: 'lg', centered: true});
    modalRef.componentInstance.singlePaymentInRepeating = true;
    modalRef.componentInstance.viewSettings = {
      createView: true,
      modalView: true,
    };
  }

}

