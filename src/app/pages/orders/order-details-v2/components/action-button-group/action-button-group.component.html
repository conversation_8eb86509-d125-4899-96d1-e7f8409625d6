<!-- Order action buttons -->
<div>

  <div *ngIf="order.contractorView">
      <button
        class="btn btn-primary"
        (click)="openWorkOrderPdfModal()">
        <i class="fa-regular fa-print me-1"></i>
        {{ "orders.orderDetails.printWorkOrder" | translate }}
      </button>
  </div>


  <!-- Action buttons dropdown -->
  <div *ngIf="!order.contractorView" class="btn-group w-100">
    <button type="button"
            (click)="toggleDropdown()"
            class="btn btn-outline-primary" [disabled]="acceptOrderLoading || sendQuoteLoading" [ngClass]="orderLoading ? '' : 'dropdown-toggle'" style="min-width: 122px; height: 38px;" data-bs-toggle="dropdown" aria-expanded="false">
      <span style="font-weight: bold;" *ngIf="!orderLoading">{{ "orders.orderDetails.otherActions" | translate }} <span class="caret"></span></span>
      <app-spinner *ngIf="orderLoading"></app-spinner>
    </button>

    <div class="dropdown-menu" [hidden]="orderLoading || acceptOrderLoading || sendQuoteLoading">

      <!--   Some buttons are hardcoded disabled buttons to handle data updates without reloading   -->

      <!-- Create work order -->
      <button
        *ngIf="order.order_status_id != 8"
        class="dropdown-item"
        (click)="openCreateWorkOrderModal()">
        <i class="fa-regular fa-calendar-plus me-1"></i>
        <span >{{"orderDetails.createWorkOrder" | translate}}</span>
      </button>

      <!-- Create repeating work order -->
      <button
        *ngIf="order.order_status_id != 8"
        class="dropdown-item"
        (click)="openCreateScheduleModal()">
        <i class="fa-regular fa-repeat me-1"></i>
        <span >{{"orderDetails.createRepeatingWorkOrder" | translate}}</span>
      </button>

      <!-- Create single payment -->
      <button
        *ngIf="order.order_status_id >= 2 && order.order_status_id != 8"
        class="dropdown-item"
        (click)="openPayment()">
        <i class="fa-regular fa-money-bill me-1"></i>
        <span >{{"orderDetails.createPayment" | translate}}</span>
      </button>

      <!-- Create repeating payment -->
      <button
        *ngIf="order.order_status_id != 8"
        class="dropdown-item"
        (click)="openRepeatingPayment()">
        <i class="fa-regular fa-money-bill-transfer me-1"></i>
        <span >{{"orderDetails.createRepeatingPayment" | translate}}</span>
      </button>

      <!-- Send quote button -->
      <button
        *ngIf="order.order_status_id !== -1"
        class="dropdown-item"
        (click)="onSendQuote()">
        <i *ngIf="!sendQuoteLoading" class="mdi mdi-email-outline me-1"></i>
        <span *ngIf="!sendQuoteLoading">{{ "orders.orderDetails.sendQuoteButton" | translate }}</span>
      </button>

      <!--  Send order confirmation  -->
      <button
        *ngIf="order.order_status_id >= 2"
        class="dropdown-item"
        (click)="sendOrderConfirmationEmail()"
        [disabled]="order.affiliate_contact ? !order.affiliate_contact.email : !order.payment_recipient?.email"
        [ngbTooltip]="((order.affiliate_contact ? !order.affiliate_contact.email : !order.payment_recipient?.email) ? 'orders.orderDetails.sendQuote.noEmail' : '') | translate"
        [ngStyle]="{'color': !order.payment_recipient?.email ? '#aaa' : '#6C757D'}">
        <i class="fa-regular fa-file-invoice-dollar me-1"></i>
        {{ "orders.orderDetails.sendConfirmationButtonLabel" | translate }}
      </button>

      <!--  Archive  -->
      <button
        *ngIf="order.archived_at == null && order.order_status_id == 7"
        [ngStyle]="{'color': order.order_status_id == 7 ? '' : '#aaa'}"
        class="dropdown-item"
        (click)="archiveOrder()">
        <i class="fa-regular fa-inbox-in me-1"></i>
        {{ "orders.orderDetails.archiveOrder" | translate }}
      </button>

      <!--  Archive - Revert  -->
      <button
        *ngIf="order.archived_at"
        [ngStyle]="{'color': order.order_status_id != 8 ? '' : '#aaa', 'cursor': order.order_status_id != 8 ? 'pointer' : 'default'}"
        [ngClass]="{'no-hover': order.order_status_id === 8}"
        class="dropdown-item"
        (click)="removeOrderFromArchive()">
        <i class="fa-regular fa-inbox-out me-1"></i>
        {{ "orders.orderDetails.removeOrderFromArchive" | translate }}
      </button>

      <!--  Print quote in PDF   -->
      <button
        class="dropdown-item"
        (click)="generateOrderPdf()">
        <i class="fa-regular fa-print me-1"></i>
        {{ "orders.orderDetails.printQuote" | translate }}
      </button>

      <!--  Print workorder in PDF   -->
      <button
        class="dropdown-item"
        (click)="openWorkOrderPdfModal()">
        <i class="fa-regular fa-print me-1"></i>
        {{ "orders.orderDetails.printWorkOrder" | translate }}
      </button>

      <!-- Reportinator - Go to report -->
      <button
        *ngIf="reportinatorEnabled && reportinatorReportId !== null"
        class="dropdown-item"
        (click)="goToReport()">
        <i class="fa-regular fa-pen-ruler me-1"></i>
        {{ "orders.orderDetails.goToReportinatorReport" | translate }}
      </button>

      <!-- Reportinator - Create new report -->
      <button
        *ngIf="reportinatorEnabled && reportinatorReportId === null"
        class="dropdown-item"
        (click)="createReport()">
        <i class="fa-regular fa-pen-ruler me-1"></i>
        {{ "orders.orderDetails.createToReportinatorReport" | translate }}
      </button>

<!--      &lt;!&ndash; Enable project view &ndash;&gt;-->
<!--      <button-->
<!--        *ngIf="!order.is_project"-->
<!--        class="dropdown-item"-->
<!--        (click)="enableProject()">-->
<!--        <i class="fa-regular fa-rectangles-mixed me-1"></i>-->
<!--        {{ "orders.orderDetails.enableProject" | translate }}-->
<!--      </button>-->

<!--      &lt;!&ndash; Disable project view &ndash;&gt;-->
<!--      <button-->
<!--        *ngIf="order.is_project"-->
<!--        class="dropdown-item"-->
<!--        (click)="disableProject()">-->
<!--        <i class="fa-regular fa-rectangle-list me-1"></i>-->
<!--        {{ "orders.orderDetails.disableProject" | translate }}-->
<!--      </button>-->

      <!-- Close order -->
      <button
        *ngIf="![7, 8].includes(order.order_status_id)"
        class="dropdown-item"
        (click)="closeOrder()">
        <i class="fa-regular fa-square-check me-1"></i>
        {{ "orders.orderDetails.closeOrder" | translate }}
      </button>

      <!--  Cancel order  -->
      <button
        *ngIf="order.order_status_id != 7 && order.order_status_id != 8"
        [ngStyle]="{'color': [7, 8].includes(order.order_status_id) ? '#aaa' : ''}"
        class="dropdown-item"
        (click)="cancelOrder()">
        <i class="fa-regular fa-delete-right me-1"></i>
        {{ (order.order_status_id === -1 ? "orders.orderDetails.deleteOrder" : "orders.orderDetails.cancelOrder") | translate }}
      </button>

      <!-- Close order -->
      <button
        class="dropdown-item"
        (click)="openSettingsModal()">
        <i class="fa-regular fa-gear me-1"></i>
        {{ "Innstillinger" | translate }}
      </button>


      <app-quote-pdf
        *ngIf="order"
        class="offscreen-quote-pdf"
        [isDropdownOpen]="isDropdownOpen"
      ></app-quote-pdf>
    </div>

  </div>
</div>
