import {AfterViewInit, ChangeDetectorRef, Component, ElementRef, Input, OnChanges, OnDestroy, OnInit, SimpleChanges, ViewChild} from '@angular/core';
import { CompanyResponse } from 'src/app/@shared/models/company.interfaces';
import {OrderLineResponse, OrderNoteResponse, OrderResponse, WorkOrderResponse} from 'src/app/@shared/models/order.interfaces';
import {currencyFormat, getImageFileFromS3, UtilsService} from 'src/app/@core/utils/utils.service';
import { ProductInformationResponse } from 'src/app/@shared/models/product.interfaces';
import {ProductService} from "../../../../../@shared/services/product.service";
import {CompanyService} from "../../../../../@shared/services/company.service";
import { PdfService } from 'src/app/@shared/services/pdf.service';
import { OrderService } from 'src/app/@shared/services/order.service';
import {forkJoin, Subject} from "rxjs";
import {OrderPaymentResponse} from "../../../../../@shared/models/payment.interfaces";
import {PaymentService} from "../../../../../@shared/services/payment.service";
import {SettingsService} from "../../../../../@shared/services/settings.service";
import {StandardImports} from "../../../../../@shared/global_import";
import {EditorContentService} from "../../../../../@shared/services/editor-content.service";
import {takeUntil} from "rxjs/operators";

export interface TempOrderPayment extends OrderPaymentResponse {
  schedule_description: string;
}

@Component({
    selector: 'app-quote-pdf',
    templateUrl: './quote-pdf.component.html',
    styleUrls: ['./quote-pdf.component.css'],
    standalone: true,
    imports: [StandardImports]
})
export class QuotePdfComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('content') contentElementRef: ElementRef;
  @ViewChild('footer') footerElementRef: ElementRef;

  order: OrderResponse;
  current_user_id: string = '';
  company: CompanyResponse;
  orderLines: OrderLineResponse[] = [];
  productInformation: ProductInformationResponse[] = [];
  @Input() isDropdownOpen: boolean;
  fixedRepeatedPayments: OrderPaymentResponse[] = [];
  repeatedWorkOrderPayments: TempOrderPayment[] = [];
  workOrders: WorkOrderResponse[] = [];

  totalDiscountAmount: number = 0;
  totalAmountExVat: number = 0;
  totalAmountIncVat: number = 0;
  showNotes: boolean = false;
  companyDetails: CompanyResponse;
  destroy$ = new Subject<void>();

  constructor(public utilsService: UtilsService,
    private cdr: ChangeDetectorRef,
    private productService: ProductService,
    private companyService: CompanyService,
    private pdfService: PdfService,
    private orderService: OrderService,
    private paymentService: PaymentService,
    private settingsService: SettingsService,
    private editorContentService: EditorContentService
  ) {}

  ngOnInit() {
    this.orderService.order$.subscribe((order) => {
      this.order = order;
      this.totalDiscountAmount = 0;
      this.totalAmountExVat = 0;
      this.totalAmountIncVat = 0;
      this.productInformation = []; // Clear product information when order changes
      this.orderLines = [];

      this.orderService.getOrderLinesForOrder(this.order.order_id).subscribe((orderLines) => {
        this.orderLines = orderLines;
        this.orderLines.map((orderLine) => {
          this.totalDiscountAmount += orderLine.discount_amount;
          this.totalAmountExVat += orderLine.gross_total_price_ex_vat;
          this.totalAmountIncVat += orderLine.gross_total_price_inc_vat;
        });

        // Get unique product IDs to avoid duplicate requests
        const uniqueProductIds = [...new Set(
          this.orderLines
            .filter(ol => ol.product_id !== null)
            .map(ol => ol.product_id!)
        )];

        const requests = uniqueProductIds.map(productId => {
          return this.productService.getProductInformation(productId);
        });

        forkJoin(requests).subscribe((results) => {
          // Create a Set to track entry_ids we've already added
          const addedEntryIds = new Set<number>();

          for (const res of results) {
            // Filter out duplicates before adding to productInformation
            const uniqueEntries = res.filter(entry => {
              if (addedEntryIds.has(entry.entry_id)) {
                return false; // Skip this entry as it's already in our list
              }
              addedEntryIds.add(entry.entry_id);
              return true;
            });

            this.productInformation.push(...uniqueEntries);
          }
        });
      });


      this.showNotes = false; // Reset notes visibility
      for (const note of order.order_notes) {
        if (note.deleted_at === null && note.internal !== 1) {
          this.showNotes = true;
          break;
        }
      }

      this.settingsService.getCompanyDetails().subscribe(res => {
        this.companyDetails = res;

        // Apply company colors
        setTimeout(() => {
          this.applyCompanyColors();
        }, 0);
      });


      // this.repeatedWorkOrderPayments = this.order.work_order_schedule_templates.filter(wosch => wosch.payment).map((workOrder) => {
      //   return {
      //     ...workOrder.payment!,
      //     schedule_description: workOrder.schedule?.schedule_description || ''
      //   };
      // });

      this.companyService.getCompanyData().subscribe((res: CompanyResponse) => {
        this.company = res;
        this.pdfService.preloadImages(this.company, this.order);
      });

      this.cdr.detectChanges();

    });

    this.orderService.orderPaymentSchedules$.subscribe((paymentSchedules) => {
      this.fixedRepeatedPayments = paymentSchedules.filter((payment) => payment.payment_schedule);
      this.cdr.detectChanges();
    });

    this.orderService.workOrders$.pipe(takeUntil(this.destroy$)).subscribe((workOrders: WorkOrderResponse[]) => {
      this.workOrders = workOrders;
    });
  }


  // Add this helper function inside your component:
  isDark(color: string): boolean {
    // Remove '#' if present
    if (color.startsWith('#')) {
      color = color.slice(1);
    }
    // Convert hex to RGB values
    const r = parseInt(color.substring(0, 2), 16);
    const g = parseInt(color.substring(2, 4), 16);
    const b = parseInt(color.substring(4, 6), 16);
    // Calculate brightness (using the formula for perceived brightness)
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    // A threshold of 128 is commonly used (0-255 scale)
    return brightness < 128;
  }

  formatNote(note: OrderNoteResponse): string {
    let text = note.deleted_at ? "" : note.note_text;
    return note.internal ? `${text}` : text;
  }

  /**
   * Converts EditorJS content to HTML for display
   * @param content The EditorJS content as a string
   * @returns HTML string
   */
  parseProductDescription(content: string): string {
    return this.editorContentService.convertToHtml(content);
  }

  displayPhoneNumber(phoneNumber: string | null): string {
    if (!phoneNumber) {
      return '';
    }
    //add +47 to the phone number if it is not there, and the format should be +47 12 34 56 78 for all phone numbers
    const countryCode = '+47';
    if (phoneNumber.startsWith(countryCode)) {
      phoneNumber = phoneNumber.slice(countryCode.length);
    }
    const pattern = /^(\d{3})(\d{2})(\d{3})$/;
    const match = phoneNumber.trim().match(pattern);
    if (match) {
      return `${countryCode} ${match[1]} ${match[2]} ${match[3]}`;
    }

    return phoneNumber;
  }

  protected readonly currencyFormat = currencyFormat;

  /**
   * Apply company colors to all elements that need it
   */
  applyCompanyColors() {
    if (!this.contentElementRef || !this.contentElementRef.nativeElement) {
      console.warn('Content element reference not available');
      return;
    }

    // Get the company color, default to #448c74 if not available
    const companyColor = this.companyDetails?.company_color || '#448c74';
    const textColor = this.isDark(companyColor) ? '#ffffff' : '#000000';

    // Set CSS variables
    this.contentElementRef.nativeElement.style.setProperty('--company-color', companyColor);
    this.contentElementRef.nativeElement.style.setProperty('--header-footer-text-color', textColor);

    // Directly apply colors to elements that might not pick up the CSS variables
    const header = this.contentElementRef.nativeElement.querySelector('.header');
    if (header) {
      header.style.backgroundColor = companyColor;
      header.style.color = textColor;
    }

    const footer = this.contentElementRef.nativeElement.querySelector('.footer-section');
    if (footer) {
      footer.style.backgroundColor = companyColor;
      footer.style.color = textColor;
    }

    const tableHeaders = this.contentElementRef.nativeElement.querySelectorAll('th');
    tableHeaders.forEach((th: HTMLElement) => {
      th.style.backgroundColor = companyColor;
      th.style.color = textColor;
    });

    const sellerCard = this.contentElementRef.nativeElement.querySelector('.seller-card .card');
    if (sellerCard) {
      sellerCard.style.backgroundColor = companyColor;
      sellerCard.style.color = textColor;
    }
  }

  ngAfterViewInit() {
    // Apply company colors after view is initialized
    setTimeout(() => {
      this.applyCompanyColors();
    }, 100);
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
