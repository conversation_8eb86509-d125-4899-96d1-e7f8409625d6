<div id="quotePdfContent" #content *ngIf="order.payment_recipient">
  <!-- Header Section -->
  <div class="header">
    <div class="title">{{ "pdf-invoice.quotationTitle" | translate }}</div>
    <div class="logo">
      <img id="s3-logo-image" alt="{{ 'pdf-invoice.s3ImageAlt' | translate }}"> <!-- The image will be made visible once loaded -->
    </div>
  </div>

  <!-- Information section -->
  <div class="information-section">
    <!-- Billed To Section -->
    <div class="section billed-to">
    <div class="heading">{{ "pdf-invoice.quoteToHeading" | translate }}</div>
    <h4><strong>{{ order.payment_recipient.name }}</strong></h4>
    <div *ngIf="order.payment_recipient.is_private && workOrders.length > 0 && workOrders[0].addresses.length > 0" class="text">{{ workOrders[0].addresses[0].display}}</div>
<!--    <div *ngIf="!order.payment_recipient.is_private" class="text">{{ order.payment_recipient.address?.display}}</div>-->
    <div class="text">{{ displayPhoneNumber(order.payment_recipient.phone) }}</div>
    <div class="text mb-2">{{ order.payment_recipient.email }}</div>
    <div class="mt-2 text" *ngIf="!order.payment_recipient.is_private && workOrders.length > 0 && workOrders[0].addresses.length > 0"><strong> {{ "pdf-invoice.deliveryAddress" | translate }}</strong> {{ workOrders[0].addresses[0].display}}</div>
  </div>

  <!-- Quotation Details Section -->
  <div class="section details">
    <div class="heading">{{ "pdf-invoice.quotationDetailsHeading" | translate }}</div>
    <div><strong>{{ "pdf-invoice.quotationNumber" | translate }}:</strong> {{ order.order_number}}</div>
    <div><strong>{{ "pdf-invoice.quotationDate" | translate }}:</strong> {{ order.created_at | date: 'EEEE d. MMM y': '' : 'nb-NO' }}</div>
    <div><strong>{{ "pdf-invoice.executionDate" | translate }}:</strong> {{ order.execution_at | date: 'EEEE d. MMM y' }}</div>
    </div>
  </div>

  <!-- Sales person -->
  <div class="information-section pt-0">
    <div class="seller-card" *ngIf="order.seller.phone">
    <h6 class="text">{{ "pdf-invoice.seller-title" | translate }}</h6>
      <div class="card" style="width: 18rem; background-color: var(--company-color) !important;">
      <div class="row no-gutters ">
        <div class="col-md-4 align-items-center p-2 d-flex" *ngIf="order.seller.profile_image_url">
          <img id="profile_img"[src]="order.seller.profile_image_url" class="card-img" alt="Profile Image">
        </div>
        <div class="col-md-8 align-items-center d-flex" *ngIf="order.seller">
          <div class="card-body p-0 py-2" style="color:#333;">
            <p class="text mb-1" *ngIf="order.seller"><strong>{{ order.seller.first_name }} {{ order.seller.last_name }}</strong></p>
            <p class="text" *ngIf="order.seller.phone">{{ displayPhoneNumber(order.seller.phone) }}</p>
            <p class="text" *ngIf="order.seller">{{ order.seller.email }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  </div>

  <!-- Notes section -->
  <div class="row px-4 pb-3" *ngIf="showNotes">
    <h5 class="mb-2 p-0">{{"workorder.notesTitle" | translate}}</h5>
    <div id="written-message" class="row custom-message-line mx-0 px-0" *ngFor="let note of order.order_notes">
      <div class="col justify-content-start ps-0 pe-0" *ngIf="!note.deleted_at && !note.internal">
        <h6 style="color: #313A46;">{{ note.updated_by_name }}</h6>
        <div [ngClass]="note.updated_by == current_user_id ? 'written-message-background-self' : 'written-message-background-other'" class="mt-1">
          <p class="mb-0 mt-0 font-14" [innerHTML]="formatNote(note)"></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Single payment order lines -->
  <div *ngIf="orderLines.length > 0">
    <h5 *ngIf="fixedRepeatedPayments.length > 0 || repeatedWorkOrderPayments.length > 0" class="mb-2 px-4">{{"pdf-invoice.singlePayment" | translate}}</h5>
    <table class="items-table">
      <!-- Table Head -->
      <thead class="table-header">
        <tr>
          <th>{{ "pdf-invoice.productName" | translate }}</th>
          <th class="text-right">{{ "pdf-invoice.qty" | translate }}</th>
          <th class="text-right">{{ "pdf-invoice.unitPrice" | translate }}</th>
          <th class="text-right">{{ "pdf-invoice.amount" | translate }}</th>
        </tr>
      </thead>
      <!-- Table Body Order lines -->
      <tbody>
        <tr *ngFor="let orderLine of orderLines">
          <td style="max-width: 300px;">{{ orderLine.order_line_name }}
            <div *ngIf="orderLine.comment"> <em>{{orderLine.comment}}</em></div>
          </td>
          <td class="text-right">{{ orderLine.quantity }} {{ orderLine.unit_abbreviation }}</td>
          <td *ngIf="order.show_prices_inc_vat === 1" class="text-right">{{ currencyFormat(orderLine.unit_price_inc_vat) }}</td>
          <td *ngIf="order.show_prices_inc_vat === 1" class="text-right">{{ currencyFormat(orderLine.calculated_total_price_inc_vat) }}</td>
          <td *ngIf="order.show_prices_inc_vat === 0" class="text-right">{{ currencyFormat(orderLine.unit_price_ex_vat) }}</td>
          <td *ngIf="order.show_prices_inc_vat === 0" class="text-right">{{ currencyFormat(orderLine.calculated_total_price_ex_vat) }}</td>
        </tr>
      </tbody>
    </table>

    <div class="container-fluid pe-4">
      <div class="row justify-content-end">
        <div class="col-auto">
          <div *ngIf="totalDiscountAmount">{{ "pdf-invoice.totalExVAT" | translate }}</div>
          <div *ngIf="totalDiscountAmount">{{ "pdf-invoice.discount" | translate }}</div>
          <div><strong>{{ "pdf-invoice.totalEx" | translate }}</strong></div>
          <div>{{ "pdf-invoice.vat" | translate }}</div>
          <div><strong>{{ "pdf-invoice.total" | translate }}</strong></div>
        </div>
        <div class="col-auto">
          <div *ngIf="totalDiscountAmount" class="text-right">{{ currencyFormat(totalDiscountAmount + totalAmountIncVat)}}</div>
          <div class="text-right" *ngIf="totalDiscountAmount">-{{ currencyFormat(totalDiscountAmount) }}</div>
          <div><strong>{{ currencyFormat(totalAmountExVat) }}</strong></div>
          <div class="text-right">{{ currencyFormat(totalAmountIncVat - totalAmountExVat) }}</div>
          <div><strong>{{ currencyFormat(totalAmountIncVat) }}</strong></div>
        </div>
      </div>
    </div>

  </div>

  <!-- Repeated fixed payment order lines -->
  <div *ngIf="fixedRepeatedPayments.length > 0">
    <h5 class="mb-2 px-4">{{"pdf-invoice.fixedPayments" | translate}}</h5>
    <div *ngFor="let payment of fixedRepeatedPayments">
      <div class="mb-2 px-4 text">{{"pdf-invoice.willBeSent" | translate}} {{payment.payment_schedule?.schedule_description}}</div>
      <!-- Items Table -->
      <table class="items-table">
        <!-- Table Head -->
        <thead class="table-header">
          <tr>
            <th>{{ "pdf-invoice.productName" | translate }}</th>
            <th class="text-right">{{ "pdf-invoice.qty" | translate }}</th>
            <th class="text-right">{{ "pdf-invoice.unitPrice" | translate }}</th>
            <th class="text-right">{{ "pdf-invoice.amount" | translate }}</th>
          </tr>
        </thead>
        <!-- Table Body Order lines -->
        <tbody>
          <tr *ngFor="let orderLine of payment.order_lines">
            <td style="max-width: 300px;">{{ orderLine.order_line_name }}
              <div *ngIf="orderLine.comment"> <em>{{orderLine.comment}}</em></div>
            </td>
            <td class="text-right">{{ orderLine.quantity }} {{ orderLine.unit_abbreviation }}</td>
            <td *ngIf="order.show_prices_inc_vat === 1" class="text-right">{{ currencyFormat(orderLine.unit_price_inc_vat) }}</td>
            <td *ngIf="order.show_prices_inc_vat === 1" class="text-right">{{ currencyFormat(orderLine.calculated_total_price_inc_vat) }}</td>
            <td *ngIf="order.show_prices_inc_vat === 0" class="text-right">{{ currencyFormat(orderLine.unit_price_ex_vat) }}</td>
            <td *ngIf="order.show_prices_inc_vat === 0" class="text-right">{{ currencyFormat(orderLine.calculated_total_price_ex_vat) }}</td>
          </tr>
        </tbody>
      </table>

      <div class="container-fluid pe-4">
        <div class="row justify-content-end">
          <div class="col-auto">
            <div *ngIf="payment.total_discount_amount_inc_vat">{{ "pdf-invoice.totalExVAT" | translate }}</div>
            <div *ngIf="payment.total_discount_amount_inc_vat">{{ "pdf-invoice.discount" | translate }}</div>
            <div><strong>{{ "pdf-invoice.totalEx" | translate }}</strong></div>
            <div>{{ "pdf-invoice.vat" | translate }}</div>
            <div><strong>{{ "pdf-invoice.total" | translate }}</strong></div>
          </div>
          <div class="col-auto">
             <div *ngIf="payment.total_discount_amount_inc_vat" class="text-right">{{ currencyFormat(payment.total_discount_amount_inc_vat + payment.total_amount_inc_vat) }}</div>
          <div class="text-right" *ngIf="payment.total_discount_amount_inc_vat">-{{ currencyFormat(payment.total_discount_amount_inc_vat) }}</div>
          <div><strong>{{ currencyFormat(payment.total_amount_ex_vat) }}</strong></div>
          <div class="text-right">{{ currencyFormat(payment.total_amount_inc_vat - payment.total_amount_ex_vat) }}</div>
          <div><strong>{{ currencyFormat(payment.total_amount_inc_vat) }}</strong></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Repeated work order payment order lines -->
  <div *ngIf="repeatedWorkOrderPayments.length > 0">
    <h5 class="mb-2 px-4">{{"pdf-invoice.workOrderPayments" | translate}}</h5>
    <div *ngFor="let payment of repeatedWorkOrderPayments">
      <div class="mb-2 px-4 text">{{"pdf-invoice.willBeSent" | translate}} {{payment.schedule_description}}</div>
      <!-- Items Table -->
      <table class="items-table">
        <!-- Table Head -->
        <thead class="table-header">
          <tr>
            <th>{{ "pdf-invoice.productName" | translate }}</th>
            <th class="text-right">{{ "pdf-invoice.qty" | translate }}</th>
            <th class="text-right">{{ "pdf-invoice.unitPrice" | translate }}</th>
            <th class="text-right">{{ "pdf-invoice.amount" | translate }}</th>
          </tr>
        </thead>
        <!-- Table Body Order lines -->
        <tbody>
          <tr *ngFor="let orderLine of payment.order_lines">
            <td style="max-width: 300px;">{{ orderLine.order_line_name }}
              <div *ngIf="orderLine.comment"> <em>{{orderLine.comment}}</em></div>
            </td>
            <td class="text-right">{{ orderLine.quantity }} {{ orderLine.unit_abbreviation }}</td>
            <td *ngIf="order.show_prices_inc_vat === 1" class="text-right">{{ currencyFormat(orderLine.unit_price_inc_vat) }}</td>
            <td *ngIf="order.show_prices_inc_vat === 1" class="text-right">{{ currencyFormat(orderLine.calculated_total_price_inc_vat) }}</td>
            <td *ngIf="order.show_prices_inc_vat === 0" class="text-right">{{ currencyFormat(orderLine.unit_price_ex_vat) }}</td>
            <td *ngIf="order.show_prices_inc_vat === 0" class="text-right">{{ currencyFormat(orderLine.calculated_total_price_ex_vat) }}</td>
          </tr>
        </tbody>
      </table>

      <div class="container-fluid pe-4">
        <div class="row justify-content-end">
          <div class="col-auto">
            <div *ngIf="payment.total_discount_amount_inc_vat">{{ "pdf-invoice.totalExVAT" | translate }}</div>
            <div *ngIf="payment.total_discount_amount_inc_vat">{{ "pdf-invoice.discount" | translate }}</div>
            <div><strong>{{ "pdf-invoice.totalEx" | translate }}</strong></div>
            <div>{{ "pdf-invoice.vat" | translate }}</div>
            <div><strong>{{ "pdf-invoice.total" | translate }}</strong></div>
          </div>
          <div class="col-auto">
             <div *ngIf="payment.total_discount_amount_inc_vat" class="text-right">{{ currencyFormat(payment.total_discount_amount_inc_vat + payment.total_amount_inc_vat) }}</div>
          <div class="text-right" *ngIf="payment.total_discount_amount_inc_vat">-{{ currencyFormat(payment.total_discount_amount_inc_vat) }}</div>
          <div><strong>{{ currencyFormat(payment.total_amount_ex_vat) }}</strong></div>
          <div class="text-right">{{ currencyFormat(payment.total_amount_inc_vat - payment.total_amount_ex_vat) }}</div>
          <div><strong>{{ currencyFormat(payment.total_amount_inc_vat) }}</strong></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row footer-section" #footer>
    <!-- Terms and Conditions Section -->
    <div class="col section terms">
      <div *ngIf="productInformation.length > 0" class="heading">{{ "pdf-invoice.importantInformation" | translate }}</div>
      <div *ngFor="let productInfo of productInformation" class="product-info mb-2">
        <p><strong>{{ productInfo.title }}</strong></p>
        <p class="product-description" [innerHTML]="parseProductDescription(productInfo.description)"></p>
      </div>
    </div>

    <!-- Additional Notes Section -->
    <div class="col section notes">
      <div class="company-information">
        <h5 class="mb-0 text-right" *ngIf="company" >{{company.company_name}}</h5>
        <p class="m-0 text-right" *ngIf="company" >{{company.address.display}}</p>
        <p class="m-0 text-right" *ngIf="company" >{{ "pdf-invoice.phone" | translate }}: {{displayPhoneNumber(company.phone)}}</p>
        <p class="m-0 text-right" *ngIf="company" >{{ "pdf-invoice.email" | translate }}: {{company.email}}</p>
      </div>
    </div>
  </div>
  <div class="generated-by mb-2"> {{ "pdf-invoice.generated-by" | translate }}</div>
</div>
