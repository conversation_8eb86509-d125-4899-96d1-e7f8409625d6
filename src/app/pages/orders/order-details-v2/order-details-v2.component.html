<div class="mt-1 p-0" style="max-width: 1140px;" *ngIf="order">

  <div class="d-flex flex-column flex-lg-row">
    <div class="d-none d-md-block">
      <h4>
        <i class="fa-regular fa-arrow-left" (click)="goBack()" style="cursor: pointer"></i>
      </h4>
    </div>
    <div class="ms-2">
      <h4 class="mb-lg-0">
        <i class="fa-regular fa-arrow-left d-inline d-md-none" routerLink="/orders" style="cursor: pointer"></i>
        <span *ngIf="!editOrderTitleActive" class="fw-bolder color-header cursor-pointer" (click)="editOrderTitle()">
          #{{ order.order_number }}
          <i class="fa-regular fa-repeat ms-1" *ngIf="order.repeating"></i>
          {{ order.order_title ? ' - ' + order.order_title : (order.service_recipient?.name ? ' - ' : '') }} {{ order.service_recipient ? 'for ' : '' }} {{ order.service_recipient?.name ? order.service_recipient?.name : '' }}
        </span>
        <div *ngIf="editOrderTitleActive" class="d-flex fw-bolder color-header">
          <span class="me-1">#{{order.order_number}}</span>
          <div style="margin-top: -10px;">
            <app-input
              [editMode]="true"
              [control]="orderTitleControl"
              [emitChangeOnBlurOnly]="true"
              [takeFocus]="true"
              (valueChange)="updateTitle()"
              (onEnterPressed)="updateTitle()"
            ></app-input>
          </div>
        </div>
        <div *ngIf="!editOrderTitleActive" class="d-inline-block ms-2">
          <span *ngIf="order.payment_recipient && !order.repeating" class="inline-badge pe-2" [innerHTML]="orderBadgePayment(order)"></span>
        </div>
        <div class="text-muted fw-normal fs-5 mt-1">
          {{"orders.orderList.source" | translate}} {{order.order_source_id === 0 ? order.order_source_name : lowerCaseFirstCharacter(order.order_source_name)}} - {{displayDate(order.created_at, false)}}
        </div>
      </h4>
    </div>

    <!-- Order action buttons-->
    <div id="orderActionButtons" class="d-flex align-items-center gap-2 ms-lg-auto mt-1">
      <app-button
        *ngIf="!order.payment_recipient"
        [loading]="isloading"
        [translationKey]="'order.orderDetails.customer.setCustomer'"
        [buttonWidth]="122"
        (buttonClick)="openCustomerModal()"
      ></app-button>
      <app-button
        *ngIf="order.order_status_id === -1 && order.payment_recipient"
        [loading]="isloading"
        [disabled]="order.payment_recipient.is_private === 0 && !order.affiliate_contact"
        [ngbTooltip]="(order.payment_recipient.is_private === 0 && !order.affiliate_contact ? 'orders.orderDetails.sendQuoteNoAffiliateContact' : '') | translate"
        [translationKey]="'orders.orderDetails.sendQuoteButton'"
        [buttonWidth]="122"
        (buttonClick)="sendQuote()"
      ></app-button>


      <div *ngIf="order.order_status_id < 2 && order.payment_recipient">
        <app-button
          type="button"
          [buttonType]="order.order_status_id === 1 ? 'solid' : 'outline'"
          [loading]="isloading"
          data-bs-toggle="dropdown"
          [translationKey]="'orders.orderDetails.confirmManually'"
          [showDropdownChevron]="true"
          [buttonWidth]="122"
        ></app-button>

        <div class="dropdown-menu">
          <app-button
            [translationKey]="'orders.orderDetails.confirmManually.withoutConfirmation'"
            [buttonType]="'dropdown'"
            (buttonClick)="onAcceptOrder(false)"
          ></app-button>

          <app-button
            [translationKey]="'orders.orderDetails.confirmManually.withConfirmation'"
            [buttonType]="'dropdown'"
            (buttonClick)="onAcceptOrder(true)"
          ></app-button>
        </div>
      </div>
      <app-action-button-group></app-action-button-group>
    </div>
  </div>

  <!--  Order status bar  -->
  <div class="status-container mt-1">
    <div
      class="rectangle px-2 d-flex justify-content-center align-items-center"
      *ngFor="let status of orderStatuses; let i = index"
      [class.no-start]="i === 0"
      [class.no-end]="i === orderStatuses.length - 1 || status.order_status_id == 2 && order.repeating && order.order_status_id !== 7"
      [class.active]="(status.order_status_id === order.order_status_id) || (status.order_status_id < order.order_status_id && order.order_status_id != 8)"
      [class.cancelled]="status.order_status_id === 8"
      [class.compact]="orderStatuses.length < 5"
    >
      <span [ngClass]="{
      'rectangle-text':
      status.order_status_id != order.order_status_id &&
      Math.abs(i - getActiveOrderStatusIndex()) > 1,
      }">{{order.repeating && status.order_status_id === 5 && order.order_status_id == 5 ? ('orderDetails.paymentStatus5WithRepeatingPayment' | translate ) : status.order_status_name}}</span>
    </div>
  </div>


  <div class="row d-flex justify-content-center mt-2" >

    <!-- Left Column -->
    <div class="col-12 col-lg-8" *ngIf="order">

      <!-- Work Order Schedule -->
<!--      <div *ngIf="order.repeating || (!order.repeating && order.work_order_schedule_templates.length > 0)" class="mb-2" style="min-width: 346px;">-->
<!--        <app-work-order-schedule-card-container [workOrderScheduleTemplates]="order.work_order_schedule_templates"></app-work-order-schedule-card-container>-->
<!--      </div>-->

      <!-- Work Order Cards -->
<!--      <div class="mb-2" *ngIf="!order.repeating || (order.repeating && workOrders.length > 0)" style="min-width: 346px;">-->
<!--        <app-work-order-card-container [workOrders]="workOrders" [orderId]="this.order.order_id"></app-work-order-card-container>-->
<!--      </div>-->

      <!--   Work order container   -->
      <div class="mb-2">
        <app-work-order-container></app-work-order-container>
      </div>

      <!-- Order Lines -->
      <div class="mb-2">
        <app-order-lines [viewSettings]="orderDetailsOrderLinesViewSettings" [order]="order" [loading]="orderLinesLoading" [orderLinesSubject]="orderLinesSubject"></app-order-lines>
      </div>

      <!-- Payment Schedule Placeholder -->
<!--      <div *ngIf="order.repeating && paymentSchedules.length === 0 && noWorkOrderRepeatingPayment && order.order_status_id < 2" class="mb-2" style="min-width: 346px;">-->
<!--        <app-payment-details [viewSettings]="repeatingPaymentPlaceholderViewSettings" [orderLinesSubject]="orderLinesSubject"></app-payment-details>-->
<!--      </div>-->

      <!-- Payment Schedules -->
<!--      <div *ngFor="let payment of paymentSchedules" class="mb-2" style="min-width: 346px;">-->
<!--        <order-payment-schedule [payment]="payment" [viewSettings]="{fixedRepeatingPaymentView: true, paymentView: true}"></order-payment-schedule>-->
<!--      </div>-->

      <!-- Payments -->
      <div class="mb-2" *ngIf="order.repeating || paymentSchedules.length > 0 || orderHasPayments" style="min-width: 346px;">
        <app-order-payments></app-order-payments>
      </div>

      <!-- Notes -->
      <div class="mb-2" style="min-width: 346px;">
        <app-timeline-notes></app-timeline-notes>
      </div>

      <!-- Timeline -->
      <div class="mb-2" style="min-width: 346px;">
        <app-order-timeline></app-order-timeline>
      </div>

    </div>

    <!-- Right Column -->
    <div class="col-12 col-lg-4 p-lg-0" *ngIf="order">

      <!-- Customer Information, Payment Method, and Partner -->
      <div class="mb-2" style="min-width: 346px;">
        <app-order-customer></app-order-customer>
      </div>

      <!-- Address and Google Maps -->
      <div *ngIf="workOrders.length > 0" class="mb-2" style="min-width: 346px;">
        <app-order-address></app-order-address>
      </div>

      <!-- Refund Payments -->
      <div *ngIf="orderHasRefundPayments" class="mb-2" style="min-width: 346px;">
        <app-order-refund-payments></app-order-refund-payments>
      </div>

      <!-- Project and department -->
      <div *ngIf="projectsEnabled || departmentsEnabled" class="mb-2" style="min-width: 346px;">
        <app-order-project-and-department></app-order-project-and-department>
      </div>

      <!-- Customer Questionnaire and Rating -->
      <div class="mb-2" style="min-width: 346px;">
        <app-order-customer-questions></app-order-customer-questions>
      </div>

      <!-- Crew Checklist -->
      <div *ngIf="order.repeating ? workOrderSchedules.length > 0 : workOrders.length > 0" class="mb-2" style="min-width: 346px;">
        <app-order-checklist></app-order-checklist>
      </div>

      <!-- Time Tracking -->
      <div class="mb-2" style="min-width: 346px;">
        <app-order-timetracking></app-order-timetracking>
      </div>

      <!-- Attachments -->
      <div class="mb-2" style="min-width: 346px;">
        <app-order-attachments></app-order-attachments>
      </div>

      <!-- Crew Reports -->
      <div *ngIf="workOrders.length > 0" class="mb-2" style="min-width: 346px;">
        <app-order-crew-reports></app-order-crew-reports>
      </div>

    </div>

  </div>
</div>
