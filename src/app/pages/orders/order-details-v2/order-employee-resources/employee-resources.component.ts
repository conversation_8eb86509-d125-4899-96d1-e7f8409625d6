import {ChangeDetector<PERSON>ef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild} from '@angular/core';
import {OrderResponse, WorkOrderResponse, WorkOrderUserRelationResponse} from "../../../../@shared/models/order.interfaces";
import {InternalUserResponse} from "../../../../@shared/models/user.interfaces";
import {ResourceResponse} from "../../../../@shared/models/resources.interfaces";
import {
  _CRM_EMP_7,
  _CRM_ORD_113, _CRM_ORD_114, _CRM_ORD_115, _CRM_ORD_116, _CRM_ORD_117,
  _CRM_RSC_2,
  _USM_ENT_0
} from "../../../../@shared/models/input.interfaces";
import {EmployeeService} from "../../../../@shared/services/employee.service";
import {ResourceService} from "../../../../@shared/services/resource.service";
import {OrderService} from "../../../../@shared/services/order.service";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {
  EmployeeAvailabilityModalComponent
} from "../../../../@shared/components/employee-availability-modal/employee-availability-modal.component";
import {StorageService} from "../../../../@core/services/storage.service";
import {SelectoriniComponent} from "../../../../@shared/components/selectorini/selectorini.component";


import {BehaviorSubject, combineLatest} from "rxjs";
import {ProfiledItemListComponent} from "../../../../@shared/components/profiled-item-list/profiled-item-list.component";
import {VerifyPopupModal} from "../../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {EmployeeAvailabilityItemResponse} from "../../../../@shared/models/employee.interfaces";
import {StandardImports} from "../../../../@shared/global_import";
import {TrackingService} from "../../../../@shared/services/tracking.service";

@Component({
  selector: 'app-employee-resources',
  templateUrl: './employee-resources.component.html',
  styleUrls: ['./employee-resources.component.css'],
  standalone: true,
  imports: [StandardImports, ProfiledItemListComponent, SelectoriniComponent]
})
export class EmployeeResourcesComponent implements OnInit, OnChanges {
  @ViewChild('userSelectorini') userSelectorini: SelectoriniComponent;
  @ViewChild('resourceSelectorini') resourceSelectorini: SelectoriniComponent;

  constructor(private resourceService: ResourceService,
              private modalService: NgbModal,
              private employeeService: EmployeeService,
              private orderService: OrderService,
              private storageService: StorageService,
              private cdr: ChangeDetectorRef,
              private trackingService: TrackingService
  ) {}

  @Input() workOrder?: WorkOrderResponse;
  @Input() cardView: boolean = true;
  @Input() payloadSubject?: BehaviorSubject<_CRM_ORD_117>
  @Input() disabled: boolean = false;
  showNoneSelected: boolean = true;
  order?: OrderResponse
  users: InternalUserResponse[] = [];
  resources: ResourceResponse[] = [];
  preselectedUsers: WorkOrderUserRelationResponse[] = [];
  preselectedResources: ResourceResponse[] = [];
  resourcesEnabled: boolean = false;
  showEmployeeInitials: boolean = false;
  showAssignmentStatus: boolean = false;

  userAssignmentloading: boolean = false;
  resourceAssignmentloading: boolean = false;

  @Output() workOrderUpdated: EventEmitter<WorkOrderResponse> = new EventEmitter<WorkOrderResponse>();

  ngOnInit() {
    if (this.workOrder) {
      this.showAssignmentStatus = this.workOrder.require_crew_work_order_confirmation;
    }

    this.storageService.resourcesEnabled$.subscribe((data) => {
      this.resourcesEnabled = data;
    });

    this.storageService.showEmployeeInitials$.subscribe((data) => {
      this.showEmployeeInitials = data;
    });


    if (this.workOrder) {
      this.showAssignmentStatus = this.workOrder.require_crew_work_order_confirmation;
      this.setPreselectedUsers()
      this.setPreselectedResources()
    }
    this.getEmployees();
    this.getResources();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['showNoneSelected']) {
      this.cdr.detectChanges();
    }
    if (changes['workOrder']) {
      this.showAssignmentStatus = this.workOrder?.require_crew_work_order_confirmation || false;
      this.setPreselectedUsers()
      this.setPreselectedResources()
      this.getEmployees();
    }
  }


  getEmployees() {
    const params: _USM_ENT_0 = {};
    let checkDate = this.workOrder?.execution_at ? this.workOrder?.execution_at : new Date();
    let year = checkDate.getFullYear();
    combineLatest([this.employeeService.getEmployeesInCompany(params), this.trackingService.getCompanyAbsences(year)]).subscribe(([employees, absences]) => {
      let absentUserMap: {[key: string]: string | null} = {};
       for (const absence of absences) {
        if (absence.active_from <= checkDate && absence.active_to >= checkDate) {
          absentUserMap[absence.user.user_id] = absence.description;
        }
       }
      this.users = employees.sort((a, b) => {
        if (a.full_name && b.full_name) {
          return a.full_name.localeCompare(b.full_name);
        }
        return 0;
      }).map((user) => {
        return {
          ...user,
          absence_description: absentUserMap[user.user_id] || null,
        }
      });
    })
  }

  getResources() {
    const params: _CRM_RSC_2 = {}
    this.resourceService.getResources(params).subscribe(res => {
      this.resources = res.data.sort((a, b) => {
        if (a.resource_name && b.resource_name) {
          return a.resource_name.localeCompare(b.resource_name);
        }
        return 0;
      });
    })
  }

  setPreselectedUsers() {
    this.preselectedUsers = [];
    if (!this.workOrder) return;
    if (this.workOrder.users.length > 0) {
      this.preselectedUsers = this.workOrder.users;
    }
  }

  setPreselectedResources() {
    this.preselectedResources = [];
    if (!this.workOrder) return;
    if (this.workOrder.resources.length > 0) {
      for (let resource of this.workOrder.resources){
        this.preselectedResources.push(resource);
      }
    }
  }


  resetUsers() {
    if (this.workOrder && this.workOrder.users.length > 0) {
      this.preselectedUsers = this.workOrder.users;
      }
    else {
      this.preselectedUsers = []
    }
  }

  async addUser(userId: string) {
    if (this.workOrder) {
      let params: _CRM_ORD_113 = {
        work_order_id: this.workOrder.work_order_id,
        user_id: userId,
      }

      if (this.workOrder?.schedule?.num_unstarted_work_orders! > 1) {
        let modalRef = this.modalService.open(VerifyPopupModal, {backdrop: 'static'});
        modalRef.componentInstance.showBody = true;
        modalRef.componentInstance.titleTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.title';
        modalRef.componentInstance.bodyBoldTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyBoldTranslationKey';
        modalRef.componentInstance.bodyMutedTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyMutedTranslationKey';
        modalRef.componentInstance.bodyRegularTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.assignUpdate';
        try {
          params.update_children_assignments = await modalRef.result;
        } catch (e) {}
      }

      this.orderService.assignUserToWorkOrder(params).subscribe(res => {
        this.workOrder = res;
        this.setPreselectedUsers();
        if (this.storageService.ownedByCompany(this.workOrder.company_id)) {
          this.orderService.refreshSingleWorkOrderInWorkOrders(this.workOrder, 'userAssignment');
        }
        this.userAssignmentloading = false;
        this.workOrderUpdated.emit(this.workOrder);
        if (this.cardView && this.storageService.ownedByCompany(this.workOrder.company_id)) {
          this.orderService.fetchAndRefreshOrder(this.workOrder.order_id, 'addUser')
        }
      }, (error) => {
        console.log(error);
        this.userAssignmentloading = false;
      })
    } else {
      if (this.payloadSubject) {
        let currentPayload = this.payloadSubject.value;
        if (!currentPayload.user_ids) {
          currentPayload.user_ids = [];
        }
        currentPayload.user_ids.push(userId);
        this.payloadSubject.next(currentPayload);
      }
    }
  }

  async removeUser(userId: string) {
    this.userAssignmentloading = true;
    if (this.workOrder) {
      let params: _CRM_ORD_114 = {
        work_order_id: this.workOrder.work_order_id,
        user_id: userId
      }

      if (this.workOrder?.schedule?.num_unstarted_work_orders! > 1) {
        let modalRef = this.modalService.open(VerifyPopupModal, {backdrop: 'static'});
        modalRef.componentInstance.showBody = true;
        modalRef.componentInstance.titleTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.title';
        modalRef.componentInstance.bodyBoldTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyBoldTranslationKey';
        modalRef.componentInstance.bodyMutedTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyMutedTranslationKey';
        modalRef.componentInstance.bodyRegularTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.unassignUpdate';
        try {
          params.update_children_assignments = await modalRef.result;
        } catch (e) {}
      }

      this.orderService.removeUserFromWorkOrder(params).subscribe(res => {
        this.workOrder = res;
        this.setPreselectedUsers();
        this.workOrderUpdated.emit(this.workOrder);
        this.userAssignmentloading = false;
        if (this.storageService.ownedByCompany(this.workOrder.company_id)) {
          this.orderService.refreshSingleWorkOrderInWorkOrders(this.workOrder, 'removeUser');
        }
        if (this.cardView && this.storageService.ownedByCompany(this.workOrder.company_id)) {
          this.orderService.fetchAndRefreshOrder(this.workOrder.order_id, 'removeUser')
        }
      }, (error) => {
        this.userAssignmentloading = false;
      })
    } else {
      if (this.payloadSubject) {
        let currentPayload = this.payloadSubject.value;
        if (!currentPayload.user_ids) {
          currentPayload.user_ids = [];
        }
        currentPayload.user_ids = currentPayload.user_ids.filter((id: string) => id !== userId);
        this.payloadSubject.next(currentPayload);
      }
    }
  }

  onSelectedUser(user: {[key: string]: any }){
    if (!this.workOrder) {
      this.addUser(user['user_id']);
      return;
    }

    // If assignment is already in progress, return
    if (this.userAssignmentloading) {
      return;
    }
    this.userAssignmentloading = true;
    let userId = user['user_id'];


    if (this.workOrder.execution_at && this.workOrder.execution_to) {
      // Set up params for availability check
      let caParams: _CRM_EMP_7 = {
        user_id: userId,
        date_from: this.workOrder.execution_at,
        date_to: this.workOrder.execution_to,
      }

      // Check availability
      this.employeeService.getEmployeeAssignmentAvailabilityForTimeSlot(caParams).subscribe((res: EmployeeAvailabilityItemResponse[]) => {
        // If conflict detected, open verification modal
        if (res.length > 0) {
          let modalRef = this.modalService.open(EmployeeAvailabilityModalComponent, {centered: true, size: 'lg', keyboard: false, backdrop: 'static'})
          modalRef.componentInstance.items = res;
          modalRef.componentInstance.user = user;
          modalRef.result.then((res: boolean) => {
            // If user confirms, assign user to order line
            if (res) {
              this.addUser(userId)
            }
            // If user cancels, reset user assignments
            else {
              this.resetUsers()
              this.userAssignmentloading = false;
            }
          }, error => {
            this.resetUsers();
            this.userAssignmentloading = false;
          })
        }

        // If no conflict detected, assign user to order line
        else {
          this.addUser(userId)
        }

      });
    } else {
      this.addUser(userId)
    }
  }

  onUserRemoved(user: {[key: string]: any }){
    if (this.disabled || this.userAssignmentloading) return;
    this.removeUser(user['user_id'])
  }


  onSelectedResource(resource: ResourceResponse | any){
    if (!this.workOrder) {
      if (this.payloadSubject) {
        let currentPayload = this.payloadSubject.value;
        if (!currentPayload.resource_ids) {
          currentPayload.resource_ids = [];
        }
        currentPayload.resource_ids.push(resource.resource_id);
        this.payloadSubject.next(currentPayload);
      }
      return;
    }
    if (this.resourceAssignmentloading) {
      return;
    }
    this.resourceAssignmentloading = true;

    const params: _CRM_ORD_115 = {
      work_order_id: this.workOrder.work_order_id,
      resource_id: resource.resource_id
    }

    this.orderService.assignResourceToWorkOrder(params).subscribe(res => {
      this.workOrder = res;
      if (this.storageService.ownedByCompany(this.workOrder.company_id)) {
        this.orderService.refreshSingleWorkOrderInWorkOrders(this.workOrder, 'resourceAssignment');
      }
      this.resourceAssignmentloading = false;
      this.workOrderUpdated.emit(this.workOrder);
      if (this.cardView && this.storageService.ownedByCompany(this.workOrder.company_id)) {
        this.orderService.fetchAndRefreshOrder(this.workOrder.order_id, 'resourceAssignment');
      }
    }, (error) => {
      this.resourceAssignmentloading = false;
    })
  }

  onResourceRemoved(resource: {[key: string]: any }){
    if (this.disabled || this.resourceAssignmentloading) return;
    this.removeResource(resource['resource_id'])
  }

  async removeResource(resource_id: number) {
    this.resourceAssignmentloading = true;
    if (this.workOrder) {
      let params: _CRM_ORD_116 = {
        work_order_id: this.workOrder.work_order_id,
        resource_id: resource_id
      }

      if (this.workOrder?.schedule?.num_unstarted_work_orders! > 1) {
        let modalRef = this.modalService.open(VerifyPopupModal, {backdrop: 'static'});
        modalRef.componentInstance.showBody = true;
        modalRef.componentInstance.titleTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.title';
        modalRef.componentInstance.bodyBoldTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyBoldTranslationKey';
        modalRef.componentInstance.bodyMutedTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyMutedTranslationKey';
        modalRef.componentInstance.bodyRegularTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.unassignUpdate';
        try {
          params.update_children_assignments = await modalRef.result;
        } catch (e) {}
      }

      this.orderService.removeResourceFromWorkOrder(params).subscribe(res => {
        this.workOrder = res;
        this.setPreselectedResources();
        this.workOrderUpdated.emit(this.workOrder);
        this.resourceAssignmentloading = false;
        if (this.storageService.ownedByCompany(this.workOrder.company_id)) {
          this.orderService.refreshSingleWorkOrderInWorkOrders(this.workOrder, 'resourceAssignment');
        }
        if (this.cardView && this.storageService.ownedByCompany(this.workOrder.company_id)) {
          this.orderService.fetchAndRefreshOrder(this.workOrder.order_id, 'removeResource')
        }
      }, (error) => {
        this.resourceAssignmentloading = false;
      })
    } else {
      if (this.payloadSubject) {
        let currentPayload = this.payloadSubject.value;
        if (!currentPayload.resource_ids) {
          currentPayload.resource_ids = [];
        }
        currentPayload.resource_ids = currentPayload.resource_ids.filter((id: number) => id !== resource_id);
        this.payloadSubject.next(currentPayload);
      }
    }
  }
}
