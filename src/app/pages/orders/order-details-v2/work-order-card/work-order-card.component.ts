import {AfterViewInit, ChangeDetectorRef, Component, ElementRef, Input, OnChanges, OnInit, Renderer2, SimpleChanges, ViewChild} from '@angular/core';
import {DetailsViewSettings, OrderSubContractorResponse, WorkOrderResponse} from "../../../../@shared/models/order.interfaces";
import {displayDate, formatFullDayAndDate, formatTimeHM, paymentStatusBadge, UtilsService, workOrderBadgeStatus} from "../../../../@core/utils/utils.service";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {OrderService} from "../../../../@shared/services/order.service";
import {WorkOrderDetailsComponent} from "../../../work-orders/components/work-order-details/work-order-details.component";
import {TranslateService} from "@ngx-translate/core";

import {EmployeeResourcesComponent} from "../order-employee-resources/employee-resources.component";
import {FormControl} from "@angular/forms";
import {_CRM_ORD_114, _CRM_ORD_116, _CRM_ORD_117, _CRM_ORD_118, _CRM_ORD_119} from "../../../../@shared/models/input.interfaces";
import {ToastService} from "../../../../@core/services/toast.service";
import {InternalUserResponse} from "../../../../@shared/models/user.interfaces";
import {ResourceResponse} from "../../../../@shared/models/resources.interfaces";
import {BehaviorSubject} from "rxjs";
import {DatepickerinoComponent} from "../../../../@shared/components/datepickerino/datepickerino.component";
import {WorkOrderDuplicateModal} from "../../../work-orders/components/work-order-details/_modals/duplicate-modal/work-order-duplicate-modal";
import {VerifyPopupModal} from "../../../../@shared/components/verify-popup-modal/verify-popup-modal";

import {ProfiledItemListComponent} from "../../../../@shared/components/profiled-item-list/profiled-item-list.component";
import {StandardImports} from "../../../../@shared/global_import";


@Component({
  selector: 'app-work-order-card',
  templateUrl: './work-order-card.component.html',
  styleUrls: ['./work-order-card.component.css'],
  standalone: true,
  imports: [StandardImports, DatepickerinoComponent, ProfiledItemListComponent, EmployeeResourcesComponent]
})
export class WorkOrderCardComponent implements OnInit, OnChanges, AfterViewInit {

  @Input() workOrder: WorkOrderResponse;
  @Input() viewSettings: DetailsViewSettings = {};
  @Input() payloadSubject?: BehaviorSubject<_CRM_ORD_117>
  @Input() multipleWorkOrderView = false;
  // order?: OrderResponse;
  executionDate: Date | null;
  fullDate: string;
  fullDateTo: string;
  formattedDurationHours: string;
  workOrderStatusBadgeClass: string;
  users: any[] = [];
  titleControl = new FormControl();
  descriptionControl = new FormControl();
  titleEditActive = false;
  descriptionEditActive = false;
  multiDayWorkOrder = false;

  loading = false;

  executionTimeStartEdit = false;
  executionTimeEndEdit = false;
  executionTimeStartControl = new FormControl();
  executionTimeEndControl = new FormControl();

  subcontractorEntry: OrderSubContractorResponse | null = null;

  descriptionOverflow = false;
  expandDescription = false;

  @ViewChild('descriptionContainer') descriptionContainer: ElementRef<HTMLElement>;

  constructor(public utilsService: UtilsService, private modalService: NgbModal, private orderService: OrderService, private renderer: Renderer2, private cdr: ChangeDetectorRef, private toastService: ToastService, private translate: TranslateService) {}

  ngOnInit(): void {
    this.orderService.order$.subscribe((order) => {
      this.initWorkOrder();
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['workOrder']) {
      this.initWorkOrder();
    }
  }

  ngAfterViewInit() {
    this.checkDescriptionOverflow();
  }

  initWorkOrder() {
    if (!this.workOrder) {
      this.executionDate = new Date();
      this.executionDate.setHours(8, 0, 0, 0);
      let executionTo = new Date();
      executionTo.setHours(9, 0, 0, 0);
      this.executionTimeStartControl.setValue(formatTimeHM(this.executionDate));
      this.executionTimeEndControl.setValue(formatTimeHM(executionTo));
      this.fullDate = formatFullDayAndDate(this.executionDate, false);
      const duration = executionTo.getTime() - this.executionDate.getTime();
      const durationHours = duration / 1000 / 60 / 60;
      this.formattedDurationHours = this.utilsService.formatDurationFromHours(durationHours)
      this.workOrderStatusBadgeClass = 'hidden';
    } else {
      this.multiDayWorkOrder = this.workOrder.execution_at?.getDate() !== this.workOrder.execution_to?.getDate();
      this.titleControl.setValue(this.workOrder.work_order_title);
      this.descriptionControl.setValue(this.workOrder.work_order_description);
      this.executionDate = this.workOrder.execution_at;
      this.executionTimeStartControl.setValue(formatTimeHM(this.workOrder.execution_at));
      this.executionTimeEndControl.setValue(formatTimeHM(this.workOrder.execution_to));
      this.fullDate = formatFullDayAndDate(this.workOrder.execution_at, false);
      this.fullDateTo = formatFullDayAndDate(this.workOrder.execution_to, false).toLowerCase();
      const duration = this.workOrder.execution_to?.getTime()! - this.workOrder.execution_at?.getTime()!;
      const durationHours = duration / 1000 / 60 / 60;
      this.formattedDurationHours = this.utilsService.formatDurationFromHours(durationHours)
      switch (this.workOrder.work_order_status_id) {
        case 0:
          this.workOrderStatusBadgeClass = 'bg-secondary';
          break;
        case 1:
          this.workOrderStatusBadgeClass = 'bg-warning';
          break;
        case 2:
          this.workOrderStatusBadgeClass = 'bg-success';
          break;
      }
    }

    this.checkDescriptionOverflow();

    if (this.executionDate) {
      let [executionAt, executionTo] = this.calculateExecutionTimes();
      this.updatePayloadSubject(executionAt, executionTo);
    }

    if (this.workOrder.subcontractors.length > 0) {
      let contract: OrderSubContractorResponse = this.workOrder.subcontractors[0];
      this.subcontractorEntry = contract;
      if (contract.accepted_at) {
        this.subcontractorEntry.popUpDescription = `${this.translate.instant('workOrderDetails.subcontractor.accepted')} ${contract.accepted_by_name} ${displayDate(contract.accepted_at, false)}`;
      } else if (contract.declined_at) {
        this.subcontractorEntry.popUpDescription = `${this.translate.instant('workOrderDetails.subcontractor.declined')} ${contract.declined_by_name} ${displayDate(contract.declined_at, false)}. ${this.translate.instant('workOrderDetails.subcontractor.declined.reason')}: ${contract.declined_reason ? contract.declined_reason : this.translate.instant('workOrderDetails.subcontractor.declined.noReason')}`;
      } else {
        this.subcontractorEntry.popUpDescription = `${this.translate.instant('workOrderDetails.subcontractor.pending')}`;
      }
    }

  }

  editWorkOrder() {
    const modalRef= this.modalService.open(WorkOrderDetailsComponent, {size: 'xl'});
    modalRef.componentInstance.workOrderId = this.workOrder.work_order_id;
    modalRef.componentInstance.viewSettings = {
      modalView: true,
      collapsedOrderLines: true,
      workOrderView: true,
    }
  }

  updatePayloadSubject(executionAt?: Date, executionTo?: Date) {
    if (this.payloadSubject) {
      let payload = this.payloadSubject.value;
      payload.execution_at = executionAt || payload.execution_at;
      payload.execution_to = executionTo || payload.execution_to;
      payload.work_order_title = this.titleControl.value;
      payload.work_order_description = this.descriptionControl.value;
      this.payloadSubject.next(payload);
    }
  }

  calculateExecutionTimes(): Date[] {
    let executionAt = new Date(this.executionDate!);
    if (this.executionTimeStartControl.value) {
      executionAt.setHours(parseInt(this.executionTimeStartControl.value.split(':')[0]), parseInt(this.executionTimeStartControl.value.split(':')[1]));
    }

    let executionTo = new Date(this.executionDate!);
    if (this.executionTimeEndControl.value) {
      executionTo.setHours(parseInt(this.executionTimeEndControl.value.split(':')[0]), parseInt(this.executionTimeEndControl.value.split(':')[1]));
    }

    if (executionAt.getTime() >= executionTo.getTime()) {
      executionTo = new Date(executionAt);
      this.executionTimeEndControl.setValue(formatTimeHM(executionTo));
    }

    return [executionAt, executionTo];
  }

  async dateChanged(executionDate: Date, executionToChanged: boolean = false) {
    let executionAt: Date;

    let cancel = false;
    if (executionDate.getTime() < new Date().getTime() && !(executionDate.getMonth() == new Date().getMonth() && executionDate.getDate() == new Date().getDate())) {
      let modalRef = this.modalService.open(VerifyPopupModal, {backdrop: 'static'});
      modalRef.componentInstance.showBody = true;
      modalRef.componentInstance.titleTranslationKey = 'workOrderDetails.pastDateModal.title';
      modalRef.componentInstance.bodyBoldTranslationKey = 'workOrderDetails.pastDateModal.bodyBold';
      modalRef.componentInstance.bodyRegularTranslationKey = 'workOrderDetails.pastDateModal.bodyRegular';
      try {
        let result: boolean = await modalRef.result;
        if (!result) {
          cancel = true;
        }
      } catch (e) {}
    }

    if (cancel) return;

    let executionTo: Date;
    // Single day order
    if (!this.multiDayWorkOrder) {
      this.executionDate = executionDate
      this.fullDate = formatFullDayAndDate(this.executionDate, false);
      if (!this.executionTimeStartControl.value) {
        this.executionTimeStartControl.setValue('08:00');
        this.executionTimeEndControl.setValue('09:00');
      }

      [executionAt, executionTo] = this.calculateExecutionTimes();
    }
    // Multiday order
    else {
      executionAt = this.workOrder.execution_at;
      executionTo = this.workOrder.execution_to;
      if (executionToChanged) {
        executionTo = executionDate;
        executionTo.setHours(this.workOrder.execution_to.getHours())
        executionTo.setMinutes(this.workOrder.execution_to.getMinutes())
      } else {
        executionAt = executionDate;
        executionAt.setHours(this.workOrder.execution_at.getHours())
        executionAt.setMinutes(this.workOrder.execution_at.getMinutes())
      }
    }
    if (this.workOrder) {
      let payload: _CRM_ORD_118 = {
        work_order_id: this.workOrder.work_order_id,
        execution_at: executionAt,
        execution_to: executionTo,
      }
      this.orderService.patchWorkOrder(payload).subscribe((workOrder) => {
        this.workOrder = workOrder;
        this.toastService.successToast('updated')
        this.initWorkOrder();
        this.orderService.refreshSingleWorkOrderInWorkOrders(workOrder, 'updateExecutionDate');
      });
    } else {
      this.updatePayloadSubject(executionAt, executionTo);
    }
  }

  updateStartTime() {
    if (!this.executionTimeStartEdit) return;
    this.executionTimeStartEdit = false;
    let [executionAt, executionTo] = this.calculateExecutionTimes();

    if (this.workOrder) {
      let payload: _CRM_ORD_118 = {
        work_order_id: this.workOrder?.work_order_id!,
        execution_at: executionAt,
        execution_to: executionTo,
      }
      this.orderService.patchWorkOrder(payload).subscribe((workOrder) => {
        this.workOrder = workOrder;
        this.toastService.successToast('updated')
        this.initWorkOrder();
        this.orderService.refreshSingleWorkOrderInWorkOrders(workOrder, 'updateStartTime');
      });
    } else {
      this.updatePayloadSubject(executionAt, executionTo);
    }
  }

  updateEndTime() {
    if (!this.executionTimeEndEdit) return;
    this.executionTimeEndEdit = false;
    let [executionAt, executionTo] = this.calculateExecutionTimes();

    if (this.workOrder) {
      let payload: _CRM_ORD_118 = {
        work_order_id: this.workOrder?.work_order_id!,
        execution_at: executionAt,
        execution_to: executionTo,
      }
      this.orderService.patchWorkOrder(payload).subscribe((workOrder) => {
        this.workOrder = workOrder;
        this.initWorkOrder();
        this.orderService.refreshSingleWorkOrderInWorkOrders(workOrder, 'updateEndTime');
      });
    } else {
      this.updatePayloadSubject(executionAt, executionTo);
    }
  }

  updateTitle() {
    if (!this.titleEditActive) return;
    this.titleEditActive = false;
    if (this.workOrder) {
      let payload: _CRM_ORD_118 = {
        work_order_id: this.workOrder.work_order_id,
        work_order_title: this.titleControl.value,
      }
      this.orderService.patchWorkOrder(payload).subscribe((workOrder) => {
        this.workOrder = workOrder;
        this.toastService.successToast('updated')
        this.initWorkOrder();
        this.orderService.refreshSingleWorkOrderInWorkOrders(workOrder, 'updateTitle');
      });
    } else {
      this.updatePayloadSubject();
    }
  }

  updateDescription() {
    if (!this.descriptionEditActive) return;
    this.descriptionEditActive = false;
    if (this.workOrder) {
      let payload: _CRM_ORD_118 = {
        work_order_id: this.workOrder.work_order_id,
        work_order_description: this.descriptionControl.value,
      }
      this.orderService.patchWorkOrder(payload).subscribe((workOrder) => {
        this.workOrder = workOrder;
        this.toastService.successToast('updated')
        this.initWorkOrder();
        this.orderService.refreshSingleWorkOrderInWorkOrders(workOrder, 'updateDescription');
      });
    } else {
      this.updatePayloadSubject();
    }
  }

  deleteWorkOrder() {
    if (!this.workOrder) return;
    let params: _CRM_ORD_119 = {
      work_order_id: this.workOrder.work_order_id
    }

    if (this.workOrder.payment && ![2, 3].includes(this.workOrder.payment.payment_status_id)) {
      let modalRef = this.modalService.open(VerifyPopupModal, { centered: true });
      modalRef.componentInstance.showBody = true;
      modalRef.componentInstance.titleTranslationKey = 'workOrder.deletePaymentModal.titleTranslationKey';
      modalRef.componentInstance.bodyBoldTranslationKey = 'workOrder.deletePaymentModal.bodyRegularTranslationKey';
      modalRef.componentInstance.bodyRegularTranslationKey = 'workOrder.deletePaymentModal.bodyRegularSecondaryTranslationKey';
      modalRef.result.then((result) => {
        if (result) {
          params.delete_payment = true;
          this.callDeleteWorkOrder(params);
        } else {
          params.delete_payment = false;
          this.callDeleteWorkOrder(params);
        }
      });
    } else {
      let modalRef = this.modalService.open(VerifyPopupModal, { centered: true });
      modalRef.result.then((result) => {
        if (result) {
          this.callDeleteWorkOrder(params);
        }
      });
    }
  }

  callDeleteWorkOrder(params: _CRM_ORD_119) {
    this.loading = true;
    this.orderService.deleteWorkOrder(params).subscribe((res) => {
      this.orderService.fetchAndRefreshOrder(this.workOrder?.order_id!, 'deleteWorkOrder');
    }, error => {
      this.loading = false;
    });
  }

  openDuplicateModal() {
    let modalRef = this.modalService.open(WorkOrderDuplicateModal, { size: 'lg', centered: true });
    modalRef.componentInstance.workOrder = this.workOrder;
  }

  workOrderUpdated(event: WorkOrderResponse) {
    this.workOrder = event;
    this.initWorkOrder();
  }

  removeResource(resource: ResourceResponse | any) {
    this.loading = true;
    let params: _CRM_ORD_116 = {
      work_order_id: this.workOrder.work_order_id,
      resource_id: resource.resource_id
    }
    this.orderService.removeResourceFromWorkOrder(params).subscribe((workOrder) => {
      this.workOrder = workOrder;
      this.orderService.refreshSingleWorkOrderInWorkOrders(workOrder, 'removeResource');
      this.loading = false;
    }, error => {
      this.loading = false;
    });
  }

  removeUser(user: InternalUserResponse | any) {
    this.loading = true;
    let params: _CRM_ORD_114 = {
      work_order_id: this.workOrder.work_order_id,
      user_id: user.user_id
    }
    this.orderService.removeUserFromWorkOrder(params).subscribe((workOrder) => {
      this.workOrder = workOrder;
      this.orderService.refreshSingleWorkOrderInWorkOrders(workOrder, 'removeUser');
      this.loading = false;
    }, error => {
      this.loading = false;
    });
  }

  checkDescriptionOverflow() {
    if (!this.descriptionContainer) return;
    setTimeout(() => {
      this.descriptionOverflow = this.descriptionContainer.nativeElement.scrollHeight > this.descriptionContainer.nativeElement.clientHeight;
    }, 0);
  }

  protected readonly workOrderBadgeStatus = workOrderBadgeStatus;
  protected readonly paymentStatusBadge = paymentStatusBadge;
}

