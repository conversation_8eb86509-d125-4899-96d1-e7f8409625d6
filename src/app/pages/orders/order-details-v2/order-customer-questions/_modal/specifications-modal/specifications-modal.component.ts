import {Component, Input, OnInit} from '@angular/core';
import {NgbActiveModal, NgbTooltip} from "@ng-bootstrap/ng-bootstrap";
import {
  OrderCustomerQuestionResponse,
  OrderResponse, OrderCustomerQuestionChoiceResponse
} from "../../../../../../@shared/models/order.interfaces";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {CommonModule} from "@angular/common";
import {displayDate, UtilsService} from "../../../../../../@core/utils/utils.service";
import {StandardImports} from "../../../../../../@shared/global_import";

@Component({
    selector: 'app-specifications-modal',
    templateUrl: './specifications-modal.component.html',
    styleUrls: ['./specifications-modal.component.css'],
    standalone: true,
    imports: [StandardImports]
})
export class SpecificationsModalComponent implements OnInit {
  @Input() order?: OrderResponse
  questions: OrderCustomerQuestionResponse[] = []

  constructor(public activeModal: NgbActiveModal, public translate: TranslateService, public utilsService: UtilsService) {
  }

  ngOnInit() {
  }

  getChoiceToolTip(choice: OrderCustomerQuestionChoiceResponse) {
    return displayDate(choice.updated_at) + ' ' + this.translate.instant('common.by') + ' ' + choice.updated_by_name
  }


}

