import {Compo<PERSON>, OnInit, ViewChild} from '@angular/core';
import {TranslateModule} from "@ngx-translate/core";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {ButtonComponent} from "../../../../../../@shared/components/button/button.component";
import {<PERSON><PERSON><PERSON>, <PERSON>ForOf, NgIf} from "@angular/common";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {CustomerQuestionTemplateResponse} from "../../../../../../@shared/models/templates.interfaces";
import {environment} from "../../../../../../../environments/environment";
import {TemplateService} from "../../../../../../@shared/services/templates.service";
import {takeUntil} from "rxjs/operators";
import {OrderCustomerQuestionChoiceResponse, OrderCustomerQuestionResponse, OrderResponse} from "../../../../../../@shared/models/order.interfaces";
import {OrderService} from "../../../../../../@shared/services/order.service";
import {lastValueFrom, Subject} from "rxjs";
import {_CRM_ORD_133, _CRM_ORD_136, _CRM_ORD_151} from "../../../../../../@shared/models/input.interfaces";
import {SpecificationsModalComponent} from "../specifications-modal/specifications-modal.component";
import {
  QuestionComponent
} from "../../../../../templates/cq-overview/_modals/cq-template-modal/components/question/question.component";
import {
  AddCustomerQuestionsToOrderComponent
} from "../add-customer-questions-to-order/add-customer-questions-to-order.component";
import {SelectoriniComponent} from "../../../../../../@shared/components/selectorini/selectorini.component";
import {Router} from "@angular/router";
import {VerifyPopupModal} from "../../../../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {StandardImports} from "../../../../../../@shared/global_import";
import {EditButtonComponent} from "../../../../../../@shared/components/edit-button/edit-button.component";
import {DeleteButtonComponent} from "../../../../../../@shared/components/delete-button/delete-button.component";

@Component({
    selector: 'app-link-customer-question-template',
    templateUrl: './link-customer-question-template.component.html',
    styleUrl: './link-customer-question-template.component.css',
    standalone: true,
  imports: [StandardImports, EditButtonComponent, DeleteButtonComponent, SelectoriniComponent]
})
export class LinkCustomerQuestionTemplateComponent implements OnInit {
  customerTemplates: CustomerQuestionTemplateResponse[] = [];
  order: OrderResponse;
  customerQuestions: OrderCustomerQuestionResponse[] = [];
  loading: boolean = false;
  selectedTemplates: CustomerQuestionTemplateResponse[] = [];
  selectedTemplateId: number | null = null;
  destroy$ = new Subject<void>();

  @ViewChild('selectTemplateSelectorini') selectTemplateSelectorini: SelectoriniComponent;

  constructor(
    private templateService: TemplateService,
    public activeModal: NgbActiveModal,
    private orderService: OrderService,
    private modalService: NgbModal,
    private route: Router
  ) {}

  ngOnInit(): void {
    this.orderService.order$.pipe(takeUntil(this.destroy$)).subscribe(order => {
      this.order = order;
      this.customerQuestions = order.customer_questions;
    });
    this.fetchCqTemplates();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  fetchCqTemplates(): void {
    this.loading = true;
    this.templateService.getCustomerQuestionTemplates().subscribe({
      next: (res) => {
        this.customerTemplates = res.sort((a, b) => a.template_name.localeCompare(b.template_name));
        this.loading = false;
      },
      error: () => {
        this.loading = false;
      }
    });
  }

  // Existing methods for selecting multiple templates
  toggleTemplateSelection(template: CustomerQuestionTemplateResponse): void {
    const index = this.selectedTemplates.findIndex(t => t.template_id === template.template_id);
    if (index >= 0) {
      // Template is already selected, remove it
      this.selectedTemplates.splice(index, 1);
    } else {
      // Template is not selected, add it
      this.selectedTemplates.push(template);
    }
  }

  isSelected(template: CustomerQuestionTemplateResponse): boolean {
    return this.selectedTemplates.some(t => t.template_id === template.template_id);
  }

  async addSelectedTemplates(): Promise<void> {
    try {
      for (const selectedTemplate of this.selectedTemplates) {
        const payload = {
          template_id: selectedTemplate.template_id,
          order_id: this.order.order_id
        };

        await lastValueFrom(
          this.orderService.assignCustomerQuestionsTemplateToOrder(payload)
        );
      }

      this.selectedTemplates = [];
      this.orderService.fetchAndRefreshOrder(this.order.order_id, 'addSelectedTemplates');

    } catch (err) {
      console.error('Failed to link templates to order', err);
      alert('An error occurred while linking templates to the order. Please try again.');
    }
  }

  goToCqTemplates(): void {
    this.route.navigate(['/templates/customer-questions']);
    this.modalService.dismissAll();
  }

  addSelectedTemplate(event?: any): void {
    const payload = {
      template_id: event.template_id,
      order_id: this.order.order_id
    };

    this.orderService.assignCustomerQuestionsTemplateToOrder(payload).subscribe({
      next: () => {
        this.selectTemplateSelectorini.deselectItem(false);

        this.orderService.fetchAndRefreshOrder(this.order.order_id, 'addSelectedTemplate');
        this.selectedTemplateId = null;
      },
    });
  }

  deleteCustomerQuestionTemplate(question: OrderCustomerQuestionResponse): void {
    const _payload: _CRM_ORD_136 = {
      order_id: this.order.order_id,
      order_question_id: question.order_question_id
    };
    this.orderService.deleteCustomerQuestion(_payload).subscribe({
      next: () => {
        this.orderService.fetchAndRefreshOrder(this.order.order_id, 'deleteCustomerQuestion');
      },
      error: (err) => {
        console.error('Failed to delete template:', err);
      }
    });
  }

  choiceChecked(choice: OrderCustomerQuestionChoiceResponse) {
    let modalRef = this.modalService.open(VerifyPopupModal);
    modalRef.componentInstance.titleTranslationKey = 'orderDetails.customerFeedback.questionnaire.verifySetChoiceModal.title';
    modalRef.result.then((result) => {
      if (result) {
        let payload: _CRM_ORD_151 = {
          order_choice_id: choice.order_choice_id,
          value: choice.value === 1 ? 0 : 1,
        }
        this.orderService.updateCustomerQuestionChoiceAsCompany(payload).subscribe({
          next: () => {
            this.orderService.fetchAndRefreshOrder(this.order.order_id, 'choiceChecked');
          },
          error: (err) => {
            console.error('Failed to set choice:', err);
          }
        });
      } else {
        console.log('reseting')
        this.customerQuestions = [];
        this.customerQuestions = this.order.customer_questions;
      }
    });
  }


  addQuestionModal() {
    const modalRef= this.modalService.open(AddCustomerQuestionsToOrderComponent, {size: 'lg'});
    // modalRef.componentInstance.questions = this.order.customer_questions;
  }

  editQuestionModal(question: OrderCustomerQuestionResponse) {
    const modalRef = this.modalService.open(AddCustomerQuestionsToOrderComponent, {size: 'lg'});
    modalRef.componentInstance.question = question;
  }

}
