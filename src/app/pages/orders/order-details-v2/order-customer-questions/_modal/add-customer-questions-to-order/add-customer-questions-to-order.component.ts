import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {FormArray, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";

import {NgbActiveModal, Ng<PERSON>Pop<PERSON>, NgbTooltip} from "@ng-bootstrap/ng-bootstrap";
import {TemplateService} from "../../../../../../@shared/services/templates.service";
import {ToastService} from "../../../../../../@core/services/toast.service";

import {CommonModule} from "@angular/common";
import {TranslateModule} from "@ngx-translate/core";
import {ButtonComponent} from "../../../../../../@shared/components/button/button.component";
import {CustomerQuestionResponse, CustomerQuestionTemplateResponse} from "../../../../../../@shared/models/templates.interfaces";
import {CdkDrag, CdkDragDrop, Cdk<PERSON>rag<PERSON><PERSON>le, CdkDropList, moveItemInArray} from "@angular/cdk/drag-drop";
import {_CRM_ORD_134, _CRM_ORD_135} from "../../../../../../@shared/models/input.interfaces";
import {TempChoice} from "../../../../../templates/cq-overview/_modals/cq-template-modal/components/question/question.component";
import {takeUntil} from "rxjs/operators";
import {OrderService} from "../../../../../../@shared/services/order.service";
import {OrderCustomerQuestionResponse, OrderResponse} from "../../../../../../@shared/models/order.interfaces";
import {Subject} from "rxjs";
import {StandardImports} from "../../../../../../@shared/global_import";

@Component({
    selector: 'app-add-customer-questions-to-order',
    templateUrl: './add-customer-questions-to-order.component.html',
    styleUrl: './add-customer-questions-to-order.component.css',
    standalone: true,
  imports: [StandardImports, CdkDropList, NgbPopover, CdkDrag, CdkDragHandle]
})
export class AddCustomerQuestionsToOrderComponent implements OnInit{
  isFormValid = false;
  customerQuestionForm: FormGroup;
  isPopoverOpen = false;
  selectedOption: number | null = null;
  tempChoices: TempChoice[] = [];
  questionTextControl: FormControl<string | null> = new FormControl(null, Validators.required);
  requiredControl: FormControl<boolean | null> = new FormControl(null, Validators.required);
  multiSelect: boolean;
  loading: boolean = false;
  order: OrderResponse

  selectedButton: string = 'option1';
  destroy$ = new Subject<void>();


  @Input() question?: OrderCustomerQuestionResponse;
  @Output() questionCloseEmitter: EventEmitter<CustomerQuestionResponse | CustomerQuestionTemplateResponse | null> = new EventEmitter<CustomerQuestionResponse | CustomerQuestionTemplateResponse | null>();

  constructor(
    public activeModal: NgbActiveModal,
    private toastService: ToastService,
    private orderService: OrderService
  ) {}


  ngOnInit() {
    this.orderService.order$.pipe(takeUntil(this.destroy$)).subscribe((order) => {
      this.order = order;
    });

    if (this.question) {
      // Editing an existing question
      this.initializeFormForEdit();
    } else {
      // Adding a new question
      this.initializeFormForAdd();
    }
  }


  private initializeFormForEdit() {
    this.questionTextControl.setValue(this.question!.question_text);
    this.multiSelect = this.question!.radio_selection === 0;
    this.requiredControl.setValue(this.question!.required === 1);

    this.question!.choices.forEach((choice, index) => {
      this.tempChoices.push({
        choiceId: choice.choice_id,
        tempChoiceId: null,
        choiceNameControl: new FormControl(choice.choice_name, Validators.required),
        choiceGhostTextControl: new FormControl(choice.choice_ghost_text),
        deleted: false,
        index: index
      });
    });
  }


  private initializeFormForAdd() {
    // Initialize form controls for a new question
    this.questionTextControl.setValue('');
    this.multiSelect = false;
    this.requiredControl.setValue(false);

    // Add two default options
    this.addOption();
    this.addOption();
  }


  // Drop event for drag-drop functionality
  drop(event: CdkDragDrop<any[]>) {
    moveItemInArray(this.tempChoices, event.previousIndex, event.currentIndex);
    this.tempChoices.forEach((choice, index) => {
      choice.index = index;  // Update the index for each choice
    });
  }


  // Getter for the options FormArray
  get options(): FormArray {
    return this.customerQuestionForm.get('options') as FormArray;
  }


  // Toggle popover for options
  togglePopover() {
    this.isPopoverOpen = !this.isPopoverOpen;
  }


  // Check if a specific option is selected
  isChecked(index: number): boolean {
    // console.log("eloi")
    return this.selectedOption === index;
  }


  selectButton(value: boolean): void {
    this.selectedButton = value ? 'option2' : 'option1';
    this.multiSelect = value;
  }


  // Select a specific option
  selectOption(option: number): void {
    // console.log("12")
    this.selectedOption = option;
  }


  // Add a new option for a question
  addOption(): void {
    this.tempChoices.push(this.createOption(this.tempChoices.length));  // Pass the current length as index
  }

  // Create a new option form group
  createOption(index: number): TempChoice {
    return {
      choiceId: null,
      tempChoiceId: Math.floor(Math.random() * 10000),
      choiceNameControl: new FormControl('', Validators.required),
      choiceGhostTextControl: new FormControl(''),
      deleted: false,
      index: index
    };
  }

  // // Go back to the template view
  // goBackToTemplate(question: CustomerQuestionResponse | null, template?: CustomerQuestionTemplateResponse): void {
  //   if (template) {
  //     this.questionCloseEmitter.emit(template);
  //   } else if (question) {
  //     this.questionCloseEmitter.emit(question);
  //   } else {
  //     this.questionCloseEmitter.emit(null);
  //   }
  // }

  removeOption(choice: TempChoice): void {
    if (choice.choiceId) {
      choice.deleted = true;
    } else {
      this.tempChoices = this.tempChoices.filter(tempChoice => tempChoice.tempChoiceId !== choice.tempChoiceId);
    }
  }

  setMultiSelect(value: boolean) {
    this.multiSelect = value;
  }

  save() {
    this.loading = true;
    if (this.question) {
      // Update existing question
      let payload: _CRM_ORD_135 = {
        order_id: this.order.order_id,
        // question_id: this.question!.question_id,
        order_question_id: this.question!.order_question_id,
        question_text: this.questionTextControl.value!,
        radio_selection: this.multiSelect ? 0 : 1,
        required: this.requiredControl.value! ? 1 : 0,
        choices: this.tempChoices.map(choice => {
          return {
            choice_id: choice.choiceId,
            choice_name: choice.choiceNameControl.value!,
            choice_ghost_text: choice.choiceGhostTextControl.value!,
            deleted: choice.deleted
          };
        })
      };

      this.orderService.updateCustomerQuestiom(payload).subscribe({
        next: (res) => {
          this.loading = false;
          this.orderService.fetchAndRefreshOrder(this.order.order_id, 'addCustomerQuestion');
          this.toastService.successToast('updated');
          this.activeModal.close();
        },
        error: (error) => {
          this.loading = false;
        }
      });
    }
    else if (!this.question) {
      let payload: _CRM_ORD_134 = {
        order_id: this.order.order_id,
        question_text: this.questionTextControl.value!,
        radio_selection: this.multiSelect ? 0 : 1,
        required: this.requiredControl.value! ? 1 : 0,
        choices: this.tempChoices.map(choice => {
          return {
            choice_id: choice.choiceId,
            choice_name: choice.choiceNameControl.value!,
            choice_ghost_text: choice.choiceGhostTextControl.value!,
            deleted: choice.deleted
          };
        })
      };
      this.orderService.createCustomerQuestion(payload).subscribe({
        next: (res) => {
          this.loading = false;
          this.orderService.fetchAndRefreshOrder(this.order.order_id, 'addCustomerQuestion');
          this.toastService.successToast('saved');
          this.activeModal.close();
        },
        error: (error) => {
          this.loading = false;
        }
      });
    }
    // else {
    //   const payload = {
    //     template_name: this.customerQuestionsTemplate.template_name,
    //     questions: []
    //   };
    //   this.templateService.createCustomerQuestionTemplate(payload).subscribe({
    //     next: (data) => {
    //       this.customerQuestionsTemplate = data;
    //       this.goBackToTemplate(null, data);
    //       let payload: _CRM_TMP_0 = {
    //         template_id: this.customerQuestionsTemplate.template_id,
    //         question_text: this.questionTextControl.value!,
    //         radio_selection: this.multiSelect ? 0 : 1,
    //         required: this.requiredControl.value! ? 1 : 0,
    //         choices: this.tempChoices.map(choice => {
    //           return {
    //             choice_id: choice.choiceId,
    //             choice_name: choice.choiceNameControl.value!,
    //             choice_ghost_text: choice.choiceGhostTextControl.value!,
    //             deleted: choice.deleted
    //           };
    //         })
    //       };
    //       this.templateService.createCustomerQuestion(payload).subscribe({
    //         next: (res) => {
    //           this.loading = false;
    //           this.goBackToTemplate(res);
    //           this.toastService.successToast('created');
    //         },
    //         error: (error) => {
    //           this.loading = false;
    //         }
    //       });
    //     },
    //     error: (error) => {
    //       console.error('Error creating template:', error);
    //     }
    //   });
    // }
  }


  isFormInvalid(): boolean {
    if (!this.questionTextControl.valid) {
      return true;
    }
    return this.tempChoices.length === 0 || this.tempChoices.some(choice => choice.choiceNameControl.invalid && !choice.deleted);

  }

}
