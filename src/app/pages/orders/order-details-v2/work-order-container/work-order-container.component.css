.order-details-card {
  border: 1px solid #DEE2E6;
  border-radius: 10px;
  box-shadow: none;
}

.schedule-overlay {
  position: absolute;
  border-radius: 0 10px 0 0;
  top: 0;
  right: 0;
  width: 70px;
  height: 70px;
  background-color: #448C74;
  clip-path: polygon(100% 0, 0 0, 100% 100%); /* Creates the triangle shape */
  z-index: 1;
}

.overlay-icon {
  position: absolute;
  top: 30%;
  right: 0;
  color: white;
  transform: translate(-50%, -50%);
}

.rotate-icon {
  transform: rotate(90deg); /* Rotate to the right */
  transition: transform 0.3s ease; /* Smooth rotation animation */
}

.collapsed-icon {
  transform: rotate(0deg); /* Reset to default position */
  transition: transform 0.3s ease; /* Smooth rotation animation */
}

.h3-mimic {
  color: #313A46;
  /*color: #6C757D;*/
  font-size: 24px;
  font-weight: 700;
  font-family: 'Nunito', sans-serif;
}

@media (min-width: 992px) {
  .styled-container {
    border-radius: 0 0 10px 10px;
    background-color: rgb(252, 252, 252);
  }
}

.border-bottom {
  border-bottom: 1px solid #DEE2E6;
}

@media (min-width: 992px) {
  .border-bottom {
    border-bottom: none !important;
  }
}
