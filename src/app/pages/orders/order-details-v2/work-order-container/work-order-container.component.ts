import {ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, Renderer2, SimpleChanges, TemplateRef, ViewChild} from '@angular/core';
import {DetailsViewSettings, OrderResponse, OrderSubContractorResponse, WorkOrderCompactResponse, WorkOrderResponse, WorkOrderStatusResponse} from "../../../../@shared/models/order.interfaces";
import {capitaliseFirstLetter, formatFullDayAndDate, formatTimeHM, paymentStatusBadge, UtilsService, workOrderBadgeStatus} from "../../../../@core/utils/utils.service";
import {NgbDateStruct, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {OrderService} from "../../../../@shared/services/order.service";
import {WorkOrderDetailsComponent, WorkOrderDetailsModal} from "../../../work-orders/components/work-order-details/work-order-details.component";
import {Ng<PERSON>lass, NgForOf, NgIf} from "@angular/common";
import {TranslateModule, TranslateService} from "@ngx-translate/core";

import {ButtonComponent} from "../../../../@shared/components/button/button.component";





import {FormControl, FormsModule, ReactiveFormsModule} from "@angular/forms";
import {_CRM_ORD_114, _CRM_ORD_116, _CRM_ORD_117, _CRM_ORD_118, _CRM_ORD_119, _CRM_ORD_170} from "../../../../@shared/models/input.interfaces";
import {ToastService} from "../../../../@core/services/toast.service";
import {InternalUserResponse} from "../../../../@shared/models/user.interfaces";
import {ResourceResponse} from "../../../../@shared/models/resources.interfaces";
import {BehaviorSubject, firstValueFrom, pairwise, Subject} from "rxjs";

import {WorkOrderDuplicateModal} from "../../../work-orders/components/work-order-details/_modals/duplicate-modal/work-order-duplicate-modal";
import {VerifyPopupModal} from "../../../../@shared/components/verify-popup-modal/verify-popup-modal";


import {CardComponent} from "../../../../@shared/components/layout/card/card.component";
import {WorkOrderCardComponent} from "../work-order-card/work-order-card.component";
import {WorkOrderListComponent, WorkOrderRow} from "../../../work-orders/components/work-order-list/work-order-list.component";
import {StandardImports} from "../../../../@shared/global_import";
import {NewWorkOrderComponent} from "../../../work-orders/components/new-work-order/new-work-order.component";
import {takeUntil} from "rxjs/operators";
import {TablerinoColumn, TablerinoComponent, TablerinoSettings} from "../../../../@shared/components/tablerino/tablerino.component";
import {ProfiledItemListComponent} from "../../../../@shared/components/profiled-item-list/profiled-item-list.component";
import {TableFooterComponent} from "../../../../@shared/components/table-footer/table-footer.component";
import {PaginationContainer} from "../../../../@shared/models/global.interfaces";
import {ButtonDoubleComponent} from "../../../../@shared/components/button-double/button-double.component";
import {TablerinoCompleteComponent} from "../../../../@shared/components/tablerino-complete/tablerino-complete.component";
import {PaginationResponse} from "../../../../@shared/models/response.interfaces";
import {WorkOrderScheduleCardComponent} from "../work-order-schedule-card/work-order-schedule-card.component";
import {HeaderFilterComponent, HeaderFiltersContainer} from "../../../../@shared/components/tablerino-header/tablerino-header.component";

@Component({
  selector: 'app-work-order-container',
  templateUrl: './work-order-container.component.html',
  styleUrls: ['./work-order-container.component.css'],
  standalone: true,
  imports: [StandardImports, CardComponent, WorkOrderCardComponent, WorkOrderListComponent, ProfiledItemListComponent, TableFooterComponent, TablerinoComponent, ButtonDoubleComponent, TablerinoCompleteComponent, WorkOrderScheduleCardComponent]
})
export class WorkOrderContainerComponent implements OnInit, OnDestroy {

  viewSettings: DetailsViewSettings = {};
  order: OrderResponse;
  workOrders: WorkOrderRow[] = [];
  workOrderSchedules: WorkOrderRow[] = [];
  workOrdersDataResponse: PaginationResponse<WorkOrderResponse[]>;
  workOrderSchedulesDataResponse: PaginationResponse<WorkOrderResponse[]>;
  workOrdersLoading = false;
  workOrderSchedulesLoading = false;
  workOrderColumnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);
  workOrderScheduleColumnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);
  selectedWorkOrderRowsSubject: BehaviorSubject<WorkOrderRow[]> = new BehaviorSubject<WorkOrderRow[]>([]);
  workOrderPaginationSubject: BehaviorSubject<PaginationContainer> = new BehaviorSubject<PaginationContainer>({page: 1, limit: 10, paginate: 1, totalPages: 0, totalItems: 0});
  selectedWorkOrderScheduleRowsSubject: BehaviorSubject<WorkOrderRow[]> = new BehaviorSubject<WorkOrderRow[]>([]);
  workOrderSchedulePaginationSubject: BehaviorSubject<PaginationContainer> = new BehaviorSubject<PaginationContainer>({page: 1, limit: 10, paginate: 1, totalPages: 0, totalItems: 0});
  workOrderHeaderFiltersContainerSubject: BehaviorSubject<HeaderFiltersContainer> = new BehaviorSubject<HeaderFiltersContainer>({filters: [], init: true});

  init: boolean = true;

  cancelWorkOrderRequest$ = new Subject<void>();
  cancelWorkOrderScheduleRequest$ = new Subject<void>();

  @ViewChild('employees', {static: true}) employees!: TemplateRef<any>;

  destroy$ = new Subject<void>();

  constructor(public utilsService: UtilsService, private modalService: NgbModal, private orderService: OrderService, private renderer: Renderer2, private cdr: ChangeDetectorRef, private toastService: ToastService, private translate: TranslateService) {
  }

  async ngOnInit() {
    this.order = await firstValueFrom(this.orderService.order$)

    this.orderService.refreshWorkOrders$.subscribe(() => {
      this.loadWorkOrders();
      this.loadWorkOrderSchedules();
    });

    this.viewSettings.repeatingView = this.order.repeating;
    this.workOrderPaginationSubject.pipe(pairwise()).subscribe((paginationDetails) => {
      if (paginationDetails[0].page !== paginationDetails[1].page) {
        this.loadWorkOrders();
      }
    });

    this.workOrderSchedulePaginationSubject.pipe(pairwise()).subscribe((paginationDetails) => {
      if (paginationDetails[0].page !== paginationDetails[1].page) {
        this.loadWorkOrderSchedules();
      }
    });

    this.workOrderHeaderFiltersContainerSubject.subscribe((headerFilters) => {
      if (!headerFilters.init) {
        this.loadWorkOrders();
      }
    });

    this.initializeWorkOrderHeaderFilters();
    this.initializeWorkOrderColumns();
    this.initializeWorkOrderScheduleColumns();
    this.loadWorkOrders();
    this.loadWorkOrderSchedules();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadWorkOrders(searchTerm: string = '') {
    this.cancelWorkOrderRequest$.next();
    let filters = this.workOrderHeaderFiltersContainerSubject.value.filters;
    let sortColumn = this.workOrderColumnsSubject.value.find(col => col.sortedAsc || col.sortedDesc) || null;
    let sortKey: string = sortColumn?.name || 'created_at';
    let sortDirection: 'asc' | 'desc' = sortColumn ? (sortColumn.sortedAsc ? 'asc' : 'desc') : 'desc';
    let params: _CRM_ORD_170 = {
      order_id: this.order.order_id,
      paginate: 1,
      work_order_status_ids: filters.find(hf => hf.parameterName === 'work_order_status_ids')!.dropDownOptions!.filter(ddo => ddo.active).map(ddo => Number(ddo.value!)),
      order_by: sortKey,
      order_direction: sortDirection,
      execution_date_from: filters.find(hf => hf.parameterName === 'execution_at')!.dateRangeFromControl!.value,
      execution_date_to: filters.find(hf => hf.parameterName === 'execution_at')!.dateRangeToControl!.value,
      search_string: searchTerm,
      search_string_columns: ['work_order_title', 'work_order_number'],
      parent_work_order_ids: filters.find(hf => hf.parameterName === 'parent_work_order_ids')!.dropDownOptions!.filter(ddo => ddo.active).map(ddo => Number(ddo.value!)),
      allow_unplanned: true
    }
    this.workOrdersLoading = true;
    this.orderService.getCompanyWorkOrdersWithFullResponse(params).pipe(takeUntil(this.destroy$ || this.cancelWorkOrderRequest$ || this.orderService.refreshWorkOrders$)).subscribe((res) => {
      this.workOrdersDataResponse = res;
      this.orderService.refreshWorkOrders(res.data, 'loadWorkOrdersWoCard');
      this.workOrders = res.data.map((wo) => {
        return {
          ...wo,
          selected: false,
        } as WorkOrderRow;
      });
      this.workOrderPaginationSubject.next({
        ...this.workOrderPaginationSubject.value,
        totalItems: res.total_items,
        totalPages: res.total_pages
      });
      this.workOrdersLoading = false;
      this.init = false;
    }, error => {
      this.workOrdersLoading = false;
    });

  }

  loadWorkOrderSchedules() {
    this.cancelWorkOrderScheduleRequest$.next();
    let params: _CRM_ORD_170 = {
      order_id: this.order.order_id,
      template_filter: true,
      paginate: 1,
    }
    this.workOrderSchedulesLoading = true;
    this.orderService.getCompanyWorkOrdersWithFullResponse(params).pipe(takeUntil(this.destroy$ || this.cancelWorkOrderScheduleRequest$ || this.orderService.refreshWorkOrders$)).subscribe((res) => {
      this.workOrderSchedulesDataResponse = res;
      this.orderService.refreshWorkOrderSchedules(res.data, 'loadWorkOrderSchedulesWoCard');
      this.workOrderSchedules = res.data.map((wo) => {
        return {
          ...wo,
          selected: false,
        };
      });
      this.workOrderSchedulePaginationSubject.next({
        ...this.workOrderSchedulePaginationSubject.value,
        totalItems: res.total_items,
        totalPages: res.total_pages
      });
      this.workOrderSchedulesLoading = false;
      this.init = false;
    }, error => {
      this.workOrderSchedulesLoading = false;
    });
  }

  initializeWorkOrderColumns() {
    this.workOrderColumnsSubject.next([
      {
        name: 'workOrderNumber',
        labelKey: 'ID',
        formatter: (wo: WorkOrderResponse) => '#' + wo.work_order_number,
        sort: false,
        visible: true,
      },
      {
        name: 'title',
        labelKey: 'workOrder.list.column.title',
        formatter: (wo: WorkOrderResponse) => wo.work_order_title,
        sort: false,
        visible: true,
      },
      {
        name: 'executionAtDate',
        labelKey: 'workOrder.list.column.date',
        formatter: (wo: WorkOrderResponse) => this.formatExecutionDate(wo),
        sort: false,
        visible: true,
      },
      {
        name: 'executionAtTime',
        labelKey: 'workOrder.list.column.time',
        formatter: (wo: WorkOrderResponse) => formatTimeHM(wo.execution_at),
        sort: false,
        visible: true,
      },
      {
        name: 'workOrderStatus',
        labelKey: 'workOrder.list.column.status',
        formatter: (wo: WorkOrderResponse) => workOrderBadgeStatus(wo),
        sort: false,
        visible: true,
      },
      {
        name: 'assignees',
        labelKey: 'workOrder.list.column.assignees',
        formatter: (wo: WorkOrderCompactResponse) => wo.users,
        sort: false,
        visible: true,
        ngTemplate: this.employees,
      },
      {
        name: 'workOrderPaymentStatus',
        labelKey: 'workOrder.list.column.paymentStatus',
        formatter: (wo: WorkOrderResponse) => wo.payment ? paymentStatusBadge(wo.payment) : '',
        sort: false,
        visible: true,
      },
    ]);
  }

  initializeWorkOrderScheduleColumns() {
    this.workOrderScheduleColumnsSubject.next([
      {
        name: 'title',
        labelKey: 'workOrder.list.column.title',
        formatter: (wo: WorkOrderResponse) => wo.work_order_title,
        sort: false,
        visible: true,
      },
      {
        name: 'frequency',
        labelKey: 'Frekvens',
        formatter: (wo: WorkOrderResponse) => capitaliseFirstLetter(wo.schedule?.schedule_description) || '',
        sort: false,
        visible: true,
      },
      {
        name: 'executionAtAndToTime',
        labelKey: 'workOrder.list.column.time',
        formatter: (wo: WorkOrderResponse) => formatTimeHM(wo.execution_at) + ' - ' + formatTimeHM(wo.execution_to),
        sort: false,
        visible: true,
      },
      {
        name: 'assignees',
        labelKey: 'workOrder.list.column.assignees',
        formatter: (wo: WorkOrderCompactResponse) => wo.users,
        sort: false,
        visible: true,
        ngTemplate: this.employees,
      },
    ]);
  }

  initializeWorkOrderHeaderFilters() {
    let headerFilters: HeaderFilterComponent[] = [
      {
        parameterName: 'execution_at',
        translationKey: 'workOrder.list.filter.execution',
        active: false,
        dateRange: true,
        dateRangeFromControl: new FormControl(),
        dateRangeToControl: new FormControl(),
        dateRangeFromParamKey: 'execution_date_from',
        dateRangeToParamKey: 'execution_date_to',
        excludes: ['unplanned'],
      },
      {
        parameterName: 'parent_work_order_ids',
        translationKey: 'Jobb-planer',
        multiSelect: true,
        dropDownOptions: [],
        active: false,
      },
      {
        parameterName: 'work_order_status_ids',
        translationKey: 'workOrder.list.filter.status',
        multiSelect: true,
        dropDownOptions: [],
        active: false,
      },
    ];
    this.workOrderHeaderFiltersContainerSubject.next({filters: headerFilters, init: true});
  }

  setRepeatingView(repeating: boolean) {
    this.viewSettings.repeatingView = repeating;
  }

  openCreateWorkOrderModal() {
    let modalRef = this.modalService.open(NewWorkOrderComponent);
    modalRef.componentInstance.orderId = this.order.order_id;
    modalRef.componentInstance.viewSettings = {repeatingView: this.viewSettings.repeatingView};
  }

  rowClicked(row: WorkOrderRow) {
    let modalRef = this.modalService.open(WorkOrderDetailsComponent, {size: 'xl'});
    modalRef.componentInstance.workOrderId = row.work_order_id;
    modalRef.componentInstance.viewSettings = WorkOrderDetailsModal;
  }

  formatExecutionDate(wo: WorkOrderResponse) {
    let text = formatFullDayAndDate(wo.execution_at, false)
    if (wo.execution_at && wo.execution_to && wo.execution_at.getDate() != wo.execution_to.getDate()) {
      text += `<i class="fa-regular fa-calendar-exclamation ms-1"></i>`
    }
    return text;
  }

  filterClicked(filter: HeaderFilterComponent) {
    if (filter.parameterName === 'parent_work_order_ids' && !filter.loaded) {
      filter.loading = true;
      this.orderService.getCompanyWorkOrders({order_id: this.order.order_id, paginate: 0, template_filter: true}).pipe(takeUntil(this.destroy$)).subscribe((res) => {
        filter.dropDownOptions = res.data.map((wo) => {
          return {
            value: wo.work_order_id,
            translationKey: wo.work_order_title,
            active: false,
          }
        });
        filter.loading = false;
        filter.loaded = true;
      });
    }

    if (filter.parameterName === 'work_order_status_ids' && !filter.loaded) {
      filter.loading = true;
      this.orderService.getWorkOrderStatuses().pipe(takeUntil(this.destroy$)).subscribe((res: WorkOrderStatusResponse[]) => {
        filter.dropDownOptions = res.map((status) => {
          return {
            value: status.work_order_status_id,
            translationKey: status.work_order_status_name,
            active: false,
          }
        });
        filter.loading = false;
        filter.loaded = true;
      });
    }

  }
}

