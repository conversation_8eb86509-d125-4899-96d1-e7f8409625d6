<app-card
[labelKey]="'customers.privateCustomers.jobs'"
[buttonHeader]="true"
[padding]="'0'"
[loading]="workOrdersLoading || workOrderSchedulesLoading"
[ghosted]="init || (workOrders.length == 0 && !init)">
  <ng-container buttonHeader>
    <app-button
      [translationKey]="viewSettings.repeatingView ? 'Opprett jobb-plan' : 'orderDetails.createWorkOrder'"
      [small]="true"
      (buttonClick)="openCreateWorkOrderModal()"
    ></app-button>
  </ng-container>

  <ng-container cardcontent>
    <div *ngIf="order">
      <app-button-double
        *ngIf="order.repeating || workOrderSchedules.length > 0"
        [leftButtonTranslationKey]="'Jobb-planer'"
        [rightButtonTranslationKey]="'Jobber'"
        [leftButtonActive]="!!viewSettings.repeatingView"
        [rightButtonActive]="!viewSettings.repeatingView"
        [leftButtonCustomClass]="'no-left-border-radius'"
        [rightButtonCustomClass]="'no-right-border-radius'"
        [boldText]="true"
        [useMaxWidth]="true"
        (leftButtonClick)="setRepeatingView(true)"
        (rightButtonClick)="setRepeatingView(false)"
      ></app-button-double>

      <!--   Single work orders   -->
      <div *ngIf="!viewSettings.repeatingView" [ngClass]="">

        <div *ngIf="workOrders.length <= 3" class="p-lg-3 styled-container">
          <div *ngIf="workOrders.length === 0">{{"orderDetails.noWorkOrder" | translate}}</div>
          <div *ngFor="let wo of workOrders; let i = index" [ngClass]="{'mb-2': i !== workOrders.length - 1, 'border-bottom': workOrders.length > 1 && i !== workOrders.length - 1}">
            <app-work-order-card [workOrder]="wo" [multipleWorkOrderView]="workOrders.length > 1"></app-work-order-card>
          </div>
        </div>

        <div *ngIf="workOrders.length > 3">
          <app-tablerino-complete
            [tableName]="'order-details-work-order-list'"
            [tableData]="workOrders"
            [headerFiltersContainerSubject]="workOrderHeaderFiltersContainerSubject"
            [settings]="{checkboxes: true, clickableRows: true}"
            [columnsSubject]="workOrderColumnsSubject"
            [loading]="workOrdersLoading"
            [selectedRowsSubject]="selectedWorkOrderRowsSubject"
            [paginationSubject]="workOrderPaginationSubject"
            [dataResponse]="workOrdersDataResponse"
            [noRounding]="true"
            (rowClickedEmitter)="rowClicked($event)"
            (quickSearchEmitter)="loadWorkOrders($event)"
            (sortEmitter)="loadWorkOrders()"
            (filterClickedEmitter)="filterClicked($event)"
          ></app-tablerino-complete>
        </div>
      </div>

      <!--   Work order schedules   -->
      <div *ngIf="viewSettings.repeatingView" [ngClass]="">
        <div *ngIf="workOrderSchedules.length > 1">
          <app-tablerino-complete
            [showHeader]="false"
            [disableSort]="true"
            [disableDrag]="true"
            [tableName]="'order-details-work-order-schedules-list'"
            [tableData]="workOrderSchedules"
            [settings]="{clickableRows: true}"
            [columnsSubject]="workOrderScheduleColumnsSubject"
            [loading]="workOrderSchedulesLoading"
            [selectedRowsSubject]="selectedWorkOrderScheduleRowsSubject"
            [paginationSubject]="workOrderSchedulePaginationSubject"
            [dataResponse]="workOrderSchedulesDataResponse"
            [noRounding]="true"
            (rowClickedEmitter)="rowClicked($event)"
          ></app-tablerino-complete>
        </div>
        <div *ngIf="workOrderSchedules.length === 1" class="p-2" style="background-color: rgb(252, 252, 252)">
          <app-work-order-schedule-card
            [workOrder]="workOrderSchedules[0]"
            [compactView]="true"
          ></app-work-order-schedule-card>
        </div>
      </div>

    </div>

  </ng-container>
</app-card>

<ng-template #employees let-column="column" let-row="row">
  <app-profiled-item-list
      [id]="column.name"
      [wrap]="false"
      [itemImageKey]="'profile_image_url'"
      [itemIdKey]="'user_id'"
      [small]="true"
      [showDeleteCross]="false"
      [employeeSelector]="true"
      [selectedItems]="row.users"
    ></app-profiled-item-list>
</ng-template>
