import {Component, Input, OnInit} from '@angular/core';
import {OrderResponse, WorkOrderResponse} from "../../../../@shared/models/order.interfaces";
import {OrderService} from "../../../../@shared/services/order.service";
import {_CRM_ORD_97} from "../../../../@shared/models/input.interfaces";
import {StandardImports} from "../../../../@shared/global_import";
import {ToggleSwitchComponent} from "../../../../@shared/components/toggle-switch/toggle-switch.component";
import {CardComponent} from "../../../../@shared/components/layout/card/card.component";

@Component({
  selector: 'app-notifications',
  templateUrl: './order-notifications.html',
  styleUrls: ['./order-notifications.css'],
  standalone: true,
  imports: [StandardImports, ToggleSwitchComponent, CardComponent]
})
export class OrderNotifications implements OnInit {
  @Input() workOrder?: WorkOrderResponse;
  order: OrderResponse
  enableCustomerNotifications: boolean;

  constructor(private orderService: OrderService) {}

  ngOnInit(){
    this.orderService.order$.subscribe(order => {
      this.order = order;
      // this.enableCustomerNotifications = order.enable_customer_notifications === 1;
    });
  }

  setOrderNotifications(enableCustomerNotifications: boolean){
    let payload: _CRM_ORD_97 = {
      order_id: this.order.order_id,
      enable_customer_notifications: enableCustomerNotifications ? 1 : 0,
    }

    this.orderService.patchOrder(payload).subscribe((res) => {
      this.orderService.refreshOrder(res, 'setOrderNotification');
    });

  }
}
