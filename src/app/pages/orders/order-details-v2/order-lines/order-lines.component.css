.order-lines-card {
  border: 1px solid #DEE2E6;
  border-radius: 10px;
  box-shadow: none;
  margin: 0;
  overflow-x: auto; /* Enable horizontal scrolling */
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on touch devices */
  display: block; /* Ensure block-level scrolling */
}

.order-lines-card:hover {
  border: 1px solid var(--disabled-primary);
  /*box-shadow: 0 0 0 1px var(--disabled-primary);*/
}

.card-body {
  white-space: nowrap; /* Prevent wrapping of content */
  min-width: 600px; /* Ensure card's width is larger than the screen on smaller devices */
}

.order-line-grey {
  background-color: #F7F7F7;
}

.order-line-column-header-setup {
  display: grid;
  grid-template-columns: 2fr 2fr 18fr 6fr 8fr 8fr 8fr;
  grid-gap: 10px;
  padding: 10px 0 10px 10px;
  text-align: center;
}

.order-line-column-header-setup.no-check {
  grid-template-columns: 2fr 18fr 6fr 8fr 8fr 8fr;
}

.order-line-column-header-setup.no-drag {
  grid-template-columns: 2fr 18fr 6fr 8fr 8fr 8fr;
}

.order-line-column-header-setup.no-drag.no-check {
  grid-template-columns: 18fr 6fr 8fr 8fr 8fr;
}

.payment-card {
  padding: 12px !important;
}

.chevron-rotate {
  transition: transform 0.3s ease-in-out; /* Add transition to transform */
}

.chevron-rotate.expanded {
  transform: rotate(90deg); /* Rotate the icon by 90 degrees when expanded */
}

.create-order-line-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border: none;
  width: 100%;
  padding: 5px;
  font-weight: bold;
  color: #6C757D
}

.create-order-line-button:hover {
  background-color: #f6f6f6;
}

.create-order-line-button:disabled{
  opacity: 0.7;
  background-color: white;
}

.order-details-header {
  padding: 16px 8px 16px 24px !important;
  font-weight: 600 !important;
}

.order-details-header.compact {
  padding: 8px 8px 8px 16px !important;
}

.order-details-header-title {
  font-size: 18px;
  font-weight: 600 !important;
  color: #2C2C2C;
}

.work-order-header {
  display: grid;
  overflow: hidden;
  grid-template-columns: 2fr 6fr 14fr 10fr 10fr 9fr 1fr;
  font-weight: 600;
  color: #6c757d;
  align-items: center;
  padding: 5px 0;
  min-height: 38px;
  background-color: #F0F0F0;
  border-top: 1px solid #E0E0E0;
  border-bottom: 1px solid #E0E0E0;
}

.work-order-header-cancelled {
  background-color: rgba(228, 139, 139, 0.32);
  border-color: rgba(228, 139, 139, 1);
}

.work-order-header-finished {
  background-color: rgba(139, 186, 149, 0.32);
  border-color: rgba(139, 186, 149, 1);
}

.work-order-header-started {
  background-color: rgba(237, 189, 117, 0.32);
  border-color: rgba(237, 189, 117, 1);
}

.work-order-header:hover {
  background-color: #e7e7e7;
}

.work-order-header-finished:hover {
  background-color: rgba(139, 186, 149, 0.50);
}

.work-order-header-cancelled:hover {
  background-color: rgba(228, 139, 139, 0.50);
}

.work-order-header-started:hover {
  background-color: rgba(237, 189, 117, 0.50);
}

.work-order-button {
  margin-left: 10px;
  margin-right: -110px;
  transition: all 0.2s ease;
}

.work-order-button-hover {
  margin-right: 10px;
  transition: all 0.2s ease;
}

.work-order-container {
}

.work-order-create-order-line-container {
  overflow: hidden;
  height: 0;
  opacity: 0;
  transition: height 200ms ease, opacity 200ms ease;
  pointer-events: none;
}

.work-order-create-order-line-container.visible {
  height: 40px; /* or auto via max-height technique */
  opacity: 1;
  pointer-events: all;
}
