import {Component, Input, OnInit, ViewChild} from '@angular/core';
import { NgbActiveModal, NgbModal, NgbTypeahead } from '@ng-bootstrap/ng-bootstrap';
import {
  PriceRuleGroupResponse,
  ProductResponse
} from "../../../../../../@shared/models/product.interfaces";
import {ProductService} from "../../../../../../@shared/services/product.service";
import {OrderService} from "../../../../../../@shared/services/order.service";
import {OrderLineResponse, PriceRuleResponse, WorkOrderResponse} from "../../../../../../@shared/models/order.interfaces";
import {displayDate, UtilsService} from "../../../../../../@core/utils/utils.service";
import {TranslateModule} from "@ngx-translate/core";
import {SelectoriniComponent} from "../../../../../../@shared/components/selectorini/selectorini.component";
import {StandardImports} from "../../../../../../@shared/global_import";

@Component({
    selector: 'app-select-work-order-modal',
    templateUrl: './select-work-order-modal.template.html',
    styleUrls: ['./select-work-order-modal.component.css'],
    standalone: true,
  imports: [StandardImports, SelectoriniComponent]
})
export class SelectWorkOrderModalComponent implements OnInit {
  selectedWorkOrder: WorkOrderResponse;
  workOrders: WorkOrderResponse[] = [];

  @ViewChild('instance', { static: true }) instance: NgbTypeahead;

  priceGroups: PriceRuleGroupResponse[] = [];

  constructor(public utilsService: UtilsService,
              public activeModal: NgbActiveModal,
              private orderService: OrderService,
              ) { }

  ngOnInit() {
    this.orderService.workOrders$.subscribe(workOrders => {
      this.workOrders = workOrders;
      if (this.workOrders.length === 1) {
        this.selectedWorkOrder = this.workOrders[0];
      }
    });
  }

  workOrderSelected(workOrder: WorkOrderResponse | any) {
    this.selectedWorkOrder = workOrder;
  }

  selectWorkOrder() {
    this.activeModal.close(this.selectedWorkOrder);
  }

  protected readonly displayDate = displayDate;
}
