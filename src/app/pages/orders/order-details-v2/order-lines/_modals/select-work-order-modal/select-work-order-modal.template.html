<div class="modal-header d-flex justify-content-between align-items-center">
  <i class="fa-regular fa-xmark fa-xl" (click)="activeModal.close()" style="font-weight: 400; cursor: pointer"></i>
  <h4 class="text-center" style="flex-grow: 1;">{{ "orderDetails.orderLines.connectToWorkOrder" | translate }}</h4>
</div>

<div class="modal-body p-3">
  <label>{{"orderDetails.orderLines.connectToWorkOrder.select" | translate}}</label>
        <app-selectorini
            [directSelection]="true"
            [disableFocusOnLoad]="true"
            [customNgTemplate]="jobItem"
            [selectedItem]="selectedWorkOrder"
            [placeholderTranslationKey]="'orderDetails.orderLines.connectToWorkOrder.select'"
            [predefinedSearchResults]="workOrders"
            [searchMainDisplayKeys]="['work_order_number', 'work_order_title']"
            (itemSelectedEmitter)="workOrderSelected($event)"
          ></app-selectorini>
</div>

<div class="modal-footer pe-3">
  <button type="submit" class="btn btn-primary" (click)="selectWorkOrder()">
    {{ "common.select" | translate }}
  </button>
</div>

<ng-template #jobItem let-item="item">
  <div class="d-flex col align-items-center justify-content-between px-2">
    <div class="">
      <div style="line-height: 12px;">{{'# ' + item.work_order_number}}{{item.work_order_title ? ' - ' + item.work_order_title : ''}}</div>
      <div class="text-muted font-12">{{displayDate(item.execution_at)}}</div>
    </div>
    <div class="d-flex align-items-center">
      <i *ngIf="item['__selected__']" class="fa-regular fa-check text-success fa-lg"></i>
    </div>
  </div>
</ng-template>
