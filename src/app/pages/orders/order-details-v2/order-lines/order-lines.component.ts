import {AfterViewInit, ChangeDetectorRef, Component, DestroyRef, EventEmitter, inject, Input, OnChanges, OnInit, Output, QueryList, SimpleChanges, ViewChild, ViewChildren} from '@angular/core';
import {DetailsViewSettings, OrderLineResponse, OrderLineRow, OrderResponse, WorkOrderCompactResponse, WorkOrderResponse} from "../../../../@shared/models/order.interfaces";
import {NgbModal, NgbPopover} from "@ng-bootstrap/ng-bootstrap";
import {OrderService} from "../../../../@shared/services/order.service";
import {ToastService} from "../../../../@core/services/toast.service";
import {currencyFormat, formatDateDMY, formatTimeHM, UtilsService, workOrderBadgeStatus} from "../../../../@core/utils/utils.service";
import {BehaviorSubject, firstValueFrom, forkJoin, Observable, Subject} from "rxjs";
import {VerifyPopupModal} from "../../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {TranslateService} from "@ngx-translate/core";
import {OrderLineRowComponent} from "./components/order-line-row/order-line-row.component";
import {CreateOrderLineRowComponent} from "./components/create-order-line-row/create-order-line-row.component";
import {OrderLinesSummaryComponent} from "../order-lines-summary/order-lines-summary-component";
import {OrderPaymentResponse} from "../../../../@shared/models/payment.interfaces";
import {takeUntil} from "rxjs/operators";
import {_CRM_ORD_12, _CRM_ORD_31, _CRM_TMP_31} from "../../../../@shared/models/input.interfaces";
import {WorkOrderTemplateResponse} from "../../../../@shared/models/templates.interfaces";
import {TemplateService} from "../../../../@shared/services/templates.service";
import {SelectWorkOrderModalComponent} from "./_modals/select-work-order-modal/select-work-order-modal.component";
import {CdkDragDrop, CdkDropList, CdkDropListGroup, moveItemInArray, transferArrayItem} from "@angular/cdk/drag-drop";
import {OrderLineDiscountModalComponent} from "./_modals/order-line-discount-modal/order-line-discount-modal.component";
import {StorageService} from "../../../../@core/services/storage.service";
import {StandardImports} from "../../../../@shared/global_import";
import {PaymentService} from "../../../../@shared/services/payment.service";
import {formatCurrency} from "@angular/common";
import {SpinnerComponent} from "../../../../@shared/components/spinner/spinner.component";
import {TimeTrackingResponse} from "../../../../@shared/models/timetracking.interfaces";
import {InternalUserResponse} from "../../../../@shared/models/user.interfaces";
import {TimeTrackingDetailsModal} from "../../../salary/_modals/time-tracking-details-modal/time-tracking-details-modal";
import {takeUntilDestroyed} from "@angular/core/rxjs-interop";
import {ButtonDoubleComponent} from "../../../../@shared/components/button-double/button-double.component";

export interface WorkOrderMapEntry {
  workOrder: WorkOrderResponse;
  workOrderLoaded: boolean;
  orderLines: OrderLineRow[];
}

@Component({
  selector: 'app-order-lines',
  templateUrl: './order-lines.component.html',
  styleUrls: ['./order-lines.component.css'],
  standalone: true,
  imports: [StandardImports, CdkDropList, OrderLineRowComponent, CreateOrderLineRowComponent, OrderLinesSummaryComponent, CdkDropListGroup, SpinnerComponent, NgbPopover, ButtonDoubleComponent]
})
export class OrderLinesComponent implements OnInit, AfterViewInit, OnChanges {
  @Input() payment?: OrderPaymentResponse;
  @Input() viewSettings: DetailsViewSettings = {};
  @Input() orderLinesSubject: BehaviorSubject<OrderLineRow[]> = new BehaviorSubject<OrderLineRow[]>([]);
  @Input() order?: OrderResponse;
  @Input() workOrder?: WorkOrderResponse;
  @Input() workOrderTemplate?: WorkOrderTemplateResponse;
  @Input() loading: boolean = false;
  @Output() orderLineAdded: EventEmitter<OrderLineRow> = new EventEmitter<OrderLineRow>();
  @Input() expanded: boolean = true;

  orderLines: OrderLineRow[] = [];
  unfilteredOrderLines: OrderLineRow[] = [];
  unMappedOrderLines: OrderLineRow[] = [];

  createOrderLineActive = false;
  noCheckedOrderLines = true;
  allChecked = false;
  editActiveOrderLineId: number | null = null;
  paymentDisabledTooltip: string;
  noOrderLinesAvailableForPayment: boolean = false;
  noOrderLinesAvailableForWorkOrderConnection: boolean = false;
  createPaymentDisabled: boolean = false;
  randomAccordionId = Math.floor(Math.random() * 1000).toString();
  orderLinesTotal = 0;
  connectedToWorkOrderWithFutureChildren = false;
  operateExVat = false;
  workOrderMap: Map<number, WorkOrderMapEntry> = new Map<number, WorkOrderMapEntry>();
  workOrderIdLoading: number | null = null;
  workOrderIdHeaderHover: number | null = null;
  workOrderIdContainerHover: Array<number> = [];
  workOrderIdCreateOrderLine: number | null = null;
  noWorkOrders: boolean = true;
  hoverTimeout: ReturnType<typeof setTimeout>;
  selectedFilter: 'all' | 'finished' | 'payment' = 'all';

  private destroy$ = new Subject<void>();
  private destroyRef = inject(DestroyRef);

  @ViewChildren(OrderLineRowComponent) orderLineRows: QueryList<OrderLineRowComponent>;

  constructor(private modalService: NgbModal, protected orderService: OrderService, private toastService: ToastService, public utilsService: UtilsService, private translate: TranslateService, private cdr: ChangeDetectorRef, private templateService: TemplateService, private storageService: StorageService, private paymentService: PaymentService) {
  }

  ngOnInit() {
    this.storageService.operateExVat$.subscribe((res) => {
      this.operateExVat = res;
    });

    this.orderLinesSubject.pipe(takeUntil(this.destroy$)).subscribe(orderLines => {
        this.initiateOrderLines(orderLines);
        this.cdr.detectChanges();
      });

    if (!this.viewSettings) {
      this.viewSettings = {
        repeatingPlaceholderView: false,
        repeatingView: false,
        createView: false,
        modalView: false,
      };
    }

    if (this.workOrder && this.workOrder.schedule && this.workOrder.schedule.num_unstarted_work_orders! > 0) {
      this.connectedToWorkOrderWithFutureChildren = true;
    }
  }

  ngAfterViewInit() {
  }


  ngOnChanges(changes: SimpleChanges) {
    if (changes['orderLinesSubject']) {
      this.destroy$.next();
      this.orderLinesSubject.pipe(takeUntil(this.destroy$)).subscribe(orderLines => {
        this.initiateOrderLines(orderLines);
      });
    }

    if (changes['order']) {
      this.updatePaymentDisabled();
    }
  }

  toggleExpand() {
    this.expanded = !this.expanded;
    if (this.expanded && this.orderLines.length == 0) {
      this.activateCreateOrderLine(null);
    }
  }

  activateCreateOrderLine(workOrderId: number | null) {
    this.createOrderLineActive = true;
    this.editActiveOrderLineId = -1;
    this.workOrderIdCreateOrderLine = workOrderId;
  }

  updatePaymentDisabled() {
    this.createPaymentDisabled = this.noOrderLinesAvailableForPayment || !this.order?.payment_recipient || this.editActiveOrderLineId !== null || (this.order?.order_status_id == 8) || (!!this.viewSettings.workOrderView && !!this.workOrder?.payment);

    if (!this.order?.payment_recipient && (this.order ? this.order.order_status_id : 0) < 2) {
      this.paymentDisabledTooltip = this.translate.instant('orderDetails.orderLines.paymentDisabledTooltip.noPaymentRecipientAndNotConfirmed');
    } else if ((this.order ? this.order.order_status_id : 0) < 2) {
      this.paymentDisabledTooltip = this.translate.instant('orderDetails.orderLines.paymentDisabledTooltip.notConfirmed')
    } else if (!this.order?.payment_recipient) {
      this.paymentDisabledTooltip = this.translate.instant('orderDetails.orderLines.paymentDisabledTooltip.noPaymentRecipient');
    } else if (this.noOrderLinesAvailableForPayment) {
      this.paymentDisabledTooltip = this.translate.instant('orderDetails.orderLines.paymentDisabledTooltip.noOrderLines');
    } else if (this.order?.order_status_id == 8) {
      this.paymentDisabledTooltip = this.translate.instant('orderDetails.orderLines.paymentDisabledTooltip.orderCancelled');
    } else if (!!this.viewSettings.workOrderView && !!this.workOrder?.payment) {
      this.paymentDisabledTooltip = this.translate.instant('orderDetails.orderLines.paymentDisabledTooltip.workOrderHasPayment');
    }

  }


  initiateOrderLines(orderLines: OrderLineRow[]) {
    this.noOrderLinesAvailableForPayment = true;
    this.noOrderLinesAvailableForWorkOrderConnection = true;
    this.noCheckedOrderLines = true;
    this.orderLinesTotal = 0;
    this.orderLines = [];
    this.unMappedOrderLines = [];
    this.workOrderMap.clear();
    this.allChecked = false;
    this.createOrderLineActive = false;
    this.editActiveOrderLineId = null;
    this.noWorkOrders = true;

    let checkedOrderLines = [];

    // Map based on payment status
    for (const orderLine of orderLines) {
      if (this.viewSettings.orderDetailsOrderLinesView && orderLine.hide_in_order_lines_list) {
        continue;
      }

      if (orderLine.work_order_id && !this.workOrderMap.has(orderLine.work_order_id)) {
        this.workOrderMap.set(
          orderLine.work_order_id, {
            workOrder: {
              work_order_id: orderLine.work_order_id,
              work_order_number: orderLine.work_order_number,
              work_order_title: orderLine.work_order_title,
              execution_at: orderLine.work_order_execution_at,
              work_order_status_id: orderLine.work_order_status_id,
              work_order_status_name: orderLine.work_order_status_name,
            } as WorkOrderResponse,
            workOrderLoaded: false,
            orderLines: []
          });
      }

      if (orderLine.payment_id && this.viewSettings.workOrderView) {
        this.createPaymentDisabled = true;
      }

      this.orderLines.push(orderLine);
      if (orderLine.work_order_id != null) {
        this.workOrderMap.get(orderLine.work_order_id)!.orderLines.push(orderLine);
        this.noWorkOrders = false;
      } else {
        this.unMappedOrderLines.push(orderLine);
      }

      this.orderLinesTotal += this.operateExVat ? orderLine.gross_total_price_ex_vat : orderLine.gross_total_price_inc_vat;
      if (orderLine.checked) {
        checkedOrderLines.push(orderLine);
      }

      if (orderLine.payment_id === null) {
        this.noOrderLinesAvailableForPayment = false;
      }

      if (orderLine.work_order_id === null) {
        this.noOrderLinesAvailableForWorkOrderConnection = false;
      }

    }

    if (!this.viewSettings.repeatingView && this.orderLines.length > 0) {
      this.expanded = true;
    }

    if (checkedOrderLines.length == this.orderLines.length && checkedOrderLines.length > 0) {
      this.allChecked = true;
    }

    if (checkedOrderLines.length > 0) {
      this.noCheckedOrderLines = false;
    }

    if (this.selectedFilter === 'all') {
      this.unfilteredOrderLines = [...this.orderLines];
    }

    this.updatePaymentDisabled();
  }

  orderLineInEditMode(orderLineId: number | null) {
    this.editActiveOrderLineId = orderLineId;

    this.updatePaymentDisabled();

    let rows = this.orderLineRows.toArray();
    if (orderLineId === null) {
      for (const row of rows) {
        row.noBottomBorder = false;
      }
    }

    let editRowIndex = rows.findIndex(row => row.orderLine.order_line_id === orderLineId);
    if ([-1, 0].includes(editRowIndex)) {
      return;
    } else {
      rows[editRowIndex - 1].noBottomBorder = true;
    }
  }

  checkAll() {
    // If all order lines are checked, uncheck all
    if (this.allChecked) {
      this.orderLinesSubject.value.forEach(orderLine => {
        orderLine.checked = false;
      });
      this.allChecked = false;
    } else {
      this.orderLinesSubject.value.forEach(orderLine => {
        if (!orderLine.locked && !((this.viewSettings.orderDetailsOrderLinesView || this.viewSettings.workOrderView) && orderLine.payment_id)) {
          orderLine.checked = true;
        }
      });
      this.noCheckedOrderLines = this.orderLinesSubject.value.filter(orderLine => !orderLine.locked && !orderLine.payment_id).length === 0;
    }
    this.orderLinesSubject.next(this.orderLinesSubject.value);
  }

  async createPayment(consolidated: boolean = false) {
    if (this.createPaymentDisabled) {
      return;
    }
    const { PaymentDetailsV2Component } = await import('src/app/pages/payments/components/payment-details-v2/payment-details-v2.component');
    let modalRef = this.modalService.open(PaymentDetailsV2Component, {size: 'lg'});
    if (this.noCheckedOrderLines) {
      modalRef.componentInstance.orderLinesSubject = new BehaviorSubject<OrderLineRow[]>([...this.orderLines]);
    } else {
      modalRef.componentInstance.orderLinesSubject = new BehaviorSubject<OrderLineRow[]>(this.orderLinesSubject.value.filter(orderLine => orderLine.checked));
    }
    modalRef.componentInstance.order = this.order;
    modalRef.componentInstance.lockedForSingle = true;
    modalRef.componentInstance.viewSettings = {paymentView: true, createView: true, repeatingView: false, orderDetailsView: true, modalView: true, consolidatedInvoiceView: consolidated};
    modalRef.componentInstance.workOrder = this.workOrder;
  }

  getCheckedOrderLines() {
    return this.orderLinesSubject.value.filter(orderLine => orderLine.checked);
  }

  openDiscountModal() {
    let modalRef = this.modalService.open(OrderLineDiscountModalComponent);
    modalRef.componentInstance.orderLineIds = this.getCheckedOrderLines().map(orderLine => orderLine.order_line_id);
    modalRef.componentInstance.orderId = this.order?.order_id!;
    modalRef.result.then((res: OrderResponse | false) => {
      if (res) {
        this.orderService.refreshOrder(res, 'orderLineDiscount');
      }
    });
  }

  orderLineDropped(event: CdkDragDrop<OrderLineRow[]>, targetWorkOrderId: number | null) {
    const sourceList: OrderLineRow[] = event.previousContainer.data;
    const targetList: OrderLineRow[] = event.container.data;

    if (event.previousContainer === event.container) {
      // Reordering within the same list
      moveItemInArray(targetList, event.previousIndex, event.currentIndex);
    } else {
      // Moving between different lists
      transferArrayItem(
        sourceList,
        targetList,
        event.previousIndex,
        event.currentIndex
      );
    }
    let payload: _CRM_ORD_31 = {
      order_line_id: event.item.data.order_line_id,
      index: event.currentIndex,
      work_order_id: targetWorkOrderId
    }
    this.orderService.updateOrderLineAsCompany(payload).subscribe((res) => {

    });
  }

  deleteOrderLines() {
    if (this.noCheckedOrderLines || this.editActiveOrderLineId !== null || this.createOrderLineActive) {
      return;
    }
    if (this.getCheckedOrderLines().length === 0) {
      return;
    }
    let modalRef = this.modalService.open(VerifyPopupModal);
    modalRef.result.then((result: boolean) => {
      if (result) {
        let orderLineWorkOrderIds: number[] = []
        let orderLineIdsToDelete = this.getCheckedOrderLines().map(orderLine => {
          if (orderLine.work_order_id) {
            orderLineWorkOrderIds.push(orderLine.work_order_id);
          }
          return orderLine.order_line_id
        });
        const requests = orderLineIdsToDelete.map(orderLineId => this.orderService.deleteOrderLineAsCompany(orderLineId));
        forkJoin(requests).subscribe(() => {
          for (const workOrderId of orderLineWorkOrderIds) {
            this.orderService.fetchAndRefreshSingleWorkOrder(workOrderId, 'orderLinesDeleteOrderLines');
          }
          this.toastService.successToast('deleted')
          this.orderLinesSubject.next(this.orderLinesSubject.value.filter(orderLine => !orderLineIdsToDelete.includes(orderLine.order_line_id)));
          if (this.viewSettings.repeatingView && !this.viewSettings.createView) {
            this.orderService.fetchAndRefreshOrderPaymentSchedules(this.payment?.order_id!);
          }
        });
      }
    });
  }

  async createOrderLine(input: {payload: _CRM_ORD_12, fetchOrder: boolean}, addDirectlyToOrder: boolean) {
    console.log('workOrders', this.workOrderEntries)
    if (this.workOrder) {
      input.payload.work_order_id = this.workOrder.work_order_id;
      input.payload.template = this.workOrder.schedule_template;
    } else if (!this.workOrderTemplate && !this.workOrder) {
      input.payload.work_order_id = this.workOrderIdCreateOrderLine;
    }

    if (addDirectlyToOrder && !this.workOrderTemplate && !this.workOrder) {
      input.payload.add_directly_to_order = true;
    }

    // If created for work order template, add order line to template and return
    if (this.workOrderTemplate) {
      let payload = input.payload as _CRM_TMP_31;
      payload.template_id = this.workOrderTemplate.template_id;
      this.loading = true;
      this.templateService.addOrderLineToWorkOrderTemplate(payload).subscribe((orderLine) => {
        this.orderLinesSubject.next([...this.orderLinesSubject.value, {...orderLine, checked: true}]);
        this.createOrderLineActive = false;
        this.editActiveOrderLineId = null;
        this.loading = false;
        this.workOrderIdCreateOrderLine = null;
      });
      return;
    }

    input.payload.order_id = this.order?.order_id! || this.payment?.order_id!;

    if (this.connectedToWorkOrderWithFutureChildren) {
      let modalRef = this.modalService.open(VerifyPopupModal, {backdrop: 'static'});
      modalRef.componentInstance.showBody = true;
      modalRef.componentInstance.titleTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.title';
      modalRef.componentInstance.bodyBoldTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyBoldTranslationKey';
      modalRef.componentInstance.bodyMutedTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyMutedTranslationKey';
      modalRef.componentInstance.bodyRegularTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.orderLineCreation';
      try {
        input.payload.update_upcoming_work_orders = await modalRef.result;
      } catch (e) {}
    }

    this.loading = true;
    this.orderService.addOrderLineAsCompany(input.payload).subscribe((orderLine) => {
      this.editActiveOrderLineId = null;
      this.createOrderLineActive = false;
      this.orderLineAdded.emit(orderLine as OrderLineRow);
      this.orderLinesSubject.next([...this.orderLinesSubject.value, {...orderLine, checked: false}]);
      this.toastService.successToast('created');
      this.loading = false;
      this.workOrderIdCreateOrderLine = null;
      if (!this.viewSettings.orderDetailsOrderLinesView) {
        this.orderService.refreshOrderLineInOrderLines(orderLine, 'post')
      }
      if (input.fetchOrder) {
        this.orderService.fetchAndRefreshOrder(this.order?.order_id || this.payment?.order_id!, 'orderLineCreation');
      }
      if (this.payment && this.viewSettings.repeatingView && !this.viewSettings.createView) {
        this.orderService.fetchAndRefreshOrderPaymentSchedules(this.payment.order_id!);
      }
    }, (error) => {
      this.loading = false;
      this.editActiveOrderLineId = null;
      this.createOrderLineActive = false;
      this.workOrderIdCreateOrderLine = null;
    });
  }

  connectOrderLinesToWorkOrder() {
    if (this.noOrderLinesAvailableForWorkOrderConnection && this.viewSettings.orderDetailsOrderLinesView) return;
    let requests: Observable<OrderLineResponse>[] = [];

    // Unlink
    if (this.workOrder && this.viewSettings.workOrderView) {
      this.loading = true;
      requests = this.getCheckedOrderLines().map(orderLine => {
        let payload: _CRM_ORD_31 = {
          order_line_id: orderLine.order_line_id,
          work_order_id: null
        };
        return this.orderService.updateOrderLineAsCompany(payload)
      });

      forkJoin(requests).subscribe(() => {
        this.toastService.successToast('updated');
        this.loading = false;
        if (this.order) {
          this.orderService.fetchAndRefreshOrderLines(this.order.order_id, 'orderLineWorkOrderUnlinkage');
          this.orderService.fetchAndRefreshOrder(this.order?.order_id || this.payment?.order_id!, 'orderLineLinking');
        }
        this.orderService.getWorkOrderById(this.workOrder!.work_order_id).subscribe(workOrder => {
        });
      });

      }

    // Link
    else {
      let modalRef = this.modalService.open(SelectWorkOrderModalComponent);
      modalRef.result.then((workOrder: WorkOrderResponse | null) => {
        if (workOrder) {
          this.loading = true;
          requests = this.getCheckedOrderLines().map(orderLine => {
            let payload: _CRM_ORD_31 = {
              order_line_id: orderLine.order_line_id,
              work_order_id: workOrder.work_order_id
            };
            return this.orderService.updateOrderLineAsCompany(payload)
          });
          forkJoin(requests).subscribe(() => {
            this.toastService.successToast('updated');
            this.loading = false;
            this.orderService.fetchAndRefreshOrder(this.order?.order_id || this.payment?.order_id!, 'orderLineLinking');
            if (this.order) {
              this.orderService.fetchAndRefreshOrderLines(this.order.order_id, 'orderLineWorkOrderLinkage');
            }
          });
        } else {
          return;
        }
      });
    }
  }

  loadWorkOrderIntoMap(workOrderId: number) {
    if (this.workOrderMap.get(workOrderId)?.workOrderLoaded) return;
    this.workOrderIdLoading = workOrderId;
    this.orderService.getWorkOrderById(workOrderId).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((workOrder) => {
      this.workOrderIdLoading = null;
      this.workOrderMap.set(workOrderId, {workOrder: workOrder, orderLines: this.workOrderMap.get(workOrderId)!.orderLines, workOrderLoaded: true});
      this.cdr.detectChanges();
    });
  }

  get workOrderEntries(): WorkOrderMapEntry[] {
    return Array.from(this.workOrderMap.values()).sort((a, b) => {
      if (a.workOrder.execution_at && b.workOrder.execution_at) {
        return a.workOrder.execution_at.getTime() - b.workOrder.execution_at.getTime();
      } else if (a.workOrder.execution_at) {
        return -1;
      } else if (b.workOrder.execution_at) {
        return 1;
      } else {
        return a.workOrder.work_order_id - b.workOrder.work_order_id;
      }
    });
  }

  getTotalSecondsFromTo(workOrder: WorkOrderResponse): number {
    let startDate = new Date();
    let endDate = new Date(0);
    let endDateSet = false;
    for (const userTtr of workOrder.user_time_tracking_response) {
      for (const ttr of userTtr.time_trackings) {
        if (ttr.started_at < startDate) {
          startDate = ttr.started_at;
        }
        if (ttr.stopped_at > endDate) {
          endDate = ttr.stopped_at;
          endDateSet = true;
        }
      }
    }
    if (!endDateSet) {
      endDate = new Date();
    }
    return Math.floor((endDate.getTime() - startDate.getTime()) / 1000);
  }

  timeTrackingClicked(workOrderId: number, row: TimeTrackingResponse, user: InternalUserResponse, event: MouseEvent) {
    let modalRef = this.modalService.open(TimeTrackingDetailsModal, {size: 'lg'});
    modalRef.componentInstance.timeTracking = row;
    modalRef.componentInstance.user = user;
    modalRef.componentInstance.timeTrackingUpdated.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((updatedTimeTracking: TimeTrackingResponse) => {
      this.workOrderIdLoading = workOrderId;
      this.orderService.getWorkOrderById(workOrderId).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((workOrder) => {
        this.workOrderMap.set(workOrderId, {workOrder: workOrder, orderLines: this.workOrderMap.get(workOrderId)!.orderLines, workOrderLoaded: true});
        this.workOrderIdLoading = null;
      });
    });
  }

  async openWorkOrder(workOrderId: number, event: MouseEvent) {
    event.stopPropagation();
    const { WorkOrderDetailsComponent } = await import('src/app/pages/work-orders/components/work-order-details/work-order-details.component');
    let modalRef = this.modalService.open(WorkOrderDetailsComponent, {size: 'xl'});
    modalRef.componentInstance.workOrderId = workOrderId;
    modalRef.componentInstance.viewSettings = {
      modalView: true,
      workOrderView: true,
      collapsedOrderLines: true,
    }
  }

  workOrderMouseEnter(popover: NgbPopover, mapEntry: WorkOrderMapEntry) {
    this.hoverTimeout = setTimeout(() => {
      this.workOrderIdHeaderHover = mapEntry.workOrder.work_order_id;
      if (!mapEntry.workOrderLoaded && this.workOrderIdLoading !== mapEntry.workOrder.work_order_id) {
        this.loadWorkOrderIntoMap(mapEntry.workOrder.work_order_id);
      }
      popover.open();
    }, 300);
  }

  workOrderMouseLeave(popover: NgbPopover, workOrderId: number) {
    clearTimeout(this.hoverTimeout);
    this.workOrderIdHeaderHover = null;
    popover.close();
  }

  containerMouseEnter(workOrderId: number) {
    this.workOrderIdContainerHover.push(workOrderId);
  }

  containerMouseLeave(workOrderId: number) {

  }

  filterOrderLines(filter: 'all' | 'finished' | 'payment') {
    this.selectedFilter = filter;
    if (filter === 'all') {
      this.orderLinesSubject.next(this.unfilteredOrderLines);
    } else if (filter === 'finished') {
      this.orderLinesSubject.next(this.unfilteredOrderLines.filter(orderLine => orderLine.work_order_id !== null && orderLine.work_order_status_id === 2));
    } else if (filter === 'payment') {
      this.orderLinesSubject.next(this.unfilteredOrderLines.filter(orderLine => orderLine.payment_id));
    }
  }

  createOrderLineCancelled() {
    this.createOrderLineActive = false;
    this.editActiveOrderLineId = null;
    this.workOrderIdCreateOrderLine = null;
  }



  protected readonly currencyFormat = currencyFormat;
  protected readonly workOrderBadgeStatus = workOrderBadgeStatus;
  protected readonly formatCurrency = formatCurrency;
  protected readonly formatDateDMY = formatDateDMY;
  protected readonly formatTimeHM = formatTimeHM;
  protected readonly onmouseenter = onmouseenter;
}
