<div class="card order-lines-card">
  <div class="card-body p-0">
    <div *ngIf="!expanded" class="d-flex justify-content-between align-items-center cursor-pointer order-details-header" [ngClass]="{'compact': viewSettings.compactView}" data-bs-toggle="collapse" [attr.data-bs-target]="'#orderLines' + randomAccordionId" (click)="toggleExpand()">
      <div class="d-flex justify-content-center align-items-center">
        <div class="d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; border-radius: 6px; background-color: #e1dddd;">
          <i class="fa-regular fa-list fa-xl"></i>
        </div>
        <div class="order-details-header-title">{{ "orderDetails.orderLines.title" | translate }} <span *ngIf="workOrder?.schedule_template">for jobb-plan</span></div>
      </div>
      <div class="d-flex align-items-center py-1">
        <span class="text-muted pe-3">{{orderLines.length}} {{"orderDetails.orderLines.orderLines." + (orderLines.length !== 1 ? 'multiple' : 'single') | translate}}</span>
        <span class="fw-bold">{{"orderDetails.orderLines.total" | translate}}: {{currencyFormat(orderLinesTotal)}}</span>
        <div type="button" class="d-flex align-items-center justify-content-center cursor-pointer p-2" style="min-width: 39px;">
          <i class="fa-regular fa-chevron-right chevron-rotate fa-lg" [ngClass]="{'expanded': expanded}"></i>
        </div>
      </div>
    </div>
    <div *ngIf="expanded" class="d-flex justify-content-between align-items-center order-details-header" [ngClass]="{'compact': viewSettings.compactView}">
      <div class="d-flex flex-column">
        <div class="d-flex align-items-center">
          <div class="order-details-header-title">{{ "orderDetails.orderLines.title" | translate }}</div>
          <app-spinner class="ms-1" *ngIf="loading"></app-spinner>
          <span *ngIf="viewSettings.compactView" class="order-details-sub-title text-muted ms-2" style="font-weight: 100; font-size: 12px;">{{ (operateExVat ? 'orderDetails.orderLines.pricesExVat' : 'orderDetails.orderLines.pricesIncVat') | translate }}</span>
        </div>
        <div *ngIf="!viewSettings.compactView" class="d-flex gap-2 align-items-center">
          <app-button-double
            *ngIf="viewSettings.orderDetailsOrderLinesView"
            [small]="true"
            [useMaxWidth]="true"
            [leftButtonActive]="selectedFilter === 'all'"
            [rightButtonActive]="selectedFilter === 'finished'"
            [leftButtonTranslationKey]="'Alle'"
            [rightButtonTranslationKey]="'Fullførte'"
            (leftButtonClick)="filterOrderLines('all')"
            (rightButtonClick)="filterOrderLines('finished')"
          ></app-button-double>
          <div *ngIf="!viewSettings.compactView" class="order-details-sub-title m-0" style="font-weight: 100;">{{ (operateExVat ? 'orderDetails.orderLines.pricesExVat' : 'orderDetails.orderLines.pricesIncVat') | translate  }}</div>
        </div>
      </div>
      <div class="d-flex">
<!--        <div *ngIf="noWorkOrders" class="me-2">-->
<!--          <app-button-->
<!--            *ngIf="viewSettings.orderDetailsOrderLinesView"-->
<!--            [disabled]="noCheckedOrderLines || (noOrderLinesAvailableForWorkOrderConnection && viewSettings.orderDetailsOrderLinesView) || editActiveOrderLineId !== null || createOrderLineActive"-->
<!--            [buttonType]="'nude'"-->
<!--            [small]="true"-->
<!--            [translationKey]="(viewSettings.workOrderView ? 'orderDetails.orderLines.connectToWorkOrder.disconnect' : 'orderDetails.orderLines.connectToWorkOrder') | translate"-->
<!--            (buttonClick)="connectOrderLinesToWorkOrder()"-->
<!--          ></app-button>-->
<!--        </div>-->
        <div class="d-flex me-2">
          <app-button
            *ngIf="!payment && !viewSettings.createView && expanded && !viewSettings.workOrderTemplateView && !viewSettings.compactView && !(viewSettings.repeatingView && workOrder)"
            [ngbTooltip]="createPaymentDisabled ? paymentDisabledTooltip : null"
            [disabled]="createPaymentDisabled || editActiveOrderLineId !== null || createOrderLineActive"
            [small]="true"
            [customClass]="viewSettings.repeatingView ? '' : 'fw-bold no-right-border-radius'"
            [translationKey]="'Opprett faktura'"
            (buttonClick)="createPayment()"
          ></app-button>

          <app-button
            *ngIf="!viewSettings.repeatingView && viewSettings.orderDetailsOrderLinesView"
            [customClass]="'no-left-border-radius'"
            [buttonType]="'dropdown-caret'"
            [buttonMaxWidth]="30"
            [small]="true"
            [disabled]="createPaymentDisabled || editActiveOrderLineId !== null || createOrderLineActive"
            type="button"
            [loading]="loading"
            data-bs-toggle="dropdown"
            [showDropdownChevron]="true"
          ></app-button>

          <div class="dropdown-menu">
            <app-button
              [translationKey]="'Legg til i samlefaktura'"
              [buttonType]="'dropdown'"
              [small]="true"
              (buttonClick)="createPayment(true)"
            ></app-button>
    </div>
        </div>
        <app-button
          *ngIf="!payment?.payment_sent_at && expanded"
          [disabled]="noCheckedOrderLines || editActiveOrderLineId !== null || createOrderLineActive"
          [buttonType]="'nude'"
          [small]="true"
          [iconClass]="'fa-regular fa-trash-alt'"
          (buttonClick)="deleteOrderLines()"
        ></app-button>

        <div type="button" class="d-flex align-items-center justify-content-center cursor-pointer p-2" data-bs-toggle="collapse" [attr.data-bs-target]="'#orderLines' + randomAccordionId" (click)="toggleExpand()" style="min-width: 39px;">
          <i class="fa-regular fa-chevron-right chevron-rotate fa-lg" [ngClass]="{'expanded': expanded}"></i>
        </div>
      </div>
    </div>

    <!--  Headers  -->
    <div [id]="'orderLines' + randomAccordionId" class="accordion-collapse collapse" [ngClass]="{'show': expanded}">
      <div class="accordion-body">
        <div class="order-line-column-header-setup order-line-grey border-top border-bottom fw-bold pe-3" [ngClass]="{'text-muted': editActiveOrderLineId !== null, 'no-check': payment?.payment_sent_at || viewSettings.createPaymentView, 'no-drag': viewSettings.createView || payment?.payment_sent_at}">

          <!--  Grip lines dummy div  -->
          <div *ngIf="!viewSettings.createView && !payment?.payment_sent_at"></div>

          <!-- Checkbox -->
          <div *ngIf="!payment?.payment_sent_at && !viewSettings.createPaymentView" class="form-check">
            <input class="form-check-input" type="checkbox" [checked]="allChecked" [disabled]="editActiveOrderLineId !== null" (click)="checkAll()">
          </div>

          <!-- Product/Order line name -->
          <div class="text-start" style="">{{"orderDetails.orderLines.description" | translate}}</div>

          <!-- Quantity -->
          <div class="text-start">{{"orderDetails.orderLines.quantity" | translate}}</div>

          <!-- Unit price inc vat -->
          <div class="text-end">{{"orderDetails.orderLines.unitPrice" | translate}}</div>

          <!-- Discount percentage -->
          <div class="text-end">{{"orderDetails.orderLines.discount" | translate}}</div>

          <!-- Gross total price inc vat -->
          <div class="text-end">{{"orderDetails.orderLines.total" | translate}}</div>

        </div>

        <div cdkDropListGroup>
          <!-- Work order order lines -->
          <div *ngFor="let mapEntry of workOrderEntries" class="work-order-container" cdkDropList [cdkDropListData]="mapEntry.orderLines" (cdkDropListDropped)="orderLineDropped($event, mapEntry.workOrder.work_order_id)" (mouseenter)="containerMouseEnter(mapEntry.workOrder.work_order_id)" (mouseleave)="containerMouseLeave(mapEntry.workOrder.work_order_id)">
            <div
              *ngIf="!workOrder"
              class="work-order-header cursor-pointer"
              [ngClass]="{'work-order-header-unstarted': mapEntry.workOrder.work_order_status_id === 0, 'work-order-header-started': mapEntry.workOrder.work_order_status_id === 1, 'work-order-header-finished': mapEntry.workOrder.work_order_status_id === 2, 'work-order-header-cancelled': mapEntry.workOrder.work_order_status_id === 8}"
              [ngbPopover]="workOrderPopover"
              #workOrderPopoverTrigger="ngbPopover"
              [popoverContext]="{ workOrder: mapEntry.workOrder, workOrderLoaded: mapEntry.workOrderLoaded }"
              [popoverClass]="'order-line-work-order-popover'"
              triggers="manual"
              placement="end"
              container="body"
              [attr.data-work-order-id]="mapEntry.workOrder.work_order_id"
              (click)="openWorkOrder(mapEntry.workOrder.work_order_id, $event)"
              (mouseenter)="workOrderMouseEnter(workOrderPopoverTrigger, mapEntry)"
              (mouseleave)="workOrderMouseLeave(workOrderPopoverTrigger, mapEntry.workOrder.work_order_id)"
            >
              <div id="dummyWorkOrderHeaderDiv"></div>
              <div>#{{mapEntry.workOrder.work_order_number}}</div>
              <div>{{mapEntry.workOrder.work_order_title}}</div>
              <div>{{formatDateDMY(mapEntry.workOrder.execution_at, '.') || 'Ingen utførelsesdato'}}</div>
              <div></div>
              <div class="d-flex align-items-center justify-content-end" [innerHTML]="workOrderBadgeStatus(mapEntry.workOrder)"></div>
            </div>
            <div *ngFor="let orderLine of mapEntry.orderLines; let i = index;">
              <order-line-row
                [orderLine]="orderLine"
                [order]="order"
                [orderLinesSubject]="orderLinesSubject"
                [noCheck]="!!this.payment?.payment_sent_at || !!viewSettings.createPaymentView"
                [viewSettings]="viewSettings"
                [orderLineIdEditActive]="editActiveOrderLineId"
                [connectedToWorkOrderWithFutureChildren]="!!workOrder?.schedule_template"
                (editActive)="orderLineInEditMode($event)"
              ></order-line-row>
            </div>

            <!-- Container specific CreateOrderLineButton and CreateOrderLineRow -->
            <create-order-line-row *ngIf="createOrderLineActive && workOrderIdCreateOrderLine === mapEntry.workOrder.work_order_id" [orderLinesSubject]="orderLinesSubject" [payment]="payment" [viewSettings]="viewSettings" [order]="order" (creationCancelled)="createOrderLineCancelled()" (orderLineCreated)="createOrderLine($event, false);"></create-order-line-row>
            <ng-container *ngTemplateOutlet="createOrderLineButton; context: { workOrderId: mapEntry.workOrder.work_order_id }"></ng-container>
          </div>

          <!-- Unmapped order lines -->
          <div *ngIf="!workOrder && !viewSettings.paymentView || (viewSettings.paymentView && unMappedOrderLines.length > 0)" cdkDropList [cdkDropListData]="unMappedOrderLines" (cdkDropListDropped)="orderLineDropped($event, null)">
            <div *ngIf="!noWorkOrders" class="work-order-header">
              <div id="dummyUnmappedHeaderDiv"></div>
              <div>Ordrelinjer uten tilknytning til jobb</div>
            </div>
            <div *ngFor="let orderLine of unMappedOrderLines; let i = index;">
              <order-line-row
                [orderLine]="orderLine"
                [order]="order"
                [orderLinesSubject]="orderLinesSubject"
                [noCheck]="!!this.payment?.payment_sent_at || !!viewSettings.createPaymentView"
                [viewSettings]="viewSettings"
                [orderLineIdEditActive]="editActiveOrderLineId"
                [connectedToWorkOrderWithFutureChildren]="!!workOrder?.schedule_template"
                (editActive)="orderLineInEditMode($event)"
              ></order-line-row>
            </div>

            <!-- Order line being created -->
            <create-order-line-row *ngIf="createOrderLineActive && workOrderIdCreateOrderLine == null" [orderLinesSubject]="orderLinesSubject" [payment]="payment" [viewSettings]="viewSettings" [order]="order" (creationCancelled)="createOrderLineCancelled()" (orderLineCreated)="createOrderLine($event, workOrderEntries.length > 0);"></create-order-line-row>

            <!-- Create order line button -->
            <ng-container id="createOrderLineButtonNoWorkOrder" *ngTemplateOutlet="createOrderLineButton; context: { workOrderId: null }"></ng-container>
          </div>

          <!-- Create order line for empty work order -->
          <div *ngIf="(workOrder || viewSettings.paymentView) && orderLines.length == 0">
            <create-order-line-row *ngIf="createOrderLineActive && workOrderIdCreateOrderLine == null" [orderLinesSubject]="orderLinesSubject" [payment]="payment" [viewSettings]="viewSettings" [order]="order" (creationCancelled)="createOrderLineCancelled()" (orderLineCreated)="createOrderLine($event, !workOrder);"></create-order-line-row>
            <ng-container id="createOrderLineButtonEmptyWorkOrder" *ngTemplateOutlet="createOrderLineButton; context: { workOrderId: workOrder?.work_order_id || null }"></ng-container>
          </div>

        </div>

      </div>

      <div *ngIf="!viewSettings.compactView || (orderLineRows && orderLineRows.length > 0)" class="">
        <app-order-lines-summary [compactView]="!!viewSettings.compactView" [orderLinesSubject]="orderLinesSubject" [showSetDiscount]="!!viewSettings.orderDetailsOrderLinesView && orderLines.length > 0" (setDiscountClicked)="openDiscountModal()"></app-order-lines-summary>
      </div>

    </div>
  </div>


</div>

<ng-template #createOrderLineButton let-workOrderId="workOrderId">
  <div [id]="'createOrderLineButton_' + (workOrderId ? workOrderId : 'noWorkOrder')" *ngIf="workOrderIdCreateOrderLine !== workOrderId && !payment?.payment_sent_at && !payment?.is_consolidated_invoice_container && !viewSettings.createPaymentView && order?.order_status_id != 8" [ngbTooltip]="(viewSettings.workOrderTemplateView && !workOrderTemplate) ? ('orderDetails.orderLines.disabledForUnSavedTemplate' | translate) : ''" class="">
    <button
      class="create-order-line-button"
      (click)="activateCreateOrderLine(workOrderId || null)"
      [disabled]="editActiveOrderLineId !== null || (viewSettings.workOrderTemplateView && !workOrderTemplate)"
    >
      <i class="fa-regular fa-plus me-1"></i>
      <span>{{"orderDetails.orderLines.addOrderLine" | translate}}</span>
    </button>
<!--    <app-button-->
<!--      [disabled]="editActiveOrderLineId !== null || !!(viewSettings.workOrderTemplateView && !workOrderTemplate)"-->
<!--      [small]="true"-->
<!--      [buttonType]="'nude'"-->
<!--      [iconClass]="'fa-regular fa-plus me-1'"-->
<!--      [iconPlacement]="'left'"-->
<!--      [translationKey]="'orderDetails.orderLines.addOrderLine'"-->
<!--      (buttonClick)="activateCreateOrderLine(workOrderId || null)"-->
<!--    ></app-button>-->
  </div>
</ng-template>

<ng-template #workOrderPopover let-workOrder="workOrder" let-workOrderLoaded="workOrderLoaded">
  <app-spinner *ngIf="workOrderIdLoading == workOrder.work_order_id"></app-spinner>
  <div *ngIf="workOrderLoaded">
    <div class="mb-1">
      <div class="fw-bold">#{{workOrder.work_order_number}} {{workOrder.work_order_title}} <span *ngIf="workOrder.execution_at">({{utilsService.formatDateWdDYM(workOrder.execution_at, false, false)}})</span></div>
      <div class="text-muted">{{workOrder.addresses.length > 0 ? workOrder.addresses[0].display : ('orderDetails.orderLines.woPopup.noAddress' | translate)}}</div>
    </div>
    <div *ngFor="let userTtr of workOrder.user_time_tracking_response" class="ps-1 mb-1">
      <div class="fw-bold">{{userTtr.user.full_name}}</div>
      <div *ngFor="let ttr of userTtr.time_trackings" class="d-flex ps-1">
        <div class="bullet-item"><span class="text-muted clickable-text" (click)="timeTrackingClicked(workOrder.work_order_id, ttr, userTtr.user, $event)">{{formatTimeHM(ttr.started_at)}} - {{formatTimeHM(ttr.stopped_at)}}</span> ({{utilsService.formatDurationFromSeconds(ttr.duration_in_seconds)}})</div>
      </div>
      <div class="ps-1">Totalt: {{utilsService.formatDurationFromSeconds(userTtr.duration_in_seconds)}}</div>
    </div>
    <div *ngIf="workOrder.user_time_tracking_response.length === 0">{{'orderDetails.orderLines.woPopup.noTrackings' | translate}}</div>
    <div *ngIf="workOrder.user_time_tracking_response.length > 0">
      <hr class="my-1">
      <div class="mb-1">{{'orderDetails.orderLines.woPopup.totalFromTo' | translate}}:
        <span class="fw-bold">{{utilsService.formatDurationFromSeconds(getTotalSecondsFromTo(workOrder.work_order_id))}}</span>
        <span class="ms-1">({{getTotalSecondsFromTo(workOrder.work_order_id) / 3600 | number:'1.1-2'}})</span>
      </div>
      <div>{{'orderDetails.orderLines.woPopup.totalSum' | translate}}:
        <span class="fw-bold">{{utilsService.formatDurationFromSeconds(workOrder.total_checked_in_seconds)}}</span>
        <span class="ms-1">({{workOrder.total_checked_in_seconds / 3600 | number: '1.1-2'}})</span>
      </div>
    </div>
  </div>
</ng-template>
