<div
  cdkDrag
  cdkDragLockAxis="y"
  [cdkDragDisabled]="orderLineIdEditActive !== null || editMode || locked"
  [cdkDragData]="orderLine"
  class="d-flex"
  [ngClass]="{
  'order-line-row': !editMode,
  'locked': locked,
  'cursor-pointer': !noCheck,
  'selected-order-line': editMode,
  'border-bottom': !editMode && !noBottomBorder,
  'border-top': forceTopBorder,
  'dragging': dragging,
  'shadow-sm': dragging,
  'pe-3': orderLine.work_order_id === null && !editMode,
  'pe-0': orderLine.work_order_id !== null && !editMode || editMode,
  }"
  (cdkDragEnded)="dragging = false;"
  (mouseenter)="orderLineMouseEnter()"
  (mouseleave)="orderLineMouseLeave()"
  (click)="editOrderLine()">

  <!-- Container for both main view and edit mode -->
  <div class="col py-2 position-relative">
    <div *ngIf="editMode" class="position-absolute text-muted font-12" style="right: -12px; top: 0;">ID: {{orderLine.order_line_id}}</div>

    <!-- Main row - View mode -->
    <div *ngIf="!editMode" class="order-line-column-setup" [ngClass]="{'no-check': noCheck || editMode, 'no-drag': viewSettings.createView || noCheck, 'cursor-pointer': !locked}" >
      <!--  Grip lines  -->
      <div cdkDragHandle *ngIf="!viewSettings.createView && !noCheck && orderLineIdEditActive !== orderLine.order_line_id" class="cursor-pointer" style="z-index: 1;" [ngClass]="{'dragging': dragging}" (mousedown)="dragging = orderLineIdEditActive === null;" (mouseup)="dragging = false;" (click)="openPayment(); $event.stopPropagation();">
        <i class="fa-regular" [ngClass]="locked ? 'fa-lock' : 'fa-grip-lines'" placement="end" [ngbTooltip]="locked ? ('orderDetails.orderLines.lockedTooltip' | translate) : null"></i>
      </div>

      <!-- Checkbox -->
      <div *ngIf="!noCheck && orderLineIdEditActive !== orderLine.order_line_id" class="form-check">
        <input class="form-check-input" type="checkbox" [disabled]="locked || orderLineIdEditActive !== null" [ngClass]="{'check-disabled': locked}" [checked]="orderLine.checked" (click)="checkOrderLine()">
      </div>

      <!-- Product/Order line name -->
      <div class="text-start">
        <i *ngIf="orderLine.track_time" class="fa-regular fa-clock me-1" [ngbTooltip]="'orderDetails.orderLines.trackTimeTooltip' | translate"></i>
        {{orderLine.order_line_name}}
      </div>

      <!-- Quantity -->
      <div class="text-start" [ngClass]="{'cursor-pointer': !viewSettings.createView || viewSettings.repeatingPlaceholderView}">{{orderLine.quantity}} {{orderLine.unit_abbreviation}}</div>

      <!-- Unit price -->
      <div class="text-end" [ngClass]="{'strikethrough': totalPricePriceRuleActive, 'cursor-pointer': !viewSettings.createView || viewSettings.repeatingPlaceholderView}">{{currencyFormat(operateExVat ? orderLine.unit_price_ex_vat : orderLine.unit_price_inc_vat)}}</div>

      <!-- Discount percentage -->
      <div class="text-end" [ngClass]="{'cursor-pointer': !viewSettings.createView || viewSettings.repeatingPlaceholderView}">{{orderLine.discount_percentage ? orderLine.discount_percentage.toString() + '%' : '0%'}}</div>

      <!-- Gross total price inc vat -->
      <div class="text-end fw-bold" [ngbTooltip]="orderLine.payment_id ? (orderLine.vat_rate + '% ' + ('common.vat' | translate)) : ''">{{currencyFormat(operateExVat ? orderLine.gross_total_price_ex_vat : orderLine.gross_total_price_inc_vat)}}</div>
    </div>

      <!-- Main row - Edit mode -->
    <div *ngIf="editMode">

      <div class="edit-order-line-column-setup">
        <!-- Product/Order line name -->
        <div class="">
          <label class="">{{"orderDetails.orderLines.description" | translate}}</label>
          <app-input
            [type]="'text'"
            [inputFieldMinWidthPx]="275"
            [control]="nameControl"
            [editMode]="true"
            [showErrorIcon]="true"
            [showErrorMessage]="false"
            (keydown)="onInputFieldKeyPress($event)"
          ></app-input>
        </div>

        <!-- Quantity -->
        <div class="">
          <label class="text-start">{{"orderDetails.orderLines.quantity" | translate}}</label>
          <app-input
            [type]="'number'"
            [control]="quantityControl"
            [centerWithNoPadding]="true"
            [editMode]="true"
            [showErrorIcon]="true"
            [showErrorMessage]="false"
            [maxDecimals]="1"
            (focusin)="quantityFocus('in')"
            (focusout)="quantityFocus('out')"
            (keydown)="onInputFieldKeyPress($event)"
          ></app-input>
        </div>

        <!-- Unit price -->
        <div class="">
          <label class="text-start">{{"orderDetails.orderLines.unitPrice" | translate}}</label>
          <app-input
            [type]="'number'"
            [control]="unitPriceControl"
            [centerWithNoPadding]="true"
            [inputSuffix]="'kr'"
            [editMode]="true"
            [showErrorIcon]="true"
            [showErrorMessage]="false"
            (focusin)="unitPriceFocus('in')"
            (focusout)="unitPriceFocus('out')"
            (keydown)="onInputFieldKeyPress($event)"
          ></app-input>
        </div>

        <!-- Discount percentage -->
        <div class="">
          <label class="text-start">{{"orderDetails.orderLines.discount" | translate}}</label>
          <app-input
            [type]="'number'"
            [control]="discountControl"
            [centerWithNoPadding]="true"
            [editMode]="true"
            [inputSuffix]="'%'"
            [showErrorIcon]="true"
            [showErrorMessage]="false"
            (focusin)="discountFocus('in')"
            (focusout)="discountFocus('out')"
            (keydown)="onInputFieldKeyPress($event)"
          ></app-input>
        </div>

        <div class="">
          <label class="text-start">{{"orderDetails.orderLines.vat" | translate}}</label>
          <app-selectorini
            [directSelection]="true"
            [predefinedSearchResults]="vatRates"
            [searchMainDisplayKeys]="['vatRateDisplay']"
            [searchSubDisplayKeys]="['vatRateDescription']"
            [selectedItem]="selectedVatRate"
            (itemSelectedEmitter)="onVatRateSelected($event)"
          ></app-selectorini>
        </div>
      </div>

    </div>

    <!--   Existing price rules -->
    <div [ngClass]="[noCheck ? 'no-check no-drag': '', viewSettings.createView ? 'no-drag' : '', editMode ? 'edit-order-line-column-setup' : 'order-line-column-setup']" *ngFor="let priceRule of priceRules" (click)="editMode ? null : editOrderLine()">
      <div *ngIf="!editMode && !viewSettings.createView && !noCheck" id="dummyGrip"></div>
      <i *ngIf="!editMode && !noCheck" class="text-muted fa-regular fa-arrow-turn-down-right"></i>
      <div class="text-start text-muted" [ngClass]="{'strikethrough': removedPriceRuleIds.includes(priceRule.price_rule_id), 'ps-1': editMode}">{{priceRule.price_rule_name}}</div>
      <div id="qtyDummy"></div>
      <div class="text-muted" [ngClass]="{'strikethrough': removedPriceRuleIds.includes(priceRule.price_rule_id), 'text-end': !editMode}">{{!priceRule.total_price_adjustment ? (priceRule.percentage_adjustment ? (priceRule.value > 0 ? '+' : '') +  priceRule.value.toString() + '%' : currencyFormat(getPriceRuleValue(priceRule))) : ''}}</div>
      <div *ngIf="!editMode" id="discountDummy"></div>
      <div class="text-muted text-end" [ngClass]="{'strikethrough': removedPriceRuleIds.includes(priceRule.price_rule_id)}">{{priceRule.total_price_adjustment ? (priceRule.percentage_adjustment ? (priceRule.value > 0 ? '+' : '') + priceRule.value.toString() + '%' : currencyFormat(getPriceRuleValue(priceRule))) : ''}}</div>
      <div *ngIf="editMode" class="d-flex align-items-center justify-content-end pe-1" (click)="removePriceRule(priceRule)">
        <i *ngIf="!removedPriceRuleIds.includes(priceRule.price_rule_id)" class="fa-regular fa-trash-can ms-2 cursor-pointer"></i>
      </div>
    </div>

    <!-- Add price rule -->
    <div *ngIf="editMode" class="mt-1" style="padding-left: 10px; display: grid; grid-template-columns: 29fr 10fr 7fr 8fr; grid-gap: 10px;">
      <div *ngIf="manualPriceRules.length > 0 && allowAddingManualPriceRule" class="me-1">
        <app-selectorini
          #manualPriceRuleSelectorini
          [directSelection]="true"
          [placeholderTranslationKey]="'orderDetails.orderLines.addPriceRulePlaceholder'"
          [predefinedSearchResults]="manualPriceRules"
          [disabledSearchResults]="manualDisabledPriceRules"
          [disabledTranslationKey]="'orderDetails.orderLines.disabledPriceRuleDescription'"
          [searchMainDisplayKeys]="['priceRuleDisplay']"
          [showCrossButton]="true"
          (itemSelectedEmitter)="onPriceRuleSelected($event)"
          (itemDeselectedEmitter)="onPriceRuleSelected($event)"
        ></app-selectorini>
      </div>
      <div *ngIf="selectedManualPriceRule && !selectedManualPriceRule.total_price_adjustment" class="d-flex align-items-center fw-bold">
        <span>{{selectedManualPriceRule.percentage_adjustment ? selectedManualPriceRule.value + '%' : currencyFormat(getPriceRuleValue(selectedManualPriceRule))}}</span>
        <i class="fa-regular fa-xmark ms-1 cursor-pointer" (click)="deselectSelectedPriceRule()"></i>
      </div>
      <div *ngIf="selectedManualPriceRule && selectedManualPriceRule.total_price_adjustment" class="d-flex align-items-center fw-bold">{{selectedManualPriceRule.value}}%</div>

      <div *ngIf="editMode && manualPriceRules.length > 0 && !allowAddingManualPriceRule" style="padding-left: 5px; font-style: italic;">{{ "orderDetails.orderLines.onlyOneManualPriceRuleAllowed" | translate }}</div>
      <div *ngIf="editMode && manualPriceRules.length === 0" style="padding-left: 5px; font-style: italic;">{{ "orderDetails.orderLines.noManualPriceRulesAvailable" | translate }}</div>
    </div>


    <!-- Comment -->
    <div class="cursor-pointer text-muted comment" [ngClass]="[noCheck ? 'no-check no-drag': '', viewSettings.createView ? 'no-drag' : '', editMode ? 'edit-order-line-column-setup' : 'order-line-column-setup']" *ngIf="orderLine.comment && !editMode">
      <div *ngIf="!editMode && !viewSettings.createView && !noCheck" id="dummyGripForComment"></div>
      <div *ngIf="!editMode && !noCheck"></div>
      <div class="text-start">{{orderLine.comment}}</div>
    </div>
    <div class="my-2" *ngIf="editMode" style="padding-left: 10px;">
      <app-input
        [type]="'text'"
        [control]="commentControl"
        [placeholderKey]="'orderDetails.orderLines.addComment'"
        [editMode]="true"
        (keydown)="onInputFieldKeyPress($event)"
      ></app-input>
    </div>

    <div *ngIf="editMode && orderLine.unit_id == 2 && this.workOrder?.work_order_status_id !== 2" class="ms-2 form-check">
      <input
        class="form-check-input me-1"
        type="checkbox"
        [disabled]="trackTimeToggleDisabled"
        [checked]="orderLine.track_time"
        (change)="orderLine.track_time = !orderLine.track_time"
      >
      <label [ngbTooltip]="trackTimeToggleDisabled ? ((orderLine.work_order_id ? 'orderDetails.orderLines.trackTimeDisabledTooltip.workOrder' : 'orderDetails.orderLines.trackTimeDisabledTooltip.order') | translate) : null">
        {{(orderLine.work_order_id ? 'orderDetails.orderLines.trackTimeLabel.workOrder' : 'orderDetails.orderLines.trackTimeLabel.order') | translate}}
      </label>
    </div>

    <div *ngIf="editMode" class="d-flex align-items-center justify-content-between" style="z-index: 1;">
      <div class="d-flex align-items-center ps-2">
        <app-button
          [small]="true"
          [themeStyle]="'danger'"
          [translationKey]="'common.delete'"
          [disabled]="loading"
          [loading]="deleteLoading"
          (buttonClick)="deleteOrderLine(); $event.stopPropagation();"
        ></app-button>
      </div>

      <div class="d-flex align-items-center justify-content-end">
        <div class="me-2" style="background-color: #fff; border-radius: 5px;">
          <app-button
            [small]="true"
            [buttonType]="'nude'"
            [translationKey]="'common.cancel'"
            [disabled]="loading || deleteLoading"
            (buttonClick)="cancelEdit(); $event.stopPropagation();"
          ></app-button>
        </div>

        <div style="background-color: #fff; border-radius: 5px;">
          <app-button
            [small]="true"
            [translationKey]="'common.save'"
            [loading]="loading"
            [disabled]="deleteLoading"
            (buttonClick)="saveOrderLine(); $event.stopPropagation();"
          ></app-button>
        </div>
      </div>

    </div>

  </div>

  <div id="dummyDivForTheCommentedOutOne" style="min-width: 15px;"></div>
<!--  <div class="d-flex align-items-center justify-content-center cursor-pointer overflow-hidden" (mouseenter)="workOrderButtonHover($event)">-->
<!--    <div-->
<!--      *ngIf="orderLine.work_order_id !== null"-->
<!--      class="d-flex align-items-center justify-content-center ps-1 work-order-button"-->
<!--      style="padding-right: 3px;"-->
<!--      [ngClass]="{'visually-hidden': editMode, 'work-order-button-hover': orderLineHovered}"-->
<!--      (click)="openWorkOrder($event)"-->
<!--      (mouseenter)="onPopoverMouseEnter(workOrderPopoverTrigger)"-->
<!--      (mouseleave)="onPopoverMouseLeave(workOrderPopoverTrigger)"-->
<!--      >-->
<!--      <i *ngIf="!editMode" class="fa-regular fa-clock work-order-button-clock" [ngClass]="{'work-order-button-clock-hover': orderLineHovered}"></i>-->
<!--    </div>-->
<!--  </div>-->

</div>
