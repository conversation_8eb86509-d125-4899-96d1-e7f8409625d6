import {Component, DestroyRef, EventEmitter, inject, Input, OnInit, Output, ViewChild} from '@angular/core';
import {DetailsViewSettings, OrderLineRow, OrderResponse, PriceRuleResponse, WorkOrderResponse} from "../../../../../../@shared/models/order.interfaces";
import {NgbModal, NgbPopover} from "@ng-bootstrap/ng-bootstrap";
import {OrderService} from "../../../../../../@shared/services/order.service";
import {currencyFormat, formatDateDMY, formatTimeHM, UtilsService} from "../../../../../../@core/utils/utils.service";
import {PaymentService} from "../../../../../../@shared/services/payment.service";
import {TranslateService} from "@ngx-translate/core";
import {FormControl, Validators} from "@angular/forms";
import {_CRM_ORD_31} from "../../../../../../@shared/models/input.interfaces";
import {ToastService} from "../../../../../../@core/services/toast.service";
import {BehaviorSubject, forkJoin} from "rxjs";
import {SelectoriniComponent} from "../../../../../../@shared/components/selectorini/selectorini.component";
import {VatRateResponse} from "../../../../../../@shared/models/payment.interfaces";
import {CdkDrag, CdkDragHandle} from "@angular/cdk/drag-drop";
import {VerifyPopupModal} from "../../../../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {StorageService} from "../../../../../../@core/services/storage.service";
import {SpinnerComponent} from "../../../../../../@shared/components/spinner/spinner.component";
import {takeUntilDestroyed} from "@angular/core/rxjs-interop";
import {StandardImports} from "../../../../../../@shared/global_import";

@Component({
  selector: 'order-line-row',
  templateUrl: './order-line-row.component.html',
  styleUrls: ['./order-line-row.component.css'],
  standalone: true,
  imports: [
    StandardImports,
    CdkDrag,
    CdkDragHandle,
    SelectoriniComponent,
    SpinnerComponent,
    NgbPopover,
  ]
})
export class OrderLineRowComponent implements OnInit {
  @Input() orderLine: OrderLineRow;
  @Input() orderLineIdEditActive: number | null;
  @Input() viewSettings: DetailsViewSettings;
  @Input() noCheck: boolean = false;
  @Input() orderLinesSubject: BehaviorSubject<OrderLineRow[]>;
  @Input() forceTopBorder: boolean = false;
  @Input() connectedToWorkOrderWithFutureChildren: boolean = false;
  @Input() order?: OrderResponse;

  @ViewChild('manualPriceRuleSelectorini') manualPriceRuleSelectorini: SelectoriniComponent;
  @ViewChild('workOrderPopoverTrigger') workOrderPopoverTrigger: NgbPopover;

  editMode = false;
  loading = false;
  deleteLoading = false;
  noBottomBorder = false;
  unitPricePriceRuleActive = false;
  unitPercentagePriceRuleActive = false;
  totalPricePriceRuleActive = false;
  connectedProductTooltip: string = '';
  selectedManualPriceRule: PriceRuleResponse | null = null;
  manualPriceRules: PriceRuleResponse[] = [];
  manualDisabledPriceRules: PriceRuleResponse[] = [];
  removedPriceRuleIds: number[] = [];
  allowAddingManualPriceRule = true;
  vatRates: VatRateResponse[] = [];
  selectedVatRate: VatRateResponse | null = null;
  dragging = false;
  priceRules: PriceRuleResponse[] = [];
  trackTimeToggleDisabled = false;
  locked = false;
  orderLineHovered = false;

  workOrderLoading = false;
  workOrder?: WorkOrderResponse | null = null;
  isPopoverLocked = false;

  nameControl: FormControl<string | null> = new FormControl('', Validators.required);
  quantityControl: FormControl<number | null> = new FormControl(0, [Validators.required, Validators.min(0)]);
  unitPriceControl: FormControl<number | null> = new FormControl(0, [Validators.required, Validators.min(0)]);
  discountControl: FormControl<number | null> = new FormControl(0, [Validators.min(0), Validators.max(100)]);
  commentControl: FormControl<string | null> = new FormControl('');

  operateExVat: boolean = false;

  private destroyRef = inject(DestroyRef);

  @Output() editActive = new EventEmitter<number | null>();

  constructor(private orderService: OrderService, private modalService: NgbModal, public utilsService: UtilsService, private paymentService: PaymentService, private translateService: TranslateService, private toastService: ToastService, private storageService: StorageService) {}


  ngOnInit() {
    this.storageService.operateExVat$.subscribe((operateExVat) => {
      this.operateExVat = operateExVat;
    });

    this.locked = this.orderLine.locked || ([1, 2, 3, 6].includes(this.orderLine.payment_status_id));

    this.selectedVatRate = {
      vat_rate_id: this.orderLine.vat_rate_id,
      vat_rate: this.orderLine.vat_rate,
      vat_rate_display_name: `${this.orderLine.vat_rate}%`,
      vatRateDisplay: `${this.orderLine.vat_rate}%`
    };

    if (this.orderLine.locked || (this.orderLine.payment_id && !(this.viewSettings.repeatingView || this.viewSettings.paymentView))) {
      this.orderLine.checked = false;
    }

    if (this.orderLine.product_id) {
      this.connectedProductTooltip = `${this.translateService.instant('orderDetails.orderLines.productLinkedTooltip1')} ${this.orderLine.product_name}. ${this.translateService.instant('orderDetails.orderLines.productLinkedTooltip2')}`;
    }
    this.initFormControls();
    this.verifyPriceRuleSetup();

    this.orderLinesSubject.subscribe((orderLines) => {
      this.initTrackTimeToggle(orderLines);
    });
  }

  initFormControls() {
    this.nameControl.setValue(this.orderLine.order_line_name);
    this.quantityControl.setValue(this.orderLine.quantity);
    this.unitPriceControl.setValue(this.operateExVat ? this.orderLine.unit_price_ex_vat : this.orderLine.unit_price_inc_vat);
    this.discountControl.setValue(this.orderLine.discount_percentage);
    this.commentControl.setValue(this.orderLine.comment);
  }

  initTrackTimeToggle(orderLines: OrderLineRow[]) {
    if (!this.orderLine.track_time) {
      // This filtering will handle both the case where the orderLine has a work_order_id and the case where it doesn't
      for (const ol of orderLines.filter(ol => ol.work_order_id === this.orderLine.work_order_id)) {
        if (ol.track_time) {
          this.trackTimeToggleDisabled = true;
          break;
        }
      }
    }
  }

  checkOrderLine() {
    this.orderLinesSubject.next(this.orderLinesSubject.value.map(ol => {
      if (ol.order_line_id === this.orderLine.order_line_id) {
        return {...ol, checked: !this.orderLine.checked};
      }
      return ol;
    }));
  }

  editOrderLine() {
    if (this.locked || this.dragging) {
      return;
    }
    if (this.orderLineIdEditActive !== null && this.orderLineIdEditActive !== this.orderLine.order_line_id) {
      return;
    }

    if (!this.workOrder && this.orderLine.work_order_id !== null && !this.workOrderLoading) {
      this.workOrderLoading = true;
      this.orderService.getWorkOrderById(this.orderLine.work_order_id!).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((workOrder) => {
        this.workOrder = workOrder;
        this.workOrderLoading = false;
      });
    }

    if (this.workOrderPopoverTrigger) {
      this.workOrderPopoverTrigger.open();
    }

    if (this.vatRates.length === 0) {
      this.orderService.getVatRates().subscribe((vatRates) => {
        this.vatRates = vatRates.sort((a, b) => b.vat_rate - a.vat_rate).map(vatRate => {
          let description = '';

          switch (vatRate.vat_rate_id) {
            case 3:
              description = 'Avgiftsfri';
              break;
            case 4:
              description = 'Utenfor avg.omr.';
              break;
            case 6:
              description = 'Avgiftsfri eksport';
              break;
            default:
              description = '';
              break;
          }

          return {
            ...vatRate,
            vatRateDisplay: `${vatRate.vat_rate}%`,
            vatRateDescription: description
          };
        });

      });
    }
    this.editMode = true;
    this.editActive.emit(this.orderLine.order_line_id);
  }

  onVatRateSelected(vatRate: VatRateResponse | any) {
    this.selectedVatRate = vatRate;
  }

  onInputFieldKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      this.saveOrderLine();
    }
  }

  controlsValid() {
    return (this.nameControl.valid || this.nameControl.disabled) && (this.quantityControl.valid || this.quantityControl.disabled) && (this.unitPriceControl.valid || this.unitPriceControl.disabled) && (this.discountControl.valid || this.discountControl.disabled) && (this.commentControl.valid || this.commentControl.disabled);
  }

  cancelEdit() {
    this.editMode = false;
    this.initFormControls();
    this.removedPriceRuleIds = [];
    this.selectedManualPriceRule = null;
    this.verifyPriceRuleSetup();
    this.editActive.emit(null);
  }

  onPriceRuleSelected(priceRule: PriceRuleResponse | any) {
    if (!priceRule) {
      this.selectedManualPriceRule = null;
    } else {
      this.selectedManualPriceRule = priceRule;
    }
    this.verifyPriceRuleSetup();
  }

  removePriceRule(priceRule: PriceRuleResponse) {
    this.removedPriceRuleIds.push(priceRule.price_rule_id);
    if (!priceRule.total_price_adjustment) {
      let numUnitPriceRules = this.orderLine.price_rules.filter(priceRule => !priceRule.total_price_adjustment && !this.removedPriceRuleIds.includes(priceRule.price_rule_id)).length;
      if (numUnitPriceRules === 0) {
        this.unitPriceControl.setValue(this.operateExVat ? this.orderLine.precalculated_unit_price_ex_vat : this.orderLine.precalculated_unit_price_inc_vat);
      }
    }

    this.verifyPriceRuleSetup();
  }

  verifyPriceRuleSetup() {
    this.priceRules = this.orderLine.price_rules.sort((a, b) => {
      // First, sort by total_price_adjustment (false comes before true)
      if (a.total_price_adjustment !== b.total_price_adjustment) {
        return a.total_price_adjustment ? 1 : -1;
      }
      // If total_price_adjustment is the same, sort by percentage_adjustment (false comes before true)
      return a.percentage_adjustment === b.percentage_adjustment ? 0 : a.percentage_adjustment ? 1 : -1;
    });

    let alreadyUsedPriceRules = this.orderLine.price_rules.map(priceRule => priceRule.price_rule_id);


    this.manualPriceRules = this.orderLine.product_price_rules.filter(priceRule => (priceRule.manual || priceRule.price_rule_type_id === 3) && !alreadyUsedPriceRules.includes(priceRule.price_rule_id)).map(priceRule => {
      return {
          ...priceRule,
          priceRuleDisplay: `${priceRule.price_rule_name} (${priceRule.percentage_adjustment ? priceRule.value + '%' : currencyFormat(this.getPriceRuleValue(priceRule))})`
        }
    });

    this.unitPricePriceRuleActive = false;
    this.totalPricePriceRuleActive = false;
    this.unitPercentagePriceRuleActive = false;
    if (this.selectedManualPriceRule) {
      if (!this.selectedManualPriceRule.total_price_adjustment) {
        this.unitPricePriceRuleActive = true;
        if (this.selectedManualPriceRule.percentage_adjustment) {
          this.unitPercentagePriceRuleActive = true;
        }
      }
      if (this.selectedManualPriceRule.total_price_adjustment) {
        this.totalPricePriceRuleActive = true;
      }
    }

    for (const priceRule of this.orderLine.price_rules) {
      if (!this.removedPriceRuleIds.includes(priceRule.price_rule_id)) {
        if (!priceRule.total_price_adjustment) {
          this.unitPricePriceRuleActive = true;
          if (priceRule.percentage_adjustment) {
            this.unitPercentagePriceRuleActive = true;
            }
        }
        if (priceRule.total_price_adjustment) {
          this.totalPricePriceRuleActive = true;
        }
      }
    }
    if (this.totalPricePriceRuleActive || this.unitPricePriceRuleActive) {
      this.unitPriceControl.disable();
    } else {
      this.unitPriceControl.enable();
    }

    this.manualDisabledPriceRules = [];
    if (this.unitPricePriceRuleActive || this.totalPricePriceRuleActive) {
      this.manualDisabledPriceRules = this.manualPriceRules.filter(priceRule => !priceRule.total_price_adjustment && !priceRule.percentage_adjustment);
    }

  }

  async openPayment() {
    if (!this.orderLine.payment_id) {
      return;
    }
    this.paymentService.getOrderPayment(this.orderLine.payment_id!).subscribe(async (payment) => {
      const { PaymentDetailsV2Component } = await import('src/app/pages/payments/components/payment-details-v2/payment-details-v2.component');
      let modalRef = this.modalService.open(PaymentDetailsV2Component, {size: 'lg'});
      modalRef.componentInstance.payment = payment;
      modalRef.componentInstance.viewSettings = {
        modalView: true,
      };
    });
  }

  async openWorkOrder(event: Event) {
    event.stopPropagation();
    const { WorkOrderDetailsComponent } = await import('../../../../../work-orders/components/work-order-details/work-order-details.component');
    let modalRef = this.modalService.open(WorkOrderDetailsComponent, {size: 'xl'});
    modalRef.componentInstance.workOrderId = this.orderLine.work_order_id;
    modalRef.componentInstance.viewSettings = {
      modalView: true,
      workOrderView: true,
      collapsedOrderLines: true,
    }
  }

  deselectSelectedPriceRule() {
    this.selectedManualPriceRule = null;
    this.manualPriceRuleSelectorini.deselectItem(false);
    this.verifyPriceRuleSetup();
  }

  async saveOrderLine() {
    if (!this.controlsValid()) {
      return;
    }
    this.loading = true;
    let payload: _CRM_ORD_31 = {
      order_line_id: this.orderLine.order_line_id,
      order_line_name: this.nameControl.value!,
      quantity: Number(this.quantityControl.value!),
      discount_percentage: this.discountControl.value ? parseInt(this.discountControl.value.toString()) : null,
      comment: this.commentControl.value || null,
      removed_price_rule_ids: this.removedPriceRuleIds,
      added_price_rule_ids: this.selectedManualPriceRule ? [this.selectedManualPriceRule.price_rule_id] : [],
      vat_rate_id: this.selectedVatRate?.vat_rate_id!,
      track_time: this.orderLine.track_time
    }

    if (!this.unitPricePriceRuleActive) {
      if (this.operateExVat) {
        payload.unit_price_inc_vat = Number(this.unitPriceControl.value) * (1 + this.selectedVatRate!.vat_rate / 100);
      } else {
        payload.unit_price_inc_vat = Number(this.unitPriceControl.value);
      }
    }

    if (this.connectedToWorkOrderWithFutureChildren) {
      let modalRef = this.modalService.open(VerifyPopupModal, {backdrop: 'static'});
      modalRef.componentInstance.showBody = true;
      modalRef.componentInstance.titleTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.title';
      modalRef.componentInstance.bodyBoldTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyBoldTranslationKey';
      modalRef.componentInstance.bodyMutedTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyMutedTranslationKey';
      modalRef.componentInstance.bodyRegularTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.orderLineUpdate';
      try {
        payload.update_upcoming_work_orders = await modalRef.result;
      } catch (e) {}
    }

    this.orderService.updateOrderLineAsCompany(payload).subscribe((orderLine) => {
      this.orderLine = {...orderLine, checked: this.orderLine.checked};
      this.removedPriceRuleIds = [];
      if (this.orderLine.work_order_id) {
        this.orderService.fetchAndRefreshSingleWorkOrder(this.orderLine.work_order_id, 'orderLineUpdatedInWorkOrder')
      }
      this.selectedManualPriceRule = null;
      this.orderLinesSubject.next(this.orderLinesSubject.value.map(ol => {
        if (ol.order_line_id === this.orderLine.order_line_id) {
          return this.orderLine;
        }
        return ol;
      }));
      this.toastService.successToast('updated');
      this.editMode = false;
      this.loading = false;
      this.editActive.emit(null);
    }, (error) => {
      this.loading = false;
    });
  }

  quantityFocus(direction: string) {
    if (direction == 'in') {
      if (this.quantityControl.value === 0) {
        this.quantityControl.setValue(null);
      }
    } else {
      if (this.quantityControl.value === null) {
        this.quantityControl.setValue(0);
      }
    }
  }

  unitPriceFocus(direction: string) {
    if (direction == 'in') {
      if (this.unitPriceControl.value === 0) {
        this.unitPriceControl.setValue(null);
      }
    } else {
      if (this.unitPriceControl.value === null) {
        this.unitPriceControl.setValue(0);
      }
    }
  }

  discountFocus(direction: string) {
    if (direction == 'in') {
      if (this.discountControl.value === 0) {
        this.discountControl.setValue(null);
      }
    } else {
      if (this.discountControl.value === null) {
        this.discountControl.setValue(0);
      }
    }
  }

  getPriceRuleValue(priceRule: PriceRuleResponse) {
    if (priceRule.percentage_adjustment || !this.operateExVat) {
      return priceRule.value;
    } else {
      return priceRule.value / (1 + this.selectedVatRate!.vat_rate / 100);
    }
  }

  workOrderButtonHover(event: MouseEvent) {
    if (!this.workOrder && this.orderLine.work_order_id !== null && !this.workOrderLoading) {
      this.workOrderLoading = true;
      this.orderService.getWorkOrderById(this.orderLine.work_order_id!).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((workOrder) => {
        this.workOrder = workOrder;
        this.workOrderLoading = false;
      });
    }
  }

  // onPopoverMouseEnter(popover: NgbPopover) {
  //   if (!this.workOrder && this.orderLine.work_order_id !== null && !this.workOrderLoading) {
  //     this.workOrderLoading = true;
  //     this.orderService.getWorkOrderById(this.orderLine.work_order_id!).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((workOrder) => {
  //       this.workOrder = workOrder;
  //       this.workOrderLoading = false;
  //     });
  //   }
  //   popover.open();
  // }

  // onPopoverMouseLeave(popover: NgbPopover) {
  //   if (!this.isPopoverLocked) {
  //     popover.close();
  //   }
  // }

  orderLineMouseEnter() {
    this.orderLineHovered = true;
  }

  orderLineMouseLeave() {
    this.orderLineHovered = false;
  }

  deleteOrderLine() {
    let modalRef = this.modalService.open(VerifyPopupModal, {centered: true});
    modalRef.result.then((result: boolean) => {
      if (result) {
        this.deleteLoading = true;
        this.orderService.deleteOrderLineAsCompany(this.orderLine.order_line_id).subscribe(() => {
          if (this.orderLine.work_order_id) {
            this.orderService.fetchAndRefreshSingleWorkOrder(this.orderLine.work_order_id, 'orderLinesDeleteOrderLines');
          }
          this.toastService.successToast('deleted')
          this.orderService.refreshOrderLineInOrderLines(this.orderLine, 'delete')
          this.orderLinesSubject.next(this.orderLinesSubject.value.filter(orderLine => orderLine.order_line_id != orderLine.order_line_id));
          this.deleteLoading = false;
        });
      }
    });
  }

  protected readonly currencyFormat = currencyFormat;
  protected readonly formatTimeHM = formatTimeHM;
  protected readonly formatDateDMY = formatDateDMY;
}
