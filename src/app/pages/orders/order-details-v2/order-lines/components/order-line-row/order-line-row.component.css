.refund-color {
  color: #ff0000;
}

.strikethrough {
  text-decoration: line-through;
}

.lock-locked-color {
  color: #f56b6b;
}

.order-line-column-setup {
  display: grid;
  align-items: center;
  grid-template-columns: 2fr 2fr 18fr 6fr 8fr 8fr 8fr;
  grid-gap: 10px;
  padding: 5px 0 5px 10px;
  text-align: center;
  border-radius: 10px;
  text-wrap: auto;
}

.order-line-column-setup.no-check {
  grid-template-columns: 2fr 18fr 6fr 8fr 8fr 8fr;
}

.order-line-column-setup.no-drag {
  grid-template-columns: 2fr 18fr 6fr 8fr 8fr 8fr;
}

.order-line-column-setup.no-drag.no-check {
  grid-template-columns: 18fr 6fr 8fr 8fr 8fr;
}

.order-line-column-setup.comment {
  grid-template-columns: 2fr 2fr 48fr;
}

.edit-order-line-column-setup {
  display: grid;
  align-items: center;
  grid-template-columns: 22fr 6fr 8fr 8fr 8fr;
  grid-gap: 10px;
  padding: 5px 0 5px 10px;
  border-radius: 10px;
}

.edit-order-line-column-setup.no-check {
  grid-template-columns: 20fr 6fr 8fr 8fr 8fr;
}

.order-line-price-rule-container {
  display: flex;
  width: 50%;
  margin-left: 20px;
  align-items: center;
}

.order-line-price-rule-content {
  display: flex;
  padding: 5px 10px;
}

.selected-order-line {
  background-color: #E6F2EE;
  border: 1px #E0E6DE;
}

.check-disabled {
  cursor: not-allowed;
  background-color: #f1f1f1;
}

.dragging {
  background-color: #F7F7F7;
  border: 1px black;
  cursor: grabbing;
}

.order-line-row {
}

.order-line-row:hover {
  background-color: #fbfbfb;
}

.order-line-row.locked {
  background-color: #fcfcfc;
}

.order-line-row.locked:hover {
  background-color: #fafafa;
}

.work-order-button {
  margin-left: 5px;
  height: 100%;
  max-height: 55px;
  background-color: var(--primary-color);
  clip-path: ellipse(100% 50% at 100% 50%);
  margin-right: -14px;
  transition: margin-right 0.2s ease;
}

.work-order-button-hover {
  margin-right: 0;
  transition: margin 0.2s ease;
}

.work-order-button-clock {
  color: var(--primary-color);
  transition: color 0.2s ease;
}

.work-order-button-clock-hover {
  color: white;
  transition: color 0.2s ease;
}

.bullet-item {
  position: relative;
  padding-left: 20px; /* Space for bullet */
}

.bullet-item::before {
  content: "•"; /* Bullet character */
  position: absolute;
  left: 0;
  top: -5px;
  color: #6c757d;
  font-size: 1.2em; /* Adjust size */
}
