import {Component, Input, OnInit} from '@angular/core';
import {
  OrderLineResponse,
  OrderLogResponse,
  OrderLogTypeResponse,
  OrderResponse
} from "../../../../@shared/models/order.interfaces";
import {_CRM_ORD_87} from "../../../../@shared/models/input.interfaces";
import {OrderService} from "../../../../@shared/services/order.service";
import {UtilsService} from "../../../../@core/utils/utils.service";
import {StandardImports} from "../../../../@shared/global_import";
import {CardComponent} from "../../../../@shared/components/layout/card/card.component";
import {SpinnerComponent} from "../../../../@shared/components/spinner/spinner.component";

@Component({
    selector: 'app-order-timeline',
    templateUrl: './order-timeline.component.html',
    styleUrls: ['./order-timeline.component.css'],
    standalone: true,
  imports: [StandardImports, CardComponent, SpinnerComponent]
})
export class OrderTimelineComponent implements OnInit {
  order: OrderResponse;
  timelineItems: OrderLogResponse[] = [];
  allTimelineItems: OrderLogResponse[] = [];
  timelineLogTypes: OrderLogTypeResponse[] = [];
  selectedLogTypes: number[] = [];
  openAccordions: number[] = [];
  logTypeDataFilter = [4]
  isLoading: boolean = false;
  logTypeIcons = [
    'fa-circle-dot',
    'fa-credit-card',
    'fa-message',
    'fa-envelope',
    'fa-file-chart-column',
    'fa-map-pin',
    'fa-circle-dot',
    'fa-user-alt'
  ]
  constructor(private orderService: OrderService, private utilsService: UtilsService) {
  }
  ngOnInit() {
    this.fetchTimelineLogTypes();
    this.orderService.order$.subscribe((order) => {
      this.order = order;
    });

    this.orderService.refreshOrderLogs(this.order.order_id);

    this.orderService.orderLogs$.subscribe((logs) => {
      this.allTimelineItems = logs;
      this.timelineItems = this.allTimelineItems.filter(log => this.selectedLogTypes.includes(log.log_type_id));
    });
    this.timelineItems = this.allTimelineItems;
  }

  fetchTimelineLogTypes(){
    this.isLoading = true;
    this.orderService.getTimelineLogTypes().subscribe((res) => {
      this.timelineLogTypes = res;
      this.selectAllLogTypes();
      this.isLoading = false;
    });
  }

  toggleAccordion(itemId: number, item: OrderLogResponse): void {
    if (!this.canToggleAccordion(item)) {
      return;
    }
    if (this.isAccordionOpen(itemId)) {
      // Remove the item id from the openAccordions array if it's already open
      this.openAccordions = this.openAccordions.filter(id => id !== itemId);
    } else {
      // Add the item id to the openAccordions array if it's closed
      this.openAccordions.push(itemId);
    }
  }

  canToggleAccordion(item: OrderLogResponse): boolean {
    return item.data.length > 0 && !this.logTypeDataFilter.includes(item.log_type_id);
  }

  isAccordionOpen(itemId: number): boolean {
    // Check if the item id is present in the openAccordions array
    return this.openAccordions.includes(itemId);
  }

  // Method to check if date changed between current item and previous item
  isDateChanged(index: number): boolean {
    if (index === 0) {
      return true; // Always show the first date
    } else {
      return this.timelineItems[index].timestamp.toDateString() !== this.timelineItems[index - 1].timestamp.toDateString();
    }
  }

  isToday(date: Date) {
    //check if date is today
    return date.toDateString() === new Date().toDateString();
  }

  // TypeScript
  selectLogType(type: OrderLogTypeResponse) {
    if (this.selectedLogTypes.includes(type.order_log_type_id)) {
      this.selectedLogTypes = this.selectedLogTypes.filter(logType => logType !== type.order_log_type_id);
    } else {
      this.selectedLogTypes.push(type.order_log_type_id);
    }
    this.timelineItems = this.allTimelineItems.filter(log => this.selectedLogTypes.includes(log.log_type_id));
  }

  selectAllLogTypes() {
    this.selectedLogTypes = this.timelineLogTypes.map(logType => logType.order_log_type_id);
    this.timelineItems = this.allTimelineItems;
  }

  formatTitle(log: OrderLogResponse): string {
    // Sms
    if (log.log_type_id === 2) {
      let smsType = log.data[0].value;
      return log.title + ' - ' + smsType;
    }
    // Email
    else if (log.log_type_id === 3) {
      return log.title + ' - ' + log.data[0].value;
    }

    else {
      return log.title;
    }


  }
  formatDataValue(logData: {value: any, label: string, data_id: string}) {
    if (logData.data_id === 'duration') {
      return this.utilsService.formatDurationFromHours(logData.value / 3600);
    }
    else {
      return logData.value;
    }
  }

  containsWarning(log: OrderLogResponse): boolean {
    if (log.log_type_id == 3) {
      for (const entry of log.data) {
        if (entry.data_id == 'status_id' && entry.value == 3) {
          return true;
        }
      }
    }
    else if (log.log_type_id == 2) {
      // Check if not delivered and log.timestamp is older than one hour
      for (const entry of log.data) {
        if (entry.data_id == 'delivered_value' && entry.value == 0) {
          if (log.timestamp.getTime() < new Date().getTime() - 3600000) {
            return true;
          }
        }
      }
    }
    return false;
  }

  containsSuccess(log: OrderLogResponse): boolean {
    if (log.log_type_id == 3) {
      for (const entry of log.data) {
        if (entry.data_id == 'status_id' && [2, 4].includes(<number>entry.value)) {
          return true;
        }
      }
    }
    else if (log.log_type_id == 2) {
      for (const entry of log.data) {
        if (entry.data_id == 'delivered_value' && entry.value == 1) {
          return true;
        }
      }
    }
    return false;
  }

  emailOpened(log: OrderLogResponse): boolean {
   if (log.log_type_id == 3) {
      for (const entry of log.data) {
        if (entry.data_id == 'status_id' && entry.value == 2) {
          return true;
        }
      }
    }
    return false;
  }

  emailLinkClicked(log: OrderLogResponse): boolean {
    if (log.log_type_id == 3) {
      for (const entry of log.data) {
        if (entry.data_id == 'status_id' && entry.value == 4) {
          return true;
        }
      }
    }
    return false;
  }

  getLogIcon(item: OrderLogResponse) {
    if (item.log_type_id === 3) {
      if (this.emailOpened(item)) {
        return 'fa-envelope-open';
      }
      if (this.emailLinkClicked(item)) {
        return 'fa-envelope-open-text';
      }
    }
    return this.logTypeIcons[item.log_type_id];
  }

  isSelectedLogType(type: OrderLogTypeResponse): boolean {
    return this.selectedLogTypes.includes(type.order_log_type_id);
  }



}
