import {ChangeDetectorRef, Component, Input, Renderer2} from '@angular/core';
import {DetailsViewSettings, WorkOrderResponse} from "../../../../@shared/models/order.interfaces";
import {UtilsService} from "../../../../@core/utils/utils.service";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {OrderService} from "../../../../@shared/services/order.service";
import {TranslateService} from "@ngx-translate/core";

import {ToastService} from "../../../../@core/services/toast.service";


import {CardComponent} from "../../../../@shared/components/layout/card/card.component";
import {WorkOrderCardComponent} from "../work-order-card/work-order-card.component";
import {WorkOrderListComponent} from "../../../work-orders/components/work-order-list/work-order-list.component";
import {StandardImports} from "../../../../@shared/global_import";
import {NewWorkOrderComponent} from "../../../work-orders/components/new-work-order/new-work-order.component";

@Component({
  selector: 'app-work-order-card-container',
  templateUrl: './work-order-card-container.component.html',
  styleUrls: ['./work-order-card-container.component.css'],
  standalone: true,
  imports: [StandardImports, CardComponent, WorkOrderCardComponent, WorkOrderListComponent]
})
export class WorkOrderCardContainerComponent {

  @Input() workOrders: WorkOrderResponse[] = [];
  @Input() orderId: number;
  @Input() viewSettings: DetailsViewSettings = {};

  loading = false;

  constructor(public utilsService: UtilsService, private modalService: NgbModal, private orderService: OrderService, private renderer: Renderer2, private cdr: ChangeDetectorRef, private toastService: ToastService, private translate: TranslateService) {}

  openCreateWorkOrderModal() {
    let modalRef = this.modalService.open(NewWorkOrderComponent);
    modalRef.componentInstance.orderId = this.orderId;
    modalRef.componentInstance.viewSettings = {};
  }

}

