import {Component, Input, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {OrderAttachmentResponse, OrderResponse, WorkOrderResponse} from "../../../../@shared/models/order.interfaces";
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ngb<PERSON>ooltip} from "@ng-bootstrap/ng-bootstrap";
import {OrderService} from "../../../../@shared/services/order.service";
import {_CRM_DEP_1, _CRM_ORD_110, _CRM_PRJ_1} from "../../../../@shared/models/input.interfaces";
import {StorageService} from "../../../../@core/services/storage.service";
import {CardComponent} from "../../../../@shared/components/layout/card/card.component";
import {firstValueFrom, Subject} from "rxjs";
import {takeUntil} from "rxjs/operators";
import {UtilsService} from "../../../../@core/utils/utils.service";
import {StandardImports} from "../../../../@shared/global_import";
import {IntegrationsService} from "../../../../@shared/services/integrations.service";
import {SelectoriniComponent} from "../../../../@shared/components/selectorini/selectorini.component";
import {ProjectResponse} from "../../../../@shared/models/projects.interfaces";
import {DepartmentResponse} from "../../../../@shared/models/departments.interfaces";
import {ExternalDepartmentResponse, ExternalProjectResponse} from "../../../../@shared/models/integrations.interfaces";
import {ProjectsService} from "../../../../@shared/services/projects.service";
import {DepartmentsService} from "../../../../@shared/services/departments.service";
import {ToastService} from "../../../../@core/services/toast.service";

@Component({
  selector: 'app-order-project-and-department',
  templateUrl: './order-project-and-department.component.html',
  styleUrls: ['./order-project-and-department.component.css'],
  standalone: true,
  imports: [StandardImports, CardComponent, SelectoriniComponent]
})
export class OrderProjectAndDepartmentComponent implements OnInit {
  order: OrderResponse;
  project: ProjectResponse | null;
  department: DepartmentResponse | null;
  destroy$ = new Subject<void>();
  existingProjects: ProjectResponse[] = [];
  existingDepartments: DepartmentResponse[] = [];
  projectLoading: boolean = false;
  departmentLoading: boolean = false;
  clearSelectoriniObservable: Subject<void> = new Subject<void>();

  departmentsEnabled: boolean = false;

  constructor(
    private modalService: NgbModal,
    public orderService: OrderService,
    private storageService: StorageService,
    private toastService: ToastService,
    public utilsService: UtilsService,
    public integrationService: IntegrationsService,
    private projectService: ProjectsService,
    private departmentService: DepartmentsService
  ) {}

  ngOnInit() {
    this.storageService.departmentsEnabled$.subscribe((enabled) => {
      this.departmentsEnabled = enabled;
    });

    this.orderService.order$.pipe(takeUntil(this.destroy$)).subscribe(order => {
      this.clearSelectoriniObservable.next();
      this.order = order;
      this.project = order.project;
      this.department = order.department;
    });
    this.projectService.getCompanyProjects().pipe(takeUntil(this.destroy$)).subscribe((projects) => {
      this.existingProjects = projects;
    });
    this.departmentService.getCompanyDepartments().pipe(takeUntil(this.destroy$)).subscribe((departments) => {
      this.existingDepartments = departments;
    });
  }

  async projectSelected(project: ExternalProjectResponse | any) {
    this.projectLoading = true;
    let internalProject: ProjectResponse | undefined = this.existingProjects.find((p) => p.accounting_id === project.external_project_id.toString());
    if (internalProject) {
      this.project = internalProject;
    } else {
      let payload: _CRM_PRJ_1 = {
        project_name: project.external_project_name,
        accounting_id: project.external_project_id,
      }
      internalProject = await firstValueFrom(this.projectService.createProject(payload));
      this.project = internalProject;
    }
    this.orderService.patchOrder({order_id: this.order.order_id, project_id: this.project?.project_id}).subscribe((order) => {
      this.order = order;
      this.orderService.refreshOrder(order, 'selectedProject');
      this.project = order.project;
      this.projectLoading = false;
      this.toastService.successToast('updated')
    });
  }

  deselectProject() {
    this.projectLoading = true;
    this.orderService.patchOrder({order_id: this.order.order_id, project_id: null}).subscribe((order) => {
      this.order = order;
      this.project = order.project;
      this.orderService.refreshOrder(order, 'deselectedProject');
      this.projectLoading = false;
      this.toastService.successToast('updated')
    });
  }

  deselectDepartment() {
    this.departmentLoading = true;
    this.orderService.patchOrder({order_id: this.order.order_id, department_id: null}).subscribe((order) => {
      this.order = order;
      this.department = order.department;
      this.orderService.refreshOrder(order, 'deselectedDepartment');
      this.departmentLoading = false;
      this.toastService.successToast('updated')
    });
  }

  async departmentSelected(department: ExternalDepartmentResponse | any) {
    this.departmentLoading = true;
    let internalDepartment: DepartmentResponse | undefined = this.existingDepartments.find((d) => d.accounting_id === department.external_department_id.toString());
    if (internalDepartment) {
      this.department = internalDepartment;
    } else {
      let payload: _CRM_DEP_1 = {
        department_name: department.external_department_name,
        accounting_id: department.external_department_id,
        match_accounting: true,
      }
      internalDepartment = await firstValueFrom(this.departmentService.createDepartment(payload));
      this.department = internalDepartment;
    }
    this.orderService.patchOrder({order_id: this.order.order_id, department_id: this.department?.department_id}).subscribe((order) => {
      this.order = order;
      this.orderService.refreshOrder(order, 'selectedDepartment');
      this.department = order.department;
      this.departmentLoading = false;
      this.toastService.successToast('updated')
    });
  }
}
