<app-card
  [labelKey]="'orderDetails.attachments.title'"
  [padding]="'0'"
  [buttonHeader]="true"
  [ghosted]="attachments.length == 0">

  <div buttonHeader>
    <app-button [customClass]="'me-2 p-0'" [translationKey]="'common.add'" [buttonType]="'link'" (buttonClick)="openAddAttachmentModal()" [small]="true"></app-button>
  </div>
  <div class="p-0" cardcontent [ngClass]="attachments.length == 0 ? 'py-3' : ''">
    <div class="col-sm p-0 border-left" id="allocated-resources">
      <div class="" style="max-height: 340px; overflow-y: auto" id="attachmentsContainer" #attachmentsContainer>
        <div class="h-100">
          <div class="">
            <div class="" style="border-radius: 0; width: 100%; min-height: 77%">
              <div class="" id="data-body">
                <p class="text-center mb-0" *ngIf="attachments.length === 0">{{ "orders.orderDetails.resources.noReportsMade" | translate }}</p>

                <div *ngFor="let attachment of attachments; let i = index;" class="mb-2" [ngClass]="i === attachments.length - 1 ? '' : 'border-bottom'" style="margin: 0 1.5rem;">
                  <div class="d-flex justify-content-between mb-2" [ngClass]="i === 0 ? 'mt-2' : ''">
                    <div>
                      <div class="d-flex">
                        <ng-container *ngIf="isImageFile(attachment); else fileIcon">
                          <span [ngbPopover]="popTemplate" [triggers]="'mouseenter:mouseleave'">
                          <img
                            [src]="attachment.file_url ? attachment.file_url : attachment.file_name"
                            [alt]="attachment.file_name"
                            class=" me-2 cursor-pointer"
                            style="max-width: 50px; max-height: 50px; border-radius: 5px"
                            (click)="openImageModal(attachment)" />
                        </span>
                          <ng-template #popTemplate>
                            <img
                              [src]="attachment.file_url ? attachment.file_url : attachment.file_name"
                              alt="Preview"
                              style="max-width: 200px; max-height: 200px;" />
                          </ng-template>
                        </ng-container>

                        <ng-template #fileIcon>
                          <i class="fa-regular fa-2x me-2" [ngClass]="getFileTypeClass(attachment)"></i>
                        </ng-template>
                        <div class="d-flex flex-column justify-content-center align-self-start">
                          <p class="mb-0" [ngbTooltip]="attachment.file_name">
                            {{ attachment.file_name.length > 34 ? (attachment.file_name | slice:0:31) + '...' : attachment.file_name }}
                          </p>
                          <span class="font-10">{{ "orders.orderDetails.uploadedBy" | translate }}: {{ attachment.uploaded_by }}</span>
                          <span class="font-10">{{ displayDate(attachment.created_at) }}</span>
                        </div>
                      </div>
                      <div class="d-flex">
                        <app-toggle-switch [state]="attachment.visible_to_customer" (stateChange)="toggleCustomerVisibility(attachment)"></app-toggle-switch>
                        <label class="font-12">{{ "orders.orderDetails.attachments.visibleToCustomer" | translate }}</label>
                      </div>
                      <div class="d-flex">
                        <app-toggle-switch [state]="attachment.visible_to_crew" (stateChange)="toggleCrewVisibility(attachment)"></app-toggle-switch>
                        <label class="font-12">{{ "orders.orderDetails.attachments.visibleToCrew" | translate }}</label>
                      </div>
                    </div>
                    <div class="d-flex align-items-center">
                      <i *ngIf="attachment.invoice_attachment_id !== null" class="text-success fa-regular fa-envelope-open-text me-2" [ngbTooltip]="'orders.orderDetails.attachments.invoiceAttached' | translate"></i>
                      <i *ngIf="attachmentLoading !== attachment.attachment_id" class="fa-regular fa-cloud-arrow-down me-2 cursor-pointer" (click)="downloadFile(attachment)"></i>
                      <app-spinner *ngIf="attachmentLoading === attachment.attachment_id" class="me-2"></app-spinner>
                      <delete-button *ngIf="attachment.invoice_attachment_id === null" [trashIconSize]="16" (delete)="deleteAttachment(attachment)"></delete-button>
                      <i *ngIf="attachment.invoice_attachment_id !== null" class="fa-regular fa-trash-can-slash" [ngbTooltip]="'orders.orderDetails.attachments.cannotDelete' | translate"></i>
                    </div>
                  </div>
                </div>

                <!-- Modal Template for Enlarged Image -->
                <ng-template #imageModalTemplate let-modal>
                  <div class="modal-header d-flex justify-content-between align-items-center">
                    <i class="fa-regular fa-xmark fa-xl" (click)="modal.close()" style="font-weight: 400; cursor: pointer"></i>
                    <h4 class="text-center" style="flex-grow: 1;">{{ "orders.orderDetails.resources.enlargedImage" | translate }}</h4>
                  </div>
                  <div class="modal-body d-flex justify-content-center">
                    <img class="img-fluid" [src]="imageUrl" alt="Enlarged Preview">
                  </div>
                </ng-template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</app-card>
