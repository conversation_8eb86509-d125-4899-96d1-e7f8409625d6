import {Component, Input, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {OrderAttachmentResponse, OrderResponse, WorkOrderResponse} from "../../../../@shared/models/order.interfaces";
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooltip} from "@ng-bootstrap/ng-bootstrap";
import {ReportsService} from "../../../../@shared/services/reports.service";
import {OrderService} from "../../../../@shared/services/order.service";
import {AddAttachmentModalComponent} from "./_modals/add-attachment-modal/add-attachment-modal.component";
import {_CRM_ORD_110} from "../../../../@shared/models/input.interfaces";
import {StorageService} from "../../../../@core/services/storage.service";
import {CommonModule} from "@angular/common";
import {CardComponent} from "../../../../@shared/components/layout/card/card.component";
import {ButtonComponent} from "../../../../@shared/components/button/button.component";
import {SpinnerComponent} from "../../../../@shared/components/spinner/spinner.component";
import {TranslateModule} from "@ngx-translate/core";
import {Subject} from "rxjs";
import {takeUntil} from "rxjs/operators";
import {displayDate, UtilsService} from "../../../../@core/utils/utils.service";
import {StandardImports} from "../../../../@shared/global_import";
import {ToggleSwitchComponent} from "../../../../@shared/components/toggle-switch/toggle-switch.component";
import {DeleteButtonComponent} from "../../../../@shared/components/delete-button/delete-button.component";
@Component({
  selector: 'app-order-attachments',
  templateUrl: './order-attachments.component.html',
  styleUrls: ['./order-attachments.component.css'],
  standalone: true,
  imports: [StandardImports, CardComponent, NgbPopover, ToggleSwitchComponent, SpinnerComponent, DeleteButtonComponent]
})
export class OrderAttachmentsComponent implements OnInit {
  @Input() workOrder?: WorkOrderResponse;
  order?: OrderResponse;
  attachments: OrderAttachmentResponse[] = [];
  imageUrl: string;
  tripletexEnabled: boolean = false;
  attachmentLoading: number = -1;

  fileTypeMap: { [key: string]: string } = {
    'pdf': 'fa-file-pdf',
    'doc': 'fa-file-word',
    'docx': 'fa-file-word',
    'xls': 'fa-file-xls',
    'xlsx': 'fa-file-xls',
    'xml': 'fa-file-xml',
    'zip': 'fa-file-zip',
    'svg': 'fa-file-svg',
    'ppt': 'fa-file-ppt',
    'pptx': 'fa-file-ppt',
    'mp4': 'fa-file-video',
    'mov': 'fa-file-video',
    'avi': 'fa-file-video',
    'mp3': 'fa-file-music',
    'wav': 'fa-file-music',
    'eps': 'fa-file-eps',
    'gif': 'fa-file-image',
    'csv': 'fa-file-csv',
    'png': 'fa-file-image',
    'jpg': 'fa-file-image',
    'jpeg': 'fa-file-image',
  };

  destroy$ = new Subject<void>();

  // Reference the modal template
  @ViewChild('imageModalTemplate') imageModalTemplate: TemplateRef<any>;

  constructor(
    private modalService: NgbModal,
    public orderService: OrderService,
    private reportService: ReportsService,
    private storageService: StorageService,
    public utilsService: UtilsService
  ) {}

  ngOnInit() {
    if (!this.workOrder) {
      this.orderService.order$.pipe(takeUntil(this.destroy$)).subscribe(order => {
        this.order = order;
        if (this.order) {
          this.updateAttachmentsFromOrder();
        }
      });
    } else {
      this.updateAttachmentsFromOrder();
    }

    this.storageService.tripletexEnabled$.subscribe((enabled) => {
      this.tripletexEnabled = enabled;
    });
  }

  private updateAttachmentsFromOrder() {
    let attachments: OrderAttachmentResponse[] = this.workOrder ? this.workOrder.attachments! : this.order?.attachments!;
    this.attachments = [];
    for (const attachment of attachments) {
      this.attachments.push(attachment);
    }
  }

  openAddAttachmentModal() {
    let modalRef = this.modalService.open(AddAttachmentModalComponent);
    modalRef.componentInstance.order = this.order;
    modalRef.componentInstance.workOrder = this.workOrder;
    modalRef.closed.subscribe((res) => {
      if (res.work_order_id) {
        this.workOrder = res;
        this.updateAttachmentsFromOrder();
        if (!this.workOrder || this.storageService.ownedByCompany(this.workOrder.company_id)) {
          this.orderService.fetchAndRefreshOrder(res.order_id, 'added wo attachment');
        }
      }
    });
  }

  isImageFile(attachment: OrderAttachmentResponse): boolean {
    const extension = attachment.file_name.split('.').pop()?.toLowerCase() ?? '';
    return ['jpg', 'jpeg', 'png', 'gif', 'svg'].includes(extension);
  }

  openImageModal(attachment: OrderAttachmentResponse) {
    const extension = attachment.file_name.split('.').pop()?.toLowerCase() ?? '';
    if (['jpg', 'jpeg', 'png', 'gif', 'svg'].includes(extension)) {
      this.imageUrl = attachment.file_url ?? attachment.file_name ?? '';
      this.modalService.open(this.imageModalTemplate, { centered: true, size: 'lg' });
    }
  }

  deleteAttachment(attachment: OrderAttachmentResponse) {
    this.orderService.deleteOrderAttachment(attachment.attachment_id).subscribe(() => {
      this.attachments = this.attachments.filter(a => a.attachment_id !== attachment.attachment_id);
      if (this.workOrder) {
        this.workOrder = { ...this.workOrder, attachments: this.attachments };
        this.orderService.removeAttachmentInOrderSubject(attachment.attachment_id);
      }
    });
  }

  downloadFile(attachment: OrderAttachmentResponse) {
    this.attachmentLoading = attachment.attachment_id;
    this.orderService.getAttachmentFile(attachment.attachment_id, (this.order?.order_id || this.workOrder?.order_id!)).subscribe((res) => {
      const url = window.URL.createObjectURL(res);
      const anchor = document.createElement('a');
      anchor.href = url;
      anchor.download = attachment.file_name;
      document.body.appendChild(anchor);
      anchor.click();
      document.body.removeChild(anchor);
      window.URL.revokeObjectURL(url);
      this.attachmentLoading = -1;
    }, error => {
      this.attachmentLoading = -1;
    });
  }

  addAttachmentToInvoice(attachment: OrderAttachmentResponse) {
    this.attachmentLoading = attachment.attachment_id;
    let payload: _CRM_ORD_110 = {
      order_id: (this.order?.order_id || this.workOrder?.order_id!),
      attachment_id: attachment.attachment_id,
      attach_to_invoice: attachment.attach_to_invoice === 1 ? 0 : 1
    };
    this.orderService.updateOrderAttachment(payload).subscribe(res => {
      this.orderService.refreshOrder(res, 'updateOrderAttachment');
      this.attachmentLoading = -1;
    });
  }

  toggleCustomerVisibility(attachment: OrderAttachmentResponse) {
    this.attachmentLoading = attachment.attachment_id;
    let payload: _CRM_ORD_110 = {
      order_id: (this.order?.order_id || this.workOrder?.order_id!),
      attachment_id: attachment.attachment_id,
      visible_to_customer: !attachment.visible_to_customer
    };
    this.orderService.updateOrderAttachment(payload).subscribe(res => {
      if (!this.workOrder || this.storageService.ownedByCompany(this.workOrder.company_id)) {
        this.orderService.refreshOrder(res, 'updateOrderAttachment');
      }
      this.attachmentLoading = -1;
    });
  }

  toggleCrewVisibility(attachment: OrderAttachmentResponse) {
    this.attachmentLoading = attachment.attachment_id;
    let payload: _CRM_ORD_110 = {
      order_id: (this.order?.order_id || this.workOrder?.order_id!),
      attachment_id: attachment.attachment_id,
      visible_to_crew: !attachment.visible_to_crew
    };
    this.orderService.updateOrderAttachment(payload).subscribe(res => {
      if (!this.workOrder || this.storageService.ownedByCompany(this.workOrder.company_id)) {
        this.orderService.refreshOrder(res, 'updateOrderAttachment');
      }
      this.attachmentLoading = -1;
    });
  }

  getFileTypeClass(attachment: OrderAttachmentResponse) {
    const extension = attachment.file_name.split('.').pop()?.toLowerCase();
    if (!extension) {
      return 'fa-file-exclamation';
    } else {
      return this.fileTypeMap[extension] || 'fa-file';
    }
  }

  protected readonly displayDate = displayDate;
}
