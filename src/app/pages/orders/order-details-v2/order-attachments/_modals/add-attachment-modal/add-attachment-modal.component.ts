import {Component, Input, OnInit} from '@angular/core';
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {FormControl, ReactiveFormsModule} from "@angular/forms";
import {OrderResponse, WorkOrderResponse} from "../../../../../../@shared/models/order.interfaces";
import {UtilsService} from "../../../../../../@core/utils/utils.service";
import {OrderService} from "../../../../../../@shared/services/order.service";
import {CommonModule} from "@angular/common";
import {TranslateModule} from "@ngx-translate/core";
import {SpinnerComponent} from "../../../../../../@shared/components/spinner/spinner.component";
import {ButtonComponent} from "../../../../../../@shared/components/button/button.component";
import {ToastService} from "../../../../../../@core/services/toast.service";
import {StandardImports} from "../../../../../../@shared/global_import";

@Component({
  selector: 'app-add-attachment-modal',
  templateUrl: './add-attachment-modal.component.html',
  styleUrls: ['./add-attachment-modal.component.css'],
  standalone: true,
  imports: [StandardImports, SpinnerComponent]
})

export class AddAttachmentModalComponent implements OnInit {
  @Input() order?: OrderResponse;
  @Input() workOrder?: WorkOrderResponse
  selectedFile: File;
  loading: boolean = false;
  isDragging: boolean = false;

  newFileControl: FormControl = new FormControl();
  fileUrlControl: FormControl = new FormControl();


  readonly MAX_FILE_SIZE = 50 * 1024 * 1024;

  constructor(public activeModal: NgbActiveModal, private modalService: NgbModal, private orderService: OrderService, private utilsService: UtilsService, private toastService: ToastService) {}

  ngOnInit(): void {
    if (!this.order && !this.workOrder) {
      throw new Error('Order or WorkOrder is required for attachment modal');
    }
  }

  triggerFileInput() {
    const fileInput = document.getElementById('new_file_url') as HTMLInputElement;
    fileInput.click();
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input && input.files && input.files.length > 0) {
      const file = input.files[0];

      if (file.size > this.MAX_FILE_SIZE) {
        this.toastService.errorToast('exceedFileSize') ;
        input.value = '';
        return;
      }
      this.selectedFile = file;
      this.setFilePreviewAndUpload(file);
      input.value = '';
    }
  }


  setFilePreviewAndUpload(file: File) {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    this.uploadFile();
  }

  uploadFile() {
    if (this.selectedFile) {
      this.loading = true;

      if (this.workOrder) {
        this.orderService.addWorkOrderAttachment(this.selectedFile, this.workOrder!.work_order_id).subscribe(res => {
          this.loading = false;
          this.activeModal.close(res);
        });
      } else {
        this.orderService.addOrderAttachment(this.selectedFile, this.order!.order_id).subscribe(res => {
          this.loading = false;
          this.orderService.refreshOrder(res, 'uploadFile');
          this.activeModal.close();
        });

      }
    }
  }

  onFileDropped(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      const file = files[0];

      if (file.size > this.MAX_FILE_SIZE) {
        this.toastService.errorToast('exceedFileSize') ;
        return;
      }

      this.selectedFile = file;
      this.setFilePreviewAndUpload(file);
    }
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    setTimeout(() => {
      this.isDragging = false;
    }, 100);
  }

}
