import {Component, EventEmitter, forwardRef, Input, OnInit, Output} from '@angular/core';
import {<PERSON><PERSON><PERSON>oda<PERSON>, NgbTooltip} from "@ng-bootstrap/ng-bootstrap";
import {BehaviorSubject} from "rxjs";
import {TranslateModule, TranslateService} from "@ngx-translate/core";

import {StandardImports} from "../../../../../@shared/global_import";
import {ToggleSwitchComponent} from "../../../../../@shared/components/toggle-switch/toggle-switch.component";
import {OrderLinesComponent} from "../../order-lines/order-lines.component";
import {OrderPaymentResponse} from "../../../../../@shared/models/payment.interfaces";
import {DetailsViewSettings, OrderLineRow, OrderResponse, WorkOrderResponse} from "../../../../../@shared/models/order.interfaces";
import {OrderService} from "../../../../../@shared/services/order.service";
import {ToastService} from "../../../../../@core/services/toast.service";
import {PaymentService} from "../../../../../@shared/services/payment.service";
import {PaymentDetailsComponent} from "../../../../payments/components/payment-details/payment-details.component";
import {UtilsService} from "../../../../../@core/utils/utils.service";
import {PaymentDetailsV2Component} from "../../../../payments/components/payment-details-v2/payment-details-v2.component";

@Component({
  selector: 'app-single-payment-card',
  templateUrl: './single-payment-card.component.html',
  styleUrls: ['./single-payment-card.component.css'],
  standalone: true,
  imports: [StandardImports, ToggleSwitchComponent, OrderLinesComponent],
})
export class SinglePaymentCardComponent implements OnInit {
  @Input() payment: OrderPaymentResponse
  @Input() viewSettings: DetailsViewSettings = {paymentView: true};
  @Input() workOrder?: WorkOrderResponse;
  @Output() paymentUpdatedEmitter = new EventEmitter<boolean>()
  order: OrderResponse;
  orderLinesSubject: BehaviorSubject<OrderLineRow[]> = new BehaviorSubject<OrderLineRow[]>([]);
  today = new Date();
  creationLoading: boolean = false;

  constructor(private modalService: NgbModal, protected orderService: OrderService, private toastService: ToastService, private paymentService: PaymentService, private translate: TranslateService, public utilsService: UtilsService) {
  }

  ngOnInit(){
    this.orderLinesSubject.next(this.payment.order_lines.sort((a, b) => {
      if (a.locked === b.locked) {return a.index - b.index;}
      return a.locked ? 1 : -1;}).map(orderLine => {
        return {
          ...orderLine,
          checked: true,
        }
      }
    ));
    this.orderService.order$.subscribe(order => {
      let prePaymentRecipientSet: boolean = this.order ? !!this.order.payment_recipient : false;
      this.order = order;
      let postPaymentRecipientSet: boolean = this.order ? !!this.order.payment_recipient : false;
      if (prePaymentRecipientSet !== postPaymentRecipientSet) {
        this.paymentService.getOrderPayment(this.payment.payment_id).subscribe(payment => {
          this.payment = payment;
        });
      }

    });
  }

  openPayment() {
    let modalRef = this.modalService.open(PaymentDetailsV2Component, {size: 'lg', centered: true});
    modalRef.componentInstance.payment = this.payment;
    modalRef.componentInstance.workOrder = this.workOrder;
    modalRef.componentInstance.viewSettings = {
      modalView: true,
      paymentView: true,
    };
    modalRef.componentInstance.paymentUpdated.subscribe((result: OrderPaymentResponse) => {
      this.payment = result;
    });
    modalRef.result.then((result) => {
      this.paymentUpdatedEmitter.emit(result);
    });
  }
}
