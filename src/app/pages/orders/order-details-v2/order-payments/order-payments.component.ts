import {Component, Input, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {DetailsViewSettings, OrderLineRow, OrderResponse, WorkOrderCompactResponse} from "../../../../@shared/models/order.interfaces";
import {OrderService} from "../../../../@shared/services/order.service";
import {OrderPaymentResponse, OrderPaymentResponseCompact} from "../../../../@shared/models/payment.interfaces";
import {PaymentService} from "../../../../@shared/services/payment.service";
import {capitaliseFirstLetter, currencyFormat, displayDate, formatDateDMY, paymentStatusBadge, UtilsService} from "../../../../@core/utils/utils.service";
import {ToastService} from "../../../../@core/services/toast.service";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {PaymentDetailsComponent} from "../../../payments/components/payment-details/payment-details.component";
import {TranslateService} from "@ngx-translate/core";
import {StorageService} from "../../../../@core/services/storage.service";
import {StandardImports} from "../../../../@shared/global_import";
import {CardComponent} from "../../../../@shared/components/layout/card/card.component";
import {SpinnerComponent} from "../../../../@shared/components/spinner/spinner.component";
import {ButtonDoubleComponent} from "../../../../@shared/components/button-double/button-double.component";
import {PaymentDetailsV2Component} from "../../../payments/components/payment-details-v2/payment-details-v2.component";
import {BehaviorSubject, combineLatest, combineLatestAll, Subject} from "rxjs";
import {TablerinoColumn, TablerinoComponent} from "../../../../@shared/components/tablerino/tablerino.component";
import {formatCurrency} from "@angular/common";
import {takeUntil} from "rxjs/operators";

@Component({
    selector: 'app-order-payments',
    templateUrl: './order-payments.component.html',
    styleUrl: './order-payments.component.css',
    standalone: true,
  imports: [StandardImports, CardComponent, SpinnerComponent, ButtonDoubleComponent, PaymentDetailsComponent, PaymentDetailsV2Component, TablerinoComponent]
})
export class OrderPaymentsComponent implements OnInit, OnDestroy {
  constructor(
    private orderService: OrderService,
    private paymentService: PaymentService,
    public utilsService: UtilsService,
    private toastService: ToastService,
    private modalService: NgbModal,
    private translateService: TranslateService,
    private storageService: StorageService
  ) {}

  viewSettings: DetailsViewSettings = {paymentView: true};
  order: OrderResponse;
  ghostedPaymentClicked = false;
  activePayment: OrderPaymentResponse | null = null;
  payments: OrderPaymentResponse[] = [];
  paymentSchedules: OrderPaymentResponse[] = [];
  loading = true;
  paymentIdLoading: number | null = null;
  expandedIds: number[] = [];
  tripletexEnabled: boolean = false;
  operateExVat: boolean = false;
  refreshKey: number = 1;

  singlePaymentColumnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);
  repeatingPaymentColumnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);

  destroy$: Subject<void> = new Subject<void>();

  ngOnInit() {
    this.initColumns();
    this.storageService.tripletexEnabled$.pipe(takeUntil(this.destroy$)).subscribe((enabled) => {
      this.tripletexEnabled = enabled;
    });

    this.storageService.operateExVat$.pipe(takeUntil(this.destroy$)).subscribe((operateExVat) => {
      this.operateExVat = operateExVat;
    });

    combineLatest([this.orderService.orderPaymentSchedules$, this.orderService.orderPayments$]).pipe(takeUntil(this.destroy$)).subscribe(([schedules, payments]) => {
      this.paymentSchedules = schedules;
      this.payments = payments.sort((a, b) => b.payment_id - a.payment_id);
    });

    // this.orderService.orderPayments$.pipe(takeUntil(this.destroy$)).subscribe((payments) => {
    //   this.loading = false;
    //   if (!payments) return;
    // });

    this.orderService.order$.pipe(takeUntil(this.destroy$)).subscribe((order) => {
      this.order = order;
      this.viewSettings.repeatingView = this.order.repeating;
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  setRepeatingView(repeating: boolean) {
    this.viewSettings.repeatingView = repeating;
  }

  initColumns() {
    this.singlePaymentColumnsSubject.next([
      {
        name: 'id',
        labelKey: 'ID',
        formatter: (p: OrderPaymentResponse) => '#' + p.payment_number,
        sort: false,
        visible: true,
      },
      {
        name: 'payment_name',
        labelKey: 'Navn',
        formatter: (p: OrderPaymentResponse) => p.payment_name,
        sort: false,
        visible: true,
      },
      {
        name: 'invoice_sent_at',
        labelKey: 'Dato sendt',
        formatter: (p: OrderPaymentResponse) => displayDate(p.invoice_sent_at, false),
        sort: false,
        visible: true,
      },
      {
        name: 'status',
        labelKey: 'Status',
        formatter: (p: OrderPaymentResponse) => paymentStatusBadge(p),
        sort: false,
        visible: true,
      },
      {
        name: 'amount',
        labelKey: 'Beløp',
        formatter: (p: OrderPaymentResponse) => currencyFormat(p.total_amount_inc_vat),
        sort: false,
        visible: true,
      },
    ]);

    this.repeatingPaymentColumnsSubject.next([
      {
        name: 'payment_name',
        labelKey: 'Navn',
        formatter: (p: OrderPaymentResponse) => p.payment_name,
        sort: false,
        visible: true,
      },
      {
        name: 'schedule_description',
        labelKey: 'Frekvens',
        formatter: (p: OrderPaymentResponse) => this.formatPaymentScheduleDescription(p),
        sort: false,
        visible: true,
      },
      {
        name: 'amount',
        labelKey: 'Beløp',
        formatter: (p: OrderPaymentResponse) => currencyFormat(p.total_amount_inc_vat),
        sort: false,
        visible: true,
      },
    ]);
  }

  openPaymentModal(payment?: OrderPaymentResponse) {
    const modalRef = this.modalService.open(PaymentDetailsV2Component, {
      size: 'lg',
      centered: true,
    });
    modalRef.componentInstance.payment = payment;
    modalRef.componentInstance.order = this.order;
    modalRef.componentInstance.viewSettings = {modalView: true, createView: !payment, orderDetailsView: true, repeatingView: this.viewSettings.repeatingView};
    modalRef.result.then(() => {
      this.refreshKey++;
    }, () => {
      this.refreshKey++;
    });
  }

  formatPaymentScheduleDescription(payment: OrderPaymentResponse): string {
    // if ()
    return payment.payment_schedule?.schedule_description!

  }
}
