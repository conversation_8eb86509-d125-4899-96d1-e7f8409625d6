<app-card
  [labelKey]="'Fakturering'"
  [padding]="'0'"
  [buttonHeader]="true"
  [ghosted]="payments.length == 0 && paymentSchedules.length == 0 && !ghostedPaymentClicked"
  (ghostChangedEmitter)="ghostedPaymentClicked = true">
  <ng-container buttonHeader>
    <div class="d-flex justify-content-end">
      <app-button
        [translationKey]="viewSettings.repeatingView ? 'Ny fakturaplan' : 'Ny faktura'"
        [buttonType]="'nude'"
        [small]="true"
        [disabled]="order.order_status_id === 8"
        (buttonClick)="openPaymentModal()"
      ></app-button>
    </div>
  </ng-container>

  <ng-container cardcontent>
    <!--  Repeating payment single view  -->
<!--    <div *ngIf="payments.length === 0 && paymentSchedules.length === 1">-->
<!--      <app-payment-details-v2-->
<!--        [payment]="paymentSchedules[0]"-->
<!--        [order]="order"-->
<!--        [viewSettings]="{paymentView: true, createView: false, repeatingView: true, orderDetailsView: true}"-->
<!--      ></app-payment-details-v2>-->
<!--    </div>-->

    <!-- Split view  -->
    <div *ngIf="payments.length > 0 || paymentSchedules.length > 0">
      <app-button-double
        *ngIf="order.repeating || paymentSchedules.length > 0"
        [leftButtonTranslationKey]="'Fakturaplaner'"
        [rightButtonTranslationKey]="'Fakturaer'"
        [leftButtonActive]="!!viewSettings.repeatingView"
        [rightButtonActive]="!viewSettings.repeatingView"
        [boldText]="true"
        [useMaxWidth]="true"
        (leftButtonClick)="setRepeatingView(true)"
        (rightButtonClick)="setRepeatingView(false)"
      ></app-button-double>

      <!--   Repeating payments   -->
      <div *ngIf="viewSettings.repeatingView" class="">

        <!--    Single repeating view    -->
<!--        <div *ngIf="paymentSchedules.length === 1">-->
<!--          <app-payment-details-v2-->
<!--            [payment]="paymentSchedules[0]"-->
<!--            [order]="order"-->
<!--            [viewSettings]="{paymentView: true, createView: false, repeatingView: true, orderDetailsView: true}"-->
<!--          ></app-payment-details-v2>-->
<!--        </div>-->

        <!--    Tablerino repeating view    -->
        <div *ngIf="paymentSchedules.length > 0" class="overflow-hidden" style="border-bottom-right-radius: 10px; border-bottom-left-radius: 10px;">
          <app-tablerino
            [tableName]="'order-payment-details-repeating-list'"
            [columnsSubject]="repeatingPaymentColumnsSubject"
            [tableData]="paymentSchedules"
            [settings]="{clickableRows: true, checkboxes: true}"
            [loading]="false"
            [noResultsTranslationKey]="'payments.noPayments'"
            (rowClickedEmitter)="openPaymentModal($event)"
          ></app-tablerino>
        </div>
      </div>

      <!--   Tablerino single payments   -->
      <div *ngIf="!viewSettings.repeatingView" class="overflow-hidden" style="border-bottom-right-radius: 10px; border-bottom-left-radius: 10px;">
        <app-tablerino
          [tableName]="'order-payment-details-single-list'"
          [columnsSubject]="singlePaymentColumnsSubject"
          [tableData]="payments"
          [settings]="{clickableRows: true, checkboxes: true}"
          [loading]="false"
          [noResultsTranslationKey]="'payments.noPayments'"
          (rowClickedEmitter)="openPaymentModal($event)"
        ></app-tablerino>
      </div>

    </div>

  </ng-container>

</app-card>
