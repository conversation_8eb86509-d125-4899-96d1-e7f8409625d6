<div class="order-details-card shadow-sm p-0">

  <!--   Schedule data   -->
  <div class="px-2 pt-2 pb-0">

    <!--    Payment method    -->
    <div class="d-flex">
      <span class="badge between-color-bg badge-min-width" style="padding: 3px 2px;">{{payment.payment_method_name}}</span>
      <span *ngIf="payment.payment_method_id === 14 && !payment.subscription_active" class="badge bg-danger badge-min-width ms-2">{{"orderDetails.paymentSchedules.noSubscriptionActive" | translate}}</span>
<!--      <span *ngIf="!payment.payment_schedule" (click)="changeAutoSending()" class="badge badge-min-width ms-2 cursor-pointer" [ngClass]="payment.auto_send_to_payment ? 'between-color-bg' : 'bg-danger'" [ngbTooltip]="'orderDetails.paymentSchedules.' + (payment.auto_send_to_payment ? 'autoSendEnabled.toolTip' : 'autoSendDisabled.toolTip') + (payment.payment_method_id === 15 ? '.consolidated' : '') | translate">{{'orderDetails.paymentSchedules.' + (payment.auto_send_to_payment ? 'autoSendEnabled' : 'autoSendDisabled') + (payment.payment_method_id === 15 ? '.consolidated' : '') | translate}}</span>-->
    </div>

    <!--   Frequency description     -->
    <div class="mt-1">
      <div class="">
        <i class="fa-regular fa-arrows-rotate me-1 cursor-pointer" [ngClass]="{'spin-animation': creationLoading}" (click)="initiatePaymentSchedule()"></i>
        <span *ngIf="!payment.payment_schedule">{{"orderDetails.paymentSchedules.frequency" | translate}} {{workOrder?.work_order_title?.toLowerCase()}}</span>
        <span *ngIf="payment.payment_schedule" [ngClass]="{'text-danger': !payment.payment_schedule.active}">{{payment.payment_schedule.active ? (("orderDetails.paymentSchedules.frequency.sent" | translate) + ' ' + payment.payment_schedule.schedule_description) : ('orderDetails.paymentSchedules.deactivated' | translate) }}</span>
      </div>
    </div>

    <!--   Start/Stop dates   -->
    <div *ngIf="payment.payment_schedule && (payment.payment_schedule.start_date > today || payment.payment_schedule.end_date)" class="d-flex mb-1">
      <div *ngIf="payment.payment_schedule.start_date > today" class="me-2" [ngClass]="{'text-muted': !payment.payment_schedule.active}">
        <i class="fa-regular fa-play me-1"></i>
        <span>
          {{"orderDetails.paymentSchedules.startsAfter" | translate}} {{utilsService.formatDateWdDYM(payment.payment_schedule.start_date, false, false)}}
        </span>
      </div>
      <div *ngIf="payment.payment_schedule.end_date" [ngClass]="{'text-muted': !payment.payment_schedule.active || payment.payment_schedule.end_date && payment.payment_schedule.end_date < today}">
        <i class="fa-regular fa-circle-stop me-1"></i>
        <span>
          {{"orderDetails.paymentSchedules.stopsAfter" | translate}} {{utilsService.formatDateWdDYM(payment.payment_schedule.end_date, false, false)}}
        </span>
      </div>
    </div>

    <!--  Bottom line of schedule data  -->
    <div class="d-flex align-items-end gap-2 mt-1" [ngClass]="{'pb-2': workOrder}">
      <!--   Next payment   -->
      <div *ngIf="payment.payment_schedule">
        <div class="fw-bold font-12">{{"orderDetails.paymentSchedules.nextPayment" | translate}}</div>
        <div *ngIf="payment.next_payment">{{utilsService.formatDateWdDYM(payment.next_payment.auto_send_at, false, false)}}</div>
        <div *ngIf="!payment.next_payment">{{"orderDetails.paymentSchedules.nextPayment.empty" | translate}}</div>
      </div>

      <!--   Previous payment   -->
      <div *ngIf="payment.payment_schedule" class="ps-2" style="border-left: 1px solid #DEE2E6">
        <div class="fw-bold font-12">{{"orderDetails.paymentSchedules.lastPayment" | translate}}</div>
        <div *ngIf="payment.previous_payment">{{utilsService.formatDateWdDYM(payment.previous_payment.auto_send_at, false, false)}}</div>
        <div *ngIf="!payment.previous_payment">{{"orderDetails.paymentSchedules.lastPayment.empty" | translate}}</div>
      </div>

      <!--   Total unpaid sent   -->
      <div *ngIf="!payment.payment_schedule" class="ps-2">
        <div class="fw-bold font-12">{{"orderDetails.paymentSchedules.totalUnpaidSentPayments" | translate}}</div>
        <div>{{payment.total_unpaid_sent_payments}}</div>
      </div>

      <!--   Total payments   -->
      <div class="ps-2" style="border-left: 1px solid #DEE2E6">
        <div class="fw-bold font-12">{{"orderDetails.paymentSchedules.totalPayments" | translate}}</div>
        <div>{{payment.total_payments}}</div>
      </div>
    </div>
  </div>

  <div *ngIf="!workOrder" class="mt-1">
    <app-order-lines-summary [compactView]="true" [paymentView]="true" [orderLinesSubject]="orderLinesSubject" ></app-order-lines-summary>
  </div>

</div>
