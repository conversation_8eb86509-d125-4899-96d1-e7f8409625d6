import {Component, EventEmitter, forwardRef, Input, OnInit, Output} from '@angular/core';
import {BehaviorSubject} from "rxjs";
import {OrderPaymentResponse} from "../../../../../@shared/models/payment.interfaces";
import {OrderLineRow, OrderResponse, WorkOrderResponse} from "../../../../../@shared/models/order.interfaces";
import {OrderService} from "../../../../../@shared/services/order.service";
import {UtilsService} from "../../../../../@core/utils/utils.service";
import {_CRM_ORD_118} from "../../../../../@shared/models/input.interfaces";
import {PaymentService} from "../../../../../@shared/services/payment.service";
import {ToastService} from "../../../../../@core/services/toast.service";
import {StandardImports} from "../../../../../@shared/global_import";
import {OrderLinesSummaryComponent} from "../../order-lines-summary/order-lines-summary-component";

@Component({
  selector: 'order-payment-schedule-summary',
  templateUrl: './payment-schedule-summary.component.html',
  styleUrls: ['./payment-schedule-summary.component.css'],
  standalone: true,
  imports: [StandardImports, OrderLinesSummaryComponent]
})
export class PaymentScheduleSummaryComponent implements OnInit {
  @Input() payment: OrderPaymentResponse;
  @Input() workOrder?: WorkOrderResponse;
  @Input() orderLinesSubject: BehaviorSubject<OrderLineRow[]> = new BehaviorSubject<OrderLineRow[]>([]);
  @Output() paymentUpdatedEmitter = new EventEmitter<boolean>()
  today = new Date();
  creationLoading: boolean = false;

  constructor(protected orderService: OrderService, public utilsService: UtilsService, private paymentService: PaymentService, private toastService: ToastService) {
  }

  ngOnInit() {
  }

  changeAutoSending() {
    // let payload: _CRM_ORD_118 = {
      // work_order_id: this.payment.work_order_id!,
      // auto_send_to_payment: !this.payment.auto_send_to_payment
    // }
    // this.orderService.patchWorkOrder(payload).subscribe(() => {
    //   this.orderService.fetchAndUpdateWorkOrder(this.payment.work_order_id!, true, 'paymentScheduleSummaryChangeAutoSend');
    // });
  }

  initiatePaymentSchedule() {
    if (!this.payment.payment_schedule || !this.payment.payment_schedule.active) return;
    this.creationLoading = true;
    this.paymentService.manuallyInitiatePaymentSchedule(this.payment.payment_schedule?.payment_schedule_id!).subscribe(() => {
      this.orderService.fetchAndRefreshOrderPayments(this.payment.order_id!);
      this.orderService.fetchAndRefreshOrderPaymentSchedules(this.payment.order_id!);
      this.toastService.successToast('updated');
      this.creationLoading = false;
      this.paymentUpdatedEmitter.emit(true);
    }, error => {
      this.creationLoading = false;
    });
  }

}
