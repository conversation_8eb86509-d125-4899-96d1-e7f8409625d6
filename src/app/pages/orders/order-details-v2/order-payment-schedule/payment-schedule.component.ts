import {Component, EventEmitter, forwardRef, Input, OnInit, Output} from '@angular/core';
import {DetailsViewSettings, OrderLineRow, OrderResponse, WorkOrderResponse} from "../../../../@shared/models/order.interfaces";
import {Ng<PERSON><PERSON>odal, NgbTooltip} from "@ng-bootstrap/ng-bootstrap";
import {OrderService} from "../../../../@shared/services/order.service";
import {ToastService} from "../../../../@core/services/toast.service";
import {UtilsService} from "../../../../@core/utils/utils.service";
import {PaymentService} from "../../../../@shared/services/payment.service";
import {BehaviorSubject} from "rxjs";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {PaymentDetailsComponent} from "../../../payments/components/payment-details/payment-details.component";
import {OrderPaymentResponse} from "../../../../@shared/models/payment.interfaces";

import {_CRM_PAY_27} from "../../../../@shared/models/input.interfaces";
import {StandardImports} from "../../../../@shared/global_import";
import {ToggleSwitchComponent} from "../../../../@shared/components/toggle-switch/toggle-switch.component";
import {OrderLinesComponent} from "../order-lines/order-lines.component";
import {PaymentScheduleSummaryComponent} from "./components/payment-schedule-summary.component";
import {PaymentDetailsV2Component} from "../../../payments/components/payment-details-v2/payment-details-v2.component";

@Component({
  selector: 'order-payment-schedule',
  templateUrl: './payment-schedule.component.html',
  styleUrls: ['./payment-schedule.component.css'],
  standalone: true,
  imports: [StandardImports, ToggleSwitchComponent, OrderLinesComponent, PaymentScheduleSummaryComponent],
})
export class PaymentScheduleComponent implements OnInit {
  @Input() payment: OrderPaymentResponse
  @Input() viewSettings: DetailsViewSettings = {paymentView: true};
  @Input() workOrder?: WorkOrderResponse;
  @Output() paymentUpdatedEmitter = new EventEmitter<boolean>()
  order: OrderResponse;
  orderLinesSubject: BehaviorSubject<OrderLineRow[]> = new BehaviorSubject<OrderLineRow[]>([]);
  today = new Date();
  creationLoading: boolean = false;

  constructor(private modalService: NgbModal, protected orderService: OrderService, private toastService: ToastService, private paymentService: PaymentService, private translate: TranslateService, public utilsService: UtilsService) {
  }

  ngOnInit() {
    this.orderLinesSubject.next(this.payment.order_lines.sort((a, b) => {
      if (a.locked === b.locked) {return a.index - b.index;}
      return a.locked ? 1 : -1;}).map(orderLine => {
        return {
          ...orderLine,
          checked: true,
        }
      }
    ));
    this.orderService.order$.subscribe(order => {
      let prePaymentRecipientSet: boolean = this.order ? !!this.order.payment_recipient : false;
      this.order = order;
      let postPaymentRecipientSet: boolean = this.order ? !!this.order.payment_recipient : false;
      if (prePaymentRecipientSet !== postPaymentRecipientSet) {
        this.paymentService.getOrderPayment(this.payment.payment_id).subscribe(payment => {
          this.payment = payment;
        });
      }

    });
  }

  editSchedule() {
    let modalRef = this.modalService.open(PaymentDetailsV2Component, {size: 'lg', centered: true});
    modalRef.componentInstance.payment = this.payment;
    modalRef.componentInstance.workOrder = this.workOrder;
    modalRef.componentInstance.viewSettings = {
      modalView: true,
      repeatingView: !this.workOrder,
      paymentView: true,
    };
    modalRef.componentInstance.paymentUpdated.subscribe((result: OrderPaymentResponse) => {
      this.payment = result;
    });
    modalRef.result.then((result) => {
      this.paymentUpdatedEmitter.emit(result);
    });
  }

  initiatePaymentSchedule() {
    this.creationLoading = true;
    this.paymentService.manuallyInitiatePaymentSchedule(this.payment.payment_schedule?.payment_schedule_id!).subscribe(() => {
      this.orderService.fetchAndRefreshOrderPayments(this.payment.order_id!);
      this.orderService.fetchAndRefreshOrderPaymentSchedules(this.payment.order_id!);
      this.creationLoading = false;
      this.paymentUpdatedEmitter.emit(true);
    }, error => {
      this.creationLoading = false;
    });
  }

  toggleScheduleActiveStatus() {
    let payload: _CRM_PAY_27 = {
      payment_id: this.payment.payment_id,
      schedule: {
        active: !this.payment.payment_schedule!.active
      }
    }
    this.paymentService.updateOrderPayment(payload).subscribe((res) => {
      this.toastService.successToast('updated')
      if (res.payment_schedule?.active) {
        this.initiatePaymentSchedule();
      }
      this.payment = res;
    });

  }

}
