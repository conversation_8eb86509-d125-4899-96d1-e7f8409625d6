<div class="card order-details-card pt-2 px-2 pb-2">
  <div class="card-body p-0">
    <div class="mb-2 d-flex justify-content-between">
      <h4 *ngIf="!viewSettings.repeatingView" class="fw-bold">{{ "orderDetails.paymentSchedules.title" | translate }} #{{payment.payment_number}} {{payment.work_order_title ? 'for ' + payment.work_order_title : ''}}</h4>
      <div *ngIf="viewSettings.repeatingView" class="fw-bold d-flex align-items-center">{{"orderDetails.paymentSchedules.title.perWorkOrder" | translate}}</div>
      <div class="d-flex justify-content-end">
        <div *ngIf="payment.payment_schedule" class="d-flex align-items-center me-3">
          <div [ngbTooltip]="!payment.payment_recipient ? ('orderDetails.paymentSchedules.addCustomerBeforeActivating' | translate) : (!order || order.order_status_id < 2 ? ('orderDetails.paymentSchedules.confirmOrderBeforeActivating' | translate) : '')">
            <app-toggle-switch
              [bigSwitch]="true"
              [labelKey]="'orderDetails.paymentSchedules.active'"
              [isDisabled]="!payment.payment_recipient && (!order || !order.payment_recipient) || (!order || order.order_status_id < 2 || order.order_status_id == 8)"
              [state]="payment.payment_schedule.active"
              (stateChange)="toggleScheduleActiveStatus()"
            ></app-toggle-switch>
          </div>
          <i class="fa-regular fa-info-circle cursor-pointer ms-1" [ngbTooltip]="('orderDetails.paymentSchedules.activeTooltip' | translate)"></i>
        </div>
        <app-button [buttonType]="'nude'" [translationKey]="'common.edit'" (buttonClick)="editSchedule()"></app-button>
      </div>
    </div>


    <!-- Order lines -->
    <div class="mb-2">
      <app-order-lines [payment]="payment" [expanded]="false" [workOrder]="workOrder" [viewSettings]="viewSettings" [orderLinesSubject]="orderLinesSubject"></app-order-lines>
    </div>

    <div class="pe-2">
      <order-payment-schedule-summary [payment]="payment" [orderLinesSubject]="orderLinesSubject" (paymentUpdatedEmitter)="paymentUpdatedEmitter.emit($event)"></order-payment-schedule-summary>
    </div>

  </div>

</div>
