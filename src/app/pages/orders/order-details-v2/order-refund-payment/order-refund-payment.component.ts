import {Component, Input, OnInit} from '@angular/core';
import {OrderResponse} from "../../../../@shared/models/order.interfaces";
import {FormControl} from "@angular/forms";
import {_CRM_ORD_97} from "../../../../@shared/models/input.interfaces";
import {OrderService} from "../../../../@shared/services/order.service";
import {OrderPaymentResponse} from "../../../../@shared/models/payment.interfaces";
import {PaymentService} from "../../../../@shared/services/payment.service";
import {currencyFormat, getPaymentStatusColor, UtilsService} from "../../../../@core/utils/utils.service";
import {ToastService} from "../../../../@core/services/toast.service";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {PaymentDetailsComponent} from "../../../payments/components/payment-details/payment-details.component";
import {StandardImports} from "../../../../@shared/global_import";
import {CardComponent} from "../../../../@shared/components/layout/card/card.component";
import {SpinnerComponent} from "../../../../@shared/components/spinner/spinner.component";

@Component({
    selector: 'app-order-refund-payments',
    templateUrl: './order-refund-payment.component.html',
    styleUrl: './order-refund-payment.component.css',
    standalone: true,
  imports: [StandardImports, CardComponent, SpinnerComponent]
})
export class OrderRefundPaymentComponent implements OnInit {
  constructor(
    private orderService: OrderService,
    private paymentService: PaymentService,
    public utilsService: UtilsService,
    private toastService: ToastService,
    private modalService: NgbModal
  ) {}

  // order: OrderResponse;
  payments: OrderPaymentResponse[];
  loading = true;
  paymentIdLoading: number | null = null;
  expandedIds: number[] = [];
  accountingStatusColorMap: {[key: number]: string} = {
    1: '',
    2: 'text-success',
    3: 'text-warning',
    4: '',
    5: '',
  }
  accountingPaymentStatusColorMap: {[key: number]: string} = {
    0: 'text-success',
    1: '',
    4: '',
    5: '',
    6: 'text-success',
    7: 'text-warning',
  }

  ngOnInit() {
    this.orderService.orderRefundPayments$.subscribe((payments) => {
      this.loading = false;
      if (!payments) return;
      this.payments = payments.sort((a, b) => b.payment_id - a.payment_id);
    });
  }

  toggleExpand(paymentId: number) {
    const index = this.expandedIds.indexOf(paymentId);
    if (index === -1) {
      this.expandedIds.push(paymentId);
    } else {
      this.expandedIds.splice(index, 1);
    }
  }

  checkPaymentStatus(payment: OrderPaymentResponse) {
    this.paymentIdLoading = payment.payment_id;
    this.paymentService.checkAccountingPaymentStatus(payment.payment_id).subscribe((res) => {
      if (res.payment_status_id === 3) {
        this.orderService.fetchAndRefreshOrder(payment.order_id!, 'checkPaymentStatus', false);
      }
      this.paymentIdLoading = null;
    }, error => {
      this.paymentIdLoading = null;
    });
  }

  protected readonly currencyFormat = currencyFormat;
  protected readonly getPaymentStatusColor = getPaymentStatusColor;
}
