import {Component, HostListener, Input, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {NgbActiveModal, NgbModal, NgbTooltip} from '@ng-bootstrap/ng-bootstrap';
import {OrderResponse, WorkOrderResponse} from "../../../../../../@shared/models/order.interfaces";
import {_CRM_ORD_118, _CRM_ORD_97} from "../../../../../../@shared/models/input.interfaces";
import {VerifyPopupModal} from "../../../../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {TimeTrackingActivityResponse} from "../../../../../../@shared/models/timetracking.interfaces";
import {OrderService} from "../../../../../../@shared/services/order.service";
import {ToastService} from "../../../../../../@core/services/toast.service";
import {TrackingService} from "../../../../../../@shared/services/tracking.service";
import {FormControl} from "@angular/forms";
import {SettingsService} from "../../../../../../@shared/services/settings.service";
import {StandardImports} from "../../../../../../@shared/global_import";
import {SelectoriniComponent} from "../../../../../../@shared/components/selectorini/selectorini.component";
import {ToggleSwitchComponent} from "../../../../../../@shared/components/toggle-switch/toggle-switch.component";
import {HelpIconComponent} from "../../../../../../@shared/components/help-icon/help-icon.component";

@Component({
    selector: 'app-verify-popup-modal',
    templateUrl: './timetracking-settings-modal.html',
    standalone: true,
  imports: [StandardImports, SelectoriniComponent, ToggleSwitchComponent, HelpIconComponent]
})

export class TimetrackingSettingsModal implements OnInit {

  @Input() workOrder?: WorkOrderResponse;
  @Input() order?: OrderResponse;

  loading: boolean = false;

  convertTrackedTimeToHours: boolean = false;
  activities: TimeTrackingActivityResponse[] = [];
  selectedActivity: TimeTrackingActivityResponse | null = null;
  transportMinutesControl = new FormControl(0)
  transportActive: boolean = false;
  defaultTransportActivitySet = false;
  defaultPauseActive: boolean = false;
  defaultPauseDurationControl: FormControl = new FormControl(0);
  defaultPauseStartTimeControl: FormControl = new FormControl('');



  @ViewChild('activitySelectoriniItem') activitySelectoriniItem: TemplateRef<any>;

  constructor(public activeModal: NgbActiveModal, private modalService: NgbModal, private orderService: OrderService, private toastService: ToastService, private trackingService: TrackingService, private settingsService: SettingsService) { }

  ngOnInit() {
    if (this.order) {
      this.convertTrackedTimeToHours = this.order.convert_tracked_time_to_working_hours;
    } else if (this.workOrder) {
      this.defaultPauseActive = this.workOrder.default_pause_duration_minutes != null;
      if (this.workOrder.default_pause_duration_minutes != null) {
        this.defaultPauseDurationControl.setValue(this.workOrder.default_pause_duration_minutes);
      }
      if (this.workOrder.default_pause_start_time != null) {
        this.defaultPauseStartTimeControl.setValue(this.workOrder.default_pause_start_time);
      }
      this.settingsService.getCompanyGeneralSettings().subscribe((settings) => {
        this.defaultTransportActivitySet = settings.default_transport_activity_id !== null;
      });

      this.convertTrackedTimeToHours = this.workOrder.convert_tracked_time_to_working_hours;
      if (this.workOrder.fixed_transport_duration_minutes) {
        this.transportActive = true;
        this.transportMinutesControl.setValue(this.workOrder.fixed_transport_duration_minutes);
      }
    }

    this.trackingService.getTimeTrackingActivities().subscribe((activities) => {
      this.activities = activities.sort((a, b) => a.index - b.index).map((a) => {
        return {
          ...a,
          ngTemplate: this.activitySelectoriniItem,
        };
      });

      if (this.workOrder) {
        this.selectedActivity = activities.find((activity) => activity.activity_id === this.workOrder?.activity_id) || null;
      } else {
        this.selectedActivity = activities.find((activity) => activity.activity_id === this.order?.activity_id) || null;
      }
    });
  }


  activitySelected(activity: TimeTrackingActivityResponse | any) {
    this.selectedActivity = activity;
  }

  async save() {
    if (!this.selectedActivity) return;

    if (this.workOrder) {
      let payload: _CRM_ORD_118 = {
        work_order_id: this.workOrder?.work_order_id!,
        convert_tracked_time_to_working_hours: this.convertTrackedTimeToHours,
        activity_id: this.selectedActivity?.activity_id,
        fixed_transport_duration_minutes: this.transportActive ? this.transportMinutesControl.value : null,
        default_pause_duration_minutes: this.defaultPauseActive ? this.defaultPauseDurationControl.value : null,
        default_pause_start_time: this.defaultPauseActive ? this.defaultPauseStartTimeControl.value : null,
      }

      let updateChildren = false;

      if (this.workOrder?.schedule?.num_unstarted_work_orders! > 1) {
        let modalRef = this.modalService.open(VerifyPopupModal, {backdrop: 'static'});
        modalRef.componentInstance.showBody = true;
        modalRef.componentInstance.titleTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.title';
        modalRef.componentInstance.bodyBoldTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyBoldTranslationKey';
        modalRef.componentInstance.bodyMutedTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyMutedTranslationKey';
        modalRef.componentInstance.bodyRegularTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.registrationType';
        try {
          payload.update_activity_and_time_tracking = await modalRef.result;
          updateChildren = payload.update_activity_and_time_tracking!;
        } catch (e) {}
      }
      this.loading = true;
      this.orderService.patchWorkOrder(payload).subscribe(
        (response) => {
          this.orderService.refreshSingleWorkOrder(response, 'updateConvertTrackedTime');
          this.loading = false;
          this.activeModal.close(true);
          if (updateChildren) {
            this.toastService.successToast('updated');
            this.orderService.refreshWorkOrders$.next();
          }
        }, error => {
          this.loading = false;
        }
      );
    } else {
      let params: _CRM_ORD_97 = {
        order_id: this.order?.order_id!,
        convert_tracked_time_to_working_hours: this.convertTrackedTimeToHours,
        activity_id: this.selectedActivity?.activity_id
      }
      this.loading = true;
      this.orderService.patchOrder(params).subscribe(
        (response) => {
          this.orderService.refreshOrder(response, 'updateConvertTrackedTime');
          this.loading = false;
          this.activeModal.close(true);
        }, error => {
          this.loading = false;
        }
      );
    }
  }

  cancel() {
    this.activeModal.close(false)
  }
}
