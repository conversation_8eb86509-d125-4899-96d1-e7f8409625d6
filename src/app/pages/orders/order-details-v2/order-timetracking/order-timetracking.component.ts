import {Component, Input, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {OrderLineResponse, OrderLineStageUserTimeTrackingResponse, OrderResponse, UserTimeTrackingResponse, WorkOrderResponse} from "../../../../@shared/models/order.interfaces";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {OrderService} from "../../../../@shared/services/order.service";
import {UtilsService} from "../../../../@core/utils/utils.service";
import {CommonModule} from "@angular/common";
import {NgbAccordionBody, NgbAccordionButton, NgbAccordionCollapse, NgbAccordionDirective, NgbAccordionHeader, NgbAccordionItem, NgbModal, NgbTooltip} from "@ng-bootstrap/ng-bootstrap";
import {takeUntil} from "rxjs/operators";
import {Subject} from "rxjs";
import {TimeTrackingActivityResponse, TimeTrackingResponse} from "../../../../@shared/models/timetracking.interfaces";
import {TabsMenuModel} from "../../../../@shared/models/calendar.interfaces";
import {StorageService} from "../../../../@core/services/storage.service";

import {TrackingService} from "../../../../@shared/services/tracking.service";
import {ToastService} from "../../../../@core/services/toast.service";
import {TimeTrackingDetailsModal} from "../../../salary/_modals/time-tracking-details-modal/time-tracking-details-modal";
import {InternalUserResponse} from "../../../../@shared/models/user.interfaces";
import {ButtonComponent} from "../../../../@shared/components/button/button.component";
import {TimetrackingSettingsModal} from "./_modals/timetracking-settings-modal/timetracking-settings-modal";
import {StandardImports} from "../../../../@shared/global_import";
import {TabsMenuComponent} from "../../../../@shared/components/tabs-menu/tabs-menu.component";
import {CardComponent} from "../../../../@shared/components/layout/card/card.component";


export interface UserCheckinTimeTrackingInterface extends TimeTrackingResponse {
  workOrderNumber: string;
}
export interface UserCheckinInterface extends UserTimeTrackingResponse {
  time_trackings: UserCheckinTimeTrackingInterface[];
}

export interface WorkOrderTimeTrackingInterface {
  start_time: string;
  start_date: Date | null;
  end_time: string;
  end_date: Date | null | string;
  duration: number;
  work_order_number: string;
  work_order_title: string | null;
}

@Component({
  selector: 'app-order-timetracking',
  templateUrl: './order-timetracking.component.html',
  styleUrls: ['./order-timetracking.component.css'],
  standalone: true,
  imports: [StandardImports, TabsMenuComponent, NgbAccordionDirective, NgbAccordionItem, NgbAccordionHeader, NgbAccordionButton, NgbAccordionCollapse, NgbAccordionBody, CardComponent]
})
export class OrderTimetrackingComponent implements OnInit {
  @Input() workOrderView: boolean = false;
  @Input() showHeader: boolean = true;
  order?: OrderResponse;
  workOrder?: WorkOrderResponse;
  workOrders: WorkOrderResponse[] = [];

  activeView = 'view1'; // Assuming 'view1' is the default active view

  userCheckinsDict: {[key: string]: UserCheckinInterface} = {};
  userCheckins: UserCheckinInterface[] = [];
  workOrderTimeTrackings: WorkOrderTimeTrackingInterface[] = [];
  orderIsOngoing: boolean = false;
  totalSeconds: number = 0;
  destroy$: Subject<void> = new Subject<void>();
  timeTrackingEnabled: boolean = false;
  tabs: TabsMenuModel[] = [{value: 'view1', translationKey: 'orderDetails.timetracking.btn.job'}, {value: 'view2', translationKey: 'orderDetails.timetracking.btn.crew'}];

  constructor(private modalService: NgbModal, private translateService: TranslateService, private orderService: OrderService, protected utilsService: UtilsService, private storageService: StorageService, private trackingService: TrackingService, private toastService: ToastService) {}

  ngOnInit() {
    this.storageService.timeTrackingEnabled$.subscribe((value) => {
      this.timeTrackingEnabled = value;
    });

    if (!this.workOrderView) {
      this.orderService.workOrders$.pipe(takeUntil(this.destroy$)).subscribe(workOrders => {
        this.workOrders = workOrders;
        if (this.workOrders.length > 0) {
          this.initTrackings();
        }
      });
      this.orderService.order$.pipe(takeUntil(this.destroy$)).subscribe(order => {
        this.order = order;
      });
    } else {
      this.orderService.workOrder$.subscribe(workOrder => {
        this.destroy$.next();
        this.workOrder = workOrder;
        this.initTrackings();
      });
    }
  }

  initTrackings() {
    this.userCheckinsDict = {};
    this.userCheckins = [];
    this.workOrderTimeTrackings = [];
    this.totalSeconds = 0;
    let workOrders = this.workOrderView ? [this.workOrder!] : this.workOrders;
    for (const wo of workOrders) {
      let woTT: WorkOrderTimeTrackingInterface = {
        start_time: '',
        end_time: '',
        start_date: null,
        end_date: null,
        duration: 0,
        work_order_number: wo.work_order_number,
        work_order_title: wo.work_order_title,
      };
      for (const entry of wo.user_time_tracking_response) {
        // If the user has not been added to the dict yet, add it
        if (!this.userCheckinsDict[entry.user.user_id]) {
          this.userCheckinsDict[entry.user.user_id] = {
            user: entry.user,
            time_trackings: [],
            duration_in_seconds: 0,
            checked_in: false,
          };
        }
        // For each specific time tracking entry, add it (with the stage name) to the user's time tracking list, and add the duration to the user's total duration
        for (const timeTracking of entry.time_trackings) {
          if (timeTracking.ongoing) {
            this.userCheckinsDict[entry.user.user_id]['checked_in'] = true;
          }
          if (timeTracking.started_at && (!woTT.start_date || woTT.start_date < woTT.start_date)) {
            woTT.start_date = timeTracking.started_at;
            woTT.start_time = this.formatDateToTime(timeTracking.started_at)!;
          }

          if (timeTracking.stopped_at && woTT.end_date != 'ongoing' && (!woTT.end_date || timeTracking.stopped_at > woTT.end_date)) {
            woTT.end_date = timeTracking.stopped_at;
            woTT.end_time = this.formatDateToTime(timeTracking.stopped_at)!;
          }

          if (timeTracking.ongoing) {
            woTT.end_date = 'ongoing';
          }

          woTT.duration += timeTracking.duration_in_seconds;

          this.userCheckinsDict[entry.user.user_id]['time_trackings'].push({
            ...timeTracking,
            workOrderNumber: wo.work_order_number,
          });
        }
        // Add the duration to the user's total duration
        this.userCheckinsDict[entry.user.user_id]['duration_in_seconds'] += entry.duration_in_seconds;
      }
      this.workOrderTimeTrackings.push(woTT);
      this.totalSeconds += woTT.duration;
    }

    this.userCheckins = Object.values(this.userCheckinsDict);
    for (const userCheckin of this.userCheckins) {
      userCheckin.time_trackings.sort((a, b) => {
        return new Date(a.started_at).getTime() - new Date(b.started_at).getTime();
      });
    }
  }
        // Get per user checkins
        // for (const wo of this.order.work_orders) {
        //
        //   // For each user that has checked in at the stage
        //   for (const entry of wo.time_trackings) {
        //     // If the user has checked in, set the order to ongoing
        //     if (entry.ongoing === 1) {
        //       this.orderIsOngoing = true;
        //     }
        //     // If the user has not been added to the dict yet, add it
        //     if (!this.userCheckinsDict[entry.user.user_id]) {
        //       this.userCheckinsDict[entry.user.user_id] = {
        //         user: entry.user,
        //         time_trackings: [],
        //         duration_in_seconds: 0,
        //         checked_in: 0,
        //       };
        //     }
        //     // For each specific time tracking entry, add it (with the stage name) to the user's time tracking list, and add the duration to the user's total duration
        //     for (const timeTracking of entry.time_trackings) {
        //       if (timeTracking.ongoing == 1) {
        //         this.userCheckinsDict[entry.user.user_id]['checked_in'] = 1;
        //       }
        //       this.userCheckinsDict[entry.user.user_id]['time_trackings'].push({
        //         ...timeTracking,
        //         stage_name: stage.stage_name,
        //       });
        //     }
        //     // Add the duration to the user's total duration
        //     this.userCheckinsDict[entry.user.user_id]['duration_in_seconds'] += entry.duration_in_seconds;
        //   }
        // }
        // this.userCheckins = Object.values(this.userCheckinsDict);
        // for (const userCheckin of this.userCheckins) {
        //   userCheckin.time_trackings.sort((a, b) => {
        //     return new Date(a.started_at).getTime() - new Date(b.started_at).getTime();
        //   });
        // }

  formatDateToTime(date: Date | null | string){
    if (!date || date === 'ongoing') {
      return null;
    } else {
      date = date as Date;
    }


    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    const formattedDate = `${hours}:${minutes}`;

    return formattedDate.toString();
  }

  formatDateTracking(date: Date | null) {
    if (!date) {
      return null;
    }
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${day}/${month}/${year}`
  }

  calculateTotalTime() {
    return this.utilsService.formatDurationFromSeconds(this.totalSeconds);
  }

  toggleView(view: string) {
    // Only change the view if it's different from the current one
    if (this.activeView !== view) {
      this.activeView = view;
    }
  }

  tabChanged(tabValue: string) {
    this.toggleView(tabValue);
  }

  openModalTimeTrackingSettings() {
    let modalRef = this.modalService.open(TimetrackingSettingsModal);
    if (this.workOrderView) {
      modalRef.componentInstance.workOrder = this.workOrder;
    } else {
      modalRef.componentInstance.order = this.order;
    }

  }

  calculateTotalDuration(entry: UserCheckinInterface): string {
    let totalDuration = 0;
    if (entry && entry.time_trackings) {
      entry.time_trackings.forEach((timeTracking: any) => {
        if(timeTracking.duration_in_seconds){
          totalDuration += timeTracking.duration_in_seconds;
        }
      });
    }
    return this.utilsService.formatDurationFromSeconds(totalDuration);
  }


  openTimeTrackingDetails(row: UserCheckinTimeTrackingInterface, user: InternalUserResponse) {
    let modalRef = this.modalService.open(TimeTrackingDetailsModal, {size: 'lg'});
    modalRef.componentInstance.timeTracking = row;
    modalRef.componentInstance.user = user;
  }

  protected readonly open = open;
}
