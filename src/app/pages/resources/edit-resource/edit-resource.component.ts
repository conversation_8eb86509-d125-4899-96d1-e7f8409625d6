import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { _CRM_RSC_1, _CRM_RSC_5 } from 'src/app/@shared/models/input.interfaces';
import {ActivatedRoute, Router, RouterLink} from '@angular/router';
import {formatDateDMY, formatDateYMD, getFormControl, resizeAndConvertToJpeg, UtilsService} from 'src/app/@core/utils/utils.service';
import { ToastService } from "../../../@core/services/toast.service";
import {
  ResourceResponse,
  ResourceTypeDataFieldResponse,
  ResourceTypeResponse
} from "../../../@shared/models/resources.interfaces";
import { ResourceService } from "../../../@shared/services/resource.service";
import {StandardImports} from "../../../@shared/global_import";
import {CardComponent} from "../../../@shared/components/layout/card/card.component";

export interface inputDataFieldInterface { [key: string]: string | number | null | Date }

@Component({
    selector: 'app-edit-resource',
    templateUrl: './edit-resource.component.html',
    styleUrls: ['./edit-resource.component.css'],
    standalone: true,
  imports: [StandardImports, RouterLink, CardComponent]
})
export class EditResourceComponent implements OnInit {
  @Output() updateParent = new EventEmitter<void>();
  resourceForm: FormGroup;
  loading = false;
  selectedPhoto: File;
  imagePreview: string;
  resourceTypes: ResourceTypeResponse[];
  selectedResourceType: ResourceTypeResponse | null;
  resource: ResourceResponse;
  success: boolean;
  failureMessage: boolean;
  dataFields: ResourceTypeDataFieldResponse[] = [];
  inputDataFields: inputDataFieldInterface = {};
  sortedDataFields: ResourceTypeDataFieldResponse[] = [];
  dataFieldTypes: { [key: string]: string } = {};
  isEditMode: boolean = false;

  constructor(private route: ActivatedRoute,
              private fb: FormBuilder,
              private resourceService: ResourceService,
              private utilsService: UtilsService,
              private router: Router,
              private toastService: ToastService) { }

  changes: { [key: string]: boolean } = {
    first_name: false,
    last_name: false,
    phone: false,
    email: false,
    role_id: false,
  };

  ngOnInit(): void {

    this.isEditMode = false;
    // Initialize the form group first
    this.resourceForm = new FormGroup({
      resource_name: new FormControl(),
      resource_description: new FormControl(),
      resource_image_url: new FormControl(),
      registration_number_search: new FormControl(),
      profile_image_url: new FormControl(),
      profile_image_url_new: new FormControl(),
    });

    // Then, fetch resource types and set the value
    this.resourceService.getResourceTypes().subscribe(res => {
      this.resourceTypes = res;
      const resourceId = this.route.snapshot.params['id']; // adjust 'resourceId' based on your route configuration

      // After fetching resource types, fetch the resource
      if (resourceId) {
        this.resourceService.getResourceById(resourceId).subscribe(
          resource => {
            this.resource = resource;

            // Find the selected resource type
            const foundResourceType = this.resourceTypes.find(rt => rt.resource_type_id === resource.resource_type_id);

            if (foundResourceType) {
              this.selectedResourceType = foundResourceType;
              this.dataFields = this.selectedResourceType.data_fields.sort((a, b) => a.index - b.index);
              this.populateForm(resource);
            } else {
              // Handle the case where the resource type is not found
              console.error('Resource type not found for the given resource');
              // You may want to redirect the user or show an error message
            }
          },
          error => {
            console.error('Error fetching resource:', error);
          }
        );
      }
    });

    this.failureMessage = false;
    this.success = false;

    this.sortedDataFields = this.dataFields.sort((a, b) => {
      const aValueExists = this.inputDataFields[a.key] !== undefined;
      const bValueExists = this.inputDataFields[b.key] !== undefined;

      if (aValueExists && !bValueExists) {
        return -1;
      }
      if (!aValueExists && bValueExists) {
        return 1;
      }
      return 0;
    });
  }

  populateForm(resource: ResourceResponse) {
    // Populate the standard form fields
    this.resourceForm.patchValue({
      resource_name: resource.resource_name,
      resource_description: resource.resource_description,
      resource_type_id: resource.resource_type_id,
      resource_image_url: resource.resource_image_url
    });

    // Populate the dynamic data fields
    if (resource.resource_data && this.dataFields) {
      this.inputDataFields = {};

      this.dataFields.forEach(field => {
        if (resource.resource_data.hasOwnProperty(field.key)) {
          if (resource.resource_data[field.key] as any instanceof Date) {
            let date = resource.resource_data[field.key] as Date;
            this.inputDataFields[field.key] = formatDateYMD(date);
          } else {
            this.inputDataFields[field.key] = resource.resource_data[field.key];
          }
          this.dataFieldTypes[field.key] = field.field_type;
        }
        // Create form controls dynamically
        this.resourceForm.addControl(field.key, new FormControl(this.inputDataFields[field.key]));
      });
      console.log("this.inputdatafields", this.inputDataFields);
    }
  }

  onResourceTypeSelected() {
    this.selectedResourceType = this.resourceTypes.find(rt => rt.resource_type_id === parseInt(this.resourceForm.value.resource_type_id))!;
    this.dataFields = this.selectedResourceType.data_fields.sort((a, b) => a.index - b.index);
    for (let dataField of this.dataFields) {
      this.inputDataFields[dataField.key] = null;
    }
  }

  updateInputDataField(event: any, key: string) {
    this.inputDataFields[key] = event.target.value;
  }

  onFileSelected(event: any) {
    const MAX_SIZE = 10485760; // 10MB in bytes
    const potentialPhoto = event.target.files[0];

    if (potentialPhoto) {
      resizeAndConvertToJpeg(potentialPhoto, 0.8).then((compressedFile: Blob) => {
        const compressedImageFile = new File([compressedFile], potentialPhoto.name, {
          type: 'image/jpeg',
        });
        this.selectedPhoto = compressedImageFile;
        this.resourceService.uploadResourceImage({image: this.selectedPhoto, resource_id: this.resource.resource_id}).subscribe(res => {
          this.success = true;
          this.loading = false;
          const reader = new FileReader();
          reader.onload = (e: any) => {
            this.imagePreview = e.target.result;
          };
          reader.readAsDataURL(this.selectedPhoto);

          this.uploadPhoto(this.resource.resource_id)?.subscribe();
          // Emit the event to notify the parent component
          this.updateParent.emit();  // Here's the change
        });
      }).catch(error => {
        this.toastService.errorToast('error_compressing_logo');
      });
    }
  }

  triggerFileInput() {
    const fileInput = document.getElementById('profile_image_url_new') as HTMLInputElement;
    fileInput.click();
  }

  onSubmit() {
    console.log("this.inputDataFields2", this.inputDataFields);
    console.log(this.dataFields);
    console.log('Form is valid');
    this.loading = true;

    // Adjust date fields by adding two hours before sending to the backend
    const adjustedInputDataFields = { ...this.inputDataFields };
    Object.keys(adjustedInputDataFields).forEach(key => {
      if (this.dataFieldTypes[key] === 'date' && adjustedInputDataFields[key]) {
        const dateValue = new Date(adjustedInputDataFields[key] as string);
        dateValue.setHours(dateValue.getHours() + 2);
        adjustedInputDataFields[key] = dateValue.toISOString().slice(0, 19).replace('T', ' ');
      }
    });

    console.log("adjustedInputDataFields", adjustedInputDataFields);

    // Construct the payload for updating the resource
    const updatePayload: _CRM_RSC_1 = {
      resource_id: this.resource.resource_id,
      resource_name: this.resourceForm.value.resource_name,
      resource_description: this.resourceForm.value.resource_description,
      resource_data: adjustedInputDataFields,
      // Include any other fields that are required for the update
    };

    // Call the updateResource method
    this.resourceService.updateResource(updatePayload).subscribe(
      res => {
        this.resource = res;
        this.success = true;
        this.loading = false;
        this.isEditMode = false;

        // Emit the event to notify the parent component
        this.updateParent.emit();
        this.toastService.successToast("resource_updated");
      },
      error => {
        console.error('Error updating resource:', error);
        this.loading = false;
        // Handle any errors here, such as displaying an error message
      }
    );
  }

  editResource() {
    this.isEditMode = true;
  }

  cancelEdit() {
    this.isEditMode = false;
    this.populateForm(this.resource);
  }

  get showButtonHeader(): boolean {
    return !!(this.selectedResourceType && [1, 2].includes(this.selectedResourceType.resource_type_id) && !this.isEditMode);
  }

  uploadPhoto(resourceId: number): any {
    const payload: _CRM_RSC_5 = {
      image: this.selectedPhoto,
      resource_id: resourceId
    };
    return this.resourceService.uploadResourceImage(payload);
  }

  formatDataType(key: string) {
    const value = this.inputDataFields[key];
    const type = this.dataFieldTypes[key];

    if (type === 'date' && value) {
      let dateValue = new Date(value as string);
      return formatDateDMY(dateValue);
    }

    return value;
  }

  protected readonly getFormControl = getFormControl;
}
