import { Component, EventEmitter, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { _CRM_RSC_0, _CRM_RSC_5 } from 'src/app/@shared/models/input.interfaces';
import { ActivatedRoute, Router } from '@angular/router';
import {formatDateYMD, getFormControl, resizeAndConvertToJpeg, UtilsService} from 'src/app/@core/utils/utils.service';
import {ToastService} from "../../../@core/services/toast.service";
import {
  ResourceResponse,
  ResourceTypeDataFieldResponse,
  ResourceTypeResponse
} from "../../../@shared/models/resources.interfaces";
import {ResourceService} from "../../../@shared/services/resource.service";
import {VehicleRegistrationDataResponse} from "../../../@shared/models/global.interfaces";
import {NgbActiveModal, NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {StandardImports} from "../../../@shared/global_import";

export interface inputDataFieldInterface { [key: string]: string | number | null }

@Component({
    selector: 'app-add-resource',
    templateUrl: './add-resource.component.html',
    styleUrls: ['add-resource.component.css'],
    standalone: true,
    imports: [StandardImports]
})
export class AddResourceComponent {
  resourceForm: FormGroup;
  loading = false;
  selectedPhoto: File;
  imagePreview: string;
  resourceTypes: ResourceTypeResponse[];
  selectedResourceType: ResourceTypeResponse;
  resource: ResourceResponse;
  success: boolean;
  failureMessage: boolean;
  dataFields: ResourceTypeDataFieldResponse[] = [];
  inputDataFields: inputDataFieldInterface = {};
  sortedDataFields: ResourceTypeDataFieldResponse[] = [];
  isEditMode: boolean = false;
  showRegnrError: boolean = false
  showSpecialCharError: boolean = false;
  @Output() updateParent = new EventEmitter<void>();

  constructor(private route: ActivatedRoute,
              private fb: FormBuilder,
              private resourceService: ResourceService,
              private utilsService: UtilsService,
              private router: Router,
              private toastService: ToastService,
              private modalService: NgbModal,
              public activeModal: NgbActiveModal
  ) {}

  changes: { [key: string]: boolean } = {
    first_name: false,
    last_name: false,
    phone: false,
    email: false,
    role_id: false,
  };

  ngOnInit(): void {
    this.resourceForm = this.fb.group({
      resource_name: ['', Validators.required],
      resource_description: [''],
      resource_type_id: [null, Validators.required],
      resource_image_url: [''],
      registration_number_search: ['', [Validators.required, Validators.pattern(/^[a-zA-Z0-9]*$/)]], // Allow only numbers and letters
    });

    // Then, fetch resource types and set the value
    this.resourceService.getResourceTypes().subscribe(res => {
      this.resourceTypes = res;

      if (this.resourceTypes && this.resourceTypes.length > 0) {
        this.resourceForm.get('resource_type_id')?.setValue(this.resourceTypes[0].resource_type_id);
      }
    });

    this.failureMessage = false
    this.success = false

    this.sortedDataFields = this.dataFields.sort((a, b) => {
      const aValueExists = this.inputDataFields[a.key] !== undefined;
      const bValueExists = this.inputDataFields[b.key] !== undefined;

      if (aValueExists && !bValueExists) {
        return -1;
      }
      if (!aValueExists && bValueExists) {
        return 1;
      }
      return 0;
    });


  }


  onResourceTypeSelected() {
    this.selectedResourceType = this.resourceTypes.find(rt => rt.resource_type_id === parseInt(this.resourceForm.value.resource_type_id))!;
    this.dataFields = this.selectedResourceType.data_fields.sort((a, b) => a.index - b.index);
    for (let dataField of this.dataFields) {
      this.inputDataFields[dataField.key] = null;
    }
  }

  updateInputDataField(event: any, key: string) {
    this.inputDataFields[key] = event.target.value;
  }

  onFileSelected(event: any) {
    const MAX_SIZE = 10485760; // 10MB in bytes
    const potentialPhoto = event.target.files[0];

    if (potentialPhoto.size > MAX_SIZE) {
      this.toastService.errorToast('photo_size');
      return;
    }

    this.selectedPhoto = potentialPhoto;

    const reader = new FileReader();
    reader.onload = (e: any) => {
      this.imagePreview = e.target.result;
    };
    reader.readAsDataURL(this.selectedPhoto);
  }

  onSubmit() {
    if (this.loading) {
      return
    }
    if (this.resourceForm.get('resource_name')!.valid) {
      this.loading = true;
      const payload: _CRM_RSC_0 = {
        resource_name: this.resourceForm.value.resource_name,
        resource_description: this.resourceForm.value.resource_description,
        resource_type_id: this.resourceForm.value.resource_type_id,
        resource_data: this.inputDataFields,
      };
      this.resourceService.createResource(payload).subscribe(res => {
        this.resource = res;
        const resourceId = res.resource_id;
        const MAX_SIZE = 10485760; // 10MB in bytes
        const initialImageFile = this.selectedPhoto;
        if (this.selectedPhoto) {
          resizeAndConvertToJpeg(initialImageFile, 0.8).then((compressedFile: Blob) => {
            const compressedImageFile = new File([compressedFile], initialImageFile.name, {
              type: 'image/jpeg',
            });
            this.selectedPhoto = compressedImageFile;
            this.resourceService.uploadResourceImage({image: this.selectedPhoto, resource_id: this.resource.resource_id}).subscribe(res => {
              this.success = true;
              this.loading = false;

              // Emit the event to notify the parent component
              this.updateParent.emit();  // Here's the change

              // Close the modal
              this.modalService.dismissAll();
              this.router.navigate(['/settings/resources/edit/', resourceId]);  // Update the route as needed
            });
            }).catch(error => {
              this.toastService.errorToast('error_compressing_logo');
            });
        }
        else {
          this.success = true;
          this.loading = false;

          // Emit the event to notify the parent component
          this.updateParent.emit();  // Here's the change

          // Close the modal
          this.modalService.dismissAll();
          this.router.navigate(['/settings/resources/edit/', resourceId]);  // Update the route as needed
        }
      });

      this.loading = false;
    } else {
      this.resourceForm.markAllAsTouched();
      this.loading = false;
    }
  }



  uploadPhoto(resourceId: number): any {
    const payload: _CRM_RSC_5 = {
      image: this.selectedPhoto,
      resource_id: resourceId
    };

    return this.resourceService.uploadResourceImage(payload);
  }

  validateInput() {
    const registrationNumber = this.resourceForm.value.registration_number_search;
    if (/[^a-zA-Z0-9]/.test(registrationNumber)) {
      // Special characters found
      this.showSpecialCharError = true;
      this.showRegnrError = false; // Optionally reset the other error
    } else {

      this.showSpecialCharError = false;
      this.showRegnrError = false;

    }
  }

  resetErrorMessage() {
    this.showRegnrError = false;
  }

  toggleEditMode(): void {
    this.showRegnrError = false;
    this.isEditMode = !this.isEditMode;
  }

  searchRegistrationNumber() {
    const registrationNumber = this.resourceForm.value.registration_number_search;
    this.resourceService.getVehicleRegistrationData(registrationNumber).subscribe(
      (res: VehicleRegistrationDataResponse) => {
        for (const [key, value] of Object.entries(res)) {
          let idfKey = key as keyof inputDataFieldInterface;
          if (this.inputDataFields.hasOwnProperty(idfKey)) {
            let fieldValue = value;
            if (value instanceof Date) {
              fieldValue = formatDateYMD(value);
            }
            this.inputDataFields[idfKey] = fieldValue as string | number | null;
          }
        }
        this.showRegnrError = false;
      },
      (error) => {
        // Error handling logic here
        console.error('Error fetching vehicle registration data:', error);
        this.showRegnrError = true;
      }
    );
  }

  protected readonly getFormControl = getFormControl;
}
