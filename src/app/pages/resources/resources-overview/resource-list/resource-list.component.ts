import {Component, Input} from '@angular/core';
import {UserEntityRelationWithUserDataResponse, UserResponse} from "../../../../@shared/models/user.interfaces";
import {EmployeeService} from "../../../../@shared/services/employee.service";
import {Column} from "../../../../@shared/components/advanced-table/advanced-table.component";
import {TranslateService} from "@ngx-translate/core";
import {catchError} from "rxjs/operators";
import {throwError} from "rxjs";
import {ToastService} from "../../../../@core/services/toast.service";
import { Router } from '@angular/router';
import {ResourceResponse} from "../../../../@shared/models/resources.interfaces";
import {ResourceService} from "../../../../@shared/services/resource.service";
import {formatDateYMD, UtilsService} from "../../../../@core/utils/utils.service";
import {StandardImports} from "../../../../@shared/global_import";
import {AdvancedTableModule} from "../../../../@shared/components/advanced-table/advanced-table.module";
@Component({
    selector: 'app-resource-list',
    templateUrl: './resource-list.component.html',
    styleUrls: ['./resource-list.component.css'],
    standalone: true,
  imports: [StandardImports, AdvancedTableModule]
})
export class ResourceListComponent {
  @Input() resources: Array<ResourceResponse> = [];
  @Input() isLoading: boolean = false;
  deleteButton: boolean = true
  columns: Array<Column> = [];

  constructor(private resourceService: ResourceService, private utilsService: UtilsService, public translate: TranslateService, public toastService: ToastService, private router: Router){}

  ngOnInit(): void {
    this.columns = [
      {
        name: 'resource_name',
        label: this.translate.instant('resources.name'),
        formatter: (r: ResourceResponse) => r.resource_name,
        sort: true
      },
      {
        name: 'resource_type_name',
        label: this.translate.instant('resources.resourceTypeName'),
        formatter: (r: ResourceResponse) => r.resource_type_name,
        sort: false
      },
      {
        name: 'resource_description',
        label: this.translate.instant('resources.resourceDescription'),
        formatter: (r: ResourceResponse) => r.resource_description,
        sort: false
      },
      {
        name: 'created_at',
        label: this.translate.instant('resources.createdAt'),
        formatter: (r: ResourceResponse) => formatDateYMD(r.created_at),
        sort: false
      }
    ];
  }


  navigateToResource(resource: ResourceResponse){
    this.router.navigateByUrl("settings/resources/edit/"+resource.resource_id)
  }

  deleteResource(resource: ResourceResponse) {
    this.resourceService.deleteResource(resource.resource_id).subscribe(
      res => {
        this.resources = this.resources.filter(rsc => rsc.resource_id !== resource.resource_id);
      });
  }
}
