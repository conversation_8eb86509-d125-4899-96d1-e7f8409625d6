import {AfterViewInit, Component, OnChanges, OnInit, Optional, SimpleChanges} from '@angular/core';
import {NgbActiveModal, NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {BehaviorSubject, forkJoin, pairwise, Subject} from "rxjs";
import {FormControl, } from "@angular/forms";
import {currencyFormat, displayDate, paymentStatusBadge, UtilsService} from "../../../@core/utils/utils.service";

import {TablerinoColumn, TablerinoSettings} from "../../../@shared/components/tablerino/tablerino.component";

import {PaginationResponse} from "../../../@shared/models/response.interfaces";
import {PaginationContainer} from "../../../@shared/models/global.interfaces";
import {StorageService} from "../../../@core/services/storage.service";
import {HeaderFilterComponent, HeaderFiltersContainer} from "../../../@shared/components/tablerino-header/tablerino-header.component";
import {takeUntil} from "rxjs/operators";
import {_CRM_PAY_41} from "../../../@shared/models/input.interfaces";
import {PaymentDetailsComponent} from "../components/payment-details/payment-details.component";
import {PaymentService} from "../../../@shared/services/payment.service";
import {OrderPaymentResponseCompact, PaymentMethodResponse, PaymentStatusResponse} from "../../../@shared/models/payment.interfaces";
import {ProductService} from "../../../@shared/services/product.service";
import {ProductCompactResponse} from "../../../@shared/models/product.interfaces";
import {CompanyService} from "../../../@shared/services/company.service";
import {CompanyPaymentMethodResponse} from "../../../@shared/models/integrations.interfaces";
import {StandardImports} from "../../../@shared/global_import";
import {PageHeaderComponent} from "../../../@shared/components/page-header/page-header.component";
import {SpinnerComponent} from "../../../@shared/components/spinner/spinner.component";
import {TablerinoCompleteComponent} from "../../../@shared/components/tablerino-complete/tablerino-complete.component";
import {PaymentDetailsV2Component} from "../components/payment-details-v2/payment-details-v2.component";

export interface PaymentRow extends OrderPaymentResponseCompact {
  selected: boolean;
}


@Component({
  selector: 'app-payments-overview',
  templateUrl: './payments-overview.component.html',
  styleUrls: ['./payments-overview.component.css'],
  standalone: true,
  imports: [StandardImports, PageHeaderComponent, SpinnerComponent, TablerinoCompleteComponent]
})
export class PaymentsOverviewComponent implements OnInit, OnChanges, AfterViewInit {
  columnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);
  paymentRows: PaymentRow[];
  settings: TablerinoSettings = {
    checkboxes: false,
    clickableRows: true,
  }
  loading: boolean = false;
  totalAmount: number = 0;
  totalAmountLoading: boolean = false;
  dataResponse: PaginationResponse<OrderPaymentResponseCompact[]>;
  paginationSubject: BehaviorSubject<PaginationContainer> = new BehaviorSubject<PaginationContainer>({page: 1, limit: 25, paginate: 1, totalPages: 0, totalItems: 0});
  selectedRowsSubject: BehaviorSubject<PaymentRow[]> = new BehaviorSubject<PaymentRow[]>([]);
  headerFiltersContainerSubject: BehaviorSubject<HeaderFiltersContainer> = new BehaviorSubject<HeaderFiltersContainer>({filters: [], init: true});
  cancelCurrentRequest$ = new Subject<void>();
  lastUsedSearchTerm: string | undefined = undefined;

  operateExVat: boolean = false;

  constructor(public utilsService: UtilsService,
              @Optional() public activeModal: NgbActiveModal,
              private modalService: NgbModal,
              private productService: ProductService,
              private paymentService: PaymentService,
              private companyService: CompanyService,
              private storageService: StorageService) {
  }

  ngOnInit() {
    this.storageService.operateExVat$.subscribe((operateExVat) => {
      this.operateExVat = operateExVat;
    });

    this.paymentService.fetchAndUpdatePaymentMethods();
    this.initializeColumns();
    this.initializeHeaderFilters();
    this.getPayments('', 'init');
    this.paginationSubject.pipe(pairwise()).subscribe((paginationDetails) => {
      if (paginationDetails[0].page !== paginationDetails[1].page) {
        this.getPayments('', 'pagination');
      }
    });
    this.headerFiltersContainerSubject.subscribe((headerFilters) => {
      if (!headerFilters.init) {
        this.getPayments('', 'headerFilters');
      }
    });
  }

  ngAfterViewInit() {
  }

  ngOnChanges(simpleChanges: SimpleChanges) {
  }

  quickSearch(searchTerm: string) {
    this.getPayments(searchTerm, 'quickSearch');
  }

  getPayments(searchTerm?: string, source?: string) {
    this.lastUsedSearchTerm = searchTerm
    this.totalAmountLoading = false;
    this.cancelCurrentRequest$.next();
    this.loading = true;
    let filters = this.headerFiltersContainerSubject.value.filters;

    let paymentStatusIds: number[] = [];
    filters.find(hf => hf.parameterName === 'payment_status_ids')!.dropDownOptions!.filter(ddo => ddo.active).map(ddo => {
      if (Number(ddo.value) === 0) {
        // If "Unpaid" is chosen, select both "Unpaid" and "Initiated" (Which is also called 'unpaid', hence the removal from dropddown options)
        paymentStatusIds.push(0);
        paymentStatusIds.push(1);
      } else if(Number(ddo.value) === 4) {
        // If "Failed" is chosen, select both "Cancelled by user" and "Cancelled by payment provider" (The latter is removed from dropdown options for simplicity)
        paymentStatusIds.push(4);
        paymentStatusIds.push(5);
      } else if (Number(ddo.value) === 7){
        // If "Refunded" is chosen, select both "Refunded" and "partally refunded" (The latter is removed from dropdown options for simplicity)
        paymentStatusIds.push(12);
        paymentStatusIds.push(13);

      } else {
        paymentStatusIds.push(Number(ddo.value!));
      }
    })

    let sortColumn = this.columnsSubject.value.find(col => col.sortedAsc || col.sortedDesc) || null;
    let sortKey: string = sortColumn?.name || 'created_at';
    let sortDirection: 'asc' | 'desc' = sortColumn ? (sortColumn.sortedAsc ? 'asc' : 'desc') : 'desc';

    let params: _CRM_PAY_41 = {
      paginate: 1,
      page: this.paginationSubject.value.page,
      limit: this.paginationSubject.value.limit,
      payment_status_ids: paymentStatusIds,
      product_ids: filters.find(hf => hf.parameterName === 'product_ids')!.dropDownOptions!.filter(ddo => ddo.active).map(ddo => Number(ddo.value!)),
      payment_method_ids: filters.find(hf => hf.parameterName === 'payment_method_ids')!.dropDownOptions!.filter(ddo => ddo.active).map(ddo => Number(ddo.value!)),
      created_at_from: filters.find(hf => hf.parameterName === 'created_at')!.dateRangeFromControl!.value,
      created_at_to: filters.find(hf => hf.parameterName === 'created_at')!.dateRangeToControl!.value,
      payment_sent_at_from: filters.find(hf => hf.parameterName === 'payment_sent_at')!.dateRangeFromControl!.value,
      payment_sent_at_to: filters.find(hf => hf.parameterName === 'payment_sent_at')!.dateRangeToControl!.value,
      captured_at_from: filters.find(hf => hf.parameterName === 'captured_at')!.dateRangeFromControl!.value,
      captured_at_to: filters.find(hf => hf.parameterName === 'captured_at')!.dateRangeToControl!.value,
      customer_name: searchTerm,
      order_by: sortKey,
      order_direction: sortDirection,
    }

    this.paymentService.getCompanyPayments(params).pipe(takeUntil(this.cancelCurrentRequest$)).subscribe((res) => {
      this.dataResponse = res;
      this.paginationSubject.next({
        ...this.paginationSubject.value,
        totalItems: res.total_items,
        totalPages: res.total_pages
      });
      this.paymentRows = res.data.map((payment) => {
        return {
          ...payment,
          selected: false,
        }
      });
      this.loading = false;
    }, error => {
      this.loading = false
    });

    this.totalAmountLoading = true;
    this.paymentService.getCompanyTotals(params).pipe(takeUntil(this.cancelCurrentRequest$)).subscribe((res) => {
      this.totalAmount = res.total;
      this.totalAmountLoading = false;
    });
  }

  initializeColumns() {
    this.columnsSubject.next([
      {
        name: 'payment_number',
        labelKey: 'ID',
        formatter: (payment: PaymentRow) => this.colorizeRow(payment, '#' + payment.payment_number),
        sort: true,
        visible: true,
      },
      {
        name: 'customer',
        labelKey: 'payments.list.customer',
        formatter: (payment: PaymentRow) => this.colorizeRow(payment, payment.payment_recipient?.name),
        sort: false,
        visible: true,
      },
      {
        name: 'payment_method_id',
        labelKey: 'payments.list.paymentMethod',
        formatter: (payment: PaymentRow) => this.colorizeRow(payment, payment.payment_method_name),
        sort: true,
        visible: true,
      },
      {
        name: 'payment_status_id',
        labelKey: 'payments.list.status',
        formatter: (payment: PaymentRow) => paymentStatusBadge(payment),
        sort: true,
        visible: true,
      },
      {
        name: 'payment_sent_at',
        labelKey: 'payments.list.paymentSentAt',
        formatter: (payment: PaymentRow) => this.colorizeRow(payment, displayDate(payment.payment_sent_at, false)),
        sort: true,
        visible: true,
      },
      {
        name: 'auto_send_at',
        labelKey: 'payments.list.autoSendAt',
        formatter: (payment: PaymentRow) => payment.payment_sent_at ? '' : this.colorizeRow(payment, displayDate(payment.auto_send_at, false)),
        sort: true,
        visible: true,
      },
      {
        name: 'total_amount_inc_vat',
        labelKey: 'payments.list.totalAmount',
        formatter: (payment: PaymentRow) => this.colorizeRow(payment, currencyFormat(this.operateExVat ? payment.total_amount_ex_vat : payment.total_amount_inc_vat)),
        sort: true,
        visible: true,
      },
      {
        name: 'captured_at',
        labelKey: 'payments.list.capturedAt',
        formatter: (payment: PaymentRow) => this.colorizeRow(payment, displayDate(payment.captured_at, false)),
        sort: true,
        visible: true,
      },
      {
        name: 'created_at',
        labelKey: 'payments.list.createdAt',
        formatter: (payment: PaymentRow) => this.colorizeRow(payment, displayDate(payment.created_at, false)),
        sort: true,
        visible: true,
      },
    ]);
  }

  initializeHeaderFilters() {
    let headerFilters: HeaderFilterComponent[] = [
      {
        parameterName: 'payment_status_ids',
        translationKey: 'payments.list.status',
        multiSelect: true,
        dropDownOptions: [],
        active: false,
      },
      {
        parameterName: 'payment_method_ids',
        translationKey: 'payments.list.paymentMethod',
        multiSelect: true,
        dropDownOptions: [],
        active: false,
      },
      {
        parameterName: 'product_ids',
        translationKey: 'payments.list.products',
        multiSelect: true,
        dropDownOptions: [],
        active: false,
      },
      {
        parameterName: 'payment_sent_at',
        translationKey: 'payments.list.paymentSentAt',
        active: false,
        dateRange: true,
        dateRangeFromControl: new FormControl(),
        dateRangeToControl: new FormControl(),
        dateRangeFromParamKey: 'payment_sent_at_from',
        dateRangeToParamKey: 'payment_sent_at_to',
      },
      {
        parameterName: 'created_at',
        translationKey: 'payments.list.createdAt',
        active: false,
        dateRange: true,
        dateRangeFromControl: new FormControl(),
        dateRangeToControl: new FormControl(),
        dateRangeFromParamKey: 'created_at_from',
        dateRangeToParamKey: 'created_at_to',
      },
      {
        parameterName: 'captured_at',
        translationKey: 'payments.list.capturedAt',
        active: false,
        dateRange: true,
        dateRangeFromControl: new FormControl(),
        dateRangeToControl: new FormControl(),
        dateRangeFromParamKey: 'captured_at_from',
        dateRangeToParamKey: 'captured_at_to',
      }
    ];

    const requests = [
      this.paymentService.getPaymentStatuses(),
      this.productService.getCompactProducts(),
      this.companyService.getCompanyPaymentMethods(),
    ];

    forkJoin(requests).subscribe({
      next: (res) => {
        let paymentStatuses = res[0] as PaymentStatusResponse[];
        let products = res[1] as ProductCompactResponse[];
        let paymentMethods = res[2] as CompanyPaymentMethodResponse[];

        // Map payment statuses (excluding "Not sent", "Initiated", "Cancelled by provider" and "Fixed payment")
        headerFilters.find(hf => hf.parameterName === 'payment_status_ids')!.dropDownOptions = paymentStatuses.sort
        ((a, b) => a.payment_status_name.localeCompare(b.payment_status_name)).filter(
          (status) => ![-2, 1, 5, 8, 10].includes(status.payment_status_id)).map
        ((status: PaymentStatusResponse) => {
          return {
            value: status.payment_status_id,
            translationKey: status.payment_status_id == 4 ? 'payments.list.paymentStatus.failed' : status.payment_status_name,
            active: false,
          }
        });

        // Map products
        headerFilters.find(hf => hf.parameterName === 'product_ids')!.dropDownOptions = products.map
        ((product: ProductCompactResponse) => {
          return {
            value: product.product_id,
            translationKey: product.product_name,
            active: false,
          }
        });

        // Map payment methods (excluding Svea and both Dintero methods)
        headerFilters.find(hf => hf.parameterName === 'payment_method_ids')!.dropDownOptions = paymentMethods.sort(
          ((a, b) => a.payment_method_name.localeCompare(b.payment_method_name))).filter(
          (method) => ![0, 1, 2, 9, 11].includes(method.payment_method_id)).map
        ((paymentMethod: PaymentMethodResponse) => {
          return {
            value: paymentMethod.payment_method_id,
            translationKey: paymentMethod.payment_method_name,
            active: false,
          }
        });


        this.headerFiltersContainerSubject.next({filters: headerFilters, init: true});
      }
    });


    this.headerFiltersContainerSubject.next({filters: headerFilters, init: true});
  }

  rowClicked(row: PaymentRow) {
    let modalRef = this.modalService.open(PaymentDetailsV2Component, {size: 'lg'});
    modalRef.componentInstance.payment = row;
    modalRef.componentInstance.fetchPayment = true;
    modalRef.componentInstance.viewSettings = {modalView: true, paymentView: true, paymentStandaloneView: true};
    modalRef.componentInstance.paymentDeleted.subscribe((payment: OrderPaymentResponseCompact) => {
      this.getPayments(this.lastUsedSearchTerm, 'paymentDeleted');
    });
  }

  colorizeRow(row: PaymentRow, value: string | undefined | null) {
    if (row.refund) {
      return `<div class="text-danger">${value}</div>`;
    } else {
      return value;
    }
  }

  protected readonly currencyFormat = currencyFormat;
}
