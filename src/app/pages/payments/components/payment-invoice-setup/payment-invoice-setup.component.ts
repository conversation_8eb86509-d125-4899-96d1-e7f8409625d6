import {Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, Renderer2, SimpleChanges, ViewChild} from '@angular/core';
import {StandardImports} from "../../../../@shared/global_import";
import {InvoiceSendTypeResponse, OrderPaymentResponse, OrderPaymentResponseCompact} from "../../../../@shared/models/payment.interfaces";
import {BehaviorSubject, combineLatest, firstValueFrom, forkJoin, Observable, of, Subject} from "rxjs";
import {SelectoriniComponent} from "../../../../@shared/components/selectorini/selectorini.component";
import {ToggleSwitchComponent} from "../../../../@shared/components/toggle-switch/toggle-switch.component";
import {HelpIconComponent} from "../../../../@shared/components/help-icon/help-icon.component";
import {takeUntil} from "rxjs/operators";
import {AffiliateResponse} from "../../../../@shared/models/affiliate.interfaces";
import {DetailsViewSettings, OrderResponse} from "../../../../@shared/models/order.interfaces";
import {AffiliateService} from "../../../../@shared/services/affiliate.service";
import {PaymentService} from "../../../../@shared/services/payment.service";
import {CustomerService} from "../../../../@shared/services/customer.service";
import {OrderService} from "../../../../@shared/services/order.service";
import {FormControl, Validators} from "@angular/forms";
import {StorageService} from "../../../../@core/services/storage.service";
import {DatepickerinoComponent} from "../../../../@shared/components/datepickerino/datepickerino.component";
import {formatDateDMY, paymentStatusBadge} from "../../../../@core/utils/utils.service";
import {SpinnerComponent} from "../../../../@shared/components/spinner/spinner.component";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {VerifyPopupModal} from "../../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {_CRM_PAY_26} from "../../../../@shared/models/input.interfaces";

export interface InvoiceDateOption {
  customDate: Date | null;
  useSendDate: boolean;
  useExecutionDate: boolean;
  display: string;
}

@Component({
  selector: 'app-payment-invoice-setup',
  templateUrl: './payment-invoice-setup.component.html',
  styleUrl: './payment-invoice-setup.component.css',
  standalone: true,
  imports: [StandardImports, SelectoriniComponent, ToggleSwitchComponent, HelpIconComponent, DatepickerinoComponent, SpinnerComponent]
})
export class PaymentInvoiceSetupComponent implements OnInit, OnChanges {
  @Input() payment: OrderPaymentResponse;
  @Input() paymentRecipientAffiliateId: number | null;
  @Input() viewSettings: DetailsViewSettings;
  @Input() expanded = false;

  paymentRecipient: AffiliateResponse | null = null;
  invoiceSendTypesLoading = false;
  invoiceSendTypes: InvoiceSendTypeResponse[] = [];
  disabledInvoiceSendTypes: InvoiceSendTypeResponse[] = [];
  selectedInvoiceSendType: InvoiceSendTypeResponse | null = null;
  canUseEHF: boolean = false;

  invoiceReferenceControl = new FormControl<string>('');
  dueDateControl = new FormControl<number>(14, [Validators.required, Validators.min(0), Validators.max(365)]);
  invoiceEmailControl = new FormControl<string>('', [Validators.email]);
  invoiceCommentControl = new FormControl<string>('');
  paymentNameControl = new FormControl<string>('');
  invoiceDateOptions: InvoiceDateOption[] = [
    {customDate: null, useSendDate: true, useExecutionDate: false, display: 'Når faktura sendes'},
    {customDate: null, useSendDate: false, useExecutionDate: false, display: 'Oppgi dato'}
  ];
  selectedInvoiceDate: InvoiceDateOption;

  paymentRecipientAndSendTypesLoading: boolean = false;

  destroy$ = new Subject<void>();

  tripletexEnabled: boolean = false;

  @ViewChild('pickerinoInvoiceDate') pickerinoInvoiceDate: DatepickerinoComponent;
  @ViewChild('invoiceDetailsExpandedBody') invoiceDetailsExpandedBody: ElementRef;

  constructor(
    private affiliateService: AffiliateService,
    private paymentService: PaymentService,
    private customerService: CustomerService,
    private orderService: OrderService,
    private storageService: StorageService,
    private modalService: NgbModal,
    private renderer: Renderer2
  ) {
    this.selectedInvoiceDate = this.invoiceDateOptions[0];
  }

  ngOnInit() {
    this.storageService.tripletexEnabled$.pipe(takeUntil(this.destroy$)).subscribe((enabled) => {
      this.tripletexEnabled = enabled;
    });

    // Set consolidated view if payment is a parent consolidated invoice or a container
    if (this.payment?.is_parent_consolidated_invoice || this.payment?.is_consolidated_invoice_container) {
      this.viewSettings.consolidatedInvoiceView = true;
    }

    // Set affiliate if payment recipient is set
    if (!this.viewSettings.createView) {
      this.paymentRecipientAffiliateId = this.payment.payment_recipient?.affiliate_id;
    }

    this.initValues();

    // Init payment recipient and send types
    this.initPaymentRecipientAndSendTypes();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['paymentRecipientAffiliateId']) {
      this.paymentRecipientAffiliateId = changes['paymentRecipientAffiliateId'].currentValue;
      this.initPaymentRecipientAndSendTypes();
    }
  }

  initValues() {
    this.invoiceEmailControl.setValue(this.payment.invoice_email);
    this.invoiceReferenceControl.setValue(this.payment.invoice_reference_text);
    this.dueDateControl.setValue(this.payment.invoice_due_date_days);
    this.invoiceCommentControl.setValue(this.payment.comment);
    this.paymentNameControl.setValue(this.payment.payment_name);
    if (this.payment.payment_sent_at) {
      this.disableElements();
    }
  }

  disableElements() {
    this.invoiceEmailControl.disable();
    this.dueDateControl.disable();
    this.invoiceReferenceControl.disable();
    this.invoiceCommentControl.disable();
    this.paymentNameControl.disable();
  }

  enableElements() {
    this.invoiceEmailControl.enable();
    this.dueDateControl.enable();
    this.invoiceReferenceControl.enable();
    this.invoiceCommentControl.enable();
    this.paymentNameControl.enable();
  }

  initPaymentRecipientAndSendTypes() {
    // Prepare fetching of invoice send types and possible payment recipient
    this.invoiceSendTypesLoading = true;
    const initRequests: Array<Observable<InvoiceSendTypeResponse[] | AffiliateResponse | null>> = [this.paymentService.getInvoiceSendTypes()];
    if (this.paymentRecipientAffiliateId) {
      initRequests.push(this.affiliateService.getAffiliateById(this.paymentRecipientAffiliateId));
    } else {
      initRequests.push(of(null));
    }

    // Fetch invoice send types and possible payment recipient
    this.paymentRecipientAndSendTypesLoading = true;
    combineLatest(initRequests).pipe(takeUntil(this.destroy$)).subscribe(([types, affiliate]) => {
      // Set invoice send types
      this.invoiceSendTypes = (types as InvoiceSendTypeResponse[]).sort((a, b) => a.invoice_send_type_id - b.invoice_send_type_id);

      // If either the payment or the parent order has a payment recipient set:
      if (affiliate) {
        this.paymentRecipient = affiliate as AffiliateResponse;

        // If payment recipient is a company, check if it can receive EHF
        if (this.paymentRecipient?.organisation_number) {
          this.setEhfAccess(this.paymentRecipient)
        }
      } else {
        this.paymentRecipient = null;
        this.dueDateChanged(14);
        this.invoiceEmailChanged('');
      }

      // If payment recipient is set and no payment, set default values
      if (this.paymentRecipient && this.viewSettings.createView) {
        // Set default due date and email
        this.dueDateChanged(this.paymentRecipient.invoice_due_date_days);
        this.invoiceEmailChanged(this.paymentRecipient.invoice_email);

        if (this.paymentRecipient?.invoice_send_type_id != null) {
          // If payment send type set, select it
          this.invoiceSendTypeSelected(this.invoiceSendTypes.find((type) => type.invoice_send_type_id === this.paymentRecipient?.invoice_send_type_id)!);
        } else {
          // If no payment send type set, select the default one from the payment recipient
          this.invoiceSendTypeSelected(this.invoiceSendTypes.find((type) => type.invoice_send_type_id === this.paymentRecipient?.invoice_send_type_id)!);
        }
      }

      // If no send type set yet, get from payment or set default to email
      if (!this.selectedInvoiceSendType) {
        if (this.payment) {
          this.invoiceSendTypeSelected(this.invoiceSendTypes.find((type) => type.invoice_send_type_id === this.payment?.invoice_send_type_id)!);
        } else {
          // If no payment recipient set and no payment, select email send type
          this.invoiceSendTypeSelected(this.invoiceSendTypes.find((type) => type.invoice_send_type_id === 0)!);
        }
      }
      this.paymentRecipientAndSendTypesLoading = false;
      this.invoiceSendTypesLoading = false;
    });
  }

  async toggleExpand(event: MouseEvent) {
    if (!this.expanded && this.viewSettings.consolidatedInvoiceView && this.payment.payment_id) {
      let modalRef = this.modalService.open(VerifyPopupModal, {centered: true, backdrop: 'static'});
      modalRef.componentInstance.showBody = true;
      modalRef.componentInstance.titleTranslationKey = 'Endre samlefaktura?';
      modalRef.componentInstance.bodyBoldTranslationKey = 'Disse fakturadetaljene gjelder for en samlefaktura. Endringer gjort på denne samlefakturaen vil også gjelde utenfor denne ordren.';
      modalRef.componentInstance.bodyRegularTranslationKey = 'Er du sikker på at du vil fortsette?';
      let answer = await modalRef.result;
      if (!answer) {
        this.expanded = false;
        return;
      }
    }
    this.expanded = !this.expanded;
  }

  dueDateChanged(value: number) {
    if (this.dueDateControl.valid) {
      // If the value is set outside control
      if (this.dueDateControl.value !== value) {
        this.dueDateControl.setValue(value, {emitEvent: false});
      }
      this.payment.invoice_due_date_days = value;
    }
  }

  invoiceEmailChanged(value: string) {
    if (this.invoiceEmailControl.valid) {
      // If the value is set outside control
      if (this.invoiceEmailControl.value !== value) {
        this.invoiceEmailControl.setValue(value, {emitEvent: false});
      }
      this.payment.invoice_email = value;
    }
  }

  invoiceReferenceChanged(value: string) {
    this.payment.invoice_reference_text = value;
  }

  paymentNameChanged(value: string) {
    this.payment.payment_name = value;
  }

  invoiceCommentChanged(value: string) {
    this.payment.comment = value;
  }

  invoiceDateOptionSelected(option: any) {
    if (!option.useSendDate) {
      this.pickerinoInvoiceDate.popupHide = false;
    } else {
      this.selectedInvoiceDate = option;
    }
  }

  invoiceDateChanged(date: Date) {
    this.selectedInvoiceDate = this.invoiceDateOptions[1];
    this.selectedInvoiceDate.customDate = date;
    this.payment.invoice_date = this.selectedInvoiceDate.customDate;
  }

  invoiceSendTypeSelected(type: InvoiceSendTypeResponse | any) {
    this.selectedInvoiceSendType = type;
    this.payment.invoice_send_type_id = this.selectedInvoiceSendType!.invoice_send_type_id;
  }

  toggleDisableCustomerPortal(value: boolean) {
    this.payment.disable_customer_portal = !value;
  }

  togglePaymentReminders(value: boolean) {
    this.payment.payment_reminders_disabled = value;
  }

  setEhfAccess(paymentRecipient: AffiliateResponse) {
    let organisationNumber = paymentRecipient.organisation_number || 'NO_NUMBER_AVAILABLE';
    if (this.paymentRecipient?.invoice_send_type_id === 1) {
      this.canUseEHF = true;
      return;
    }
    this.customerService.checkPeppolEHF(organisationNumber).pipe(takeUntil(this.destroy$)).subscribe((res) => {
      this.disabledInvoiceSendTypes = [];
      this.canUseEHF = res.can_receive_ehf === 1;
      this.invoiceSendTypes.forEach((type) => {
        if (type.invoice_send_type_id !== 0 && !this.canUseEHF) {
          this.disabledInvoiceSendTypes.push(type);
        }
      });
    });
  }

  protected readonly formatTimeYMD = formatDateDMY;
  protected readonly paymentStatusBadge = paymentStatusBadge;
}
