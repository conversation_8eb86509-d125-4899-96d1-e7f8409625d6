.invoice-card {
  border: 1px solid #DEE2E6;
  border-radius: 10px;
  box-shadow: none;
  margin: 0;
  overflow-x: auto; /* Enable horizontal scrolling */
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on touch devices */
  display: block; /* Ensure block-level scrolling */
}

.invoice-card:hover {
  border: 1px solid var(--disabled-primary);
  /*box-shadow: 0 0 0 1px var(--disabled-primary);*/
}

.card-body {
  white-space: nowrap; /* Prevent wrapping of content */
  min-width: 600px; /* Ensure card's width is larger than the screen on smaller devices */
}

.chevron-rotate {
  transition: transform 0.3s ease-in-out; /* Add transition to transform */
}

.chevron-rotate.expanded {
  transform: rotate(90deg); /* Rotate the icon by 90 degrees when expanded */
}

/*.order-details-header {*/
/*  padding: 16px 8px 16px 24px !important;*/
/*  font-weight: 600 !important;*/
/*}*/

/*.order-details-header.compact {*/
/*  padding: 8px 8px 8px 16px !important;*/
/*}*/

.order-details-header-title {
  font-size: 18px;
  font-weight: 600 !important;
  color: #2C2C2C;
}

.order-details-header {
  padding: 16px 8px 16px 24px;
  font-weight: 600;
  cursor: pointer;
  transition: padding 0.3s ease-in-out;

  &.compact {
    padding: 8px 8px 8px 16px;
  }
}

.chevron-rotate {
  transition: transform 0.3s ease-in-out;
}

.chevron-rotate.expanded {
  transform: rotate(90deg);
}

/*.collapse-open {*/
/*  opacity: 1;*/
/*  transition: all 0.3s ease, opacity 0.3s ease;*/
/*}*/

/*.collapse-closed {*/
/*  opacity: 0;*/
/*  max-height: 0;*/
/*  transition: all 0.3s ease, opacity 0.3s ease;*/
/*}*/

.consolidation-gear {
  z-index: 999;
  cursor: pointer;
}

.consolidation-gear:hover {
  cursor: pointer;
  color: #181c2f;
}
