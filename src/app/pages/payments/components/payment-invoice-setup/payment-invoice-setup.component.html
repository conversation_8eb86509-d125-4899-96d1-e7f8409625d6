<div class="card invoice-card" style="overflow: visible;">
  <div class="card-body p-0">

    <!--  Closed  -->
    <div *ngIf="!expanded" class="d-flex justify-content-between align-items-center cursor-pointer order-details-header" [ngClass]="{'compact': viewSettings.compactView}" (click)="toggleExpand($event)">
      <div class="d-flex align-items-center col">
        <div class="d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; border-radius: 6px; background-color: #e1dddd;">
          <i *ngIf="! paymentRecipientAndSendTypesLoading" class="fa-regular fa-file-invoice fa-xl"></i>
          <app-spinner *ngIf="paymentRecipientAndSendTypesLoading"></app-spinner>
        </div>
        <div class="col">
          <div class="d-flex align-items-center">
            <div class="order-details-header-title">Fakturadetaljer{{payment.payment_name && !viewSettings.consolidatedInvoiceView ? ' - ' + payment.payment_name : ''}}</div>
            <span *ngIf="!viewSettings.consolidatedInvoiceView && !viewSettings.createView && payment && !payment?.template" class="ms-2" [innerHTML]="paymentStatusBadge(payment)">{{payment.payment_status_name}}</span>
            <div class="d-flex align-items-center">
              <span *ngIf="viewSettings.consolidatedInvoiceView" class="ms-2 badge badge-min-width badge-payment-queued">Samlefaktura</span>
              <div *ngIf="viewSettings.consolidatedInvoiceView" class="ms-2 text-muted">{{payment?.payment_name}}</div>
            </div>
          </div>
          <!-- Icons -->
          <div class="d-flex text-muted font-12 gap-2">

            <!-- Send type -->
            <div class="d-flex align-items-center" [ngbTooltip]="'Fakturatype'" placement="top">
              <i class="fa-regular fa-envelopes-bulk" style="margin-right: 3px;"></i>
              <div>{{this.selectedInvoiceSendType?.invoice_send_type_name}}</div>
            </div>

            <div *ngIf="payment?.invoice_send_type_id !== 3" class="d-flex gap-2">
              <!-- Email -->
              <div class="d-flex align-items-center" [ngbTooltip]="'Faktura-epost'" placement="top" container="body">
                <i class="fa-regular fa-envelope" style="margin-right: 3px;" [ngClass]="this.invoiceEmailControl.value ? 'text-muted' : 'text-warning'"></i>
                <div>{{this.invoiceEmailControl.value || 'Mangler e-post'}}</div>
              </div>

              <!-- Invoice date -->
              <div *ngIf="this.selectedInvoiceDate.customDate" class="d-flex align-items-center" [ngbTooltip]="'Fakturadato'" placement="top" container="body">
                <i class="fa-regular fa-calendar-days" style="margin-right: 3px;"></i>
                <div>{{formatTimeYMD(this.selectedInvoiceDate.customDate)}}</div>
              </div>

              <!-- Due date -->
              <div class="d-flex align-items-center" [ngbTooltip]="'Forfallsdato'" placement="top" container="body">
                <i class="fa-regular fa-calendar-exclamation" style="margin-right: 3px;"></i>
                <div>{{dueDateControl.value}} dager forfall</div>
              </div>

              <!-- Invoice reference -->
              <div class="d-flex align-items-center" [ngbTooltip]="'Fakturareferanse'" placement="top" container="body">
                <i class="fa-regular fa-memo" style="margin-right: 3px;"></i>
                <div>{{this.invoiceReferenceControl.value || 'Ingen referanse'}}</div>
              </div>

              <!-- Customer portal -->
              <div class="d-flex align-items-center" [ngClass]="{'text-success': !payment.disable_customer_portal}" [ngbTooltip]="payment.disable_customer_portal ? 'Kunden kan ikke betale i kundeportal' : 'Kunden kan velge å betale direkte i kundeportalen'" placement="top" container="body">
                <i class="fa-regular " style="margin-right: 3px;" [ngClass]="payment.disable_customer_portal ? 'fa-xmark' : 'fa-check'"></i>
                <div>{{'Kundeportal'}}</div>
              </div>
            </div>



          </div>
        </div>
      </div>
      <div class="d-flex align-items-center py-1">
        <div type="button" class="d-flex align-items-center justify-content-center cursor-pointer p-2" style="min-width: 39px;">
          <i class="fa-regular fa-chevron-right chevron-rotate fa-lg" [ngClass]="{'expanded': expanded}"></i>
        </div>
      </div>
    </div>

    <!--  Open  -->
    <div *ngIf="expanded" class="d-flex justify-content-between align-items-center order-details-header border-bottom cursor-pointer" [ngClass]="{'compact': viewSettings.compactView}" (click)="toggleExpand($event)">
      <div class="d-flex flex-column">
        <div class="d-flex align-items-center">
            <div class="order-details-header-title">Fakturadetaljer</div>
            <div class="d-flex align-items-center">
              <span *ngIf="viewSettings.consolidatedInvoiceView" class="ms-2 badge badge-min-width badge-payment-queued">Samlefaktura</span>
              <div *ngIf="viewSettings.consolidatedInvoiceView" class="ms-2 text-muted">{{payment?.payment_name}}</div>
            </div>
          </div>
      </div>
      <div type="button" class="d-flex align-items-center justify-content-center p-2" style="min-width: 39px;">
        <i class="fa-regular fa-chevron-right chevron-rotate fa-lg" [ngClass]="{'expanded': expanded}"></i>
      </div>
    </div>

      <div *ngIf="expanded" class="" [ngClass]="{ 'collapse-open': expanded, 'collapse-closed': !expanded }">
        <div class="p-2">
          <div class="d-flex">
            <!--    Left side    -->
            <div class="col-6 pe-2">
              <div class="d-flex mb-2">
                <!-- Invoice type -->
                <div class="" [ngClass]="[selectedInvoiceSendType?.invoice_send_type_id != 3 ? 'col-6 pe-2': 'col-12']">
                  <label>{{ "paymentDetails.invoiceSettings.invoiceSendType" | translate }}</label>
                  <app-selectorini
                    [disabled]="!!payment.payment_sent_at"
                    [directSelection]="true"
                    [predefinedSearchResults]="invoiceSendTypes"
                    [disabledSearchResults]="disabledInvoiceSendTypes"
                    [loading]="invoiceSendTypesLoading"
                    [selectedItem]="selectedInvoiceSendType"
                    [searchMainDisplayKeys]="['invoice_send_type_name']"
                    (itemSelectedEmitter)="invoiceSendTypeSelected($event)"
                  ></app-selectorini>
                </div>

                <!--  Due date  -->
                <div *ngIf="selectedInvoiceSendType?.invoice_send_type_id != 3" class="col-6 ps-1">
                  <div class="">
                    <label>{{ "orders.orderDetails.invoiceSettings.dueDate" | translate }}</label>
                    <app-input
                      [hideDecimals] = true
                      [editMode]="true"
                      [control]="dueDateControl"
                      type="number"
                      [inputSuffix]="'orders.orderDetails.invoiceSettings.dueDatePostFix' | translate"
                      (valueChange)="dueDateChanged($event)"
                    ></app-input>
                  </div>
                </div>
              </div>

              <!--  Invoice date   -->
              <div *ngIf="selectedInvoiceSendType?.invoice_send_type_id != 3" class="d-flex mb-2 position-relative">
                <div class="w-100">
                  <label>{{ "Fakturadato" | translate }}</label>
                  <div class="d-flex align-items-center gap-1">
                    <div class="w-100">
                      <app-selectorini
                        [directSelection]="true"
                        [predefinedSearchResults]="invoiceDateOptions"
                        [loading]="invoiceSendTypesLoading"
                        [disabled]="!!viewSettings.consolidatedInvoiceView || !!payment.payment_sent_at"
                        [selectedItem]="selectedInvoiceDate"
                        [customNgTemplate]="invoiceDateOption"
                        (itemSelectedEmitter)="invoiceDateOptionSelected($event)"
                      ></app-selectorini>
                    </div>
                    <i *ngIf="!viewSettings.consolidatedInvoiceView" class="fa-regular fa-calendar-days fa-xl cursor-pointer" (click)="pickerinoInvoiceDate.toggle($event)"></i>
                  </div>
                </div>
               <app-datepickerino
                  #pickerinoInvoiceDate
                  [popup]="true"
                  [selectedDates]="[invoiceDateOptions[1].customDate]"
                  [referenceDate]="invoiceDateOptions[1].customDate"
                  [compact]="true"
                  (datesSelectedEmitter)="invoiceDateChanged($event[0])"
                ></app-datepickerino>
              </div>

              <!-- Invoice email -->
              <div *ngIf="selectedInvoiceSendType?.invoice_send_type_id != 3" class="mb-2">
                <label>{{ "orders.orderDetails.invoiceSettings.invoiceEmail" | translate }}</label>
                <app-input
                  [editMode]="true"
                  [control]="invoiceEmailControl"
                  (valueChange)="invoiceEmailChanged($event)"
                ></app-input>
              </div>

              <!-- Payment reminders -->
              <div *ngIf="selectedInvoiceSendType?.invoice_send_type_id != 3" class="d-flex align-items-center justify-content-between mb-1">
                <div class="d-flex align-items-center">
                  <label for="paymentReminderSwitch" class="ps-1 me-1" (click)="togglePaymentReminders(!payment.payment_reminders_disabled)">{{'paymentDetails.paymentReminders' | translate}}</label>
                  <app-help-icon [tooltipKey]="'paymentDetails.paymentReminders.tooltip'"></app-help-icon>
                </div>
                <app-toggle-switch
                  id="paymentReminderSwitch"
                  [bigSwitch]="true"
                  [labelKey]="''"
                  [state]="!payment.payment_reminders_disabled"
                  (stateChange)="togglePaymentReminders($event)"
                ></app-toggle-switch>
              </div>

            </div>

            <!--   Right side   -->
            <div class="col-6 ps-2">

              <!--    Customer portal toggle switch    -->
              <label *ngIf="selectedInvoiceSendType?.invoice_send_type_id != 3"></label>
              <div *ngIf="selectedInvoiceSendType?.invoice_send_type_id != 3" class="mb-2 py-2 ps-2" style="border: 1px solid #DEE2E6; border-radius: 8px;">
                <div class="d-flex align-items-center justify-content-between">
                  <div class="d-flex align-items-center">
                    <label for="customerToggleSwitch" class="me-1" (click)="toggleDisableCustomerPortal(!payment.disable_customer_portal)">Kunden kan betale i kundeportalen</label>
                    <app-help-icon [tooltipKey]="'Når denne er aktivert vil kunden kunne betale med f.eks kort eller Vipps i kundeportalen. Hvis denne er deaktivert vil kunden kun kunne betale via fakturaen som sendes.'"></app-help-icon>
                  </div>
                  <app-toggle-switch
                    id="customerToggleSwitch"
                    [bigSwitch]="true"
                    [labelKey]="''"
                    [isDisabled]="[3, 12].includes(payment.payment_status_id)"
                    [state]="!payment.disable_customer_portal"
                    (stateChange)="toggleDisableCustomerPortal($event)"
                  ></app-toggle-switch>
                </div>

                <div class="d-flex justify-content-evenly align-items-center pe-2" style="height: 47px;">
                  <img src="assets/images/payments/visa+master.png" height="40">
                </div>
              </div>

              <!-- Invoice reference -->
              <div *ngIf="selectedInvoiceSendType?.invoice_send_type_id != 3" class="" [ngClass]="{'mb-2': tripletexEnabled}">
                <label>{{ "orders.orderDetails.invoiceSettings.invoiceReference" | translate }}</label>
                <app-input
                  [editMode]="true"
                  [control]="invoiceReferenceControl"
                  (valueChange)="invoiceReferenceChanged($event)"
                ></app-input>
              </div>

              <!--  Invoice Comment  -->
              <div *ngIf="tripletexEnabled && selectedInvoiceSendType?.invoice_send_type_id != 3" class="mb-2">
                <label>{{ "orders.orderDetails.invoiceSettings.invoiceComment" | translate }}</label>
                <app-input
                  [editMode]="true"
                  [placeholderKey]="'Oppgi en kommentar som skal vises på fakturaen'"
                  [control]="invoiceCommentControl"
                  (valueChange)="invoiceCommentChanged($event)"
                ></app-input>
              </div>

              <!--  Payment name  -->
              <div class="">
                <div class="d-flex gap-1 align-items-center">
                  <label>Fakturanavn</label>
                  <app-help-icon [noPadding]="true" [tooltipKey]="'Legg inn et egendefinert navn på fakturaen for å lette kunne kjenne den i Between'"></app-help-icon>
                </div>
                <app-input
                  [editMode]="true"
                  [placeholderKey]="'Eks. Forhåndsbetaling for befaring'"
                  [control]="paymentNameControl"
                  (valueChange)="paymentNameChanged($event)"
                ></app-input>
              </div>

            </div>

          </div>

        </div>


      </div>

  </div>
</div>

<ng-template #invoiceDateOption let-item="item" let-selected="selected" let-disabled="disabled">
  <div class="d-flex justify-content-between col align-items-center pe-3">
    <div class="ms-2">
      <div>{{item.customDate && selected ? formatTimeYMD(item.customDate) : item.display}}</div>
    </div>
  </div>
</ng-template>
