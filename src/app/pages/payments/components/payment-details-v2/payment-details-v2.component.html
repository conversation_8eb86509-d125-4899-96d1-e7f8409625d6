<div *ngIf="!viewSettings.createView" class="hidden-id" style="position: absolute; top: 2px; right: 3px; text-align: right; padding: 0 0 20px 20px;">{{payment.payment_id}}</div>
<div *ngIf="viewSettings.modalView" class="d-flex align-items-center justify-content-between border-bottom py-2">
  <div class="col-4"></div>
  <h4 class="col-4 text-center">{{!viewSettings.createView ? '#' + payment.payment_number + (payment.payment_name ? (' - ' + payment.payment_name) : '') : viewSettings.repeatingView ? 'Opprett fakturaplan' : 'Opprett faktura'}}</h4>
  <div class="col-4 d-flex align-items-center justify-content-end pe-3">
    <ng-container *ngTemplateOutlet="!viewSettings.createView && !viewSettings.consolidatedInvoiceView && !viewSettings.repeatingView ? actionButtons : null"></ng-container>
  </div>
</div>

<div *ngIf="initComplete" class="p-3">
  <!-- Payment recipient -->
  <div *ngIf="!viewSettings.createView" class="d-flex align-items-center details-card order-details-header compact mb-2 cursor-pointer " (click)="navigateToPaymentRecipient()">
    <div class="d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; border-radius: 6px; background-color: #e1dddd;">
      <i class="fa-regular" [ngClass]="payment.payment_recipient.is_private ? 'fa-user fa-xl' : payment.affiliate_contact ? 'fa-building-user fa-lg' : 'fa-building fa-xl'"></i>
    </div>
    <div>
      <div class="order-details-header-title">
        <span>{{payment.payment_recipient.name}}</span>
        <span *ngIf="payment.affiliate_contact" class="text-muted ms-1">- {{payment.affiliate_contact.name}}</span>
      </div>
      <div class="d-flex text-muted font-12 gap-2">
        <div *ngIf="payment.payment_recipient?.organisation_number" class="d-flex align-items-center">
          <i class="fa-regular fa-building" style="margin-right: 3px;"></i>
          <div>{{payment.payment_recipient?.organisation_number}}</div>
        </div>
        <div class="d-flex align-items-center">
          <i class="fa-regular fa-envelope" style="margin-right: 3px;"></i>
          <div>{{payment.affiliate_contact?.email || payment.payment_recipient.email || 'Mangler e-post'}}</div>
        </div>
        <div class="d-flex align-items-center">
          <i class="fa-regular fa-phone text-muted" style="margin-right: 3px;"></i>
          <div>{{payment.affiliate_contact?.phone || payment.payment_recipient.phone || 'Mangler telefon'}}</div>
        </div>

      </div>

    </div>
  </div>


  <!-- Payment schedule setup -->
  <div *ngIf="viewSettings.repeatingView && payment" class="mb-2">
    <app-payment-schedule-setup
      [payment]="payment"
      [viewSettings]="viewSettings"
      [paymentRecipientId]="paymentRecipientAffiliateId"
      (scheduleOptionSelectedEvent)="scheduleOptionSelected($event)"
    ></app-payment-schedule-setup>
  </div>

    <div *ngIf="consolidatedInvoiceParents.length > 1" class="mt-2">
        <label>{{"paymentDetails.consolidatedInvoice.selectInvoice" | translate}}</label>
        <app-selectorini
          [directSelection]="true"
          [disableFocusOnLoad]="true"
          [disabled]="!!payment && [3, 6].includes(payment.payment_status_id)"
          [createItemTranslationKey]="'paymentDetails.consolidatedInvoice.createNew'"
          [placeholderTranslationKey]="'paymentDetails.consolidatedInvoice.placeholder'"
          [selectedItem]="selectedConsolidatedInvoiceParent"
          [predefinedSearchResults]="consolidatedInvoiceParents"
          [loading]="consolidatedInvoiceLoading"
          [searchMainDisplayKeys]="['payment_name']"
          (itemSelectedEmitter)="consolidatedInvoiceParentSelected($event)"
        ></app-selectorini>
      </div>

  <!-- Payment invoice setup -->
  <div *ngIf="!selectedConsolidatedInvoiceParent && payment" class="mb-2">
    <app-payment-invoice-setup
      [payment]="payment"
      [paymentRecipientAffiliateId]="paymentRecipientAffiliateId"
      [viewSettings]="{compactView: true, repeatingView: !!viewSettings.repeatingView, createView: !!viewSettings.createView}"
    ></app-payment-invoice-setup>
  </div>

  <div *ngIf="selectedConsolidatedInvoiceParent" class="mb-2">
    <app-payment-invoice-setup
      [payment]="selectedConsolidatedInvoiceParent"
      [paymentRecipientAffiliateId]="paymentRecipientAffiliateId"
      [viewSettings]="{compactView: true, consolidatedInvoiceView: true, createView: !!viewSettings.createView}"
    ></app-payment-invoice-setup>
  </div>

  <!-- Order lines -->
  <app-order-lines
    *ngIf="payment?.schedule_option?.fixed !== false"
    [order]="order"
    [payment]="this.viewSettings.createView ? undefined : payment"
    [orderLinesSubject]="orderLinesSubject"
    [viewSettings]="{paymentView: true, compactView: true}"
  ></app-order-lines>
</div>

<hr class="m-0">

<!-- Footer -->
<div *ngIf="!payment?.invoice_sent_at && !payment?.captured_at">
  <!-- Footer - Create -->
  <div *ngIf="viewSettings.createView" class="d-flex justify-content-end p-3 gap-2">
    <!-- Single payment - Create, no send -->
    <app-button
      *ngIf="!viewSettings.repeatingView && viewSettings.createView && payment?.invoice_send_type_id !== 3"
      [disabled]="isSaveDisabled() || sendLoading"
      [ngbTooltip]="saveDisabledKey"
      [loading]="saveLoading"
      [translationKey]="'Lagre og send senere'"
      [themeStyle]="'secondary'"
      (buttonClick)="createPayment(true)"
    ></app-button>

    <!-- Single payment - Create and send -->
    <app-button
      *ngIf="!viewSettings.repeatingView"
      [disabled]="isSendDisabled() || saveLoading"
      [ngbTooltip]="saveDisabledKey || sendDisabledKey"
      [loading]="sendLoading"
      [translationKey]="sendButtonText"
      (buttonClick)="createPayment(false)"
    ></app-button>

    <!-- Create repeating -->
    <app-button
      *ngIf="viewSettings.repeatingView"
      [disabled]="isSaveDisabled()"
      [ngbTooltip]="saveDisabledKey"
      [loading]="saveLoading"
      [translationKey]="'Lagre fakturaplan'"
      (buttonClick)="createPayment(true)"
    ></app-button>
  </div>

  <!-- Footer - Update -->
  <div *ngIf="!viewSettings.createView" class="d-flex justify-content-between">
    <div class="d-flex align-items-center p-3">
      <app-button
        [translationKey]="viewSettings.repeatingView ? 'Slett fakturaplan' : 'Slett faktura'"
        [loading]="deleteLoading"
        [themeStyle]="'danger'"
        (buttonClick)="deletePayment()"
      ></app-button>
    </div>
    <div class="d-flex justify-content-end p-3 gap-2">
      <!-- Single payment - Save, no send -->
      <app-button
        *ngIf="!viewSettings.repeatingView && !viewSettings.createView && payment?.invoice_send_type_id !== 3"
        [disabled]="isSaveDisabled() || sendLoading"
        [ngbTooltip]="saveDisabledKey"
        [loading]="saveLoading"
        [translationKey]="'Lagre og send senere'"
        [themeStyle]="'secondary'"
        (buttonClick)="updatePayment(true)"
      ></app-button>

      <!-- Single payment - Save and send -->
      <app-button
        *ngIf="!viewSettings.repeatingView && payment.invoice_send_type_id !== 3"
        [disabled]="isSendDisabled() || saveLoading"
        [ngbTooltip]="saveDisabledKey || sendDisabledKey"
        [loading]="sendLoading"
        [translationKey]="sendButtonText"
        (buttonClick)="updatePayment(false)"
      ></app-button>

      <!-- Save repeating -->
      <app-button
        *ngIf="viewSettings.repeatingView && payment.invoice_send_type_id !== 3"
        [disabled]="isSaveDisabled()"
        [ngbTooltip]="saveDisabledKey"
        [loading]="saveLoading"
        [translationKey]="'Lagre fakturaplan'"
        (buttonClick)="updatePayment(false)"
      ></app-button>

      <!-- Mark cash invoice as paid -->
      <app-button
        *ngIf="!viewSettings.createView && payment?.payment_method_id === 4 && payment?.payment_status_id !== 3"
        [disabled]="isSaveDisabled()"
        [loading]="loading"
        [translationKey]="'Marker som betalt'"
        (buttonClick)="executeMarkOrderAsPaid(true)"
      ></app-button>
    </div>
  </div>
</div>

<ng-template #actionButtons>
  <!-- Payment action buttons-->
  <div id="buttonOuterContainerDiv" class="d-flex align-items-center justify-content-end" style="max-width: 70px;">
    <label></label>
    <div class="btn-group d-flex align-items-end">
      <button type="button" (click)="this.isActionButtonsDropdownOpen = !this.isActionButtonsDropdownOpen;" class="btn btn-primary" [disabled]="loading" [ngClass]="loading ? '' : 'dropdown-toggle'" style="width: 178px; height: 38px;" data-bs-toggle="dropdown" aria-expanded="false">
        <span *ngIf="!loading">{{ "orders.orderDetails.otherActions" | translate }} <span class="caret"></span></span>
        <app-spinner *ngIf="loading"></app-spinner>
      </button>

      <div #dropDownButtonsDiv *ngIf="!viewSettings.createView" class="dropdown-menu" [hidden]="loading">

        <!--  Credit invoice   -->
        <div [ngbTooltip]="payment && [10, 15].includes(payment.payment_method_id) && payment.refunded_amount > 0 ? ('orders.orderDetails.creditInvoice.tooltip' | translate) : null">
          <button
            *ngIf="payment?.accounting_transfer_at"
            [disabled]="payment && [10, 15].includes(payment.payment_method_id) && payment.refunded_amount > 0"
            [ngStyle]="{'color': payment && payment.refunded_amount > 0 ? '#aaa' : '#6C757D'}"
            class="dropdown-item"
            (click)="creditInvoice()">
            <i class="fa-regular fa-file-xmark me-1"></i>
            <span>{{ "orders.orderDetails.creditInvoice" | translate }}</span>
          </button>
        </div>

        <!--  Capture manually   -->
        <button
          *ngIf="payment?.payment_sent_at && [2, 5].includes(payment?.payment_status_id!) && payment?.payment_method_id! == 5"
          class="dropdown-item"
          (click)="captureManually()">
          <i class="fa-regular fa-money-check-dollar-pen me-1"></i>
          {{ "orders.orderDetails.captureManually" | translate }}
        </button>

        <!--  Mark order as paid   -->
        <button
          *ngIf="payment?.payment_status_id! !== 3"
          class="dropdown-item"
          (click)="manuallyMarkOrderAsPaid()">
          <i class="fa-regular fa-money-check-dollar-pen me-1"></i>
          {{ "orders.orderDetails.markOrderAsPaid" | translate }}
        </button>

        <!--  Download invoice PDF   -->
        <button
          *ngIf="payment?.invoice_id! && (tripletexEnabled || fikenEnabled)"
          class="dropdown-item"
          (click)="downloadPdf()">
          <i class="fa-regular fa-download me-1"></i>
          {{ "paymentDetails.downloadInvoicePDF" | translate }}
        </button>

        <!--  Send receipt   -->
        <button
          *ngIf="payment && payment.payment_status_id === 3"
          class="dropdown-item"
          (click)="sendReceipt()">
          <i class="fa-regular fa-receipt me-1"></i>
          {{ "paymentDetails.sendReceipt" | translate }}
        </button>

        <!--  Refund  -->
        <button
          *ngIf="payment"
          class="dropdown-item"
          (click)="onRefundClick()"
          [disabled]="![3, 10].includes(payment.payment_status_id) || payment.payment_method_id === 4 || (payment.total_amount_inc_vat === payment.refunded_amount)"
          [ngStyle]="{'color': payment && (![3, 10].includes(payment.payment_status_id) || payment.payment_method_id === 4 || (payment.total_amount_inc_vat === payment.refunded_amount)) ? '#aaa' : '#6C757D'}">
          <i class="fa-regular fa-rotate-left me-1"></i>
          {{ "orders.orderDetails.refundButtonLabel" | translate }}
        </button>

        <!--  Open logs   -->
        <button
          class="dropdown-item"
          (click)="openPaymentLogs()">
          <i class="fa-regular fa-bars-staggered me-1"></i>
          {{ "orderDetails.payments.eventLog" | translate }}
        </button>

        <!--  Open consolidated invoice   -->
        <button
          *ngIf="payment?.payment_method_id === 15 && !payment?.is_consolidated_invoice_container && !payment?.is_parent_consolidated_invoice"
          class="dropdown-item"
          (click)="openConsolidatedInvoice()">
          <i class="fa-regular fa-file-lines me-1"></i>
          {{ "orderDetails.payments.goToConsolidationContainer" | translate }}
        </button>

        <!--  Open project and department modal  -->
        <button
          *ngIf="payment && (projectsEnabled || departmentsEnabled)"
          class="dropdown-item"
          (click)="openProjectAndDepartmentModal()"
          [ngClass]="{'text-muted': !!payment?.accounting_transfer_at || payment.payment_method_id === 15}">
          <i class="fa-regular fa-building-memo me-1"></i>
          {{ (fikenEnabled ? 'orderDetails.payments.projectAndDepartment.projectOnly' : 'orderDetails.payments.projectAndDepartment') | translate }}
        </button>

        <!--  Delete payment   -->
        <button
          *ngIf="!viewSettings.createView && !payment.payment_sent_at"
          class="dropdown-item"
          (click)="payment?.payment_status_id === 3 ? $event.stopPropagation() : deletePayment()"
          [disabled]="deleteLoading"
          [ngClass]="{'text-muted': payment?.payment_status_id === 3}">
          <i class="fa-regular fa-trash-alt me-1"></i>
          {{ "common.delete" | translate }}
        </button>


      </div>

    </div>
  </div>
</ng-template>
