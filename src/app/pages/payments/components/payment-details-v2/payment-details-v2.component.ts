import {ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Optional, Output, SimpleChanges} from '@angular/core';
import {StandardImports} from "../../../../@shared/global_import";
import {HelpIconComponent} from "../../../../@shared/components/help-icon/help-icon.component";
import {SelectoriniComponent} from "../../../../@shared/components/selectorini/selectorini.component";
import {ScheduleSetupComponent} from "../../../../@shared/components/schedule-component/schedule-setup.component";
import {SpinnerComponent} from "../../../../@shared/components/spinner/spinner.component";
import {ToggleSwitchComponent} from "../../../../@shared/components/toggle-switch/toggle-switch.component";
import {OrderLinesComponent} from "../../../orders/order-details-v2/order-lines/order-lines.component";
import {PaymentScheduleSetupComponent} from "../payment-schedule-setup/payment-schedule-setup.component";
import {OrderPaymentResponse, OrderPaymentResponseCompact, PaymentScheduleOptionResponse} from "../../../../@shared/models/payment.interfaces";
import {DetailsViewSettings, OrderLineRow, OrderResponse, PaymentRecipientResponse} from "../../../../@shared/models/order.interfaces";
import {BehaviorSubject, firstValueFrom, Subject} from "rxjs";
import {_CRM_PAY_26, _CRM_PAY_27, ScheduleInput} from "../../../../@shared/models/input.interfaces";
import {PaymentInvoiceSetupComponent} from "../payment-invoice-setup/payment-invoice-setup.component";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {PaymentService} from "../../../../@shared/services/payment.service";
import {OrderService} from "../../../../@shared/services/order.service";
import {takeUntil} from "rxjs/operators";
import {ButtonDoubleComponent} from "../../../../@shared/components/button-double/button-double.component";
import {VerifyPopupModal} from "../../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {ToastService} from "../../../../@core/services/toast.service";
import {StorageService} from "../../../../@core/services/storage.service";
import {PaymentLogsModalComponent} from "../payment-logs-modal/payment-logs-modal.component";
import {ProjectAndDepartmentModalComponent} from "../payment-details/_modals/project-and-department-modal/project-and-department-modal";
import {RefundOrderModalComponent} from "../../../orders/order-details-v2/components/action-button-group/_modals/refund-order-modal/refund-order-modal.component";
import {Router} from "@angular/router";

@Component({
  selector: 'app-payment-details-v2',
  templateUrl: './payment-details-v2.component.html',
  styleUrl: './payment-details-v2.component.css',
  standalone: true,
  imports: [
    StandardImports,
    HelpIconComponent,
    SelectoriniComponent,
    ScheduleSetupComponent,
    SpinnerComponent,
    ToggleSwitchComponent,
    OrderLinesComponent,
    PaymentScheduleSetupComponent,
    PaymentInvoiceSetupComponent,
    ButtonDoubleComponent
  ]
})

export class PaymentDetailsV2Component implements OnInit, OnChanges, OnDestroy {
  @Input() payment: OrderPaymentResponse;
  @Input() order?: OrderResponse;
  @Input() viewSettings: DetailsViewSettings = {paymentView: true}
  @Input() orderLinesSubject: BehaviorSubject<OrderLineRow[]> = new BehaviorSubject<OrderLineRow[]>([]);
  @Input() lockedForSingle: boolean = false;
  @Input() fetchPayment: boolean = false;

  paymentRecipientAffiliateId: number | null = null;
  saveDisabledKey: string | null = null;
  sendDisabledKey: string | null = null;
  consolidatedInvoiceUpdated: boolean = false;
  isActionButtonsDropdownOpen: boolean = false;
  loading: boolean = false;
  sendLoading: boolean = false;
  saveLoading: boolean = false;
  deleteLoading: boolean = false;

  tripletexEnabled: boolean = false;
  fikenEnabled: boolean = false;
  projectsEnabled: boolean = false;
  departmentsEnabled: boolean = false;
  initComplete: boolean = false;

  consolidatedInvoiceParents: OrderPaymentResponseCompact[] = [];
  selectedConsolidatedInvoiceParent: OrderPaymentResponse | null = null;
  consolidatedInvoiceLoading: boolean = false;

  @Output() paymentDeleted = new EventEmitter<number>();

  destroy$ = new Subject<void>();

  constructor(
      private modalService: NgbModal,
      private paymentService: PaymentService,
      private orderService: OrderService,
      private toastService: ToastService,
      private storageService: StorageService,
      private router: Router,
      private cdr: ChangeDetectorRef,
      @Optional() private activeModal: NgbActiveModal
  ) {}

  async ngOnInit() {
    // Detach payment
    if (this.payment) {
      this.payment = JSON.parse(JSON.stringify(this.payment));
    }

    this.orderService.order$.pipe(takeUntil(this.destroy$)).subscribe((order) => {
      this.order = order;
    });

    this.storageService.tripletexEnabled$.pipe(takeUntil(this.destroy$)).subscribe((enabled) => {
      this.tripletexEnabled = enabled;
    });

    this.storageService.fikenEnabled$.pipe(takeUntil(this.destroy$)).subscribe((enabled) => {
      this.fikenEnabled = enabled;
    });

    this.storageService.projectsEnabled$.pipe(takeUntil(this.destroy$)).subscribe((enabled) => {
      this.projectsEnabled = enabled;
    });

    this.storageService.departmentsEnabled$.pipe(takeUntil(this.destroy$)).subscribe((enabled) => {
      this.departmentsEnabled = enabled;
    });

    if (this.fetchPayment && this.payment) {
      this.payment = await firstValueFrom(this.paymentService.getOrderPayment(this.payment.payment_id))
    }

    // this.paymentService.getOrderPayment(consolidatedInvoiceId).pipe(takeUntil(this.destroy$)).subscribe((res) => {
    //   this.payment.consolidated_parent_payment = res;
    //   this.consolidatedInvoiceUpdated = false;
    // });

    if (this.viewSettings.createView) {
      this.initEmpty();
    } else {
      this.initPayment();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['order']) {
      if (!this.payment) {
        this.initEmpty();
      }
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  initEmpty() {
    // Init payment recipient ID
    this.paymentRecipientAffiliateId = this.order?.payment_recipient?.affiliate_id || null;
    this.payment = {
      auto_capture: false,
      invoice_email: '',
      invoice_reference_text: '',
      invoice_due_date_days: 14,
      invoice_send_type_id: 0,
      invoice_date: null,
      payment_name: '',
      comment: null,
      disable_customer_portal: false,
      payment_reminders_disabled: false,
      work_order_ids: Array<number>(),
    } as OrderPaymentResponse;
    this.initComplete = true;
  }

  initEmptyConsolidatedInvoice() {
    this.consolidatedInvoiceParentSelected({
      invoice_send_type_id: 0,
      invoice_due_date_days: 14,
      order_lines: [],
      is_parent_consolidated_invoice: true,
    } as unknown as OrderPaymentResponse);
    this.consolidatedInvoiceUpdated = false;
  }

  initPayment() {
    if (!this.payment) {
      throw new Error('Payment is null on payment init');
    }

    // If the payment is attached to a consolidated invoice, set the consolidated invoice
    if (this.payment.payment_method_id === 15 && !this.payment.is_consolidated_invoice_container && !this.payment.is_parent_consolidated_invoice) {
      this.paymentService.getOrderPayment(this.payment.consolidated_invoice_payment_id!).pipe(takeUntil(this.destroy$)).subscribe((res) => {
        this.selectedConsolidatedInvoiceParent = res;
      });
    }

    // Init order lines
    this.orderLinesSubject.next(this.payment.order_lines.map(orderLine => {
      return {
        ...orderLine,
        checked: !orderLine.locked && !orderLine.payment_id,
      }
    }));

    // Init payment recipient ID
    this.paymentRecipientAffiliateId = this.payment.payment_recipient?.affiliate_id || null;
    this.initComplete = true;
  }

  isSaveDisabled() {
    if (this.paymentRecipientAffiliateId == null) {
      this.saveDisabledKey = 'Du må velge kunde før du oppretter faktura.';
      return true;
    }

    this.saveDisabledKey = null;
    return false;
  }

  isSendDisabled() {
    if (this.order && this.order.order_status_id < 2) {
      this.sendDisabledKey = 'Ordren må være bekreftet før du kan sende faktura';
      return true;
    }

    let orderLines = this.orderLinesSubject.getValue();
    const totalAmount = orderLines.reduce((sum, ol) => sum + ol.gross_total_price_inc_vat, 0);
    if (orderLines.length === 0) {
      this.sendDisabledKey = 'Fakturaen må ha minst én ordrelinje før den kan sendes';
      return true;
    } else if (totalAmount === 0) {
        this.sendDisabledKey = 'Fakturaen kan ikke sendes med totalbeløp lik 0';
        return true;
    }

    this.sendDisabledKey = null;
    return false;
  }

  get sendButtonText() {
    if (this.payment.schedule_option?.consolidated || !!this.selectedConsolidatedInvoiceParent) {
      return 'Legg til samlefaktura';
    } else if (this.payment?.invoice_send_type_id === 3) {
      return 'Opprett kontantfaktura';
    } else {
      return 'Send faktura';
    }
  }

  ///////////////////////////////////////////////
  ////////////// Create payment /////////////////
  ///////////////////////////////////////////////
  async createPayment(createOnly: boolean) {
    if (createOnly) {
      this.saveLoading = true;
    } else {
      this.sendLoading = true;
    }

    let paymentMethodId = 10;
    // If the selected schedule option or the invoice payload is set to send in a consolidated invoice, set the payment method ID to 15
    if (this.payment.schedule_option?.consolidated || !!this.selectedConsolidatedInvoiceParent) {
      paymentMethodId = 15;
    }
    // If the selected schedule option is set to auto capture, set the payment method ID to 14
    else if (this.payment.auto_capture) {
      paymentMethodId = 14;
    }
    // If the invoice send type is set to cash invoice, set the payment method ID to 4 (Former external payment)
    else if (this.payment.invoice_send_type_id === 3) {
      paymentMethodId = 4;
    }

    // Update consolidated invoice parent (will also update container)
    if (paymentMethodId === 15) {
      if (this.selectedConsolidatedInvoiceParent?.payment_id) {
        if (this.consolidatedInvoiceUpdated) {
          let payload: _CRM_PAY_27 = {
            payment_id: this.selectedConsolidatedInvoiceParent?.payment_id!,
            payment_method_id: paymentMethodId,
            payment_name: this.selectedConsolidatedInvoiceParent.payment_name,
            invoice_send_type_id: this.selectedConsolidatedInvoiceParent.invoice_send_type_id,
            invoice_email: this.selectedConsolidatedInvoiceParent.invoice_email,
            invoice_reference_text: this.selectedConsolidatedInvoiceParent.invoice_reference_text,
            invoice_due_date_days: this.selectedConsolidatedInvoiceParent.invoice_due_date_days,
            comment: this.selectedConsolidatedInvoiceParent.comment!,
            payment_reminders_disabled: this.selectedConsolidatedInvoiceParent.payment_reminders_disabled,
            disable_customer_portal: this.selectedConsolidatedInvoiceParent.disable_customer_portal,
          };
          await firstValueFrom(this.paymentService.updateOrderPayment(payload))
        }
      } else {
        if (this.selectedConsolidatedInvoiceParent) {
          let payload: _CRM_PAY_26 = {
            payment_recipient_id: this.paymentRecipientAffiliateId!,
            payment_method_id: 15,
            order_line_ids: [],
            order_id: null,
            send_to_payment: this.payment.schedule_option?.auto_send || false,
            template: this.viewSettings.repeatingView,
            is_parent_consolidated_invoice: true,
            auto_send: false,
            payment_name: this.selectedConsolidatedInvoiceParent.payment_name,
            invoice_send_type_id: this.selectedConsolidatedInvoiceParent.invoice_send_type_id,
            invoice_email: this.selectedConsolidatedInvoiceParent.invoice_email,
            invoice_reference_text: this.selectedConsolidatedInvoiceParent.invoice_reference_text,
            invoice_due_date_days: this.selectedConsolidatedInvoiceParent.invoice_due_date_days,
            comment: this.selectedConsolidatedInvoiceParent.comment,
            disable_customer_portal: this.selectedConsolidatedInvoiceParent.disable_customer_portal,
            payment_reminders_disabled: this.selectedConsolidatedInvoiceParent.payment_reminders_disabled,
            schedule_option_id: this.payment.schedule_option?.option_id || null,
            schedule: this.payment.payment_schedule || null,
          }
          this.selectedConsolidatedInvoiceParent = await firstValueFrom(this.paymentService.createOrderPayment(payload));
        }
      }
    }

    let scheduleInput: ScheduleInput | null = null;
    if (this.payment.payment_schedule) {
      scheduleInput = this.payment.payment_schedule as ScheduleInput;
    }


    let payload: _CRM_PAY_26 = {
      order_id: this.order?.order_id || null,
      order_line_ids: this.orderLinesSubject.getValue().map(line => line.order_line_id),
      consolidated_invoice_payment_id: this.selectedConsolidatedInvoiceParent?.payment_id,
      template: this.viewSettings.repeatingView,
      payment_recipient_id: this.paymentRecipientAffiliateId || undefined,
      payment_method_id: paymentMethodId,

      payment_name: this.payment.payment_name,
      invoice_send_type_id: this.payment.invoice_send_type_id,
      invoice_email: this.payment.invoice_email,
      invoice_reference_text: this.payment.invoice_reference_text,
      invoice_due_date_days: this.payment.invoice_due_date_days,
      comment: this.payment.comment,
      payment_reminders_disabled: this.payment.payment_reminders_disabled,
      disable_customer_portal: this.payment.disable_customer_portal,

      schedule_option_id: this.viewSettings.repeatingView ? this.payment.schedule_option?.option_id || null : null,
      schedule: scheduleInput,
      auto_send: this.payment.schedule_option?.auto_send || false,
      work_order_ids: this.payment.work_order_ids,
      send_to_payment: !createOnly && paymentMethodId !== 15,
    };

    this.paymentService.createOrderPayment(payload).subscribe((res) => {
      this.payment = res;
      if (this.order) {
        this.orderService.fetchAndRefreshOrderPaymentSchedules(this.order.order_id);
        this.orderService.fetchAndRefreshOrderPayments(this.order.order_id);
        this.orderService.fetchAndRefreshOrderLines(this.order.order_id, 'createPayment');
      }
      this.saveLoading = false;
      this.sendLoading = false;
      if (this.payment.invoice_send_type_id !== 3) {
        this.closeSelf();
      } else {
        this.initComplete = false;
        this.viewSettings.createView = false;
        this.cdr.detectChanges();
        this.initPayment();
      }
    }, error => {
      this.saveLoading = false;
      this.sendLoading = false;
    });
  }

  ///////////////////////////////////////////////
  ////////////// Update payment /////////////////
  ///////////////////////////////////////////////
  async updatePayment(saveOnly: boolean) {
    if (saveOnly) {
      this.saveLoading = true;
    } else {
      this.sendLoading = true;
    }

    let paymentMethodId = 10;
    // If the selected schedule option or the invoice payload is set to send in a consolidated invoice, set the payment method ID to 15
    if (this.payment.schedule_option?.consolidated || !!this.selectedConsolidatedInvoiceParent) {
      paymentMethodId = 15;
    }
    // If the selected schedule option is set to auto capture, set the payment method ID to 14
    else if (this.payment.auto_capture) {
      paymentMethodId = 14;
    }

    // Update consolidated invoice parent (will also update container)
    if (paymentMethodId === 15 && this.consolidatedInvoiceUpdated && this.selectedConsolidatedInvoiceParent) {
      let payload: _CRM_PAY_27 = {
        payment_id: this.selectedConsolidatedInvoiceParent?.payment_id!,
        payment_method_id: paymentMethodId,
        payment_name: this.selectedConsolidatedInvoiceParent.payment_name,
        invoice_send_type_id: this.selectedConsolidatedInvoiceParent.invoice_send_type_id,
        invoice_email: this.selectedConsolidatedInvoiceParent.invoice_email,
        invoice_reference_text: this.selectedConsolidatedInvoiceParent.invoice_reference_text,
        invoice_due_date_days: this.selectedConsolidatedInvoiceParent.invoice_due_date_days,
        comment: this.selectedConsolidatedInvoiceParent.comment!,
        payment_reminders_disabled: this.selectedConsolidatedInvoiceParent.payment_reminders_disabled,
        disable_customer_portal: this.selectedConsolidatedInvoiceParent.disable_customer_portal,
      };
      await firstValueFrom(this.paymentService.updateOrderPayment(payload))
    }

    let payload: _CRM_PAY_27 = {
      payment_id: this.payment?.payment_id!,
      payment_method_id: paymentMethodId,
      payment_name: this.payment.payment_name,
      invoice_send_type_id: this.payment.invoice_send_type_id,
      invoice_email: this.payment.invoice_email,
      invoice_reference_text: this.payment.invoice_reference_text,
      invoice_due_date_days: this.payment.invoice_due_date_days,
      comment: this.payment.comment!,
      payment_reminders_disabled: this.payment.payment_reminders_disabled,
      disable_customer_portal: this.payment.disable_customer_portal,
      work_order_ids: this.payment.work_order_ids,

      schedule_option_id: this.viewSettings.repeatingView ? this.payment.schedule_option?.option_id || null : null,
      schedule: this.payment.payment_schedule || null,
      auto_send: false,
      send_to_payment: !saveOnly && paymentMethodId !== 15,
    };

    if (this.selectedConsolidatedInvoiceParent) {
      payload.consolidated_invoice_payment_id = this.selectedConsolidatedInvoiceParent?.payment_id;
    }

    this.paymentService.updateOrderPayment(payload).subscribe((res) => {
      if (this.order) {
        this.orderService.fetchAndRefreshOrderPaymentSchedules(this.order.order_id);
        this.orderService.fetchAndRefreshOrderPayments(this.order.order_id);
      }
      this.saveLoading = false;
      this.sendLoading = false;
      this.closeSelf();
    }, error => {
      this.saveLoading = false;
      this.sendLoading = false;
    });
  }

  closeSelf() {
    if (this.viewSettings.modalView && this.activeModal) {
      this.activeModal.dismiss();
    }
  }

  async deletePayment() {
    let modalRef = this.modalService.open(VerifyPopupModal);
    let result = await modalRef.result;
    if (!result) {
      return;
    }
    this.deleteLoading = true;
    this.paymentService.deleteOrderPayment(this.payment.payment_id).subscribe(() => {
      if (this.order) {
        if (this.viewSettings.repeatingView) {
          this.orderService.fetchAndRefreshOrderPaymentSchedules(this.order.order_id);
        } else {
          this.orderService.fetchAndRefreshOrderPayments(this.order.order_id);
          this.orderService.fetchAndRefreshOrderLines(this.order.order_id, 'deletePayment');
        }
      }
      this.deleteLoading = false;
      this.closeSelf();
    });
  }

  creditInvoice() {
    let modalRef = this.modalService.open(VerifyPopupModal, {centered: true});
    modalRef.result.then((proceed) => {
      if (proceed) {
        this.loading = true;
        this.paymentService.creditInvoice(this.payment?.payment_id!).pipe(takeUntil(this.destroy$)).subscribe({
          next: (res) => {
            this.loading = false;
            this.payment = res;
            if (this.order) {
              this.orderService.fetchAndRefreshOrder(res.order_id!, 'creditInvoice');
              this.orderService.fetchAndRefreshOrderPayments(res.order_id!);
              this.orderService.refreshOrderLogs(this.order.order_id);
            }
            this.toastService.successToast('invoiceCredited');
          },
          error: (error) => {
            this.loading = false;
          }
        });
      }
    });
  }

  captureManually() {
    this.loading = true;
    this.orderService.capturePaymentManually(this.payment?.payment_id!).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.loading = false;
        this.payment = res;
        if (this.order) {
          this.orderService.fetchAndRefreshOrder(res.order_id!, 'captureManually');
          this.orderService.refreshOrderLogs(this.order.order_id);
        }
        this.toastService.successToast('updated')
      },
      error: (error) => {
        this.loading = false;
      }
    });
  }

  executeMarkOrderAsPaid(closeModal: boolean = false) {
    this.loading = true;
      this.paymentService.markPaymentAsPaid(this.payment?.payment_id!).pipe(takeUntil(this.destroy$)).subscribe({
        next: (res) => {
          this.loading = false;
          this.payment = res;
          this.orderService.fetchAndRefreshOrder(res.order_id!, 'manuallyMarkOrderAsPaid');
          if (this.order) {
            this.orderService.refreshOrderLogs(this.order.order_id);
          }
          this.toastService.successToast('updated')
          if (closeModal && this.activeModal) {
            this.activeModal.close();
          }
        },
        error: (error) => {
          this.loading = false;
        }
      });
  }

  manuallyMarkOrderAsPaid() {
    let modalRef = this.modalService.open(VerifyPopupModal, {});
    modalRef.result.then((proceed) => {
      if (proceed) {
        this.executeMarkOrderAsPaid();
      } else {
        return;
      }
    });
  }

  downloadPdf() {
    if (this.loading || !this.payment) return;
    this.loading = true;
    this.paymentService.getPaymentInvoicePdfFromTripletex(this.payment.payment_id).pipe(takeUntil(this.destroy$)).subscribe((response: Blob) => {
      this.loading = false;
      const fileUrl = URL.createObjectURL(response);
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = 'invoice_' + this.payment!.order_number + '.pdf';
      link.click();
      URL.revokeObjectURL(fileUrl);
      link.remove();
    }, error => {
      this.loading = false;
    });
  }

  sendReceipt() {
    if (this.loading) return;
    this.loading = true;
    this.paymentService.sendReceipt({payment_id: this.payment!.payment_id}).pipe(takeUntil(this.destroy$)).subscribe((res) => {
      this.loading = false;
      if (this.order) {
        this.orderService.refreshOrderLogs(this.order.order_id);
      }
      this.toastService.successToast('sent');
    }, error => {
      this.loading = false;
    });
  }

  openPaymentLogs() {
    let modalRef = this.modalService.open(PaymentLogsModalComponent, {size: 'xl'});
    modalRef.componentInstance.payment = this.payment;
  }

  openProjectAndDepartmentModal() {
    if ((this.payment?.accounting_transfer_at || this.payment.payment_method_id === 15) && !this.viewSettings.consolidatedInvoiceView) return;
    let modalRef = this.modalService.open(ProjectAndDepartmentModalComponent, {});
    modalRef.componentInstance.payment = this.payment;
    modalRef.result.then((res) => {
      if (res) {
        this.paymentService.getOrderPayment(this.payment?.payment_id!).pipe(takeUntil(this.destroy$)).subscribe((res) => {
        this.payment = res;
      });
      }
    });
  }

  onRefundClick() {
    if (!this.payment) return;
    if (![3, 10].includes(this.payment.payment_status_id) || this.payment.payment_method_id === 4 || (this.payment.total_amount_inc_vat === this.payment.refunded_amount)) {
      return;
    }

    const modalRef = this.modalService.open(RefundOrderModalComponent, { size: 'xl'});
    modalRef.componentInstance.payment = this.payment;
    modalRef.componentInstance.orderRefunded.pipe(takeUntil(this.destroy$)).subscribe((res: OrderResponse) => {
      this.paymentService.getOrderPayment(this.payment!.payment_id).pipe(takeUntil(this.destroy$)).subscribe((res) => {
        this.payment = res;
      });
      this.orderService.refreshOrder(res, 'onRefundClick');
      this.orderService.fetchAndRefreshOrderPayments(this.payment!.order_id!);
      this.orderService.fetchAndRefreshOrderRefundPayments(this.payment!.order_id!);
      modalRef.close();
    });
  }

  openConsolidatedInvoice() {
    this.paymentService.getOrderPayment(this.payment?.consolidation_container_id!).pipe(takeUntil(this.destroy$)).subscribe((res) => {
      let modalRef = this.modalService.open(PaymentDetailsV2Component, {size: 'lg'});
      modalRef.componentInstance.payment = res;
      modalRef.componentInstance.viewSettings = {modalView: true, paymentView: true, paymentStandaloneView: true};
      modalRef.componentInstance.paymentDeleted.pipe(takeUntil(this.destroy$)).subscribe((payment: OrderPaymentResponseCompact) => {
        if (!this.viewSettings.paymentStandaloneView) {
          this.orderService.fetchAndRefreshOrderPayments(this.payment!.order_id!);
        }
      });
    });
  }

  navigateToPaymentRecipient() {
    if (this.payment.payment_recipient) {
      this.router.navigateByUrl(`affiliates/details/${this.payment.payment_recipient.affiliate_id}`);
      this.activeModal?.close();
    }
  }

  scheduleOptionSelected(option: PaymentScheduleOptionResponse) {
    if (this.payment.schedule_option?.consolidated != option.consolidated) {
      this.consolidatedInvoiceParents = [];
    }

    if (!option.consolidated) {
        this.selectedConsolidatedInvoiceParent = null;
        this.consolidatedInvoiceUpdated = false;
    }

    if (option.consolidated && !option.custom_schedule && this.paymentRecipientAffiliateId != null) {
      this.consolidatedInvoiceLoading = true;
      // Get (or create) consolidated invoice parents for payment recipient
      this.paymentService.getConsolidatedInvoiceParents(this.paymentRecipientAffiliateId).subscribe((payments) => {
        let filteredPayments = payments.filter((payment) => payment.schedule_option?.option_id === option.option_id);
        console.log('Filtered consolidated invoice parents:', filteredPayments);
        if (filteredPayments.length > 0) {
          this.consolidatedInvoiceParents = filteredPayments
          this.consolidatedInvoiceParentSelected(filteredPayments[0]);
        } else {
          this.initEmptyConsolidatedInvoice();
        }
        this.consolidatedInvoiceLoading = false;
      }, error => {
        this.consolidatedInvoiceLoading = false;
      });
    }
  }

  consolidatedInvoiceParentSelected(parent: OrderPaymentResponseCompact | any) {
    this.selectedConsolidatedInvoiceParent = parent;
  }
}
