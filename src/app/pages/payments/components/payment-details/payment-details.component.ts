import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Optional,
  Output,
  ViewChild
} from '@angular/core';
import {NgbActiveModal, NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {BehaviorSubject, forkJoin, scheduled, Subject} from "rxjs";
import {TranslateService} from "@ngx-translate/core";
import {FormControl, Validators} from "@angular/forms";
import {InvoiceSendTypeResponse, OrderPaymentResponse, OrderPaymentResponseCompact, PaymentMethodResponse} from "../../../../@shared/models/payment.interfaces";
import {DetailsViewSettings, OrderLineRow, OrderResponse, WorkOrderResponse} from "../../../../@shared/models/order.interfaces";
import {AffiliateResponse} from "../../../../@shared/models/affiliate.interfaces";
import {currencyFormat, displayDate, formatDateDMY, formatFullDayAndDate, getPaymentStatusColor, upperCaseFirstLetter, UtilsService} from "../../../../@core/utils/utils.service";
import {OrderService} from "../../../../@shared/services/order.service";
import {CompanyService} from "../../../../@shared/services/company.service";
import {StorageService} from "../../../../@core/services/storage.service";
import {PaymentService} from "../../../../@shared/services/payment.service";
import {ToastService} from "../../../../@core/services/toast.service";
import {CustomerService} from "../../../../@shared/services/customer.service";
import {AffiliateService} from "../../../../@shared/services/affiliate.service";
import {_CRM_PAY_26, _CRM_PAY_27, _CRM_PAY_36, _CRM_PAY_7, ScheduleInput} from "../../../../@shared/models/input.interfaces";
import {SelectoriniComponent} from "../../../../@shared/components/selectorini/selectorini.component";
import {formatCurrency} from "@angular/common";
import {SpinnerComponent} from "../../../../@shared/components/spinner/spinner.component";
import {VerifyPopupModal} from "../../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {ScheduleSetupComponent} from "../../../../@shared/components/schedule-component/schedule-setup.component";
import {OrderLinesComponent} from "../../../orders/order-details-v2/order-lines/order-lines.component";
import {RefundOrderModalComponent} from "../../../orders/order-details-v2/components/action-button-group/_modals/refund-order-modal/refund-order-modal.component";
import {Router} from "@angular/router";
import {PaymentLogsModalComponent} from "../payment-logs-modal/payment-logs-modal.component";
import {takeUntil} from "rxjs/operators";
import {HelpIconComponent} from "../../../../@shared/components/help-icon/help-icon.component";
import {StandardImports} from "../../../../@shared/global_import";
import {ToggleSwitchComponent} from "../../../../@shared/components/toggle-switch/toggle-switch.component";
import {ProjectAndDepartmentModalComponent} from "./_modals/project-and-department-modal/project-and-department-modal";
import {PaymentDetailsV2Component} from "../payment-details-v2/payment-details-v2.component";

export interface TempScheduleWorkOrder {
  work_order_id: number;
  work_order_number: string;
  work_order_title: string;
  schedule_description: string;
  future_children: number;
}

@Component({
  selector: 'app-payment-details',
  templateUrl: './payment-details.component.html',
  styleUrls: ['./payment-details.component.css'],
  standalone: true,
  imports: [
    StandardImports,
    HelpIconComponent,
    SelectoriniComponent,
    ScheduleSetupComponent,
    SpinnerComponent,
    ToggleSwitchComponent,
    OrderLinesComponent
  ]
})
export class PaymentDetailsComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() payment?: OrderPaymentResponse | undefined;
  @Input() workOrder?: WorkOrderResponse;
  @Input() workOrderTemplate?: WorkOrderResponse;
  @Input() viewSettings: DetailsViewSettings;
  @Input() orderLinesSubject: BehaviorSubject<OrderLineRow[]> = new BehaviorSubject<OrderLineRow[]>([]);
  @Input() singlePaymentInRepeating: boolean = false;
  @Input() fetchPayment: boolean = false;
  @Input() paymentRecipient: AffiliateResponse;

  paymentMethods: PaymentMethodResponse[] = [];
  disabledPaymentMethods: PaymentMethodResponse[] = [];
  order: OrderResponse;
  selectedPaymentMethod: PaymentMethodResponse = {payment_method_id: -1, payment_method_name: ''};
  accountingEnabled: boolean = false;
  tripletexEnabled: boolean = false;
  fikenEnabled: boolean = false;
  addedOrderLines: OrderLineRow[] = [];

  saveAndSendLoading: boolean = false;
  saveWithoutSendingLoading: boolean = false;
  saveLoading: boolean = false;
  deleteLoading: boolean = false;
  loading: boolean = false;
  accountingRefreshLoading: boolean = false;

  repeatingPlaceholderToggleDisabledTooltip: string = '';

  isActionButtonsDropdownOpen: boolean;

  fixedRepeatingControl: FormControl<boolean | null> = new FormControl<boolean>(true);

  noOrderLinesSelected: boolean = false;
  canUseEHF: boolean = false;
  invoiceSendTypes: InvoiceSendTypeResponse[] = []
  disabledInvoiceSendTypes: InvoiceSendTypeResponse[] = [];
  selectedInvoiceSendType: InvoiceSendTypeResponse;
  removedOrderLineIds: number[] = [];
  paymentRemindersDisabled: boolean = false;
  customPaymentReminder1: number | null;
  customPaymentReminder2: number | null;
  availableButtons: boolean = false;

  daysOfMonth: {first: boolean, label: string}[] = []
  selectedDayOfMonth: {first: boolean, label: string};
  startDates: {formatted: string, date: Date, setToNull: boolean}[] = [];
  nextMonthFirstDay: Date;
  selectedStartDate: {formatted: string, date: Date, setToNull: boolean};
  repeatingWorkOrders: TempScheduleWorkOrder[] = [];
  selectedRepeatingWorkOrder: TempScheduleWorkOrder | null = null;
  fixedPaymentStopUnitTypes: {typeName: string, viewName: string}[];
  selectedFixedPaymentStopUnitType: {typeName: string, viewName: string};
  fixedPaymentStopQuantityControl: FormControl<number | null> = new FormControl<number>(1, [Validators.min(1)]);
  scheduleExecutionDates: Date[] = [];
  companyPaymentRemindersActive: boolean = true;

  scheduleInputSubject: BehaviorSubject<ScheduleInput> = new BehaviorSubject<ScheduleInput>({} as ScheduleInput);

  invoiceReferenceControl = new FormControl<string>('');
  dueDateControl = new FormControl<number>(14, [Validators.required, Validators.min(0), Validators.max(365)]);
  invoiceEmailControl = new FormControl<string>('', [Validators.email]);
  invoiceCommentControl = new FormControl<string>('');
  paymentNameControl = new FormControl<string>('', [Validators.required]);
  selectedConsolidatedInvoice: OrderPaymentResponseCompact | null = null;
  consolidatedInvoices: OrderPaymentResponseCompact[] = [];
  consolidatedInvoicesLoading: boolean = false;

  saveDontSendTranslationKey = ''
  saveAndSendTranslationKey = ''
  saveNotSendDisabledTooltip: string = '';
  saveAndSendDisabledTooltip: string = '';

  scheduleInput: ScheduleInput;

  accountingStatusColorMap: {[key: number]: string} = {
    1: '',
    2: 'text-success',
    3: 'text-warning',
    4: '',
    5: '',
  }
  accountingPaymentStatusColorMap: {[key: number]: string} = {
    0: 'text-success',
    1: '',
    4: '',
    5: '',
    6: 'text-success',
    7: 'text-warning',
  }

  projectsEnabled: boolean = false;
  departmentsEnabled: boolean = false;

  @Output() paymentSent = new EventEmitter<OrderPaymentResponse>();
  @Output() paymentUpdated = new EventEmitter<OrderPaymentResponse>();
  @Output() paymentDeleted = new EventEmitter<number>();
  @Output() loadingEmitter = new EventEmitter<boolean>();

  @ViewChild('dropDownButtonsDiv') dropDownButtonsDiv: ElementRef;

  private destroy$ = new Subject<void>();
  constructor(public utilsService: UtilsService,
              @Optional() public activeModal: NgbActiveModal,
              private orderService: OrderService,
              private companyService: CompanyService,
              private modalService: NgbModal,
              private storageService: StorageService,
              private translate: TranslateService,
              private paymentService: PaymentService,
              private toastService: ToastService,
              private customerService: CustomerService,
              private affiliateService: AffiliateService,
              private cdr: ChangeDetectorRef,
              private router: Router) {
    this.fixedPaymentStopUnitTypes = [
      {typeName: 'never', viewName: this.translate.instant('paymentDetails.fixedPaymentStopUnitType.never')},
      {typeName: 'months', viewName: this.translate.instant('paymentDetails.fixedPaymentStopUnitType.months')},
      {typeName: 'years', viewName: this.translate.instant('paymentDetails.fixedPaymentStopUnitType.years')},
    ]
    this.selectedFixedPaymentStopUnitType = this.fixedPaymentStopUnitTypes[0];
    this.fixedPaymentStopQuantityControl.disable();
  }

  ngOnInit() {
    this.storageService.companyPaymentRemindersActive$.pipe(takeUntil(this.destroy$)).subscribe((active) => {
      this.companyPaymentRemindersActive = active;
    });

    this.storageService.projectsEnabled$.subscribe((enabled) => {
      this.projectsEnabled = enabled;
    });
    this.storageService.departmentsEnabled$.subscribe((enabled) => {
      this.departmentsEnabled = enabled;
    });

    this.storageService.powerOfficeEnabled$.pipe(takeUntil(this.destroy$)).subscribe((powerOfficeEnabled) => {
      if (powerOfficeEnabled) {
        this.invoiceEmailControl.setValidators([
          Validators.required,
          Validators.email
        ])
        this.invoiceEmailControl.updateValueAndValidity();
      }
    });

    if (this.viewSettings.consolidatedInvoiceView) {
      this.viewSettings.paymentStandaloneView = true;
      this.selectedPaymentMethod = {payment_method_id: 10, payment_method_name: 'Consolidated invoice'};
      this.mapSendTypesAndRecipient();
      if (this.viewSettings.createView && this.paymentRecipient) {
        this.paymentNameControl.setValue(this.paymentRecipient.name);
      }
    }

    if (this.fetchPayment) {
      this.paymentService.getOrderPayment(this.payment?.payment_id!).pipe(takeUntil(this.destroy$)).subscribe((res) => {
        this.payment = res;
        this.initPaymentDetails();
      });
    } else {
      this.initPaymentDetails();
    }

    if (this.viewSettings.listView) {
      this.orderService.order$.pipe(takeUntil(this.destroy$)).subscribe((order) => {
        this.order = order;
      });
    }

    if (this.payment && this.payment.payment_method_id === 15 && !this.payment.is_parent_consolidated_invoice && !this.payment.is_consolidated_invoice_container) {
      this.paymentMethodSelected({payment_method_id: 15, payment_method_name: this.payment.payment_method_name});
    }

  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  initPaymentDetails() {
    this.storageService.accountingEnabled$.pipe(takeUntil(this.destroy$)).subscribe((accountingEnabled) => {
      this.accountingEnabled = accountingEnabled;
    });

    this.storageService.tripletexEnabled$.pipe(takeUntil(this.destroy$)).subscribe((tripletexEnabled) => {
      this.tripletexEnabled = tripletexEnabled;
    });

    this.storageService.fikenEnabled$.pipe(takeUntil(this.destroy$)).subscribe((fikenEnabled) => {
      this.fikenEnabled = fikenEnabled;
    });

    if (this.payment?.payment_status_id === 0 && (this.payment.payment_method_id === 10 || this.payment.is_consolidated_invoice_container)) {
      this.payment.payment_status_name = this.translate.instant('orderDetails.payments.paymentStatusInvoiceNotSent')
    }

    if (this.payment?.payment_method_id === 15 && this.payment.consolidation_container_id !== null) {
      this.paymentService.getOrderPayment(this.payment.consolidated_invoice_payment_id!).pipe(takeUntil(this.destroy$)).subscribe((res) => {
        this.selectedConsolidatedInvoice = res;
      });
    }

    this.paymentRemindersDisabled = this.payment?.payment_reminders_disabled || false;

    if (!this.payment || !this.payment.payment_schedule) {
      this.scheduleInputSubject.next({
          date: 1,
          every: 1,
          schedule_repeat_type_id: 2,
          schedule_repeat_type_name: this.translate.instant('orderSchedules.monthly'),
          weekdays: [0],
          nth_weekday: 1,
          instances_in_advance: 1,
          active: false,
          start_date: null,
          end_date: null,
          executionAt: new Date(),
          sourceName: 'paymentDetails',
        });
    } else {
      let schedule = this.payment.payment_schedule;
      this.scheduleInputSubject.next({
        date: schedule.date,
        every: schedule.every,
        schedule_repeat_type_id: schedule.schedule_repeat_type_id,
        schedule_repeat_type_name: schedule.schedule_repeat_type_name,
        weekdays: schedule.weekdays,
        nth_weekday: schedule.nth_weekday,
        instances_in_advance: 1,
        active: schedule.active,
        start_date: schedule.start_date,
        end_date: schedule.end_date,
        sourceName: 'paymentDetails',
      });
    }

    if (this.workOrderTemplate) {
      this.fixedRepeatingControl.setValue(false);
      let tempWorkOrder: TempScheduleWorkOrder = {
        work_order_id: this.workOrderTemplate.work_order_id,
        work_order_number: '#' + this.workOrderTemplate.work_order_number,
        work_order_title: this.workOrderTemplate.work_order_title,
        schedule_description: this.workOrderTemplate.schedule?.schedule_description || '',
        future_children: this.workOrderTemplate.schedule?.num_unstarted_work_orders || 0,
      }
      this.repeatingWorkOrderSelected(tempWorkOrder);
    }

    if (this.viewSettings.repeatingPlaceholderView) {
      this.viewSettings.repeatingView = true;
      this.viewSettings.createView = true;
      this.setRepeatingPlaceholderToggleDisabledTooltip();
    }

    this.fixedRepeatingControl.valueChanges.pipe(takeUntil(this.destroy$)).subscribe((value) => {
      this.setRepeatingPlaceholderToggleDisabledTooltip();
    });

    this.verifyFixedPaymentControlDisabled();

    if (this.payment && !this.viewSettings.consolidatedInvoiceView) {
      this.orderLinesSubject = new BehaviorSubject<OrderLineRow[]>([]);
      this.orderLinesSubject.next(this.payment.order_lines.sort((a, b) => {
      if (a.locked === b.locked) {return a.index - b.index;}
      return a.locked ? 1 : -1;}).map(orderLine => {
        return {
          ...orderLine,
          checked: true,
        }
      }));
    }

    this.orderLinesSubject.pipe(takeUntil(this.destroy$)).subscribe(orderLines => {
      if (this.viewSettings.createView) {
        this.noOrderLinesSelected = orderLines.length === 0;
      }
      this.setRepeatingPlaceholderToggleDisabledTooltip();
    })

    this.setTranslationKeys();

    // If invoice selected as payment method for existing payment init invoice values
    if(this.payment?.payment_method_id === 10 || this.payment?.is_consolidated_invoice_container || this.payment?.is_parent_consolidated_invoice) {
      this.selectedInvoiceSendType = {invoice_send_type_id: this.payment.invoice_send_type_id, invoice_send_type_name: this.payment.invoice_send_type_name};
      this.dueDateControl.setValue(this.payment.invoice_due_date_days);
      this.invoiceEmailControl.setValue(this.payment.invoice_email);
      this.invoiceReferenceControl.setValue(this.payment.invoice_reference_text);
      this.invoiceCommentControl.setValue(this.payment.comment);
      if (this.payment.invoice_sent_at) {
        this.invoiceReferenceControl.disable();
        this.dueDateControl.disable();
        this.invoiceEmailControl.disable();
        this.invoiceCommentControl.disable();
      }
    }

    if ((this.viewSettings.createView || this.viewSettings.repeatingPlaceholderView) && !this.viewSettings.consolidatedInvoiceView) {
      this.orderService.order$.pipe(takeUntil(this.destroy$)).subscribe(order => {
        this.order = order;
        this.initialisePaymentMethods('this.orderService.order$.pipe(');
        if (this.viewSettings.repeatingView) {
          this.initialiseSchedule();
          this.setRepeatingPlaceholderToggleDisabledTooltip();
          // if (order.work_order_schedule_templates.length === 1 && !order.work_order_schedule_templates[0].payment) {
          //   let workOrder = order.work_order_schedule_templates[0];
          //   this.selectedRepeatingWorkOrder = {
          //     work_order_id: workOrder.work_order_id,
          //     work_order_number: '#' + workOrder.work_order_number,
          //     work_order_title: workOrder.work_order_title,
          //     schedule_description: upperCaseFirstLetter(workOrder.schedule?.schedule_description)!,
          //     future_children: workOrder.schedule?.num_unstarted_work_orders || 0,
          //   }
          // } else {
          //   this.selectedRepeatingWorkOrder = null;
          // }
          // this.repeatingWorkOrders = order.work_order_schedule_templates.filter(workOrder => !workOrder.payment).map((workOrder: WorkOrderResponse) => {
          //   return {
          //     work_order_id: workOrder.work_order_id,
          //     work_order_number: '#' + workOrder.work_order_number,
          //     work_order_title: workOrder.work_order_title,
          //     schedule_description: upperCaseFirstLetter(workOrder.schedule?.schedule_description)!,
          //     future_children: workOrder.schedule?.num_unstarted_work_orders || 0,
          //   }
          // });
        }
      });
    } else if (!this.viewSettings.consolidatedInvoiceView && !this.viewSettings.createPaymentView) {
      this.initialisePaymentMethods('else if (!this.viewSettings.consolidatedInvoiceView && !this.viewSettings.createPaymentView)');
      if (this.viewSettings.repeatingView) {
        this.initialiseSchedule();
      }
    }

    if (this.viewSettings.consolidatedInvoiceView) {
      if (this.payment) {
        this.paymentNameControl.setValue(this.payment.payment_name);
      }
      this.initialiseSchedule();
    }

  }

  ngAfterViewInit() {
    if (!this.viewSettings.createView && !this.viewSettings.repeatingView && !this.viewSettings.consolidatedInvoiceView && !this.workOrder?.schedule_template) {
      this.availableButtons = this.dropDownButtonsDiv.nativeElement.children.length > 0;
    }

    this.cdr.detectChanges();
  }

  initialisePaymentMethods(source: string) {
    // Fetch payment methods
    this.paymentService.paymentMethods$.pipe(takeUntil(this.destroy$)).pipe(takeUntil(this.destroy$)).subscribe((paymentMethods) => {

      if (paymentMethods.length === 0) {
        this.paymentService.fetchAndUpdatePaymentMethods();
        return;
      }

      this.disabledPaymentMethods = [];

      const selectablePaymentMethodIds = [3, 4];
      if (this.viewSettings.repeatingView || (this.payment?.parent_payment_id && this.payment.subscription_active) || this.workOrder?.schedule_template) {
        selectablePaymentMethodIds.push(14);
      }
      if (this.accountingEnabled) {
        selectablePaymentMethodIds.push(10);
        selectablePaymentMethodIds.push(15);
      }

      this.paymentMethods = paymentMethods.filter(paymentMethod => selectablePaymentMethodIds.includes(paymentMethod.payment_method_id) && (paymentMethod.enabled == 1 || paymentMethod.payment_method_id == 15));
      if (!this.selectedPaymentMethod || this.selectedPaymentMethod.payment_method_id === -1) {
        this.paymentMethodSelected(paymentMethods.find(paymentMethod => paymentMethod.payment_method_id === (this.payment?.payment_method_id || 3)));
      }

      if (!this.viewSettings.createView && this.payment) {
        if (this.payment.provider_payment_method_name && this.payment.payment_status_id === 3) {
          this.selectedPaymentMethod.payment_method_name = this.payment.provider_payment_method_name;
        }
      }

      if (this.payment?.payment_method_id != 10 && this.payment?.accounting_transfer_at) {
        this.disabledPaymentMethods = this.disabledPaymentMethods.concat(this.paymentMethods.filter(paymentMethod => paymentMethod.payment_method_id === 10));
        this.paymentMethods.forEach(paymentMethod => {
          if (paymentMethod.payment_method_id === 10) {
            paymentMethod.disabledMessage = this.translate.instant('paymentDetails.selectPaymentMethod.invoiceDisabledBecausePushed');
          }
        })

      }

      if (this.payment?.payment_method_id == 10 && this.payment.accounting_transfer_at) {
        this.disabledPaymentMethods = this.disabledPaymentMethods.concat(this.paymentMethods.filter(paymentMethod => paymentMethod.payment_method_id === 15));
        this.paymentMethods.forEach(paymentMethod => {
          if (paymentMethod.payment_method_id === 15) {
            paymentMethod.disabledMessage = this.translate.instant('paymentDetails.selectPaymentMethod.consolidatedDisabledBecauseInvoice');
          }
        })
      }

      if (this.order && !this.order.payment_recipient) {
        this.disabledPaymentMethods = this.disabledPaymentMethods.concat(this.paymentMethods.filter(paymentMethod => [10, 15].includes(paymentMethod.payment_method_id)));
        this.paymentMethods.forEach(paymentMethod => {
          if ([10, 15].includes(paymentMethod.payment_method_id)) {
            paymentMethod.disabledMessage = this.translate.instant('paymentDetails.selectPaymentMethod.noCustomer');
          }
        })
      }

      if (this.viewSettings.createView && this.order.payment_recipient?.is_private === 0) {
        this.selectedPaymentMethod = paymentMethods.find(paymentMethod => paymentMethod.payment_method_id === 10)!;
        this.mapSendTypesAndRecipient();
      }

    });
  }

  initialiseSchedule() {
    // Day of month init
    this.daysOfMonth = [
      {first: true, label: this.translate.instant('paymentDetails.dayOfMonth.first')},
      {first: false, label: this.translate.instant('paymentDetails.dayOfMonth.last')},
    ]
    if (this.payment?.payment_schedule?.date === 28) {
      this.selectedDayOfMonth = this.daysOfMonth[1];
    } else {
      this.selectedDayOfMonth = this.daysOfMonth[0];
    }

    // Start dates init
    this.nextMonthFirstDay = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1, 12);
    this.startDates = [{ formatted: this.translate.instant('paymentDetails.firstPayment.disabled'), date: this.nextMonthFirstDay, setToNull: true }];

    if (this.payment?.payment_schedule) {
      this.selectedStartDate = {formatted: formatFullDayAndDate(this.payment.payment_schedule.start_date, true, false), date: this.payment.payment_schedule.start_date, setToNull: !this.payment.payment_schedule.start_date};
    } else {
      this.selectedStartDate = this.startDates[0];
    }

    if (this.payment) {
      this.fixedRepeatingControl.setValue(!!this.payment.payment_schedule);
    } else if (!this.workOrderTemplate && !this.selectedRepeatingWorkOrder) {
      this.fixedRepeatingControl.setValue(true);
    }
    if (this.viewSettings.createView) {
      this.scheduleInput = {
        active: (!!this.order?.payment_recipient || !!this.payment?.payment_recipient),
        date: 1,
        every: 1,
        schedule_repeat_type_id: 2,
        schedule_repeat_type_name: this.translate.instant('orderSchedules.monthly'),
        weekdays: [],
        nth_weekday: 1,
        instances_in_advance: 20,
        start_date: this.selectedStartDate.date,
        end_date: null,
      }

    } else {
      this.scheduleInput = {
        ...this.payment?.payment_schedule!,
        initDescription: this.payment?.payment_schedule?.schedule_description,
      } as ScheduleInput;

      // this.repeatingWorkOrderSelected({
      //   work_order_id: this.payment?.work_order_id!,
      //   work_order_number: '#' + this.payment?.work_order_number!,
      //   work_order_title: this.payment?.work_order_title!,
      //   schedule_description: ''
      // });

    }

    if (this.fixedRepeatingControl.value) {
      this.getStartDates();
      this.scheduleChanged();
    }
  }

  verifyFixedPaymentControlDisabled() {
    if (!!this.payment) {
      this.fixedRepeatingControl.disable()
    } else {
      this.fixedRepeatingControl.enable()
    }
  }

  setTranslationKeys() {
    if (this.viewSettings.repeatingView) {
      if (this.viewSettings.createView) {
        this.saveDontSendTranslationKey = 'common.create';
        this.saveAndSendTranslationKey = 'common.create';
      } else {
        this.saveDontSendTranslationKey = 'common.save';
        this.saveAndSendTranslationKey = 'common.save';
      }
    } else {
      if (this.viewSettings.createView) {
        this.saveDontSendTranslationKey = 'paymentDetails.saveWithoutSend.create';
        if (this.selectedPaymentMethod.payment_method_id === 15 && !this.payment?.is_consolidated_invoice_container) {
          this.saveAndSendTranslationKey = 'common.save';
        } else {
          this.saveAndSendTranslationKey = 'paymentDetails.saveAndSend.create';
        }
      } else {
        this.saveDontSendTranslationKey = 'paymentDetails.saveWithoutSend.update';
        this.saveAndSendTranslationKey = this.selectedPaymentMethod?.payment_method_id === 15 && !this.payment?.is_consolidated_invoice_container ? 'common.save' : 'paymentDetails.saveAndSend.update';
        if (this.payment?.payment_sent_at && this.selectedPaymentMethod?.payment_method_id !== 15) {
          this.saveAndSendTranslationKey += '.again';
        }
      }
    }
  }

  scheduleChanged() {
    if (!this.scheduleInput) return;
    this.calculateFixedPaymentStopDate()
    this.getExecutionDates();
  }

  getExecutionDates() {
    if (!this.scheduleInput) return;
    let payload: _CRM_PAY_36 = {
      ...this.scheduleInput,
    }
    this.paymentService.getSchedulePreview(payload).pipe(takeUntil(this.destroy$)).subscribe((res) => {
      this.scheduleExecutionDates = res.execution_dates;
    });
  }

  getStartDates() {
    if (!this.scheduleInput) return;
    let payload: _CRM_PAY_36 = {
      ...this.scheduleInput,
      instances_in_advance: 24,
    }
    this.paymentService.getSchedulePreview(payload).pipe(takeUntil(this.destroy$)).subscribe((res) => {
      this.startDates = [{formatted: this.translate.instant('paymentDetails.firstPayment.disabled'), date: res.execution_dates[0], setToNull: true}];
      res.execution_dates.forEach((date) => {
        this.startDates.push({formatted: formatFullDayAndDate(date, true, false), date: date, setToNull: false});
      });
      this.selectedStartDate = this.startDates[0];
    });
  }

  setRepeatingPlaceholderToggleDisabledTooltip() {
    if (!this.order?.payment_recipient) {
      this.repeatingPlaceholderToggleDisabledTooltip = "paymentDetails.repeatingPlaceholderToggleDisabledTooltip.noCustomer";
    }
    else if (this.noOrderLinesSelected) {
      this.repeatingPlaceholderToggleDisabledTooltip = "paymentDetails.repeatingPlaceholderToggleDisabledTooltip.noOrderLines";
    } else if (!this.fixedRepeatingControl.value && !this.selectedRepeatingWorkOrder) {
      this.repeatingPlaceholderToggleDisabledTooltip = "paymentDetails.repeatingPlaceholderToggleDisabledTooltip.noSchedule";
    }
  }

  paymentMethodSelected(paymentMethod: PaymentMethodResponse | any) {
    this.selectedPaymentMethod = paymentMethod;
    this.setTranslationKeys();
    if (this.selectedPaymentMethod.payment_method_id === 10 || this.payment?.is_consolidated_invoice_container) {
      this.mapSendTypesAndRecipient();
    }

    if (this.selectedPaymentMethod.payment_method_id === 15 && !this.payment?.is_consolidated_invoice_container) {
      this.consolidatedInvoicesLoading = true;
      this.paymentService.getConsolidatedInvoiceParents(this.payment?.payment_recipient.affiliate_id || this.order?.payment_recipient?.affiliate_id || this.paymentRecipient?.affiliate_id).subscribe((res) => {
        this.consolidatedInvoices = res.map((invoice) => {
          return {
            ...invoice,
            payment_number: '#' + invoice.payment_number,
          }
        });
        this.consolidatedInvoicesLoading = false;
        if (this.consolidatedInvoices.length === 1) {
          this.selectedConsolidatedInvoice = this.consolidatedInvoices[0];
        }
      });
    }
  }

  mapSendTypesAndRecipient() {
    if (this.paymentRecipient && this.invoiceSendTypes.length > 0 && !this.viewSettings.consolidatedInvoiceView) {
      if (!this.selectedInvoiceSendType) {
        this.selectedInvoiceSendType = this.invoiceSendTypes.find((type) => type.invoice_send_type_id === this.paymentRecipient.invoice_send_type_id)!;
      }
      if (this.paymentRecipient?.organisation_number) {
        this.setEhfAccess(this.paymentRecipient)
      }
    } else {
      const requests = []
      requests.push(this.affiliateService.getAffiliateById(this.payment?.payment_recipient?.affiliate_id || this.order?.payment_recipient?.affiliate_id || this.paymentRecipient.affiliate_id));
      requests.push(this.paymentService.getInvoiceSendTypes());
      forkJoin(requests).pipe(takeUntil(this.destroy$)).subscribe(([first, second]) => {
        if ('affiliate_id' in first) {
          this.paymentRecipient = first;
        } else {
          this.paymentRecipient = second as AffiliateResponse;
        }

        if (second instanceof Array) {
          this.invoiceSendTypes = second;
        } else {
          this.invoiceSendTypes = first as InvoiceSendTypeResponse[];
        }

        // Map values
        if (!this.payment?.invoice_due_date_days) {
          this.dueDateControl.setValue(this.paymentRecipient.invoice_due_date_days);
        }
        if (!this.payment?.invoice_email) {
          this.invoiceEmailControl.setValue(this.paymentRecipient.invoice_email);
        }
        if (!this.selectedInvoiceSendType) {
          this.selectedInvoiceSendType = this.invoiceSendTypes.find((type) => type.invoice_send_type_id === this.paymentRecipient.invoice_send_type_id)!;
        }
        if (this.paymentRecipient?.organisation_number) {
          this.setEhfAccess(this.paymentRecipient)
        } else {
            this.invoiceSendTypes = this.invoiceSendTypes.filter((type) => type.invoice_send_type_id !== 1);
        }
      });
    }
  }

  invoiceSendTypeSelected(type: any) {
    this.selectedInvoiceSendType = type;
  }

  setEhfAccess(paymentRecipient: AffiliateResponse) {
    let organisationNumber = paymentRecipient.organisation_number || 'NO_NUMBER_AVAILABLE';

    if (this.paymentRecipient.invoice_send_type_id === 1) {
      this.canUseEHF = true;
      return;
    }

    this.customerService.checkPeppolEHF(organisationNumber).pipe(takeUntil(this.destroy$)).subscribe((res) => {
      this.disabledInvoiceSendTypes = [];
      this.canUseEHF = res.can_receive_ehf === 1;
      this.invoiceSendTypes.forEach((type) => {
        if (type.invoice_send_type_id !== 0 && !this.canUseEHF) {
          this.disabledInvoiceSendTypes.push(type);
        }
      });
    });
  }

  repeatingWorkOrderSelected(workOrder: TempScheduleWorkOrder | any) {
    this.selectedRepeatingWorkOrder = workOrder;
    this.setRepeatingPlaceholderToggleDisabledTooltip();
  }

  dayOfMonthSelected(day: any, source?: string) {
    this.selectedDayOfMonth = day;
    this.scheduleInput.date = day.first ? 1 : 28;
    this.scheduleChanged();
    this.getStartDates();
    this.selectedStartDate
  }

  startDateSelected(date: any) {
    this.selectedStartDate = date;
    this.scheduleInput.start_date = date.date;
    this.scheduleChanged()
  }

  setLoading(loading: boolean) {
    this.loading = loading;
    this.loadingEmitter.emit(loading);
  }

  checkPaymentStatus() {
    this.setLoading(true);
    this.paymentService.checkAccountingPaymentStatus(this.payment!.payment_id).pipe(takeUntil(this.destroy$)).subscribe((res) => {
      this.payment = res;
      this.initPaymentDetails();
      if (res.payment_status_id === 3) {
        this.orderService.fetchAndRefreshOrder(res.order_id!, 'checkPaymentStatus', false);
        this.orderService.refreshOrderLogs(res.order_id!)
      }
      this.setLoading(false);
    }, error => {
      this.setLoading(false);
    });
  }

  fixedPaymentStopUnitTypeSelected(unitType: {typeName: string, viewName: string} | any) {
    this.selectedFixedPaymentStopUnitType = unitType;
    if (this.selectedFixedPaymentStopUnitType.typeName === 'never') {
      this.fixedPaymentStopQuantityControl.disable();
    } else {
      this.fixedPaymentStopQuantityControl.enable();
    }
    this.scheduleChanged();
  }

  calculateFixedPaymentStopDate() {
    if (!this.scheduleInput) return;
    if (this.selectedFixedPaymentStopUnitType.typeName === 'never') {
      this.scheduleInput.end_date = null;
    } else if (this.fixedPaymentStopQuantityControl.value) {
      const startDate = this.selectedStartDate.date;

      const endDate = new Date(startDate);

      switch (this.selectedFixedPaymentStopUnitType.typeName) {
        case 'weeks':
          endDate.setDate(endDate.getDate() + parseInt(this.fixedPaymentStopQuantityControl.value.toString()) * 7);
          break;
        case 'months':
          endDate.setMonth(endDate.getMonth() + parseInt(this.fixedPaymentStopQuantityControl.value.toString()));
          break;
        case 'years':
          endDate.setFullYear(endDate.getFullYear() + parseInt(this.fixedPaymentStopQuantityControl.value.toString()));
          break;
        default:
          this.scheduleInput.end_date = null;
          return;
      }

      this.scheduleInput.end_date = endDate;
    } else {
      this.scheduleInput.end_date = null;
    }
  }

  consolidatedInvoiceSelected(invoice: OrderPaymentResponseCompact | any) {
    this.selectedConsolidatedInvoice = invoice;
  }

  // Save and send to payment
  saveAndSendToPayment() {
    if ((this.selectedPaymentMethod.payment_method_id === 10 || this.payment?.is_consolidated_invoice_container) && this.invoiceEmailControl.invalid) {
      this.invoiceEmailControl.markAsTouched();
      return
    }
    this.saveAndSendLoading = true;
    this.saveLoading = true;
    if (this.viewSettings.createView) {
      let payload: _CRM_PAY_26 = {
        payment_method_id: this.selectedPaymentMethod.payment_method_id,
        order_line_ids: this.orderLinesSubject.value.map(orderLine => orderLine.order_line_id),
        payment_reminders_disabled: this.paymentRemindersDisabled,
        order_id: this.order.order_id,
        send_to_payment: true,
        work_order_ids: this.workOrder?.work_order_id? [this.workOrder?.work_order_id] : [],
      }

      if (this.selectedPaymentMethod.payment_method_id === 10 || this.payment?.is_consolidated_invoice_container) {
        payload.invoice_reference_text = this.invoiceReferenceControl.value!;
        payload.invoice_due_date_days = this.dueDateControl.value!;
        payload.invoice_email = this.invoiceEmailControl.value!;
        payload.invoice_send_type_id = this.selectedInvoiceSendType.invoice_send_type_id;
        payload.comment = this.invoiceCommentControl.value!;
      }

      if (this.selectedPaymentMethod.payment_method_id === 15 && !this.payment?.is_consolidated_invoice_container) {
        payload.consolidated_invoice_payment_id = this.selectedConsolidatedInvoice!.payment_id;
      }

      this.paymentService.createOrderPayment(payload).pipe(takeUntil(this.destroy$)).subscribe((res) => {
        this.orderService.fetchAndRefreshOrder(this.order.order_id, 'createPayment', true);
        this.orderService.refreshOrderLogs(this.order.order_id);
        this.paymentUpdated.emit(res);
        this.paymentSent.emit(res);
        this.toastService.successToast('created')
        this.saveAndSendLoading = false;
        this.activeModal.close();
      }, error => {
        this.saveAndSendLoading = false;
      });
    } else {
      // Update existing payment
      let updatePayload: _CRM_PAY_27 = {
        payment_id: this.payment?.payment_id!,
        payment_method_id: this.selectedPaymentMethod.payment_method_id,
        removed_order_line_ids: this.removedOrderLineIds,
        payment_reminders_disabled: this.paymentRemindersDisabled,
        custom_payment_reminder_1_hours_delta: this.customPaymentReminder1,
        custom_payment_reminder_2_hours_delta: this.customPaymentReminder2
      }

      if (this.selectedPaymentMethod.payment_method_id === 10 || this.payment?.is_consolidated_invoice_container) {
        updatePayload.invoice_reference_text = this.invoiceReferenceControl.value!;
        updatePayload.invoice_due_date_days = this.dueDateControl.value!;
        updatePayload.invoice_email = this.invoiceEmailControl.value!;
        updatePayload.invoice_send_type_id = this.selectedInvoiceSendType.invoice_send_type_id;
        updatePayload.comment = this.invoiceCommentControl.value!;
      }

      if (this.selectedPaymentMethod.payment_method_id === 15 && !this.payment?.is_consolidated_invoice_container) {
        updatePayload.consolidated_invoice_payment_id = this.selectedConsolidatedInvoice!.payment_id;
      }

      this.paymentService.updateOrderPayment(updatePayload).pipe(takeUntil(this.destroy$)).subscribe((res) => {
        this.payment = res;
        this.paymentUpdated.emit(res);
        let sendPayload: _CRM_PAY_7 = {
          payment_id: this.payment.payment_id,
        }
        if (!this.payment.template) {
          this.paymentService.sendToPayment(sendPayload).pipe(takeUntil(this.destroy$)).subscribe(() => {
            this.toastService.successToast('updated')
            this.paymentService.getOrderPayment(this.payment?.payment_id!).pipe(takeUntil(this.destroy$)).subscribe((res) => {
              this.payment = res;
              this.paymentUpdated.emit(res);
              this.paymentSent.emit(res);
            });
            if (!this.viewSettings.paymentStandaloneView) {
              this.orderService.fetchAndRefreshOrder((this.payment?.order_id || this.order.order_id), 'updatePayment', true);
              this.orderService.refreshOrderLogs(this.payment?.order_id || this.order.order_id);
            }
            this.saveAndSendLoading = false;
            this.saveLoading = false;
            this.activeModal.close();
          }, error => {
            this.saveAndSendLoading = false;
            this.saveLoading = false;
          });
        } else {
          if (!this.viewSettings.paymentStandaloneView) {
            this.orderService.fetchAndRefreshOrder((this.payment?.order_id || this.order.order_id), 'updatePayment', true);
            this.orderService.refreshOrderLogs(this.payment?.order_id || this.order.order_id);
          }
          this.saveAndSendLoading = false;
          this.saveLoading = false;
          this.activeModal.close();
        }

      }, error => {
        this.saveAndSendLoading = false;
          this.saveLoading = false;
      });
    }
  }

  // Save without sending to payment
  async saveWithoutSendingToPayment() {
    this.saveWithoutSendingLoading = true;
    this.saveLoading = true;
    if (this.viewSettings.createView) {
      let postPayload: _CRM_PAY_26 = {
        payment_method_id: this.selectedPaymentMethod.payment_method_id,
        order_line_ids: this.orderLinesSubject.value.map(orderLine => orderLine.order_line_id),
        payment_reminders_disabled: this.paymentRemindersDisabled,
        order_id: this.viewSettings.consolidatedInvoiceView ? null : this.order.order_id,
        send_to_payment: false,
        template: this.viewSettings.repeatingView,
        work_order_ids: this.workOrder?.work_order_id ? [this.workOrder?.work_order_id] : [],
      }

      if (this.selectedPaymentMethod.payment_method_id === 10 || this.payment?.is_consolidated_invoice_container || this.viewSettings.consolidatedInvoiceView) {
        postPayload.invoice_reference_text = this.invoiceReferenceControl.value!;
        postPayload.invoice_due_date_days = this.dueDateControl.value!;
        postPayload.invoice_email = this.invoiceEmailControl.value!;
        postPayload.invoice_send_type_id = this.selectedInvoiceSendType.invoice_send_type_id;
        postPayload.comment = this.invoiceCommentControl.value!;
      }

      if (this.viewSettings.consolidatedInvoiceView) {
        postPayload.is_parent_consolidated_invoice = true;
        postPayload.payment_recipient_id = this.paymentRecipient.affiliate_id;
        postPayload.payment_name = this.paymentNameControl.value!;
      }

      if (this.selectedPaymentMethod.payment_method_id === 15 && !this.payment?.is_consolidated_invoice_container) {
        postPayload.consolidated_invoice_payment_id = this.selectedConsolidatedInvoice!.payment_id;
      }

      if (this.viewSettings.repeatingView || (this.viewSettings.consolidatedInvoiceView && this.fixedRepeatingControl.value)) {
        // Repeating payment per job
        if (!this.fixedRepeatingControl.value) {
          postPayload.work_order_ids = this.selectedRepeatingWorkOrder?.work_order_id ? [this.selectedRepeatingWorkOrder?.work_order_id] : [];

          // If order is not yet approved, update upcoming work orders
          if (this.order && this.order.order_status_id < 2) {
            postPayload.update_upcoming_work_orders = true;
          } else {
            if (this.selectedRepeatingWorkOrder?.future_children! > 0) {
              let modalRef = this.modalService.open(VerifyPopupModal, {backdrop: 'static'});
              modalRef.componentInstance.showBody = true;
              modalRef.componentInstance.titleTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.title';
              modalRef.componentInstance.bodyBoldTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyBoldTranslationKey.repeatingPaymentCreation';
              modalRef.componentInstance.bodyMutedTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyMutedTranslationKey';
              modalRef.componentInstance.bodyRegularTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.repeatingPaymentCreation';
              try {
                postPayload.update_upcoming_work_orders = await modalRef.result;
              } catch (e) {}
            }
          }
        }

        // Fixed repeating payment
        else {
          postPayload.schedule = this.scheduleInputSubject.value;
          // if (this.selectedStartDate.setToNull) {
          //   postPayload.schedule.start_date = null;
          // }
          if (postPayload.schedule.start_date) {
            postPayload.schedule.start_date.setHours(12);
          }
          if (postPayload.schedule.end_date) {
            postPayload.schedule.end_date.setHours(12);
          }
        }
      }

      this.paymentService.createOrderPayment(postPayload).pipe(takeUntil(this.destroy$)).subscribe((res) => {
        this.toastService.successToast('created')

        if ((this.order && this.order.order_status_id > 1) && (this.viewSettings.repeatingPlaceholderView || (postPayload.schedule && this.viewSettings.repeatingView && !this.viewSettings.repeatingPlaceholderView && this.viewSettings.createView))) {
          let payload: _CRM_PAY_27 = {
            payment_id: res.payment_id,
            schedule: {
              active: true
            }
          }
          this.paymentService.updateOrderPayment(payload, false).pipe(takeUntil(this.destroy$)).subscribe((res) => {
            this.paymentService.manuallyInitiatePaymentSchedule(res.payment_schedule?.payment_schedule_id!, false).pipe(takeUntil(this.destroy$)).subscribe(() => {
              this.paymentUpdated.emit(res);
              this.orderService.fetchAndRefreshOrder(this.order.order_id, 'createPaymentManuallyInitiate');
              this.orderService.fetchAndRefreshOrderPaymentSchedules(this.order.order_id);
              this.orderService.fetchAndRefreshOrderPayments(this.order.order_id);
              this.orderService.refreshOrderLogs(this.order.order_id);
              this.saveWithoutSendingLoading = false;
              this.saveLoading = false;
              if (this.activeModal) {
                this.activeModal.close(res);
              }
            });
          }, error => {
            this.orderService.fetchAndRefreshOrder(this.order.order_id, 'createPaymentSetActiveError');
            this.orderService.fetchAndRefreshOrderPaymentSchedules(this.order.order_id);
            this.orderService.fetchAndRefreshOrderPayments(this.order.order_id);
            this.orderService.refreshOrderLogs(this.order.order_id);
            this.saveWithoutSendingLoading = false;
            this.saveLoading = false;
            if (this.activeModal) {
              this.activeModal.close(res);
            }
          });
        } else {
          if (this.order) {
            this.orderService.fetchAndRefreshOrder(this.order.order_id, 'createPayment');
          }
          this.paymentUpdated.emit(res);
          this.saveWithoutSendingLoading = false;
          this.saveLoading = false;
          if (this.activeModal) {
            this.activeModal.close(res);
          }
        }
      }, error => {
        this.saveWithoutSendingLoading = false;
        this.saveLoading = false;
      });
    } else {
      // Update existing payment
      let updatePayload: _CRM_PAY_27 = {
        payment_id: this.payment!.payment_id,
        payment_method_id: this.selectedPaymentMethod.payment_method_id,
        removed_order_line_ids: this.removedOrderLineIds,
        payment_reminders_disabled: this.paymentRemindersDisabled,
        custom_payment_reminder_1_hours_delta: this.customPaymentReminder1,
        custom_payment_reminder_2_hours_delta: this.customPaymentReminder2,
      }

      if ((this.viewSettings.repeatingView || this.viewSettings.consolidatedInvoiceView) && this.fixedRepeatingControl.value) {
        updatePayload.schedule = this.scheduleInputSubject.value;
        if (updatePayload.schedule.start_date) {
          updatePayload.schedule.start_date.setHours(12);
        }
        if (updatePayload.schedule.end_date) {
          updatePayload.schedule.end_date.setHours(12);
        }
      }

      if (this.selectedPaymentMethod.payment_method_id === 10 || this.payment?.is_consolidated_invoice_container || this.viewSettings.consolidatedInvoiceView) {
        updatePayload.invoice_reference_text = this.invoiceReferenceControl.value!;
        updatePayload.invoice_due_date_days = this.dueDateControl.value!;
        updatePayload.invoice_email = this.invoiceEmailControl.value!;
        updatePayload.invoice_send_type_id = this.selectedInvoiceSendType.invoice_send_type_id;
        updatePayload.comment = this.invoiceCommentControl.value!;
      }

      if (this.viewSettings.consolidatedInvoiceView) {
        updatePayload.payment_name = this.paymentNameControl.value!;
      }

      if (this.selectedPaymentMethod.payment_method_id === 15 && !this.payment?.is_consolidated_invoice_container) {
        updatePayload.consolidated_invoice_payment_id = this.selectedConsolidatedInvoice!.payment_id;
      }

      this.paymentService.updateOrderPayment(updatePayload).pipe(takeUntil(this.destroy$)).subscribe((res) => {
        this.payment = res;
        this.paymentUpdated.emit(res);
        this.loading = false;
        this.saveWithoutSendingLoading = false;
        this.saveLoading = false;
        if (!this.viewSettings.paymentStandaloneView) {
          this.orderService.fetchAndRefreshOrderPayments(this.payment.order_id!);
          this.orderService.fetchAndRefreshOrderPaymentSchedules(this.payment.order_id!);
        }
        this.activeModal.close();
      }, error => {
        this.loading = false;
        this.saveLoading = false;
        this.saveWithoutSendingLoading = false;
      });
    }

  }

  downloadPdf() {
    if (this.loading || !this.payment) return;
    this.setLoading(true);
    this.paymentService.getPaymentInvoicePdfFromTripletex(this.payment.payment_id).pipe(takeUntil(this.destroy$)).subscribe((response: Blob) => {
      this.setLoading(false);
      const fileUrl = URL.createObjectURL(response);
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = 'invoice_' + this.payment!.order_number + '.pdf';
      link.click();
      URL.revokeObjectURL(fileUrl);
      link.remove();
    }, error => {
      this.setLoading(false);
    });
  }

  retryAccountingSync() {
    if (this.loading) return;
    this.setLoading(true);
    this.accountingRefreshLoading = true;
    this.paymentService.retryAccountingSynchronisation(this.payment!.payment_id).pipe(takeUntil(this.destroy$)).subscribe((res) => {
      this.setLoading(false);
      this.accountingRefreshLoading = false;
      this.payment = res;
      this.paymentUpdated.emit(res);
      this.toastService.successToast('updated')
      if (!this.viewSettings.paymentStandaloneView) {
        this.orderService.fetchAndRefreshOrder(res.order_id!, 'retryAccountingSync');
        this.orderService.refreshOrderLogs(this.payment.order_id!)
        this.orderService.fetchAndRefreshOrderPayments(this.payment.order_id!);
      }
      this.initialisePaymentMethods('retryAccountingSync()');
    }, (error) => {
      this.setLoading(false);
      this.accountingRefreshLoading = false;
    })
  }

  togglePaymentReminder() {
    this.paymentRemindersDisabled = !this.paymentRemindersDisabled;
    // this.setLoading(true);
    // this.paymentService.togglePaymentReminder(this.payment!.payment_id, !this.payment!.payment_reminders_disabled).subscribe((res) => {
    //   this.payment = res;
    //   this.setLoading(false);
    // }, error => {
    //   this.setLoading(false);
    // });
  }

  creditInvoice() {
    let modalRef = this.modalService.open(VerifyPopupModal, {});
    modalRef.result.then((proceed) => {
      if (proceed) {
        this.setLoading(true);
        this.paymentService.creditInvoice(this.payment?.payment_id!).pipe(takeUntil(this.destroy$)).subscribe({
          next: (res) => {
            this.setLoading(false);
            this.payment = res;
            this.paymentUpdated.emit(res);
            this.initialisePaymentMethods('creditInvoice()');
            if (this.order) {
              this.orderService.fetchAndRefreshOrder(res.order_id!, 'creditInvoice');
              this.orderService.fetchAndRefreshOrderPayments(res.order_id!);
              this.orderService.refreshOrderLogs(this.order.order_id);
            }
            this.toastService.successToast('invoiceCredited');
          },
          error: (error) => {
            this.setLoading(false);
          }
        });
      }
    });
  }

  manuallyMarkOrderAsPaid() {
    let modalRef = this.modalService.open(VerifyPopupModal, {});
    modalRef.result.then(
      (proceed) => {
        if (proceed) {
          this.setLoading(true);
          this.paymentService.markPaymentAsPaid(this.payment?.payment_id!).pipe(takeUntil(this.destroy$)).subscribe({
            next: (res) => {
              this.setLoading(false);
              this.payment = res;
              this.paymentUpdated.emit(res);
              this.orderService.fetchAndRefreshOrder(res.order_id!, 'manuallyMarkOrderAsPaid');
              if (this.order) {
                this.orderService.refreshOrderLogs(this.order.order_id);
              }
              this.toastService.successToast('updated')
            },
            error: (error) => {
              this.setLoading(false);
            }
          });
        } else {
          return;
        }
      });
  }

  captureManually() {
    this.setLoading(true);
    this.orderService.capturePaymentManually(this.payment?.payment_id!).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.setLoading(false);
        this.payment = res;
        this.paymentUpdated.emit(res);
        if (this.order) {
          this.orderService.fetchAndRefreshOrder(res.order_id!, 'captureManually');
          this.orderService.refreshOrderLogs(this.order.order_id);
        }
        this.toastService.successToast('updated')
      },
      error: (error) => {
        this.setLoading(false);
      }
    });
  }

  deletePayment() {
    if (this.payment?.accounting_transfer_at || (this.payment?.payment_status_id === 3 && this.payment?.payment_method_id !== 4)) {
      return;
    }

    let modalRef = this.modalService.open(VerifyPopupModal, {});
    modalRef.result.then((proceed) => {
      if (proceed) {
        this.callDeletePayment();
      }
    });
  }

  callDeletePayment() {
    this.deleteLoading = true;
    this.paymentService.deleteOrderPayment(this.payment!.payment_id).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        if (!this.viewSettings.paymentStandaloneView) {
          this.orderService.fetchAndRefreshOrder(this.payment!.order_id!, 'deletePayment');
          this.orderService.fetchAndRefreshOrderPayments(this.payment!.order_id!);
          if (this.viewSettings.repeatingView) {
            this.orderService.fetchAndRefreshOrderPaymentSchedules(this.payment!.order_id!);
          }
        }
        this.deleteLoading = false;
        this.toastService.successToast('deleted');
        this.paymentDeleted.emit(this.payment!.payment_id);
        this.closePaymentModal();
      },
      error: (error) => {
        this.deleteLoading = false;
      }
    });
  }

  onRefundClick() {
    if (!this.payment) return;
    if (![3, 10].includes(this.payment.payment_status_id) || this.payment.payment_method_id === 4 || (this.payment.total_amount_inc_vat === this.payment.refunded_amount)) {
      return;
    }

    const modalRef = this.modalService.open(RefundOrderModalComponent, { size: 'xl'});
    modalRef.componentInstance.payment = this.payment;
    modalRef.componentInstance.orderRefunded.pipe(takeUntil(this.destroy$)).subscribe((res: OrderResponse) => {
      this.paymentService.getOrderPayment(this.payment!.payment_id).pipe(takeUntil(this.destroy$)).subscribe((res) => {
        this.payment = res;
        this.paymentUpdated.emit(res);
      });
      this.orderService.refreshOrder(res, 'onRefundClick');
      this.orderService.fetchAndRefreshOrderPayments(this.payment!.order_id!);
      this.orderService.fetchAndRefreshOrderRefundPayments(this.payment!.order_id!);
      modalRef.close();
    });
  }

  toggleDropdown(): void {
    this.isActionButtonsDropdownOpen = !this.isActionButtonsDropdownOpen;
  }

  sendReceipt() {
    if (this.loading) return;
    this.setLoading(true);
    this.paymentService.sendReceipt({payment_id: this.payment!.payment_id}).pipe(takeUntil(this.destroy$)).subscribe((res) => {
      this.setLoading(false);
      if (this.order) {
        this.orderService.refreshOrderLogs(this.order.order_id);
      }
      this.toastService.successToast('sent');
    }, error => {
      this.setLoading(false);
    });
  }

  openPaymentLogs() {
    let modalRef = this.modalService.open(PaymentLogsModalComponent, {size: 'xl'});
    modalRef.componentInstance.payment = this.payment;
  }

  openProjectAndDepartmentModal() {
    if ((this.payment?.accounting_transfer_at || this.selectedPaymentMethod.payment_method_id === 15) && !this.viewSettings.consolidatedInvoiceView) return;
    let modalRef = this.modalService.open(ProjectAndDepartmentModalComponent, {});
    modalRef.componentInstance.payment = this.payment;
    modalRef.result.then((res) => {
      if (res) {
        this.paymentService.getOrderPayment(this.payment?.payment_id!).pipe(takeUntil(this.destroy$)).subscribe((res) => {
        this.payment = res;
        this.initPaymentDetails();
      });
      }
    });
  }

  closePaymentModal(result: boolean = true) {
    if (this.singlePaymentInRepeating) {
      const requests = this.addedOrderLines.map(orderLine => {
        return this.orderService.deleteOrderLineAsCompany(orderLine.order_line_id)
      });
      if (requests.length > 0) {
        forkJoin(requests).subscribe(() => {
          this.activeModal.close(result);
        });
      } else {
        this.activeModal.close(result);
      }
    }
    if (this.viewSettings.modalView) {
      this.activeModal.close(result);
    }
  }

  createConsolidatedInvoice() {
    let modal = this.modalService.open(PaymentDetailsV2Component, { size: 'lg' });
    let viewSettings: DetailsViewSettings = {
      modalView: true,
      createView: true,
      consolidatedInvoiceView: true,
    }
    modal.componentInstance.viewSettings = viewSettings;
    modal.componentInstance.paymentRecipient = this.order?.payment_recipient || this.paymentRecipient;
    modal.componentInstance.paymentUpdated.pipe(takeUntil(this.destroy$)).subscribe((payment: OrderPaymentResponseCompact) => {
      payment = {
        ...payment,
        payment_number: '#' + payment.payment_number,
      }
      this.consolidatedInvoiceSelected(payment);
    });
  }

  isSaveNotSendDisabled(): boolean {
    this.saveNotSendDisabledTooltip = '';
    if (this.noOrderLinesSelected) {
      this.saveNotSendDisabledTooltip = 'paymentDetails.noOrderLinesSelected';
      return true;
    }
    else if (this.saveLoading) {
      return true;
    }
    else if (this.viewSettings.repeatingView && !this.fixedRepeatingControl.value && !this.selectedRepeatingWorkOrder) {
      this.saveAndSendDisabledTooltip = 'paymentDetails.noWorkOrderSelected';
      return true;
    }
    else if (!this.selectedPaymentMethod) {
      this.saveAndSendDisabledTooltip = 'paymentDetails.noPaymentMethodSelected';
      return true;
    }
    else if (!this.selectedInvoiceSendType && (this.selectedPaymentMethod?.payment_method_id === 10 || this.viewSettings.consolidatedInvoiceView || !!this.payment?.is_consolidated_invoice_container)) {
      this.saveAndSendDisabledTooltip = 'paymentDetails.noInvoiceSendTypeSelected';
      return true;
    }
    else if ((this.selectedPaymentMethod?.payment_method_id === 10 || this.payment?.is_consolidated_invoice_container || this.viewSettings.consolidatedInvoiceView) && this.invoiceEmailControl.invalid) {
      this.saveAndSendDisabledTooltip = 'paymentDetails.invalidInvoiceEmail';
      return true;
    }
    else if ((this.selectedPaymentMethod?.payment_method_id === 10 || this.payment?.is_consolidated_invoice_container || this.viewSettings.consolidatedInvoiceView) && this.dueDateControl.invalid) {
      this.saveAndSendDisabledTooltip = 'paymentDetails.invalidDueDate';
      return true;
    }
    return false;
  }

  isSaveAndSendDisabled(): boolean {
    this.saveAndSendDisabledTooltip = '';
    if (this.noOrderLinesSelected) {
      this.saveAndSendDisabledTooltip = 'paymentDetails.noOrderLinesSelected';
      return true;
    }
    else if (this.saveLoading) {
      return true;
    }
    else if (this.viewSettings.repeatingView && !this.fixedRepeatingControl.value && !this.selectedRepeatingWorkOrder) {
      this.saveAndSendDisabledTooltip = 'paymentDetails.noWorkOrderSelected';
      return true;
    }
    else if (!this.selectedPaymentMethod) {
      this.saveAndSendDisabledTooltip = 'paymentDetails.noPaymentMethodSelected';
      return true;
    }
    else if (this.payment && [3, 6, 10, 11].includes(this.payment.payment_status_id!)) {
      this.saveAndSendDisabledTooltip = 'paymentDetails.alreadySent';
      return true;
    }
    else if (this.selectedPaymentMethod?.payment_method_id === 15 && !this.selectedConsolidatedInvoice && !this.payment?.is_consolidated_invoice_container) {
      this.saveAndSendDisabledTooltip = 'paymentDetails.noConsolidatedInvoiceSelected';
      return true;
    }
    else if (!this.selectedInvoiceSendType && (this.selectedPaymentMethod?.payment_method_id === 10 || this.viewSettings.consolidatedInvoiceView || !!this.payment?.is_consolidated_invoice_container)) {
      this.saveAndSendDisabledTooltip = 'paymentDetails.noInvoiceSendTypeSelected';
      return true;
    }
    else if ((this.selectedPaymentMethod?.payment_method_id === 10 || this.payment?.is_consolidated_invoice_container || this.viewSettings.consolidatedInvoiceView) && this.invoiceEmailControl.invalid) {
      this.saveAndSendDisabledTooltip = 'paymentDetails.invalidInvoiceEmail';
      return true;
    }
    else if ((this.selectedPaymentMethod?.payment_method_id === 10 || this.payment?.is_consolidated_invoice_container || this.viewSettings.consolidatedInvoiceView) && this.dueDateControl.invalid) {
      this.saveAndSendDisabledTooltip = 'paymentDetails.invalidDueDate';
      return true;
    }
    else if (this.payment?.is_consolidated_invoice_container && (this.payment.order_lines.length === 0 || this.payment.total_amount_inc_vat === 0)) {
      this.saveAndSendDisabledTooltip = 'paymentDetails.consInvNoOrderLinesSelected';
      return true;
    }
    return false;
  }

  openConsolidatedInvoice() {
    this.paymentService.getOrderPayment(this.payment?.consolidation_container_id!).pipe(takeUntil(this.destroy$)).subscribe((res) => {
      let modalRef = this.modalService.open(PaymentDetailsV2Component, {size: 'lg'});
      modalRef.componentInstance.payment = res;
      modalRef.componentInstance.viewSettings = {modalView: true, paymentView: true, paymentStandaloneView: true};
      modalRef.componentInstance.paymentDeleted.pipe(takeUntil(this.destroy$)).subscribe((payment: OrderPaymentResponseCompact) => {
        if (!this.viewSettings.paymentStandaloneView) {
          this.orderService.fetchAndRefreshOrderPayments(this.payment!.order_id!);
        }
      });
    });
  }

  goToOrder() {
    if (!this.payment?.order_id) return;
    this.router.navigate(['orders/details/', this.payment.order_id]);
    this.activeModal.close();
  }

  getInvoiceDueDate() {
    if (!this.payment) return '';
    if (!this.payment.invoice_sent_at) return '';
    const date = new Date(this.payment.invoice_sent_at);
    date.setDate(date.getDate() + this.payment.invoice_due_date_days);
    return formatDateDMY(date);
  }

  protected readonly scheduled = scheduled;
  protected readonly getPaymentStatusColor = getPaymentStatusColor;
  protected readonly formatDateDMY = formatDateDMY;
  protected readonly formatTimeYMD = formatDateDMY;
  protected readonly formatCurrency = formatCurrency;
  protected readonly currencyFormat = currencyFormat;
  protected readonly displayDate = displayDate;
}
