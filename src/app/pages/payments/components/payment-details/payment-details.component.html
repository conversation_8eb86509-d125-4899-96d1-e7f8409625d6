<div *ngIf="payment" class="hidden-id" style="position: absolute; top: 2px; right: 3px; text-align: right; padding: 0 0 20px 20px;">{{payment.payment_id}}</div>
<div *ngIf="viewSettings.modalView" class="border-bottom" style="padding: 16px;">
  <div class="d-flex justify-content-between align-items-center mb-2">
    <i class="fa-regular fa-xmark fa-xl" (click)="activeModal.close()" style="font-weight: 400; cursor: pointer"></i>
    <h4 class="text-center" style="flex-grow: 1;">{{ viewSettings.consolidatedInvoiceView ? ('paymentDetails.consolidatedInvoice.title' | translate) : viewSettings.createView ? ("paymentDetails.title.create" + (viewSettings.repeatingView ? '.repeating': '') | translate) : payment?.refund ? ('common.refund' | translate) + ' #' : '#' + payment?.payment_number + (payment?.payment_name ? (' - ' + payment?.payment_name) : '')}}</h4>
  </div>
  <div *ngIf="payment?.order_id && viewSettings.paymentStandaloneView" class="">
    <app-button
      [translationKey]="'workOrderDetails.goToOrder'"
      [buttonWidth]="178"
      (buttonClick)="goToOrder()"
    />
  </div>
</div>


<div [ngClass]="[viewSettings.modalView ? 'modal-body p-3': 'card order-details-card p-0', viewSettings.listView ? 'no-border' : '']">

  <div *ngIf="viewSettings.repeatingPlaceholderView" class="order-details-header border-bottom">
    <div class="order-details-header-title">{{"paymentDetails.createRepeatingPayment" | translate}}</div>
  </div>

  <div *ngIf="viewSettings.consolidatedInvoiceView" class="mb-3">
    <div class="d-flex">
      <label>{{'paymentDetails.consolidatedInvoice.name' | translate}}</label>
      <app-help-icon class="ms-1" [tooltipKey]="'paymentDetails.consolidatedInvoice.name.tooltip' | translate"></app-help-icon>
    </div>
    <div class="col-6">
      <app-input
        [editMode]="true"
        [control]="paymentNameControl"
      ></app-input>
    </div>
  </div>

  <div *ngIf="viewSettings.repeatingView && !workOrderTemplate" class="" [ngClass]="viewSettings.repeatingPlaceholderView ? 'px-3' : 'px-1'">
    <!--  Repeat type (work order | fixed)  -->
    <div *ngIf="(viewSettings.repeatingPlaceholderView || viewSettings.repeatingView) && !workOrderTemplate" class="my-2">
      <label class="mb-1 fw-bold">{{"paymentDetails.repeatingType" | translate}}</label>
      <div>
        <div class="form-check form-check-inline me-3">
          <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1" [formControl]="fixedRepeatingControl" [value]="true">
          <label class="form-check-label cursor-pointer" for="inlineRadio1">{{"paymentDetails.tabs.fixed" | translate}}</label>
          <i class="fa-regular fa-info-circle cursor-pointer ms-1" [ngbTooltip]="'paymentDetails.repeatingTypeTooltip.fixed' | translate"></i>
        </div>
        <div class="form-check form-check-inline">
          <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio2" [formControl]="fixedRepeatingControl" [value]="false">
          <label class="form-check-label cursor-pointer" for="inlineRadio2">{{"paymentDetails.tabs.repeating" | translate}}</label>
          <i class="fa-regular fa-info-circle cursor-pointer ms-1" [ngbTooltip]="'paymentDetails.repeatingTypeTooltip.perJob' | translate"></i>
        </div>
      </div>
    </div>

    <hr *ngIf="viewSettings.repeatingPlaceholderView || viewSettings.repeatingView" class="my-2">

    <div class="">
      <!-- Repeating payment per work order setup -->
      <div *ngIf="!fixedRepeatingControl.value" class="col-6">
        <label class="fw-bold">{{"paymentDetails.workOrder" | translate}}</label>
        <app-selectorini
            [directSelection]="true"
            [disableFocusOnLoad]="true"
            [disabled]="!viewSettings.createView"
            [selectedItem]="selectedRepeatingWorkOrder"
            [placeholderTranslationKey]="'paymentDetails.selectWorkOrder'"
            [predefinedSearchResults]="repeatingWorkOrders"
            [searchMainDisplayKeys]="['work_order_title']"
            [selectedMainParenthesisedDisplayKeys]="['schedule_description']"
            [searchSubDisplayKeys]="['schedule_description']"
            (itemSelectedEmitter)="repeatingWorkOrderSelected($event)"
          ></app-selectorini>
      </div>

      <!--    Schedule setup    -->
      <div [ngClass]="{'visually-hidden': !fixedRepeatingControl.value}">
        <app-schedule-setup
          [paymentView]="true"
          [scheduleInputSource]="scheduleInputSubject"
        ></app-schedule-setup>
      </div>

    </div>

  </div>

  <!-- Consolidated invoice Auto/Manual -->
  <div *ngIf="viewSettings.consolidatedInvoiceView">
    <div>
      <div class="form-check form-check-inline me-3">
        <input class="form-check-input" type="radio" name="inlineRadioOptionsConsInv" id="inlineConsInvRadio1" [formControl]="fixedRepeatingControl" [value]="true">
        <label class="form-check-label cursor-pointer" for="inlineConsInvRadio1">{{"paymentDetails.consolidatedInvoice.autoSend" | translate}}</label>
        <i class="fa-regular fa-info-circle cursor-pointer ms-1" [ngbTooltip]="'paymentDetails.consolidatedInvoice.autoSend.tooltip' | translate"></i>
      </div>
      <div class="form-check form-check-inline">
        <input class="form-check-input" type="radio" name="inlineRadioOptionsConsInv" id="inlineConsInvRadio2" [formControl]="fixedRepeatingControl" [value]="false">
        <label class="form-check-label cursor-pointer" for="inlineConsInvRadio2">{{"paymentDetails.consolidatedInvoice.sendManually" | translate}}</label>
        <i class="fa-regular fa-info-circle cursor-pointer ms-1" [ngbTooltip]="'paymentDetails.consolidatedInvoice.sendManually.tooltip' | translate"></i>
      </div>
    </div>

    <!--    Schedule setup    -->
    <div [ngClass]="{'visually-hidden': !fixedRepeatingControl.value}" class="mt-3">
      <app-schedule-setup
        [paymentView]="true"
        [viewSettings]="viewSettings"
        [scheduleInputSource]="scheduleInputSubject"
      ></app-schedule-setup>
    </div>

  </div>

  <hr *ngIf="viewSettings.repeatingPlaceholderView || viewSettings.repeatingView" [ngClass]="[viewSettings.repeatingPlaceholderView ? 'mx-3' : 'mx-1']">

  <!-- Payment method selector and payment action buttons -->
  <div class="mb-2" [ngClass]="[viewSettings.repeatingPlaceholderView ? 'px-3' : 'ps-1']">
    <div class="d-flex justify-content-between">
      <div *ngIf="!viewSettings.consolidatedInvoiceView" class="mb-1 col-6">
        <label class="fw-bold">{{"paymentDetails.selectPaymentMethod" | translate}}</label>
        <app-selectorini
          [directSelection]="true"
          [disableFocusOnLoad]="true"
          [disabled]="payment?.payment_status_id === 3 || !!payment?.is_consolidated_invoice_container || (!!payment?.consolidation_container_id && (payment?.payment_status_id === 3 || payment?.payment_status_id === 6))"
          [selectedItem]="selectedPaymentMethod"
          [predefinedSearchResults]="paymentMethods"
          [searchMainDisplayKeys]="['payment_method_name']"
          [itemIdKey]="'payment_method_id'"
          [disabledSearchResults]="disabledPaymentMethods"
          [disabledItemSubStringKey]="'disabledMessage'"
          (itemSelectedEmitter)="paymentMethodSelected($event)"
        ></app-selectorini>
      </div>

      <!-- Payment action buttons-->
      <div *ngIf="!viewSettings.createView && !viewSettings.consolidatedInvoiceView && !viewSettings.repeatingView && !workOrder?.schedule_template" id="buttonOuterContainerDiv" class="">
        <label></label>
        <div class="btn-group d-flex align-items-end me-2">
          <button type="button" (click)="toggleDropdown()" class="btn btn-primary" [disabled]="loading || !availableButtons" [ngClass]="loading ? '' : 'dropdown-toggle'" style="width: 178px; height: 38px;" data-bs-toggle="dropdown" aria-expanded="false">
            <span *ngIf="!loading">{{ "orders.orderDetails.otherActions" | translate }} <span class="caret"></span></span>
            <app-spinner *ngIf="loading"></app-spinner>
          </button>

          <div #dropDownButtonsDiv *ngIf="!viewSettings.createView" class="dropdown-menu" [hidden]="loading">

            <!--  Credit invoice   -->
            <div [ngbTooltip]="payment && [10, 15].includes(payment.payment_method_id) && payment.refunded_amount > 0 ? ('orders.orderDetails.creditInvoice.tooltip' | translate) : null">
              <button
                *ngIf="payment?.accounting_transfer_at"
                [disabled]="payment && [10, 15].includes(payment.payment_method_id) && payment.refunded_amount > 0"
                [ngStyle]="{'color': payment && payment.refunded_amount > 0 ? '#aaa' : '#6C757D'}"
                class="dropdown-item"
                (click)="creditInvoice()">
                <i class="fa-regular fa-file-xmark me-1"></i>
                <span *ngIf="payment?.payment_method_id === 10 || payment?.is_consolidated_invoice_container">{{ "orders.orderDetails.creditInvoice" | translate }}</span>
                <span *ngIf="payment?.payment_method_id !== 10 && !payment?.is_consolidated_invoice_container">{{ "orders.orderDetails.resetAccounting" | translate }}</span>
              </button>
            </div>

            <!--  Capture manually   -->
            <button
              *ngIf="payment?.payment_sent_at && [2, 5].includes(payment?.payment_status_id!) && payment?.payment_method_id! == 5"
              class="dropdown-item"
              (click)="captureManually()">
              <i class="fa-regular fa-money-check-dollar-pen me-1"></i>
              {{ "orders.orderDetails.captureManually" | translate }}
            </button>

            <!--  Mark order as paid   -->
            <button
              *ngIf="payment?.payment_sent_at! && payment?.payment_status_id! !== 3 && ![10, 15].includes(payment?.payment_method_id!)"
              class="dropdown-item"
              (click)="manuallyMarkOrderAsPaid()">
              <i class="fa-regular fa-money-check-dollar-pen me-1"></i>
              {{ "orders.orderDetails.markOrderAsPaid" | translate }}
            </button>

            <!--  Download invoice PDF   -->
            <button
              *ngIf="payment?.invoice_id! && (tripletexEnabled || fikenEnabled)"
              class="dropdown-item"
              (click)="downloadPdf()">
              <i class="fa-regular fa-download me-1"></i>
              {{ "paymentDetails.downloadInvoicePDF" | translate }}
            </button>

            <!--  Send receipt   -->
            <button
              *ngIf="payment && payment.payment_status_id === 3"
              class="dropdown-item"
              (click)="sendReceipt()">
              <i class="fa-regular fa-receipt me-1"></i>
              {{ "paymentDetails.sendReceipt" | translate }}
            </button>

            <!--  Refund  -->
            <button
              *ngIf="payment"
              class="dropdown-item"
              (click)="onRefundClick()"
              [disabled]="![3, 10].includes(payment.payment_status_id) || payment.payment_method_id === 4 || (payment.total_amount_inc_vat === payment.refunded_amount)"
              [ngStyle]="{'color': payment && (![3, 10].includes(payment.payment_status_id) || payment.payment_method_id === 4 || (payment.total_amount_inc_vat === payment.refunded_amount)) ? '#aaa' : '#6C757D'}">
              <i class="fa-regular fa-rotate-left me-1"></i>
              {{ "orders.orderDetails.refundButtonLabel" | translate }}
            </button>

            <!--  Open logs   -->
            <button
              class="dropdown-item"
              (click)="openPaymentLogs()">
              <i class="fa-regular fa-bars-staggered me-1"></i>
              {{ "orderDetails.payments.eventLog" | translate }}
            </button>

            <!--  Open consolidated invoice   -->
            <button
              *ngIf="payment?.payment_method_id === 15 && !payment?.is_consolidated_invoice_container && !payment?.is_parent_consolidated_invoice"
              class="dropdown-item"
              (click)="openConsolidatedInvoice()">
              <i class="fa-regular fa-file-lines me-1"></i>
              {{ "orderDetails.payments.goToConsolidationContainer" | translate }}
            </button>

            <!--  Open project and department modal  -->
            <button
              *ngIf="payment && (projectsEnabled || departmentsEnabled)"
              class="dropdown-item"
              (click)="openProjectAndDepartmentModal()"
              [ngClass]="{'text-muted': !!payment?.accounting_transfer_at || selectedPaymentMethod.payment_method_id === 15}">
              <i class="fa-regular fa-building-memo me-1"></i>
              {{ (fikenEnabled ? 'orderDetails.payments.projectAndDepartment.projectOnly' : 'orderDetails.payments.projectAndDepartment') | translate }}
            </button>

            <!--  Delete payment   -->
            <button
              *ngIf="!viewSettings.createView"
              class="dropdown-item"
              (click)="payment?.payment_status_id === 3 ? $event.stopPropagation() : deletePayment()"
              [disabled]="deleteLoading"
              [ngClass]="{'text-muted': payment?.payment_status_id === 3}">
              <i class="fa-regular fa-trash-alt me-1"></i>
              {{ "common.delete" | translate }}
            </button>


          </div>

        </div>
      </div>
    </div>


    <!-- Payment method specific views -->
    <div *ngIf="selectedPaymentMethod" class="mb-2">
      <!-- Selection in customer portal -->
      <div *ngIf="selectedPaymentMethod?.payment_method_id == 3">
        <div *ngIf="viewSettings.createView" class="text-muted">{{"paymentDetails.paymentMethodDescription.id3" | translate}}</div>
      </div>

      <!-- Quickpay auto capture -->
      <div *ngIf="selectedPaymentMethod?.payment_method_id == 14">
        <div *ngIf="viewSettings.createView" class="text-muted">{{"paymentDetails.paymentMethodDescription.id14" | translate}}</div>
      </div>

      <!-- Invoice -->
      <div *ngIf="selectedPaymentMethod?.payment_method_id === 10 || payment?.is_consolidated_invoice_container || viewSettings.consolidatedInvoiceView">
        <div *ngIf="viewSettings.createView && !viewSettings.consolidatedInvoiceView" class="text-muted">{{"paymentDetails.paymentMethodDescription.id10" | translate}}</div>
        <div class="mt-3">
          <div class="row mb-2">
            <h4 class="mb-2">{{ "paymentDetails.invoiceSettings" | translate }}</h4>

            <div class="d-flex gap-2">
              <!-- Send type -->
              <div class="col">
                <label>{{ "paymentDetails.invoiceSettings.invoiceSendType" | translate }}</label>
                <app-selectorini
                  [searchable]="false"
                  [showCrossButton]="false"
                  [disabled]="!!payment?.invoice_sent_at!"
                  [predefinedSearchResults]="invoiceSendTypes"
                  [disabledSearchResults]="disabledInvoiceSendTypes"
                  [selectedItem]="selectedInvoiceSendType"
                  [searchMainDisplayKeys]="['invoice_send_type_name']"
                  (itemSelectedEmitter)="invoiceSendTypeSelected($event)"
                ></app-selectorini>
              </div>

              <!--  Invoice Due date  -->
              <div class="col">
                <div class="col-6">
                  <div class="">
                    <label>{{ "orders.orderDetails.invoiceSettings.dueDate" | translate }}</label>
                    <app-input
                      [hideDecimals] = true
                      [editMode]="true"
                      [control]="dueDateControl"
                      type="number"
                      [inputSuffix]="'orders.orderDetails.invoiceSettings.dueDatePostFix' | translate"
                    ></app-input>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-12 d-flex gap-2 mb-2">
            <!--  Invoice Reference  -->
            <div class="col">
              <div class="form-group">
                <label>{{ "orders.orderDetails.invoiceSettings.invoiceReference" | translate }}</label>
                <app-input
                  [editMode]="true"
                  [control]="invoiceReferenceControl"
                ></app-input>
              </div>
            </div>

            <!--  Invoice email  -->
            <div class="col">
              <div class="">
                <label>{{ "orders.orderDetails.invoiceSettings.invoiceEmail" | translate }}</label>
                <app-input
                  [editMode]="true"
                  [control]="invoiceEmailControl"
                ></app-input>
              </div>
            </div>
          </div>

          <div *ngIf="tripletexEnabled" class="">
            <!--  Invoice Comment  -->
            <div class="col">
              <div class="">
                <label>{{ "orders.orderDetails.invoiceSettings.invoiceComment" | translate }}</label>
                <app-input
                  [editMode]="true"
                  [control]="invoiceCommentControl"
                ></app-input>
              </div>
            </div>
          </div>

        </div>
      </div>

      <div *ngIf="payment && (projectsEnabled || departmentsEnabled) && viewSettings.consolidatedInvoiceView" class="mt-2">
        <div class="mb-1">
          <div *ngIf="projectsEnabled"><span class="fw-bold">{{"orderDetails.payments.project" | translate}}:</span> {{payment.project?.project_name}}</div>
          <div *ngIf="departmentsEnabled"><span class="fw-bold">{{"orderDetails.payments.department" | translate}}:</span> {{payment.department?.department_name}}</div>
        </div>
        <app-button
          [translationKey]="'orderDetails.payments.projectAndDepartment'"
          (buttonClick)="openProjectAndDepartmentModal()">
        </app-button>
      </div>

      <!-- External payment -->
      <div *ngIf="selectedPaymentMethod?.payment_method_id == 4">
        <div *ngIf="viewSettings.createView" class="text-muted">{{"paymentDetails.paymentMethodDescription.id4" | translate}}</div>
      </div>

      <!-- Consolidated invoice -->
      <div *ngIf="selectedPaymentMethod?.payment_method_id == 15 && !payment?.is_consolidated_invoice_container && !viewSettings.consolidatedInvoiceView">
        <div class="text-muted">{{"paymentDetails.paymentMethodDescription.id15" | translate}}</div>

        <div *ngIf="!payment?.is_consolidated_invoice_container" class="mt-2 mb-3 col-6">
          <label>{{"paymentDetails.consolidatedInvoice.selectInvoice" | translate}}</label>
          <app-selectorini
            [directSelection]="true"
            [disableFocusOnLoad]="true"
            [disabled]="!!payment && [3, 6].includes(payment.payment_status_id)"
            [createItemTranslationKey]="'paymentDetails.consolidatedInvoice.createNew'"
            [placeholderTranslationKey]="'paymentDetails.consolidatedInvoice.placeholder'"
            [showCreateItem]="true"
            [selectedItem]="selectedConsolidatedInvoice"
            [predefinedSearchResults]="consolidatedInvoices"
            [loading]="consolidatedInvoicesLoading"
            [searchMainDisplayKeys]="['payment_name']"
            (itemSelectedEmitter)="consolidatedInvoiceSelected($event)"
            (createItemEmitter)="createConsolidatedInvoice()"
          ></app-selectorini>
        </div>

      </div>


    </div>

  <!-- Payment info -->
    <div *ngIf="!viewSettings.consolidatedInvoiceView" class="mb-2">
      <!-- Payment reminders -->
      <div *ngIf="![10, 15].includes(selectedPaymentMethod?.payment_method_id!) && payment?.payment_status_id !== 3 && companyPaymentRemindersActive"  class="d-flex">
        <app-toggle-switch
          [bigSwitch]="true"
          [labelKey]="'paymentDetails.paymentReminders' | translate"
          [disabledTooltipKey]="'orderDetails.payments.paymentReminder.disabledToolTip'"
          [isDisabled]="(!!payment?.payment_sent_at && payment?.payment_reminders_disabled) || (payment?.payment_status_id === 3)"
          [state]="payment ? !payment.payment_reminders_disabled : true"
          (stateChange)="togglePaymentReminder()"
        ></app-toggle-switch>
        <app-help-icon class="ms-1" [tooltipKey]="'paymentDetails.paymentReminders.tooltip'"></app-help-icon>
      </div>

      <!--  Payment data  -->
      <div *ngIf="payment && !viewSettings.createView && !viewSettings.repeatingView">
        <div><span class="fw-bold">{{"ID"}}:</span> #{{payment.payment_number}}</div>
        <div><span class="fw-bold">{{"orderDetails.payments.paymentMethod" | translate}}:</span> {{payment.payment_method_name}}</div>
        <div *ngIf="payment.auto_send_at"><span class="fw-bold">{{"orderDetails.payments.autoSendAt" | translate}}:</span> {{displayDate(payment.auto_send_at)}}</div>
        <div><span class="fw-bold me-1">{{"orderDetails.payments.paymentStatus" | translate}}:</span> <span [ngClass]="getPaymentStatusColor(payment)">{{payment.payment_status_name}}</span></div>
        <div *ngIf="payment.payment_sent_at && ![10, 15].includes(payment.payment_method_id) && !payment.captured_at"><span class="fw-bold">{{"orderDetails.payments.nextPaymentReminder" | translate}}:</span><span [ngClass]="{'strikethrough': payment.payment_status_id === 3}"> {{displayDate(payment.payment_reminder_status === 0 ? payment.payment_reminder_1_scheduled_at : payment.payment_reminder_2_scheduled_at)}}</span></div>
        <div *ngIf="payment.payment_method_id !== 10 && (payment.consolidation_container_id === null || [3, 6].includes(payment.payment_status_id))"><span class="fw-bold">{{"orderDetails.payments.sentAt" | translate}}:</span> {{payment.payment_sent_at ? displayDate(payment.payment_sent_at) : ''}}</div>
        <div *ngIf="![0, null].includes(payment.paid_amount)" ><span class="fw-bold">{{"orderDetails.payments.paidAmount" | translate}}:</span> {{currencyFormat(payment.paid_amount)}}</div>
        <div><span class="fw-bold">{{"orderDetails.payments.paidAt" | translate}}:</span> {{payment.captured_at ? displayDate(payment.captured_at) : ("orderDetails.payments.notPaidYet" | translate)}}</div>
        <div *ngIf="projectsEnabled"><span class="fw-bold">{{"orderDetails.payments.project" | translate}}:</span> {{selectedPaymentMethod.payment_method_id === 15 ? ('orderDetails.payments.setByConsolidatedInvoice' | translate) : payment.project?.project_name}}</div>
        <div *ngIf="departmentsEnabled"><span class="fw-bold">{{"orderDetails.payments.department" | translate}}:</span> {{selectedPaymentMethod.payment_method_id === 15 ? ('orderDetails.payments.setByConsolidatedInvoice' | translate) : payment.department?.department_name}}</div>

        <!--   Invoice data   -->
        <div *ngIf="payment.payment_method_id === 10 || payment?.is_consolidated_invoice_container"><span class="fw-bold">{{"orderDetails.payments.invoiceDate" | translate}}:</span><span> {{payment.invoice_date ? formatTimeYMD(payment.invoice_date) : ("orderDetails.payments.invoiceNotSent" | translate)}}</span></div>
        <div *ngIf="payment.payment_method_id === 10 || payment?.is_consolidated_invoice_container"><span class="fw-bold">{{"orderDetails.payments.dueDate" | translate}}:</span>
          <span *ngIf="!payment.invoice_sent_at && payment.payment_status_id != 3"> {{"orders.orderDetails.invoiceNotSent" | translate}}</span>
          <span *ngIf="payment.payment_status_id === 3"> {{getInvoiceDueDate()}}</span>
          <span *ngIf="payment.invoice_sent_at && payment.payment_status_id != 3" [ngClass]="{'text-danger': payment.actual_invoice_due_date_days !== null && payment.actual_invoice_due_date_days < 1}"> {{payment.actual_invoice_due_date_text}}</span>
        </div>

        <!--   Accounting data   -->
        <div *ngIf="payment.consolidation_container_id === null">
          <span class="fw-bold">{{"orderDetails.payments.accountingStatus" | translate}}:</span>
          <span [ngClass]="accountingStatusColorMap[payment.accounting_transfer_status_id]!"> {{payment.accounting_transfer_status_name}}</span>
          <span (click)="retryAccountingSync()"><i class="fa fa-arrows-rotate ms-1" [ngClass]="accountingRefreshLoading ? 'refresh-accounting-loading' : 'refresh-accounting'"></i></span>
        </div>

        <div *ngIf="payment.payment_reference">
          <span class="fw-bold">{{"orderDetails.payments.paymentReference" | translate}}:</span>
          <span> {{payment.payment_reference}}</span>
        </div>

        <div>
          <div class="cursor-pointer clickable-text" style="color: var(--primary-color) !important;" *ngIf="payment.payment_status_id === 6" (click)="checkPaymentStatus()">{{"orders.orderDetails.accountingStatus.checkPayment" | translate}}</div>
        </div>

      </div>
    </div>

  </div>

  <!-- Order lines -->
  <div *ngIf="!viewSettings.consolidatedInvoiceView" class="mb-2" [ngClass]="{'px-2': viewSettings.repeatingPlaceholderView}">
    <app-order-lines [payment]="payment" [order]="order" [orderLinesSubject]="orderLinesSubject" [viewSettings]="viewSettings" (orderLineAdded)="addedOrderLines.push($event)"></app-order-lines>
  </div>

  <div *ngIf="viewSettings.repeatingPlaceholderView" class="d-flex mt-3 mb-2 me-2 justify-content-end">
    <app-button
      [disabled]="noOrderLinesSelected || saveLoading || (viewSettings.repeatingView && !fixedRepeatingControl.value && !selectedRepeatingWorkOrder) || (!selectedPaymentMethod)"
      [ngbTooltip]="noOrderLinesSelected ? ('paymentDetails.noOrderLinesSelected' | translate) : null"
      [translationKey]="saveDontSendTranslationKey"
      [loading]="saveWithoutSendingLoading"
      [themeStyle]="'primary'"
      (buttonClick)="saveWithoutSendingToPayment()"
    ></app-button>
  </div>

</div>

<!-- Footer buttons -->
<div *ngIf="viewSettings.modalView || viewSettings.listView" class="d-flex justify-content-between p-2 modal-footer">
  <div class="d-flex">
    <button *ngIf="viewSettings.modalView" type="submit" class="btn btn-secondary me-2" (click)="closePaymentModal(false)">{{ "common.cancel" | translate }}</button>
    <button *ngIf="!viewSettings.createView" [disabled]="deleteLoading || (payment?.payment_status_id === 3 && payment?.payment_method_id !== 4)" type="submit" class="btn btn-danger" (click)="deletePayment()" [ngbTooltip]="payment?.accounting_transfer_at ? ('paymentDetails.selectPaymentMethod.disabledDeleteTooltip' | translate) : null">
      <span *ngIf="!deleteLoading">{{ "common.delete" | translate }}</span>
      <app-spinner *ngIf="deleteLoading"></app-spinner>
    </button>
  </div>
  <div class="d-flex" *ngIf="!viewSettings.consolidatedInvoiceView">
    <div class="me-2">
    <!--   Save, but don't send / Save repeating payment  -->
     <app-button
       id="saveButNotSendButton"
       *ngIf="!(payment && payment?.payment_status_id === 3 || (selectedPaymentMethod.payment_method_id === 15 && !viewSettings.repeatingView)) || !!payment?.is_consolidated_invoice_container"
        [disabled]="isSaveNotSendDisabled()"
        [ngbTooltip]="saveNotSendDisabledTooltip | translate"
        [translationKey]="saveDontSendTranslationKey"
        [loading]="saveWithoutSendingLoading"
        [themeStyle]="this.workOrder?.schedule_template ? 'primary' : viewSettings.repeatingView ? 'primary': 'secondary'"
        (buttonClick)="saveWithoutSendingToPayment()"
      ></app-button>
    </div>

    <!--   Save and send  -->
    <app-button
      id="saveAndSendButton"
      *ngIf="!viewSettings.repeatingView && !(payment && payment?.payment_status_id === 3) && (!workOrder?.schedule_template || selectedPaymentMethod?.payment_method_id === 15)"
      [disabled]="isSaveAndSendDisabled()"
      [ngbTooltip]="saveAndSendDisabledTooltip | translate"
      [translationKey]="saveAndSendTranslationKey"
      [loading]="saveAndSendLoading"
      [themeStyle]="'primary'"
      (buttonClick)="saveAndSendToPayment()"
    ></app-button>
  </div>

  <!-- Create consolidated invoice -->
  <app-button
    *ngIf="viewSettings.consolidatedInvoiceView"
    [disabled]="invoiceEmailControl.invalid || dueDateControl.invalid || !selectedInvoiceSendType || paymentNameControl.invalid"
    [translationKey]="viewSettings.createView ? 'common.create' : 'common.save'"
    [loading]="saveWithoutSendingLoading"
    [themeStyle]="'primary'"
    (buttonClick)="saveWithoutSendingToPayment()"
  ></app-button>
</div>

