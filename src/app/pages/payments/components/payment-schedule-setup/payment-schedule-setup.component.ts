import {ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, ViewChild} from '@angular/core';
import {StandardImports} from "../../../../@shared/global_import";
import {OrderPaymentResponse, PaymentScheduleOptionResponse, PaymentScheduleResponse} from "../../../../@shared/models/payment.interfaces";
import {BehaviorSubject, Subject} from "rxjs";
import {_CRM_PAY_26, ScheduleInput} from "../../../../@shared/models/input.interfaces";
import {ButtonDoubleComponent} from "../../../../@shared/components/button-double/button-double.component";
import {SelectoriniComponent} from "../../../../@shared/components/selectorini/selectorini.component";
import {ScheduleSetupComponent} from "../../../../@shared/components/schedule-component/schedule-setup.component";
import {ToggleSwitchComponent} from "../../../../@shared/components/toggle-switch/toggle-switch.component";
import {HelpIconComponent} from "../../../../@shared/components/help-icon/help-icon.component";
import {formatFullDayAndDate, UtilsService} from "../../../../@core/utils/utils.service";
import {DetailsViewSettings, WorkOrderResponse} from "../../../../@shared/models/order.interfaces";
import {PaymentService} from "../../../../@shared/services/payment.service";
import {OrderService} from "../../../../@shared/services/order.service";
import {takeUntil} from "rxjs/operators";

@Component({
  selector: 'app-payment-schedule-setup',
  templateUrl: './payment-schedule-setup.component.html',
  styleUrl: './payment-schedule-setup.component.css',
  standalone: true,
  imports: [StandardImports, ButtonDoubleComponent, SelectoriniComponent, ScheduleSetupComponent, ToggleSwitchComponent, HelpIconComponent]
})
export class PaymentScheduleSetupComponent implements OnInit {
  @Input() payment: OrderPaymentResponse;
  @Input() viewSettings: DetailsViewSettings;
  @Input() paymentRecipientId: number | null = null;
  @Input() repeatingJobsAvailable: boolean = false;
  scheduleInputSubject: BehaviorSubject<ScheduleInput> = new BehaviorSubject<ScheduleInput>({} as ScheduleInput);
  fixedPaymentSchedule: boolean = true;
  enableAutoCapture: boolean = false;
  firstSchedulePayment: Date | null = null;
  workOrderScheduleTemplates: WorkOrderResponse[] = [];
  selectedWorkOrderScheduleTemplates: WorkOrderResponse[] = [];

  expanded: boolean = false;

  // Schedule options
  fixedScheduleOptions: PaymentScheduleOptionResponse[] = []
  perJobScheduleOptions: PaymentScheduleOptionResponse[] = []
  selectedScheduleOption: PaymentScheduleOptionResponse | null = null;
  scheduleOptionsLoading: boolean = false;

  @Output() scheduleOptionSelectedEvent = new EventEmitter<PaymentScheduleOptionResponse>();

  @ViewChild('workOrderScheduleTemplateSelectorini') workOrderScheduleTemplateSelectorini: SelectoriniComponent;

  destroy$ = new Subject<void>();

  constructor(public utilService: UtilsService, private paymentService: PaymentService, private orderService: OrderService) {
  }

  ngOnInit() {
    // Init schedule input listener to update payment schedule
    this.scheduleInputSubject.subscribe((scheduleInput) => {
      if (scheduleInput) {
        this.payment.payment_schedule = scheduleInput as PaymentScheduleResponse;
      }
    })

    // Load work order schedule templates
    this.orderService.workOrderSchedules$.pipe(takeUntil(this.destroy$)).subscribe((workOrders) => {
      this.workOrderScheduleTemplates = workOrders;
      if (this.workOrderScheduleTemplates.length === 0) {
        // Alert user that they need to select work orders
      } else if (this.workOrderScheduleTemplates.length === 1) {
        this.selectedWorkOrderSchedulesUpdated(workOrders);
      } else {
        this.selectedWorkOrderSchedulesUpdated(workOrders.filter((wo) => this.payment.work_order_ids?.includes(wo.work_order_id!)));
      }
    });

    // Load payment schedule options
    this.scheduleOptionsLoading = true;
    this.paymentService.getPaymentScheduleOptions().subscribe((scheduleOptions) => {
      this.fixedScheduleOptions = scheduleOptions.filter((option) => option.fixed);
      this.perJobScheduleOptions = scheduleOptions.filter((option) => !option.fixed);
      if (!this.viewSettings.createView) {
        this.scheduleOptionSelected(scheduleOptions.find((option) => option.option_id === this.payment?.schedule_option?.option_id)!)
      } else {
        this.scheduleOptionSelected(this.fixedPaymentSchedule ? this.fixedScheduleOptions[0] : this.perJobScheduleOptions[0])
      }
      this.scheduleOptionsLoading = false;
    }, error => {
      this.scheduleOptionsLoading = false;
    });

    if (this.viewSettings.createView) {
      this.initEmpty();
    } else {
      this.initPayment();
    }
  }

  initEmpty() {
  }

  initPayment() {
    this.fixedPaymentSchedule = this.payment.schedule_option?.fixed!;
    this.scheduleOptionSelected(this.payment.schedule_option!);
  }

  paymentScheduleTypeSet(fixed: boolean) {
    this.fixedPaymentSchedule = fixed;
    // If fixed, remove work order schedule templates, else set the global order one
    if (fixed) {
      this.selectedWorkOrderScheduleTemplates = [];
      this.payment.work_order_ids = [];
    } else {
      if (this.workOrderScheduleTemplates.length === 0) {
        // Alert user that they need to select work orders
      } else if (this.workOrderScheduleTemplates.length === 1) {
        this.selectedWorkOrderSchedulesUpdated(this.selectedWorkOrderScheduleTemplates);
      } else {
        this.selectedWorkOrderSchedulesUpdated(this.workOrderScheduleTemplates.filter((wo) => this.payment.work_order_ids?.includes(wo.work_order_id!)));
      }
    }

    // If per job is selected, but current schedule option is fixed, set to first per job option
    if (!this.fixedPaymentSchedule && this.fixedScheduleOptions.map(option => option.option_id).includes(this.selectedScheduleOption?.option_id!)) {
      this.scheduleOptionSelected(this.perJobScheduleOptions[0]);
    }
    // If fixed is selected, but current schedule option is per job, set to first fixed option
    else if (this.fixedPaymentSchedule && this.perJobScheduleOptions.map(option => option.option_id).includes(this.selectedScheduleOption?.option_id!)) {
      this.scheduleOptionSelected(this.fixedScheduleOptions[0]);
    }
  }

  toggleEnableAutoCapture(value: boolean) {
    this.enableAutoCapture = value;
  }

  selectedWorkOrderSchedulesUpdated(workOrderSchedules: WorkOrderResponse[] | any[]) {
    // Get last selected work order schedule template
    // if (!workOrderSchedules || workOrderSchedules.length === 0) {
    //   this.selectedWorkOrderScheduleTemplates = [];
    //   return;
    // }
    // let lastSelectedWorkOrderSchedule = workOrderSchedules[workOrderSchedules.length - 1];
    // if (lastSelectedWorkOrderSchedule.work_order_id === -1) {
    //   workOrderSchedules = workOrderSchedules.filter(workOrder => workOrder.work_order_id === -1);
    //   this.selectedWorkOrderScheduleTemplates = workOrderSchedules;
    // } else {
    //   this.selectedWorkOrderScheduleTemplates = workOrderSchedules.filter(workOrder => workOrder.work_order_id !== -1);
    // }
    this.selectedWorkOrderScheduleTemplates = workOrderSchedules;
    this.payment.work_order_ids = this.selectedWorkOrderScheduleTemplates.map(workOrder => workOrder.work_order_id);
    if (this.workOrderScheduleTemplates.length === 1 && this.workOrderScheduleTemplateSelectorini) {
      this.workOrderScheduleTemplateSelectorini.showDropDown = false;
    }
    console.log('Selected work order schedules updated:', this.selectedWorkOrderScheduleTemplates);
  }

  scheduleOptionSelected(option: PaymentScheduleOptionResponse | any) {
    if (!option) return;

    this.payment.schedule_option = option;
    this.selectedScheduleOption = option;
    this.scheduleOptionSelectedEvent.emit(option);

    if (this.selectedScheduleOption?.requires_schedule) {
      let dateDict: {[key: number]: number} = {
        1: 1,
        2: -1,
        5: -1,
      };

      this.scheduleInputSubject.next({
        date: dateDict[option.option_id] || 1,
        nth_weekday: 1,
        weekdays: [0],
        every: 1,
        schedule_repeat_type_id: 2,
        schedule_repeat_type_name: 'Månedlig',
        instances_in_advance: 5,
      } as ScheduleInput);
    } else {
      this.payment.payment_schedule = null;
    }
  }


  protected readonly formatFullDayAndDate = formatFullDayAndDate;
}
