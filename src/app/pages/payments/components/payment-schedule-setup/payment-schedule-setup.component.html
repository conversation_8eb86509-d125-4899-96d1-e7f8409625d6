<div>
  <div class="d-flex">
    <!-- Left side -->
    <div class="" [ngClass]="expanded ? 'col' : 'col-7'">

      <!--  Payment schedule description  -->
      <div *ngIf="!viewSettings.createView" class="details-card" [ngClass]="{ 'collapse-open': expanded, 'collapse-closed': !expanded }">
        <div class="d-flex align-items-center cursor-pointer" style="padding: 8px 8px 8px 16px;" (click)="expanded = !expanded">
          <div class="d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; border-radius: 6px; background-color: #e1dddd;">
            <i class="fa-regular fa-xl" [ngClass]="payment.schedule_option?.fixed ? 'fa-calendar-check' : 'fa-ballot-check'"></i>
          </div>
          <div [ngClass]="{'d-flex align-items-center gap-1': expanded}">
              <ng-container *ngTemplateOutlet="scheduleOption; context: { item: payment?.schedule_option, notSelectorini: true }"></ng-container>
          </div>
          <div type="button" class="d-flex align-items-center justify-content-end col p-2" style="min-width: 39px;">
            <i class="fa-regular fa-chevron-right chevron-rotate fa-lg" [ngClass]="{'expanded': expanded}"></i>
          </div>
        </div>

        <!-- Expanded -->
        <div *ngIf="expanded" class="px-2 py-2 border-top">
          <!-- Schedule options dropdown -->
          <div class="col-6">
            <ng-container *ngTemplateOutlet="scheduleOptionsDropdown"></ng-container>
          </div>

          <!-- Custom schedule selector -->
          <div *ngIf="selectedScheduleOption?.custom_schedule" class="mt-2">
            <app-schedule-setup
              [paymentView]="true"
              [scheduleInputSource]="scheduleInputSubject"
            ></app-schedule-setup>
          </div>

          <!-- Work order selector -->
          <div class="mt-2">
            <ng-container *ngTemplateOutlet="workOrderScheduleTemplateDropdown"></ng-container>
          </div>

        </div>

      </div>


      <!-- Fixed / per job selector -->
      <div class="mb-2" *ngIf="viewSettings.createView">
        <label>Hvordan skal du fakturere?</label>
        <app-button-double
          [leftButtonTranslationKey]="'Fast sum'"
          [rightButtonTranslationKey]="'Per jobb'"
          [leftButtonActive]="fixedPaymentSchedule"
          [rightButtonActive]="!fixedPaymentSchedule"
          [rightButtonDisabled]="workOrderScheduleTemplates.length == 0"
          [rightButtonNgbTooltip]="workOrderScheduleTemplates.length == 0 ? 'Du må opprette en gjentagende jobb for å kunne velge dette alternativet.' : null"
          [useMaxWidth]="true"
          [boldText]="true"
          [small]="true"
          (leftButtonClick)="paymentScheduleTypeSet(true)"
          (rightButtonClick)="paymentScheduleTypeSet(false)"
        ></app-button-double>
      </div>

      <!--  Job plan dropdown  -->
      <div *ngIf="!fixedPaymentSchedule && viewSettings.createView" class="mb-2">
        <ng-container *ngTemplateOutlet="workOrderScheduleTemplateDropdown"></ng-container>
      </div>

      <!--  Schedule options dropdown  -->
      <ng-container *ngTemplateOutlet="viewSettings.createView ? scheduleOptionsDropdown : null"></ng-container>
<!--      <div class="">-->
<!--        <label>Når ønsker du å fakturere?</label>-->
<!--        <app-selectorini-->
<!--          [customNgTemplate]="scheduleOption"-->
<!--          [directSelection]="true"-->
<!--          [predefinedSearchResults]="fixedPaymentSchedule ? fixedScheduleOptions : perJobScheduleOptions"-->
<!--          [selectedItem]="selectedScheduleOption"-->
<!--          [searchMainDisplayKeys]="['name']"-->
<!--          [searchSubDisplayKeys]="['subtext']"-->
<!--          [loading]="scheduleOptionsLoading"-->
<!--          (itemSelectedEmitter)="scheduleOptionSelected($event)"-->
<!--        ></app-selectorini>-->
<!--        <div *ngIf="selectedScheduleOption && selectedScheduleOption.requires_schedule && selectedScheduleOption.auto_send && !selectedScheduleOption.custom_schedule && firstSchedulePayment" class="ps-1 text-muted font-12 mt-1">Den første fakturaen sendes {{utilService.formatFullDayAndDate(firstSchedulePayment).toLowerCase()}}</div>-->
<!--      </div>-->

    </div>
    <!-- Right side -->
    <div *ngIf="!expanded" class="col" [ngClass]="[!!payment?.payment_schedule ? 'ms-2' : 'ms-2']">

      <div class="py-2 ps-2" [ngClass]="{'h-100 d-flex align-items-center': !viewSettings.createView}" style="border: 1px solid #DEE2E6; border-radius: 8px;">
        <div class="d-flex align-items-center justify-content-between col mb-0">
          <div class="d-flex align-items-center">
            <label for="autoCaptureSwitch" class="me-1" (click)="toggleEnableAutoCapture(!enableAutoCapture)">Få betalt automatisk</label>
            <app-help-icon *ngIf="!viewSettings.createView" [tooltipKey]="'Når denne er aktivert vil kunden kunne registrere kortet i kundeportalen, og alle fremtidige betalinger kan trekkes direkte fra kundens kort'"></app-help-icon>
          </div>
          <app-toggle-switch
            id="autoCaptureSwitch"
            [bigSwitch]="true"
            [labelKey]="''"
            [state]="enableAutoCapture"
            (stateChange)="toggleEnableAutoCapture($event)"
          ></app-toggle-switch>
        </div>
        <div *ngIf="viewSettings.createView" class="text-muted font-12 pe-2">Kunden faktureres og belastes automatisk <br> basert på valgt faktureringsintervall så snart kundens kort er registrert. Kortet legges inn av kunden selv i kundeportalen.</div>
      </div>
    </div>

  </div>

  <!--  Custom schedule setup  -->
  <div class="mt-2" [ngClass]="{'visually-hidden': !selectedScheduleOption?.custom_schedule}">
    <app-schedule-setup
      [paymentView]="true"
      [scheduleInputSource]="scheduleInputSubject"
      (firstInstanceChange)="firstSchedulePayment = $event"
    ></app-schedule-setup>
  </div>
</div>

<ng-template #scheduleOption let-item="item" let-selected="selected" let-notSelectorini="notSelectorini">
  <div class="d-flex justify-content-between col align-items-center" [ngClass]="notSelectorini ? 'pe-0' : 'pe-3'">
    <div class="" [ngClass]="[notSelectorini ? 'ms-0' : 'ms-2']">
      <div class="d-flex align-items-center">{{item.name}}</div>
      <div *ngIf="!selected" class="text-muted font-12"><i class="fa-regular me-1 fa-xs" [ngClass]="item.custom_schedule ? 'fa-pen-to-square' : item.auto_send ? 'fa-arrows-rotate text-success' : 'fa-warning text-warning'"></i>{{item.subtext}}</div>
    </div>
    <app-help-icon *ngIf="item.description" [tooltipKey]="item.description"></app-help-icon>
  </div>
</ng-template>

<ng-template #repeatingJobItem let-item="item">
  <div class="d-flex col align-items-center justify-content-between px-2">
    <div class="">
      <div style="line-height: 12px;">{{'# ' + item.work_order_number}}{{item.work_order_title ? ' - ' + item.work_order_title : ''}}</div>
      <div class="text-muted font-12">Gjentas {{item.schedule.schedule_description}}</div>
    </div>
    <div class="d-flex align-items-center">
      <i *ngIf="item['__selected__']" class="fa-regular fa-check text-success fa-lg"></i>
    </div>
  </div>
</ng-template>

<ng-template #repeatingJobResult  let-items="items">
  <div class="d-flex justify-content-between align-items-center" style="height: 38px">
    <div *ngIf="items && items.length === 1" class="px-2" style="padding-top: 2px;">
      <div style="line-height: 12px;">{{'# ' + items[0].work_order_number}}{{items[0].work_order_title ? ' - ' + items[0].work_order_title : ''}}</div>
      <div class="text-muted font-12">Gjentas {{items[0].schedule.schedule_description}}</div>
    </div>
    <div *ngIf="items && items.length > 1" class="px-2">
      Du har valgt {{items.length}} jobber
    </div>
  </div>
</ng-template>

<ng-template #workOrderScheduleTemplateDropdown>
  <label>Velg jobb-planene fakturaplanen skal gjelde for</label>
  <app-selectorini
    #workOrderScheduleTemplateSelectorini
    [directSelection]="true"
    [itemIdKey]="'work_order_id'"
    [multiSelect]="true"
    [hideDropDownOnSelect]="false"
    [customNgTemplate]="repeatingJobItem"
    [multipleResultNgTemplate]="repeatingJobResult"
    [selectedItems]="selectedWorkOrderScheduleTemplates"
    [predefinedSearchResults]="workOrderScheduleTemplates"
    (selectedItemsUpdatedEmitter)="selectedWorkOrderSchedulesUpdated($event)"
  ></app-selectorini>
</ng-template>

<!--  Schedule options dropdown  -->
<ng-template #scheduleOptionsDropdown>
  <div class="">
    <label>Når ønsker du å fakturere?</label>
    <app-selectorini
      [customNgTemplate]="scheduleOption"
      [directSelection]="true"
      [predefinedSearchResults]="fixedPaymentSchedule ? fixedScheduleOptions : perJobScheduleOptions"
      [selectedItem]="selectedScheduleOption"
      [searchMainDisplayKeys]="['name']"
      [searchSubDisplayKeys]="['subtext']"
      [loading]="scheduleOptionsLoading"
      (itemSelectedEmitter)="scheduleOptionSelected($event)"
    ></app-selectorini>
    <div *ngIf="selectedScheduleOption && selectedScheduleOption.requires_schedule && selectedScheduleOption.auto_send && !selectedScheduleOption.custom_schedule && firstSchedulePayment" class="ps-1 text-muted font-12 mt-1">Den første fakturaen sendes {{formatFullDayAndDate(firstSchedulePayment).toLowerCase()}}</div>
  </div>
</ng-template>
