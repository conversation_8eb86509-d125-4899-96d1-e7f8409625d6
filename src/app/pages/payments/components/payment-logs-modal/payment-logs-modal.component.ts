import {Component, Input, OnInit} from '@angular/core';
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {OrderPaymentResponse, PaymentLogEntryResponse} from "../../../../@shared/models/payment.interfaces";
import {OrderService} from "../../../../@shared/services/order.service";
import {PaymentService} from "../../../../@shared/services/payment.service";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {SpinnerComponent} from "../../../../@shared/components/spinner/spinner.component";
import {ButtonComponent} from "../../../../@shared/components/button/button.component";
import {NgForOf, NgIf} from "@angular/common";

import {formatTimeHM, formatTimeHMSS, formatDateDMY} from "../../../../@core/utils/utils.service";
import {StandardImports} from "../../../../@shared/global_import";

@Component({
    selector: 'app-payment-list-modal',
    templateUrl: './payment-logs-modal.component.html',
    styleUrls: ['./payment-logs-modal.component.css'],
    standalone: true,
  imports: [StandardImports, SpinnerComponent]
})
export class PaymentLogsModalComponent implements OnInit {
  @Input() payment: OrderPaymentResponse;
  loading = false;
  logEntries: PaymentLogEntryResponse[] = [];

  constructor(public activeModal: NgbActiveModal,
              private modalService: NgbModal,
              private orderService: OrderService,
              private paymentService: PaymentService,
              private translate: TranslateService) {

  }

  ngOnInit(): void {
    this.paymentService.getPaymentLogs(this.payment.payment_id).subscribe((logs) => {
      this.logEntries = logs.map(log => {

        this.updateAuthorizationKeys(log.request_payload, "********");
        this.updateAuthorizationKeys(log.data, "********");

        if (log.url && log.url[log.url.length - 1] === "?") {
          log.url = log.url.slice(0, -1);
        }

        if (log.data) {
          log.dataStr = JSON.stringify(log.data, null, 2);
          log.dataStr = "\n" + log.dataStr;

        }
        if (log.request_payload) {
          log.requestStr = JSON.stringify(log.request_payload, null, 2);
          log.requestStr = "\n" + log.requestStr;
        }
        return log;
      });
    });
  }

 updateAuthorizationKeys(obj: any, newValue: string): void {
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      if (key === "Authorization") {
        obj[key] = newValue;
      } else if (typeof obj[key] === "object" && obj[key] !== null) {
        this.updateAuthorizationKeys(obj[key], newValue); // Recursively traverse nested objects or arrays
      }
    }
  }
}

  protected readonly formatTimeYMD = formatDateDMY;
  protected readonly formatTimeHM = formatTimeHM;
  protected readonly JSON = JSON;
  protected readonly formatTimeHMSS = formatTimeHMSS;
}
