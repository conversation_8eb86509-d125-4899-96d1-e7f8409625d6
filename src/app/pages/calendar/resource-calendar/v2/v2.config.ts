import { BryntumCalendarProps, BryntumGridProps } from '@bryntum/calendar-angular';
import {EventModel, EventStore, LocaleHelper, LocaleManager, RadioGroup, Store, StringHelper, EventEdit, Calendar, Popup, DateHelper, Label, Widget, Column, EventTooltip, Model, ResourceModel} from '@bryntum/calendar';
import { CustomEventModel } from './custom-classes.model';
import { he, id } from 'date-fns/locale';
import '@bryntum/calendar/locales/calendar.locale.No.js'



/**  Localization config **/

export const localeNo = {
        localeName : 'No',
        localeDesc : 'Norsk',
        localeCode : 'no',


        List : {
        },

        Column: {
        'Duration': 'Varighet',
        'Jobs' : 'Jobber',
        },

        Widget : {
        'No contractor' : 'Ingen underleverandør',
        'Show unassigned' : 'Vis ikke tildelt',
        'Wk Emp' : 'Uke ansatt',
        'Mo Emp' : 'Mnd ansatt',
        'Width': 'Bredde',
        'Unscheduled Events': 'Ikke tildelte jobber',
        'Show weekends': 'Vis helger',
        'Loading events...': 'Laster hendelser...',
        'Filter events' : 'Filtrer hendelser',
        'Resource': 'Ressurser',
        'Emoloyees': 'Ansatt',
        'Time:' : 'Tid:',
        'Customer': 'Kunde',
        'Address' : 'Adresse',
        'Status': 'Status',
        'Assigned to': 'Tildelt',
        'Time': 'Tid',
        'Start': 'Start',
        'h': 't',
        'm': 'm',
        'Comment:': 'Kommentar:',
        'Description:' : 'Beskrivelse:',
        'Employee:': 'Ansatt',
        'Employees:': 'Ansatte:',
        'Customer contact': 'Kontaktperson',
        'No employees added': 'Ingen ansatte lagt til',
        'No customer': 'Ingen kunde',
        "No address": "Ingen addresse",
        "No name": "Ingen jobbnavn",
        'No resources': 'Ingen ressurser lagt til',
        'No address added': 'Ingen addresse lagt til',
        'No description added': 'Ingen beskrivelse lagt til',
        'No comment added': 'Ingen kommentar lagt til',
        'buttonEventType': 'Eventtyper',
        'etOrder': 'Ordre',
        'etEmployee': 'Ansatteventer',
        'etOther': 'Annet',
        'Order statuses': 'Ordrestatuser',
        'Job status' : 'Jobbstatus',
        'Order status' : 'Ordrestatus',
        'Draft': 'Utkast',
        'Quote sent' : 'Tilbud sendt',
        'Accepted by customer': 'Akseptert av kunde',
        'Confirmed': 'Bekreftet',
        'Job started': 'Jobb startet',
        'Job finished': 'Jobb ferdig',
        'Sent to payment': 'Sendt til betaling',
        'Closed': 'Avsluttet',
        'Event': 'Hendelse',
        'Order': 'Ordre',
        'Created by' : 'Opprettet av',
        'Open order': 'Åpne ordre',
        'Open job': 'Åpne jobb',
        'Weekly resources': 'Ukentlige ressurser',
        },

        EventEdit : {
        "decade" : "Alle",
        },

        AgendaView : {
        Agenda : 'Liste'
        },

        DayView : {
            Day : 'Dag'
        },

        WeekView : {
            Week : 'Uke'
        },

        MonthView : {
            Month : 'Måned'
        },

        DayResourceView : {
            Resource : 'Daglige ressurser',
            Label : 'Daglige ressurser'
        },

        ResourceView : {
            resourceView : 'Ukentlige ressurser',
            label : 'Ukentlige ressurser'
        },

        DateHelper : {
            weekStartDay : 1, // 0-6 (0: Sunday, 1: Monday etc.)
    }
}

LocaleHelper.publishLocale(localeNo);

/**  Calendar config **/

export const calendarProps = (
    onValueChanges: (value: any, source: any) => void,
    openWorkOrderModal: (eventRecord: any) => void,
    openOrderModal: (eventRecord: any) => void,
    companyTitleTags: string[]): BryntumCalendarProps => ({

    // Start life looking at this date
    date: new Date(),
    stateful: true,
    stateId: 'user-calendar-state',
    weekStartDay: 1,
    appendTo: 'container',
    weekExpanderFeature: true,
    dragFeature: {
        removeFromExternalStore: false
    },

    externalEventSourceFeature: {
        grid: 'unscheduled'
    },

    modeDefaults: {
        timeFormat: 'HH:mm',
        dayStartTime: '06:00',
        hourHeight: 100,
        zoomOnMouseWheel: true
    },

    eventStore: {
        modelClass: CustomEventModel
    },

    eventTooltipFeature: {
        align: 'l-r',
        tooltip: {
            tools: {
                delete: false,
            }
        },
        renderer: data => {
            const eventRecord = data.eventRecord as CustomEventModel;

            const translations = {
                status: Widget.L('Job status'),
                orderStatus: Widget.L('Order status'),
                customer: Widget.L('Customer'),
                customerContact: Widget.L('Customer contact'),
                addresses: Widget.L('Address'),
                assignedTo: Widget.L('Assigned to'),
                time: Widget.L('Time'),
                noCustomer: Widget.L('No customer'),
                noResources: Widget.L('No resources'),
                noAddresses: Widget.L('No address added'),
                unnamedResource: Widget.L('No employees added'),
                createdBy: Widget.L('Created by'),
            };

            // Check if `address` is an array and iterate over it if true
            const addressList = Array.isArray(eventRecord.address)
                ? eventRecord.address.map(addr => `<li>${StringHelper.encodeHtml(addr)}</li>`).join('')
                : StringHelper.encodeHtml(eventRecord.address || translations.noAddresses);

            // Check if `resource` exists and is an array or object
            let resourceList = translations.noResources;
            if (Array.isArray(eventRecord.resource)) {
                resourceList = eventRecord.resource
                    .map(res => `<li>${StringHelper.encodeHtml(res.name || translations.unnamedResource)}</li>`)
                    .join('');
            } else if (eventRecord.resource && eventRecord.resource.name) {
                resourceList = StringHelper.encodeHtml(eventRecord.resource.name || translations.unnamedResource);
            }

            if (eventRecord.orderId) {
                // Version 1: If `orderId` exists
                return `
                    <dl>
                        ${eventRecord.get('workOrderStatusName') ? `<dt>${translations.status}:</dt><dd>${eventRecord.workOrderStatusName}</dd>` : ''}
                        ${eventRecord.get('orderStatusName') ? `<dt>${translations.orderStatus}:</dt><dd>${eventRecord.orderStatusName}</dd>` : ''}
                        <dt>${translations.customer}:</dt>
                        <dd>
                            ${eventRecord.service_recipient_name
                                ? StringHelper.encodeHtml(eventRecord.service_recipient_name)
                                : translations.noCustomer}
                        </dd>
                        ${eventRecord.get('affiliate_contact_name') ? `<dt>${translations.customerContact}:</dt><dd>${StringHelper.encodeHtml(eventRecord.affiliate_contact_name)}</dd>` : ''}
                        <dt>${translations.addresses}:</dt>
                        <dd>
                            <ul style="padding:0">
                                ${addressList}
                            </ul>
                        </dd>
                        <dt>${translations.assignedTo}:</dt>
                        <dd>
                            <ul style="padding:0">
                                ${resourceList}
                            </ul>
                        </dd>
                        <dt>${translations.time}:</dt>
                        <dd>
                            ${DateHelper.format(new Date(eventRecord.startDate), 'LT')} - ${DateHelper.format(new Date(eventRecord.endDate), 'LT')}
                        </dd>
                        <dt>${translations.createdBy}:</dt>
                        <dd>
                            ${eventRecord.created_by_name}
                        </dd>
                    </dl>
                `;
            } else {
                // Version 2: If `orderId` does not exist
                return `
                    <dl>
                        ${eventRecord.get('workOrderStatusName') ? `<dt>${translations.status}:</dt><dd>${eventRecord.workOrderStatusName}</dd>` : ''}
                        <dt>${translations.assignedTo}:</dt>
                        <dd>
                            <ul style="padding:0">
                                ${resourceList}
                            </ul>
                        </dd>
                        <dt>${translations.time}:</dt>
                        <dd>
                            ${DateHelper.format(new Date(eventRecord.startDate), 'LT')} - ${DateHelper.format(new Date(eventRecord.endDate), 'LT')}
                        </dd>
                    </dl>
                `;
            }
        }
    },

    eventContextMenu: false,

    eventMenuFeature: {

        items: {
            duplicate: null,
            delete: true,
        }
    },

    // Show event presence as a count badge in the date picker cells
    datePicker : {
        showEvents: 'count',

    },



    eventEditFeature: {
        items: {
            nameField: {
                label: 'Navn',
                name: 'name',
                labelCls: 'labelField',
                weight: 20,
                labelPosition: 'above',
            },
            eventTypeField: {
                type: "radiogroup",
                name: "eventTypeId",
                label: "",
                labelPosition: "before",
                weight: 10,
                items: [
                    {
                        type: "radio",
                        name: "eventTypeId",
                        text: Widget.L('Order'),
                        value: 0,
                        checkedValue: 0,
                        cls: "customRadioButton",
                        checked: true
                    },
                    {
                        type: "radio",
                        name: "eventTypeId",
                        text: Widget.L('Event'),
                        value: 1,
                        cls: "customRadioButton",
                        checkedValue: 1
                    }
                ],
                listeners: {
                    change: ({ value, source }: { value: any, source: any}) => {
                        onValueChanges(value, source.name);
                    }
                }
            },
            workOrderTemplateField: {
                label: 'Jobb-mal',
                type: 'combo',
                store: {},
                name: 'work_order_template_id',  // Bind it to 'product_id'
                placeholder: 'Velg mal',
                displayField: 'name',  // Field used to display in the dropdown
                valueField: 'id',  // Field used as the value
                weight: 30,
                labelPosition: 'above',
                hidden: true
            },
            resourceField: {
                label: 'Ansatte og ressuser',
                labelPosition: 'above',
                placeholder : 'Velg ressuser',
                store: {
                    groupers: [
                        { field: 'group' },
                ]
            },
                primaryFilter: {
                    operator: null,
                }
            },
            colorField : {
                hidden: false,
                picker: {
                    colors: [
                        { color: '#FF0000', text: 'Red' },
                        { color: '#FFC0CB', text: 'Pink' },
                        { color: '#800080', text: 'Purple' },
                        { color: '#FF00FF', text: 'Magenta' },
                        { color: '#EE82EE', text: 'Violet' },
                        { color: '#4B0082', text: 'Indigo' },
                        { color: '#0000FF', text: 'Blue' },
                        { color: '#00FFFF', text: 'Cyan' },
                        { color: '#008080', text: 'Teal' },
                        { color: '#008000', text: 'Green' },
                        { color: '#66CDAA', text: 'Gantt Green' },
                        { color: '#00FF00', text: 'Lime' },
                        { color: '#FFFF00', text: 'Yellow' },
                        { color: '#FFA500', text: 'Orange' },
                        { color: '#FF4500', text: 'Deep Orange' },
                        { color: '#808080', text: 'Gray' },
                        { color: '#D3D3D3', text: 'Light Gray' }
                    ],
                      colorClasses: null,
                },
                name: 'custom_event_color'
            },
            EventColorField: {
                hidden: false,
            },
            createOrderField: {
                label: 'Create Order',
                type: 'checkbox',
                name: 'create_order',
                cls: 'createOrderCheckbox',
                editable: false,
                value: false,
                hidden: false,
                weight: 100,
            },
            orderIdField: {
                label: 'Order ID',
                type: 'number',
                name: 'orderId',
                weight: 10,
                hidden: true
            },
            workOrderIdField: {
                label: 'Work Order ID',
                type: 'number',
                name: 'workOrderId',
                weight: 20,
                hidden: true
            },
            recurrenceCombo: null,
            editRecurrenceButton: null
        },
        editorConfig: {
            // modal: true,
            showEventColor: true,
            autoClose: false,
            centered: true,
            anchor: null,
            titleRenderer(eventRecord: any) {
                if (eventRecord.originalData.orderId) {
                    return 'Rediger ordre';
                } else if (eventRecord.meta.isCreating) {
                    return 'Opprett ordre eller hendelse';
                } else
                    return 'Rediger hendelse';
            },
            bbar: {
                items: {
                    deleteButton: {
                        weigth: 100,
                        cls:'b-button-secondary'
                    },
                    saveButton: {
                        weigth: 200,
                    },
                    customOrderButton: {
                        weight: 200,
                        type: 'button',
                        hidden: true,
                        text: Widget.L('Open order'),
                        onClick: 'up.openOrderModal',
                        cls: 'b-button-primary',
                    },
                    customWorkOrderButton: {
                        weight: 200,
                        type: 'button',
                        hidden: true,
                        text: Widget.L('Open job'),
                        onClick: 'up.openWorkOrderModal',
                        cls: 'b-button-primary',
                    },
                    cancelButton: null
                }
            },
            openWorkOrderModal(record: EventModel) {
                openWorkOrderModal(record);
            },
            openOrderModal(record: EventModel) {
                openOrderModal(record);
            }
        },


    },

    modes: {
        day: {
            showResourceAvatars: 'last',
            hideNonWorkingDays: false,
            eventRenderer: ({ eventRecord, renderData }) => {
                const event = eventRecord as CustomEventModel;

                // Set the event class for styling
                renderData.cls = { ...renderData.cls, 'order-event': true };

                if (event.orderId != null) {
                    // Define mapping between tags and event properties
                    const tagMapping: Record<string, string> = {
                        workOrderTitle: event.name || '',
                        customerName: event.service_recipient_name || '',
                        paymentRecipientName: event.payment_recipient_name || '',
                        orderId: event.orderNumber || '',
                        workOrderId: event.workOrderNumber || '',
                        address: event.address || '',
                    };

                    // Process companyTitleTags
                    const storedTags = localStorage.getItem('companyTitleTags');
                    const companyTitleTags: string[] = storedTags ? JSON.parse(storedTags) : ['workOrderTitle', 'address', 'customerName'];

                    // Handle duplicate customer names
                    if (companyTitleTags.includes('customerName') && companyTitleTags.includes('paymentRecipientName')) {
                        if (tagMapping['customerName'] === tagMapping['paymentRecipientName']) {
                            const prIndex = companyTitleTags.indexOf('paymentRecipientName');
                            const srIndex = companyTitleTags.indexOf('customerName');
                            if (prIndex > srIndex) {
                                companyTitleTags.splice(prIndex, 1);
                            } else {
                                companyTitleTags.splice(srIndex, 1);
                            }
                        }
                    }

                    // Generate event title
                    const eventTitle = companyTitleTags
                        .map((tag) => {
                            if (tag === 'orderId') {
                                return '#' + tagMapping[tag] || '';
                            } else if (tag === 'workOrderId') {
                                return '#' + tagMapping[tag] || '';
                            } else {
                                return tagMapping[tag] || '';
                            }
                        })
                        .filter((value) => value)
                        .join(' - ');

                    // Calculate event duration
                    const start = new Date(event.startDate);
                    const end = new Date(event.endDate);
                    const durationMs = end.getTime() - start.getTime();
                    const durationInMinutes = Math.floor(durationMs / 1000 / 60);
                    const durationInHours = durationInMinutes / 60;
                    const hours = Math.floor(durationInMinutes / 60);
                    const minutes = durationInMinutes % 60;

                    // Check if the event has a duration of 24 hours or more
                    const isAllDayEvent = durationInHours >= 24;

                    // Check if the event spans multiple days
                    const isMultiDayEvent = start.getDate() !== end.getDate() ||
                                           start.getMonth() !== end.getMonth() ||
                                           start.getFullYear() !== end.getFullYear();

                    // Fetch translations for "h" and "m" using Bryntum's Widget.L
                    const translatedHours = Widget.L('h'); // Translation key for "h"
                    const translatedMinutes = Widget.L('m'); // Translation key for "m"

                    // Return a DOM config object
                    return {
                        children: [
                            // Only include duration if not an all-day event and not a multi-day event
                            ...(!isAllDayEvent && !isMultiDayEvent ? [{
                                tag: 'div',
                                className: 'b-event-header',
                                html: `<b style="font-size: 1.4em;">${hours}${translatedHours} ${minutes}${translatedMinutes}</b>`
                            }] : []),
                            {
                                tag: 'div',
                                className: 'b-cal-event-desc b-cal-event-desc-complex',
                                children: [{
                                    tag: 'span',
                                    className: 'b-event-name',
                                    html: StringHelper.encodeHtml(eventTitle)
                                }]
                            }
                        ]
                    };
                } else {
                    // Calculate event duration for non-order events too
                    const start = new Date(event.startDate);
                    const end = new Date(event.endDate);
                    const durationMs = end.getTime() - start.getTime();
                    const durationInMinutes = Math.floor(durationMs / 1000 / 60);
                    const durationInHours = durationInMinutes / 60;
                    const hours = Math.floor(durationInMinutes / 60);
                    const minutes = durationInMinutes % 60;

                    // Check if the event has a duration of 24 hours or more
                    const isAllDayEvent = durationInHours >= 24;

                    // Check if the event spans multiple days
                    const isMultiDayEvent = start.getDate() !== end.getDate() ||
                                           start.getMonth() !== end.getMonth() ||
                                           start.getFullYear() !== end.getFullYear();

                    // Fetch translations for "h" and "m" using Bryntum's Widget.L
                    const translatedHours = Widget.L('h'); // Translation key for "h"
                    const translatedMinutes = Widget.L('m'); // Translation key for "m"

                    // Return a DOM config object
                    return {
                        children: [
                            // Only include duration if not an all-day event and not a multi-day event
                            ...(!isAllDayEvent && !isMultiDayEvent ? [{
                                tag: 'div',
                                className: 'b-event-header',
                                html: `<b style="font-size: 1.2em;">${hours}${translatedHours} ${minutes}${translatedMinutes}</b>`
                            }] : []),
                            {
                                tag: 'div',
                                className: 'b-cal-event-desc b-cal-event-desc-complex',
                                children: [{
                                    tag: 'span',
                                    className: 'b-event-name',
                                    html: StringHelper.encodeHtml(eventRecord.name)
                                }]
                            }
                        ]
                    };
                }
            }
        },
        week: {
            eventRenderer: ({ eventRecord, renderData }) => {
                const event = eventRecord as CustomEventModel;

                // Set the event class for styling
                renderData.cls = { ...renderData.cls, 'order-event': true };

                if (event.orderId != null) {
                    // Define mapping between tags and event properties
                    const tagMapping: Record<string, string> = {
                        workOrderTitle: event.name || '',
                        customerName: event.service_recipient_name || '',
                        paymentRecipientName: event.payment_recipient_name || '',
                        orderId: event.orderNumber || '',
                        workOrderId: event.workOrderNumber || '',
                        address: event.address || '',
                    };

                    // Process companyTitleTags
                    const storedTags = localStorage.getItem('companyTitleTags');
                    const companyTitleTags: string[] = storedTags ? JSON.parse(storedTags) : ['workOrderTitle', 'address', 'customerName'];

                    // Handle duplicate customer names
                    if (companyTitleTags.includes('customerName') && companyTitleTags.includes('paymentRecipientName')) {
                        if (tagMapping['customerName'] === tagMapping['paymentRecipientName']) {
                            const prIndex = companyTitleTags.indexOf('paymentRecipientName');
                            const srIndex = companyTitleTags.indexOf('customerName');
                            if (prIndex > srIndex) {
                                companyTitleTags.splice(prIndex, 1);
                            } else {
                                companyTitleTags.splice(srIndex, 1);
                            }
                        }
                    }

                    // Generate event title
                    const eventTitle = companyTitleTags
                        .map((tag) => {
                            if (tag === 'orderId') {
                                return '#' + tagMapping[tag] || '';
                            } else if (tag === 'workOrderId') {
                                return '#' + tagMapping[tag] || '';
                            } else {
                                return tagMapping[tag] || '';
                            }
                        })
                        .filter((value) => value)
                        .join(' - ');

                    // Calculate event duration
                    const start = new Date(event.startDate);
                    const end = new Date(event.endDate);
                    const durationMs = end.getTime() - start.getTime();
                    const durationInMinutes = Math.floor(durationMs / 1000 / 60);
                    const durationInHours = durationInMinutes / 60;
                    const hours = Math.floor(durationInMinutes / 60);
                    const minutes = durationInMinutes % 60;

                    // Check if the event has a duration of 24 hours or more
                    const isAllDayEvent = durationInHours >= 24;

                    // Check if the event spans multiple days
                    const isMultiDayEvent = start.getDate() !== end.getDate() ||
                                           start.getMonth() !== end.getMonth() ||
                                           start.getFullYear() !== end.getFullYear();

                    // Fetch translations for "h" and "m" using Bryntum's Widget.L
                    const translatedHours = Widget.L('h'); // Translation key for "h"
                    const translatedMinutes = Widget.L('m'); // Translation key for "m"

                    // Return a DOM config object instead of HTML string
                    return {
                        children: [
                            // Only include duration if not an all-day event and not a multi-day event
                            ...(!isAllDayEvent && !isMultiDayEvent ? [{
                                tag: 'div',
                                className: 'b-event-header',
                                html: `<b style="font-size: 1.4em;">${hours}${translatedHours} ${minutes}${translatedMinutes}</b>`
                            }] : []),
                            {
                                tag: 'div',
                                className: 'b-cal-event-desc b-cal-event-desc-complex',
                                children: [{
                                    tag: 'span',
                                    className: 'b-event-name',
                                    html: StringHelper.encodeHtml(eventTitle)
                                }]
                            }
                        ]
                    };
                } else {
                    // Calculate event duration for non-order events too
                    const start = new Date(event.startDate);
                    const end = new Date(event.endDate);
                    const durationMs = end.getTime() - start.getTime();
                    const durationInMinutes = Math.floor(durationMs / 1000 / 60);
                    const durationInHours = durationInMinutes / 60;
                    const hours = Math.floor(durationInMinutes / 60);
                    const minutes = durationInMinutes % 60;

                    // Check if the event has a duration of 24 hours or more
                    const isAllDayEvent = durationInHours >= 24;

                    // Check if the event spans multiple days
                    const isMultiDayEvent = start.getDate() !== end.getDate() ||
                                           start.getMonth() !== end.getMonth() ||
                                           start.getFullYear() !== end.getFullYear();

                    // Fetch translations for "h" and "m" using Bryntum's Widget.L
                    const translatedHours = Widget.L('h'); // Translation key for "h"
                    const translatedMinutes = Widget.L('m'); // Translation key for "m"

                    // Return a DOM config object
                    return {
                        children: [
                            // Only include duration if not an all-day event and not a multi-day event
                            ...(!isAllDayEvent && !isMultiDayEvent ? [{
                                tag: 'div',
                                className: 'b-event-header',
                                html: `<b style="font-size: 1.2em;">${hours}${translatedHours} ${minutes}${translatedMinutes}</b>`
                            }] : []),
                            {
                                tag: 'div',
                                className: 'b-cal-event-desc b-cal-event-desc-complex',
                                children: [{
                                    tag: 'span',
                                    className: 'b-event-name',
                                    html: StringHelper.encodeHtml(eventRecord.name)
                                }]
                            }
                        ]
                    };
                }
            },
            columnHeaderRenderer({ events }) {
                const totalMinutes = events.reduce((totalMinutes, event) => {
                    // Convert the event duration to minutes
                    return totalMinutes + DateHelper.as('minute', event.fullDuration.magnitude, event.fullDuration.unit);
                }, 0);

                const hours = Math.floor(totalMinutes / 60);
                const minutes = totalMinutes % 60;

                // Fetch translations for "h" and "m" using Widget.L
                const translatedHours = Widget.L('h'); // Translation key for "h"
                const translatedMinutes = Widget.L('m'); // Translation key for "m"

                return `${hours}${translatedHours} ${minutes}${translatedMinutes}`;
            },


            showResourceAvatars: 'last',
            hideNonWorkingDays: false,
        },
        month: {
            eventRenderer: ({ eventRecord }) => {
                const event = eventRecord as CustomEventModel;

                if (event.orderId != null) {

                // Define mapping between tags and event properties
                const tagMapping: Record<string, string> = {
                    workOrderTitle: event.name || '',
                    customerName: event.service_recipient_name || '',
                    orderId: event.orderNumber || '',
                    workOrderId: event.workOrderNumber || '',
                    address: event.address || '',
                };

                // Attempt to fetch `companyTitleTags` from localStorage
                const storedTags = localStorage.getItem('companyTitleTags');
                const companyTitleTags: string[] = storedTags
                    ? JSON.parse(storedTags)
                    : ['workOrderTitle', 'address', 'customerName']; // Default fallback

                // Generate event title based on titleTags order
                const eventTitle = companyTitleTags
                    .map((tag) => tagMapping[tag] || '') // Map tags to event properties
                    .filter((value) => value) // Remove any empty values
                    .join(' - '); // Combine with a delimiter

                return `
                    <span class="b-event-name">${StringHelper.encodeHtml(eventTitle)}</span>

                `;
                } else {
                    return `
                    ${eventRecord.name}
                `;
                }
            },
            hideNonWorkingDays: false,
        },
        year: false,


        agenda: {
            hideNonWorkingDays: false,
            eventRenderer: ({ eventRecord }) => {

                const event = eventRecord as CustomEventModel;
                if (event.orderId != null) {
                // Define mapping between tags and event properties
                const tagMapping: Record<string, string> = {
                    workOrderTitle: event.name || '',
                    customerName: event.service_recipient_name || '',
                    orderId: event.orderNumber || '',
                    workOrderId: event.workOrderNumber || '',
                    address: event.address || '',
                };

                // Attempt to fetch `companyTitleTags` from localStorage
                const storedTags = localStorage.getItem('companyTitleTags');
                const companyTitleTags: string[] = storedTags
                    ? JSON.parse(storedTags)
                    : ['workOrderTitle', 'address', 'customerName']; // Default fallback

                // Generate event title based on titleTags order
                const eventTitle = companyTitleTags
                    .map((tag) => tagMapping[tag] || '') // Map tags to event properties
                    .filter((value) => value) // Remove any empty values
                    .join(' - '); // Combine with a delimiter

                return `
                    <span class="b-event-name">${StringHelper.encodeHtml(eventTitle)}</span>

                `;
                } else {
                    return `
                    ${eventRecord.name}
                `;
                }
            },
        },

        dayresource: {
            weight: 40,
            hideNonWorkingDays: false,
            minResourceWidth: '10em',
            shortEventDuration: '1 hour',
            showHeaderAvatars: true,
            allDayEvents: {
                // @ts-ignore
                resourceHeaderExtraRenderer(resource, { resourceDayEvents }) {
                    const totalMinutes = resourceDayEvents.reduce((totalMinutes : number, event: EventModel) => {
                        // Convert the event duration to minutes
                        return totalMinutes + DateHelper.as('minute', event.fullDuration.magnitude, event.fullDuration.unit);
                    }, 0);
    
                    const hours = Math.floor(totalMinutes / 60);
                    const minutes = totalMinutes % 60;
    
                    // Fetch translations for "h" and "m" using Widget.L
                    const translatedHours = Widget.L('h'); // Translation key for "h"
                    const translatedMinutes = Widget.L('m'); // Translation key for "m"
    
                    return `
                                <div class="header">Total tid ${hours}${translatedHours} ${minutes}${translatedMinutes}</div>`;
                },
            }
        },

        resource : {
            title : Widget.L('Weekly resources'),
            type               : 'resource',
            resourceWidth      : '30em',
            hideNonWorkingDays: false,
        },
    },

    tbar: {
        items: {
            orderStatusMenuButton: {
                type: 'button',
                text: Widget.L('Order statuses'),
                menu: [
                    {
                        text: Widget.L('Draft'),
                        id: '-1',
                        checked: true,

                    },
                    {
                        text: Widget.L('Quote sent'),
                        id: '0',
                        checked: true,

                    },
                    {
                        text: Widget.L('Accepted by customer'),
                        id: '1',
                        checked: true,

                    },
                    {
                        text: Widget.L('Confirmed'),
                        id: '2',
                        checked: true,

                    },
                    {
                        text: Widget.L('Job started'),
                        id: '4',
                        checked: true,

                    },
                    {
                        text: Widget.L('Job finished'),
                        id: '5',
                        checked: true,

                    },
                    {
                        text: Widget.L('Closed'),
                        id: '7',
                        checked: true,

                    }
                ],
            },
            contractorMenuButton: {
                type: 'button',
                text: Widget.L('No contractor'),
                menu: [],
            },
            modeSelector : {
                minified: true,
              },
        },

    },


    sidebar: {
        items: {
            eventFilter : {
                placeholder: Widget.L('Filter events'),
                weight : 100,
            },
            resourceFilterFilter: null,
            resourceFilter : {
                weight : 300,
                selectAllItem : true,
                store: {
                    groupers: [
                        { field: 'group' },
                ]
            },
                cls : 'custom-resource-filter',
              },
              showUnassigned : {
                weight : 200,
                name: 'showUnassigned',
                type   : 'slidetoggle',
                label  : Widget.L('Show unassigned'),
                checked : true,
                listeners: {
                    change: ({ value, source }: { value: any, source: any}) => {
                        onValueChanges(value, source.name);
                    }
                }
              },
        }
    },
});





/**  Grid config **/
// Create a function that accepts a store as a parameter
export const getGridProps = (unassignedEventStore: EventStore): BryntumGridProps => ({
    id: 'unscheduled',
    title: 'Unscheduled Events',
    collapsible: true,
    flex: '0 0 300px',
    ui: 'calendar-banner',
    store: unassignedEventStore,
    stripeFeature: true,
    sortFeature: 'startDate',
    cellEditFeature: false,
    groupFeature: {
        field: 'eventDate',
        cls: 'b-grid-group-cell',
        size: {
            height: 10
        },
        renderer: ({ groupRowFor, count, isFirstColumn, groupColumn, size }) => {
            // You don't need to explicitly use `size` for now
            size.height = 50; // Optionally set a value if needed in the future

            const formattedDate = DateHelper.format(new Date(groupRowFor), 'dddd DD.MM.YY');
            const columnName = groupColumn ? groupColumn.text : 'Unknown column';

            return `
                <div>
                    ${isFirstColumn ? `<span class="first-column-indicator text-capitalize"><strong>${formattedDate}</strong></span>` : ''}
                </div>
            `;
        }
    },
    columns: [
        {
            type: 'column',
            text: Column.L('Jobs'),
            flex: 1,
            autoHeight: true,
            field: 'name',
            htmlEncode: false,
            renderer: ({ record }: { record: Model }) => {
                const event = record as CustomEventModel;

                // Fetch translations for placeholders
                const noCustomer = Widget.L('No customer'); // Translation key for "No customer"
                const noAddress = Widget.L('No address'); // Translation key for "No address"
                const noName = Widget.L('No name'); // Translation key for "No name"

                // Only add fields that have data
                const serviceRecipientName = event.service_recipient_name
                    ? `${event.service_recipient_name} <br>`
                    : `${noCustomer} <br>`;
                const address = event.address
                    ? `${event.address} <br>`
                    : `${noAddress} <br>`;
                const name = event.name
                    ? `${event.name} <br>`
                    : `${noName} <br>`;
                const date = `${DateHelper.format(new Date(event.startDate), 'dddd DD.MM.YY LT')}`;

                return `<i class="${event.iconCls}"></i>  <div class="py-2">
                    ${serviceRecipientName || ''}
                    ${address || ''}
                    ${name}
                     </div>`;
            }
        },
        {
            type: 'column',
            text: Column.L('Start'),
            groupable: false,
            width: 50,
            field: 'time',
            htmlEncode: false,

            renderer: ({ record }: { record: Model }) => {
                const event = record as CustomEventModel;

                // Only add fields that have data
                const date = `${DateHelper.format(new Date(event.startDate), 'LT')}`;

                return `
                    ${date}`;
            }
        },
        {
            type: 'column',
            text: Column.L('Duration'),
            width: 70,
            align: 'right',
            editor: false,
            field: 'duration',
            renderer: ({ record }: { record: Model }) => {
                const event = record as EventModel;
                // Calculate event duration
                const start = new Date(event.startDate);
                const end = new Date(event.endDate);
                const durationMs = end.getTime() - start.getTime();
                const durationInMinutes = Math.floor(durationMs / 1000 / 60);
                const hours = Math.floor(durationInMinutes / 60);
                const minutes = durationInMinutes % 60;

                // Fetch translations for "h" and "m" using Widget.L
                const translatedHours = Widget.L('h'); // Translation key for "h"
                const translatedMinutes = Widget.L('m'); // Translation key for "m"

                return `${hours}${translatedHours} ${minutes}${translatedMinutes}`;
            }
        }
    ],
    autoHeight: true,
    rowHeight: 100,
});
