import {AfterViewInit, Component, ElementRef, Input, OnInit, Renderer2, ViewChild, ViewEncapsulation} from '@angular/core';
import {BryntumCalendarComponent, BryntumCalendarModule} from '@bryntum/calendar-angular';
import {AssignmentModel, AssignmentStore, Button, Calendar, Checkbox, CrudManager, DatePicker, EventStore, FilterField, Grid, LocaleManager, Mask, Menu, MenuItem, MonthView, ResourceFilter, ResourceStore, StateProvider, Widget} from '@bryntum/calendar';
import {calendarProps, getGridProps} from './v2.config';
import {EventService} from 'src/app/@shared/services/event.service';
import {_CRM_AFF_1, _CRM_EVT_1, _CRM_RSC_2, _USM_ENT_0} from 'src/app/@shared/models/input.interfaces';
import {EmployeeService} from 'src/app/@shared/services/employee.service';
import {ToastService} from 'src/app/@core/services/toast.service';
import {OrderDetailsV2Component} from "../../../orders/order-details-v2/order-details-v2.component";
import {OverlayItem, OverlayService} from "../../../../@shared/services/overlay.service";
import {CustomEventModel} from './custom-classes.model';
import {ProductService} from 'src/app/@shared/services/product.service';
import {BryntumGridComponent} from '@bryntum/calendar-angular-view';
import {BryntumChangeSetResponse} from 'src/app/@shared/models/events.interfaces';
import {TemplateService} from 'src/app/@shared/services/templates.service';
import {WorkOrderTemplateResponse} from 'src/app/@shared/models/templates.interfaces';
import {ResourceService} from 'src/app/@shared/services/resource.service';
import {catchError, forkJoin, map, of} from 'rxjs';
import {AffiliateService} from 'src/app/@shared/services/affiliate.service';
import {AffiliateResponse} from 'src/app/@shared/models/affiliate.interfaces';
import {LangChangeEvent, TranslateService} from '@ngx-translate/core';
import {CompanyService} from 'src/app/@shared/services/company.service';
import {StorageService} from 'src/app/@core/services/storage.service';
import {WorkOrderDetailsComponent} from 'src/app/pages/work-orders/components/work-order-details/work-order-details.component';
import {FormsModule} from "@angular/forms";
import {CommonModule} from "@angular/common";
import { environment } from 'src/environments/environment';
import { AuthService } from 'src/app/@core/services/auth.service';
import { JwtService } from 'src/app/@core/services/jwt.service';
import { convertPayloadDatetime, convertResponseDatetime, formatDateYMD } from 'src/app/@core/utils/utils.service';


@Component({
    selector: 'app-v2',
    templateUrl: './v2.component.html',
    styleUrls: ['./v2.component.scss'],
    encapsulation: ViewEncapsulation.None,
    standalone: true,
  imports: [FormsModule, CommonModule, BryntumCalendarModule]
})

export class V2Component implements OnInit, AfterViewInit {
    @Input() contactView: boolean = false;

    @ViewChild('mainCalendarDiv') mainCalendarDiv: ElementRef;

    resources: any[] = [];
    events:  any[] = [];
    assignments: any[] = [];
    crudManager: CrudManager;

    eventStore: EventStore = new EventStore({
        modelClass: CustomEventModel  // Use CustomEventModel here
    });
    resourceStore: ResourceStore = new ResourceStore();
    assignmentStore: AssignmentStore = new AssignmentStore();
    unassignedEventStore: EventStore = new EventStore();
    private datePicker!: DatePicker
    currentDatePickerStartDate: Date;
    currentDatePickerEndDate: Date;
    currentEventRecord: CustomEventModel;
    workOrderTemplates: WorkOrderTemplateResponse[] = [];
    contractors: AffiliateResponse[] = [];
    selectedDate: Date
    calendarMode: string = '';
    debounceTimeout: any = null; // For debouncing
    currentMode: string = 'month';
    companyTitleTags: string[] = ['workOrderTitle', 'address', 'customerName'];
    dateFrom: Date;
    dateTo: Date;
    contractorId: number;



    constructor(private eventService: EventService,
        private employeeService: EmployeeService,
        private resourceService: ResourceService,
        private toastService: ToastService,
        private overlayService: OverlayService,
        private affiliateService: AffiliateService,
        private productSerivice: ProductService,
        private renderer: Renderer2,
        private templateService: TemplateService,
        private translateService: TranslateService,
        private companyService:  CompanyService,
        private storageService: StorageService,
        private jwtService: JwtService,

    ) { }


    @ViewChild(BryntumCalendarComponent) calendarComponent: BryntumCalendarComponent;
    @ViewChild(BryntumGridComponent) gridComponent: BryntumGridComponent;

    private calendar: Calendar;
    private resourceFilter: ResourceFilter;
    private resourceFilterFilter: FilterField;
    private autoRowHeight: Checkbox;
    private grid: Grid

    ngOnInit(): void {
        // this.setupCrudManager();
        this.getAllWorkOrderTemplates()
      }
    // Insert your calendarProps here directly
    calendarProps = calendarProps(
        this.onValueChanges.bind(this),
        this.openWorkOrderModal.bind(this),
        this.openOrderModal.bind(this),
        this.companyTitleTags
    );

    gridProps = getGridProps(this.unassignedEventStore);  // Pass the store dynamically

    // Setup CrudManager with stores and API transport
    setupCrudManager(): void {
        this.crudManager = new CrudManager({
          onLoad: (response: any) => {
            if (response && response.response && response.response.events) {
              const allEvents = response.response.events.rows;
              const allAssignments = response.response.assignments.rows;
              let unAssignedEvents: any[] = [];
              convertResponseDatetime(allEvents);

              // Filter events with no assigned resources (unassigned) and assign them to unassignedEventStore
              unAssignedEvents = allEvents.filter((event: any) => {
                return allAssignments.some((assignment: any) => assignment.eventId === event.id);
              });
              unAssignedEvents.forEach((event: any) => {
                event.startDate.setSeconds(1)
              });
              this.unassignedEventStore.data = unAssignedEvents;
              
            }
            

          },  
          onBeforeSync: (payload: any) => {
            if (payload.pack && payload.pack.events) {
              payload = convertPayloadDatetime(payload.pack.events);
            }
          },
          onSync: (response: any) => {
            console.log('response2', response);
            if (response.response.events.rows[0].$PhantomId != null) {
                if (response.response.events.rows[0].orderId != null && response.response.events.rows[0].fromSchedule === false) {
                this.openOrderModal(response.response.events.rows[0].orderId);
                } if (response.response.events.rows[0].orderId != null && response.response.events.rows[0].fromSchedule === true) {
                this.openWorkOrderModal(response.response.events.rows[0].id);
                }
              }
              if (response.response.success) {
                this.crudManager.acceptChanges();
              }
          },
            resourceStore: new ResourceStore(),  // Ensure these are properly initialized
            eventStore: new EventStore({
                modelClass: CustomEventModel,  // Use CustomEventModel here
            }),
            writeAllFields: true,
            assignmentStore: new AssignmentStore(),
            autoLoad: true,  // Disable automatic loading from default transport
            autoSync: true,  // Disable automatic syncing from default transport

            // Disable default transport since we will handle load and sync manually
            transport: {
                load : {
                url    : environment.crmApiUrl + 'events/',
                method : 'GET',
                headers: {
                    'Accept-Language': this.translateService.currentLang || 'en',
                    'Authorization': 'Bearer ' + this.jwtService.getAccessToken(),
                    'Content-Type': 'application/json',
                    'Platform': 'core',
                    'Accept-Encoding': 'gzip, deflate, br, zstd'
                },
              params: {
                date_from: formatDateYMD(this.datePicker.startDate),
                date_to: formatDateYMD(this.datePicker.endDate),
                company_id: this.storageService.getSelectedCompanyId(),
                contractor_id: this.contractorId || -1
            }
                },
                sync: {
                    url: environment.crmApiUrl + 'events/',
                    method: 'PATCH',
                    headers: {
                        'Accept-Language': this.translateService.currentLang || 'en',
                        'Authorization': 'Bearer ' + this.jwtService.getAccessToken(),
                        'Content-Type': 'application/json',
                        'Platform': 'core'
                    },
                    data: {
                        company_id: this.storageService.getSelectedCompanyId(),
                    }

                } 
            },
            supportShortSyncResponse: true,
            encoder: {
                requestData: {
                    company_id: this.storageService.getSelectedCompanyId(),
                }
            }
        });

        // Listen to CrudManager changes and debounce sync calls
        // this.crudManager.on('hasChanges', () => {
        //     if (this.debounceTimeout) {
        //         clearTimeout(this.debounceTimeout);  // Clear previous timeout
        //     }

        //     this.debounceTimeout = setTimeout(() => {
        //         this.syncData();
        //     }, 100);
        // });
    }


        // Function to load events from the EventService
        loadEventsFromApi(date_from: Date, date_to: Date, contractor_id?: number): void {
            Mask.mask(this.translateService.instant('calendar.loadingEvents'), this.mainCalendarDiv.nativeElement);
            this.loadResources();
            this.currentDatePickerStartDate = date_from;
            this.currentDatePickerEndDate = date_to;

            // Define params and conditionally add contractor_id if it's provided
            const params: _CRM_EVT_1 = {
                date_from: date_from,
                date_to: date_to,
                ...(contractor_id !== undefined && { contractor_id })  // Add contractor_id only if it has a value
            };

            this.eventService.getEvents(params).subscribe({
                next: (response: any) => {
                    if (response && response.data.events && response.data.assignments) {
                        const allEvents = response.data.events.rows;
                        const allAssignments = response.data.assignments.rows;

                        // Assign data to assignmentStore
                        this.crudManager.assignmentStore.data = allAssignments;

                        // Filter events with no assigned resources (unassigned) and assign them to unassignedEventStore
                        const unassignedEvents = allEvents.filter((event: any) => {
                            // Find if this event has an assignment
                            const isAssigned = allAssignments.some((assignment: any) => assignment.eventId === event.id);
                            return !isAssigned;  // Return true if there are no assignments (unassigned)
                        });

                        // Events that are assigned will remain in the main eventStore
                        const assignedEvents = allEvents.filter((event: any) => {
                            return allAssignments.some((assignment: any) => assignment.eventId === event.id);
                        });

                        // Set unassigned events to unassignedEventStore
                        this.unassignedEventStore.data = unassignedEvents;

                        // Set assigned events to eventStore
                        this.crudManager.eventStore.data = allEvents;

                        // Notify the CrudManager that the data is now loaded
                        this.crudManager.trigger('load');
                        Mask.unmask(this.mainCalendarDiv.nativeElement);
                    } else {
                        console.error('API response is missing expected data.');
                    }
                },
                error: (error: any) => {
                    console.error('Failed to load events from API', error);
                }
            });
        }

        syncData(): void {
            const changes = this.crudManager.changes;

            // If there are no changes, log the information and exit
            if (!changes || Object.keys(changes).length === 0) {
              console.log('No changes to sync');
              return;
            }

            // You can log the changes for debugging
            console.log('Changes to sync:', changes);

            // Prepare the payload based on the changes
            const payload = {
            ...changes
            };

            // Send the changes to your backend API
            this.eventService.updateEvent(payload).subscribe({
              next: (response: BryntumChangeSetResponse) => {

                const allEvents = response.data.events.rows;
                const allAssignments = response.data.assignments;

                // Transform the response into a changeset
                const eventChangeset = {
                    added: [], // If no new records, leave this empty
                    updated: allEvents, // Use all events from the response
                    removed: [] // If no removed records, leave this empty
                };

                // Transform the response into a changeset
                const assignmentChangeset = {
                    added: [], // If no new records, leave this empty
                    updated: allAssignments, // Use all events from the response
                    removed: [] // If no removed records, leave this empty
                };

                // Apply the changeset using CrudManager's applyChangeset method
                this.crudManager.eventStore.applyChangeset(eventChangeset);

                this.crudManager.assignmentStore.applyChangeset(allAssignments);

                // Commit the changes after successful sync
                if (response.data.success) {
                    this.crudManager.acceptChanges();
                    if (response.data.events.rows[0].$PhantomId != null) {
                        if (response.data.events.rows[0].orderId != null && response.data.events.rows[0].fromSchedule === false) {
                        this.openOrderModal(response.data.events.rows[0].orderId);
                        } if (response.data.events.rows[0].orderId != null && response.data.events.rows[0].fromSchedule === true) {
                        this.openWorkOrderModal(response.data.events.rows[0].id);
                        }
                }

                // Optionally, show a toast message or update UI
                this.toastService.successToast('syncSuccessfull');
                }
            },
            error: (error: any) => {
                console.error('Failed to sync data', error);
                // Check for network connectivity issues (504 Gateway Timeout or other network errors)
                if (error.status === 504 || !navigator.onLine || error.name === 'HttpErrorResponse' && error.status === 0) {
                  // User is offline or experiencing network issues
                  this.crudManager.revertChanges();
                  this.toastService.errorToast('networkError');
                } else if (error.error.asset_id == 'no_multi_day_work_orders') {
                  // Specific business rule error
                  this.crudManager.revertChanges();
                  this.toastService.errorToast('noMultiDayWorkOrders');
                } else {
                  // Other errors
                  this.crudManager.revertChanges();
                  this.toastService.errorToast('syncFailed');
                }
              }
            });
          }

          loadContractors() {
            const params: _CRM_AFF_1 = {
                calendar_available: true,
                subcontractors_only: 1
            };

            this.affiliateService.getAffiliates(params).subscribe({
                next: (affiliates) => {
                    this.contractors = affiliates.data;

                    // Populate the contractor menu button with the loaded contractors
                    this.populateContractorMenu();
                },
                error: (error) => {
                    console.error('Failed to load contractors', error);
                }
            });
        }

        populateContractorMenu() {
          // Create the button
          let cMenuButton = this.calendar.widgetMap['contractorMenuButton'] as Button;
          // Create the menu to be attached to the button
          const cMenuButtonMenu = new Menu();

          // Add the "No contractor" menu item
          const noContractorMenuItem = new MenuItem({
            text: Widget.L('No contractor'),
            onItem: () => {
              // Call the API without a contractor ID
              this.crudManager.load();
              cMenuButton.text = Widget.L('No contractor');
            }
          })
          cMenuButtonMenu.add(noContractorMenuItem);

          // Add the contractors to the menu
          this.contractors.forEach(contractor => {
            const menuItem = new MenuItem({
              id: contractor.affiliate_id.toString(),
              text: contractor.name || 'Unknown Contractor',
              onItem: () => {
                console.log('contractorId', contractor.affiliate_id);
                this.contractorId = contractor.affiliate_id;
                this.crudManager.load();
                  cMenuButton.text = contractor.name || 'Unknown Contractor';
              }
            });
            cMenuButtonMenu.add(menuItem);
          });

          // Assign the menu to the button.menu
          cMenuButton.menu = cMenuButtonMenu;

          // Hide the menu initially, and show if contractors available
          cMenuButton.menu.hide();
          if (this.contractors.length === 0) {
              cMenuButton.hide();
          } else {
              cMenuButton.show();
          }
        }

        // Order Status filter
        populateOrderStatusFilterMenu(): void {
            let osMenuButton = this.calendar.widgetMap['orderStatusMenuButton'] as Button;
            let osMenu = osMenuButton.menu as Menu;
            let osMenuItems = osMenu.items as MenuItem[];

            osMenuItems.forEach(item => {
                item.on({
                    toggle: (menuItem: MenuItem) => {
                        const idName = menuItem.element.id;
                        const id = parseInt(idName, 10);

                        if (!menuItem.checked) {
                            // Add filter
                            this.calendar.eventStore.addFilter({
                                id: idName,
                                filterBy: (e: CustomEventModel) => e.orderStatusId !== id
                            });
                        } else {
                            // Remove filter
                            this.calendar.eventStore.removeFilter(idName);
                        }

                        // Update filters and save state
                        this.updateOrderStatuses();
                    }
                });
            });
        }

          loadResources() {

            // Create a shared array to hold both employees and resources
            let combinedResources: any[] = [];

            // Define the parameters for both API calls
            const employeeParams: _USM_ENT_0 = {taskable: 1};
            const resourceParams: _CRM_RSC_2 = {};

            // Create observables for both API calls
            const employees$ = this.employeeService.getEmployeesInCompany(employeeParams).pipe(
                map((res) => res.map((employee) => ({
                    id: employee.user_id,
                    name: `${employee.full_name}`,
                    role: `${employee.role_name}`,
                    imageUrl: employee.profile_image_url,
                    group: this.translateService.instant('calendar.employees'), // Translation key for "Ansatte"
                }))),
                catchError((error) => {
                    console.error('Failed to load employees', error);
                    return of([]); // Return empty array on error
                })
            );

            const resources$ = this.resourceService.getResources(resourceParams).pipe(
                map((res) => res.data.map((resource) => ({
                    id: resource.resource_id,
                    name: `${resource.resource_name}`,
                    imageUrl: resource.resource_image_url,
                    group: this.translateService.instant('calendar.resources'), // Translation key for "Ansatte"
                }))),
                catchError((error) => {
                    console.error('Failed to load resources', error);
                    return of([]); // Return empty array on error
                })
            );

            // Combine the two observables
            forkJoin([employees$, resources$]).subscribe({
                next: ([employees, resources]) => {
                    // Merge both employee and resource data
                    combinedResources = [...employees, ...resources];

                    // Assign the combined resources to the resourceStore
                    this.crudManager.resourceStore.data = combinedResources;
                    this.crudManager.trigger('load');
                },
                error: (error) => {
                    console.error('Failed to load employees or resources', error);
                }
            });
        }


        onValueChanges(value: any, source: any): void {

            if (source === 'hideWeekends') {
                let view = this.calendar.activeView as Calendar;

                      view.hideNonWorkingDays = value;
                    }

            if (source === 'eventTypeId'){
            // Access widgets in the editor form
            const resourceField = this.calendar.features.eventEdit.editor.widgetMap['resourceField'];
            const customOrderButton = this.calendar.features.eventEdit.editor.widgetMap['customOrderButton'];
            const workOrderTemplateField = this.calendar.features.eventEdit.editor.widgetMap['workOrderTemplateField'];
            const deleteButton = this.calendar.features.eventEdit.editor.widgetMap['deleteButton'];
            const createOrderField = this.calendar.features.eventEdit.editor.widgetMap['createOrderField'];
            const saveButton = this.calendar.features.eventEdit.editor.widgetMap['saveButton'];



            // Show or hide fields based on the selected event type
            if (source === 'eventTypeId') {
                if (value === 0) {
                workOrderTemplateField.hidden = false;
                deleteButton.hidden = true;
                createOrderField.hidden = false;

                if (this.calendar.features.eventEdit.editor.values['orderId']) {
                    customOrderButton.hidden = false;
                    createOrderField.hidden = true;
                }
                 } else if (value === 1) {
                    customOrderButton.hidden = true;
                    workOrderTemplateField.hidden = true;
                    createOrderField.hidden = true;
                    if (this.calendar.features.eventEdit.editor.values['workOrderStatusId']) {
                        deleteButton.hidden = false;
                    }
                }
            }
        }
    }

        // onActiveItemChange({ activeItem }: { activeItem: any }): void {
        //     // Only meaningful if we are on the month view
        //     this.autoRowHeight.disabled = activeItem.modeName !== 'month';
        // }

        onAutoRowHeightChanged({ checked }: { checked: boolean }): void {
            (this.calendar.modes as { month: MonthView }).month.autoRowHeight = checked;
        }

        // Called as the resourceFilterFilter's onChange handler
        // onResourceFilterFilterChange({ value }: { value: string }): void {
        //     // A filter with an id replaces any previous filter with that id.
        //     // Leave any other filters which may be in use in place.
        //     (this.resourceFilter.store as Store).filter({
        //         id       : 'resourceFilterFilter',
        //         filterBy : r => r.name.toLowerCase().startsWith(value.toLowerCase()) // a function which returns true to include the record
        //     });
        // }


        ngAfterViewInit(): void {

          this.localizationSetup();

          this.loadContractors();

          this.initializeCalendar();
          // this.setupWidgetListeners();

          this.restoreCalendarState();
            //this.loadEventsFromApi(this.datePicker.startDate, this.datePicker.endDate);

          this.setupCrudManager();
        this.loadResources();
          this.setupCalendarListeners();

          this.unscheduledFilter();
          // The following lines are only needed for ViewEncapsulation.ShadowDom
          (document.fonts as any).add(new FontFace('FontAwesome6Free', `url(src/assets/fonts/fa-solid-900.woff2)`));

          // Event Menu
          const eventMenu = this.calendar.features.eventMenu as any;
          console.log('eventMenu', eventMenu);

                      this.dateFrom = this.currentDatePickerStartDate;
            this.dateTo = this.currentDatePickerEndDate;
            console.log('dateFrom', this.dateFrom);
            console.log('dateTo', this.dateTo);

        }

        updateOrderStatuses(): void {
            const filters = this.calendar.eventStore.filters;
            const orderStatuses: number[] = [];

            filters.forEach((filter: any) => {
                if (filter.id) {
                    const id = parseInt(filter.id, 10); // Extract the numeric part
                    if (!isNaN(id)) {
                        orderStatuses.push(id);
                    } else {
                        console.warn('Invalid ID:', filter.id); // Warn if the ID is invalid
                    }
                } else {
                    console.warn('Filter does not have an ID:', filter); // Warn if the filter has no ID
                }
            });

            // Save the active order statuses to the state
            StateProvider.instance.setValue('orderStatus-filters', orderStatuses);
        }

        // Initializes the calendar instance and widget references
        private initializeCalendar(): void {
            this.calendar = this.calendarComponent.instance;
            const { widgetMap } = this.calendar;
            this.autoRowHeight = widgetMap['autoRowHeight'] as Checkbox;
            this.resourceFilterFilter = widgetMap['resourceFilterFilter'] as FilterField;
            this.resourceFilter = widgetMap['resourceFilter'] as ResourceFilter;
            this.datePicker = widgetMap['datePicker'] as DatePicker;
                // // Set up the showUnassigned widget
            this.calendar.widgetMap['showUnassigned'].on('change', (value: any) => {
                if (!value.checked) {
                this.calendar.eventStore.filter({
                    id: 'unassignedFilter',
                    filterBy: (r: any) => r.resources.length !== 0
                })
                }
                else {
                this.calendar.eventStore.removeFilter('unassignedFilter')
                }
                })

                this.populateContractorMenu();
                this.populateOrderStatusFilterMenu();
            }

        unscheduledFilter(){
            let date = this.selectedDate as Date; // Ensure selectedDate is treated as a Date

                const selectedDateStartOfDay = date.setHours(0, 0, 0, 0);
                const calendarDateStartOfDay = (this.calendar.date as Date).setHours(0, 0, 0, 0);

                if (this.calendarMode === 'resource' || this.calendarMode === 'dayresource') {

                    // Calculate the start and end of the week
                    const startOfWeek = new Date(date);
                    startOfWeek.setDate(date.getDate() - date.getDay() + (date.getDay() === 0 ? -6 : 1)); // Adjust for Monday as the start
                    startOfWeek.setHours(0, 0, 0, 0);

                    const endOfWeek = new Date(startOfWeek);
                    endOfWeek.setDate(startOfWeek.getDate() + 6);
                    endOfWeek.setHours(23, 59, 59, 999);

                    this.unassignedEventStore.addFilter({
                        id: 'unassignedFilter',
                        filterBy: (e: CustomEventModel) => {
                            const eventStartTime = new Date(e.startDate).getTime();
                            const filterResult =
                                e.eventTypeId === 0 &&
                                e.orderStatusId > 0 &&
                                eventStartTime >= startOfWeek.getTime() &&
                                eventStartTime <= endOfWeek.getTime();

                            return filterResult;
                        }
                    });
                }
        }

  openOrderModal(event: any): void {
    const eventEditor = this.calendar.features.eventEdit.editor;
    if (eventEditor) {
      eventEditor.hide();
    }

    const orderId = this.currentEventRecord.orderId;

    // Capture the overlay ID when opening the modal
    const overlayId = this.overlayService.openOverlay(
      'ORD-' + orderId,
      OrderDetailsV2Component,
      { orderId: orderId, overlay: true },
      'Order ID #' + this.currentEventRecord.orderNumber
      + (this.currentEventRecord.name ? ' - ' + this.currentEventRecord.name : '')
    );

    // Subscribe to the overlayClosed event and log the full overlay object
    this.overlayService.overlayClosed.subscribe((closedOverlay: OverlayItem) => {
      if (closedOverlay.id === overlayId) {
        this.fetchAndApplyChanges([orderId], 'order');
      }
    });

    // Log the opened overlay object
    const openedOverlay = this.overlayService['overlays'].find(o => o.id === overlayId);

    setTimeout(() => {
        // Select the rendered overlay component by its tag
        const overlayElement = document.querySelector('app-order-details-v2') as HTMLElement;

        if (overlayElement) {
        // Append styles dynamically using Renderer2
        this.renderer.setStyle(overlayElement, 'display', 'flex');
        this.renderer.setStyle(overlayElement, 'justify-content', 'center');
        }
    }, 0);  // Delay to ensure the overlay has been rendered
    }

  openWorkOrderModal(event: any): void {
    const workOrderId: number = Number(this.currentEventRecord.id);

    // Close or hide the event editor
    const eventEditor = this.calendar.features.eventEdit.editor;
    if (eventEditor) {
      eventEditor.hide();
    }

    // Open the overlay and capture the overlay ID
    const overlayId = this.overlayService.openOverlay(
      'WO-' + workOrderId,
      WorkOrderDetailsComponent,
      {workOrderId: workOrderId, viewSettings: { modalView: true, workOrderStandaloneView: false, fromCalendar: true }},
      '#' + this.currentEventRecord.workOrderNumber + (this.currentEventRecord.name ? ' - ' + this.currentEventRecord.name : ''));

    this.overlayService.overlayClosed.subscribe((closedOverlay: OverlayItem) => {
      if (closedOverlay.id === overlayId) {
        this.fetchAndApplyChanges([workOrderId], 'work_order');
      }
    });

        setTimeout(() => {
          const overlayElement = document.querySelector('app-work-order-details') as HTMLElement;
          if (overlayElement) {
            // this.renderer.setStyle(overlayElement, 'display', 'flex');
            // this.renderer.setStyle(overlayElement, 'justify-content', 'center');
          }
        }, 0); // Delay to ensure the overlay has been rendered
      }


  fetchAndApplyChanges(ids: number[], type: 'work_order' | 'order'): void {
    // Display loading mask
    Mask.mask(this.translateService.instant('calendar.loadingEvents'), this.mainCalendarDiv.nativeElement);

    // Define params and conditionally add contractor_id if it's provided
    const params: _CRM_EVT_1 = {
      work_order_ids: type === 'work_order' ? ids : null,
      order_ids: type === 'order' ? ids : null
    };


    this.eventService.getEvents(params).subscribe({
        next: (response: any) => {
          if (response && response.data.events && response.data.assignments) {
            const allEvents = response.data.events.rows;
            const allAssignments = response.data.assignments.rows;

            const allEventsIds = allEvents.map((event: any) => event.id);

            // Fetch assignments currently in the store that match the event IDs from the response
            const currentAssignments = this.crudManager.assignmentStore.query((a: AssignmentModel) => allEventsIds.includes(a.eventId));

            // Match old assignments with new assignments
            const currentAssignmentIds = currentAssignments.map((a: any) => a.id);
            const newAssignmentIds = allAssignments.map((a: any) => a.id);

            // Determine added assignments
            const addedAssignments = allAssignments.filter((a: any) => !currentAssignmentIds.includes(a.id));

            // Determine removed assignments
            const removedAssignments = currentAssignments.filter((a: any) => !newAssignmentIds.includes(a.id));

            // Updated assignments (same IDs but potentially different data)
            const updatedAssignments = allAssignments.filter((a: any) => currentAssignmentIds.includes(a.id));


            // Prepare changesets for events and assignments
            const eventChangeset = {
              added: [], // Assuming all events are updated for now
              updated: allEvents, // Use all events from the response
              removed: [] // Assuming no events are removed for now
            };

            const assignmentChangeset = {
              added: addedAssignments,
              updated: updatedAssignments,
              removed: removedAssignments
            };

            // Apply the changesets using CrudManager
            this.crudManager.eventStore.applyChangeset(eventChangeset);
            this.crudManager.assignmentStore.applyChangeset(assignmentChangeset);

          // Optionally, unassign unassigned events
          const unassignedEvents = allEvents.filter((event: any) => {
            const isAssigned = allAssignments.some((assignment: any) => assignment.eventId === event.id);
            return !isAssigned; // Return true if the event is unassigned
          });

          this.unassignedEventStore.applyChangeset(unassignedEvents);

          // Update UI to reflect changes
          Mask.unmask(this.mainCalendarDiv.nativeElement);
          this.toastService.successToast('syncSuccessfull');
        } else {
          console.error('API response is missing expected data.');
          Mask.unmask(this.mainCalendarDiv.nativeElement);
        }
      },
      error: (error: any) => {
        console.error('Failed to fetch and apply changeset', error);
        Mask.unmask(this.mainCalendarDiv.nativeElement);
      }
    });

  }








        // Sets up event listeners for widgets
        setupWidgetListeners(): void {
            this.autoRowHeight.on({
                change: this.onAutoRowHeightChanged,
                thisObj: this
            });

            // this.resourceFilterFilter.on({
            //     change: this.onResourceFilterFilterChange,
            //     thisObj: this
            // });
        }

        // Restores the calendar state (date and mode) from the StateProvider
        restoreCalendarState(): void {
            const storedDate = StateProvider.instance.getValue('calendar-date');
            const storedMode = StateProvider.instance.getValue('calendar-mode') as { mode: string };
            const storedFilters = StateProvider.instance.getValue('orderStatus-filters') as number[];

            // Restore the date
            this.calendar.date = storedDate ? new Date(storedDate.toString()) : new Date();

            // Restore the mode
            this.calendar.mode = storedMode && storedMode.mode ? storedMode.mode : 'month';
            this.calendarMode = this.calendar.mode;

            // Restore order status filters


            if (storedFilters) {
                let osMenuButton = this.calendar.widgetMap['orderStatusMenuButton'] as Button;
                let osMenu = osMenuButton.menu as Menu;
                let osMenuItems = osMenu.items as MenuItem[];

                storedFilters.forEach(filterId => {
                    // Add the filter back to the eventStore
                    this.calendar.eventStore.addFilter({
                        id: filterId.toString(),
                        filterBy: (e: CustomEventModel) => e.orderStatusId !== filterId
                    });

                    // Update the UI to reflect the restored filter state
                    const menuItem = osMenuItems.find(item => item.element.id === filterId.toString()); // Define menuItem here
                    if (menuItem) {
                        menuItem.checked = false; // Ensure the UI reflects the restored state
                    }
                });
            }
        }

        getAllWorkOrderTemplates() {
            this.templateService.getWorkOrderTemplates().subscribe({
                next: (templates) => {
                    this.workOrderTemplates = templates.sort((a, b) => a.template_name.localeCompare(b.template_name));
                },
                error: (error) => {
                    console.error('Failed to load Workorder Templates', error);
                }
            });
        }

        // Sets up listeners for calendar-related changes (date and mode)
        setupCalendarListeners(): void {
            let currentMode = this.calendar.mode; // Track the initial mode
            this.selectedDate = new Date(this.datePicker.date as Date);

            this.calendar.on({
                dateChange: (event: any) => {
                    this.onDateChangeNew(event);
                    StateProvider.instance.setValue('calendar-date', event.date);
                },
                activeItemChange: (event: any) => {
                    const newMode = this.calendar.mode;
                    this.currentMode = newMode;
                    if (newMode !== currentMode) {
                        StateProvider.instance.setValue('calendar-mode', { mode: newMode });
                        currentMode = newMode;
                        this.calendarMode = newMode;
                        this.currentMode = newMode;
                    }
                    this.unscheduledFilter();
                },
                onEventContextMenu: (event: any) => {
                    console.log('beforeEventMenuShow - 866', event);
                },
                eventContextMenu: (event: any) => {
                    console.log('beforeEventMenuShow - 866', event);
                },
                // beforeAutoCreate: (event: any) => {
                //     console.log('beforeAutoCreate - 866', event);
                //     // Set the event color based on the event type
                //     const eventType = event.eventType;
                //     if (eventType === 0) {
                //         event.color = '#FF0000';  // Red for unassigned events
                //     } else if (eventType === 1) {
                //         event.color = '#00FF00';  // Green for assigned events
                //     }
                // },
                beforeEventEditShow: this.beforeEventEditShow.bind(this), // Ensure `this` is correctly bound
                beforeEventSave: this.beforeEventSave.bind(this)
            });


        }

        onAddNewClick(): void {
        }

        onDateChangeNew(event: any) {
           console.log('loadOn Top');
           console.log('this.currentDatePickerStartDate', this.currentDatePickerStartDate)
           console.log('this.currentDatePickerEndDate', this.currentDatePickerEndDate)
           console.log('this.datePicker.startDate', this.datePicker.startDate)
           console.log('this.datePicker.endDate', this.datePicker.endDate)

            // Ensure selectedDate is a Date object
            this.selectedDate = new Date(this.calendar.date as Date);
                // Compare the actual date values using getTime()
                
                if (this.currentDatePickerStartDate?.getTime() !== this.datePicker.startDate.getTime() ||
                this.currentDatePickerEndDate?.getTime() !== this.datePicker.endDate.getTime()) {


                // Update the current start and end dates
                this.currentDatePickerStartDate = this.datePicker.startDate;
                this.currentDatePickerEndDate = this.datePicker.endDate;

                // Call the method to load events with the new date range
                console.log('load');
                this.crudManager.load( {request: {
                    params: {
                        date_from: formatDateYMD(this.datePicker.startDate),
                        date_to: formatDateYMD(this.datePicker.endDate),
                        company_id: this.storageService.getSelectedCompanyId(),
                        contractor_id: this.contractorId || -1
                    }
                }}
                    
                )
                }

                this.unscheduledFilter();

            }

          // ---------------------------- Localisation ----------------------------
          // Commenting out localizationSetup to test if it's the source of the problem

          localizationSetup(): void {
            //  English localization extensions
            const en = {
                localeName: 'En',
                localeDesc: 'English (US)',
                localeCode: 'en-US',

                List : {
                'Select All' : 'Select all',
                'Not confirmed': 'Not confirmed',
                'Confirmed': 'Confirmed',
                'Job started': 'Job started',
                'Job completed': 'Job completed',
                'Sent to payment': 'Sent to payment',
                'Closed': 'Closed',
                },

                Column: {
                'Start': 'Start',
                'End': 'End',
                'Order': 'Order',
                'Duration': 'Duration'
                },

                Widget : {
                'No contractor' : 'No contractor',
                'Show unassigned' : 'Show unassigned',
                'Week per emp' : 'WK EMP',
                'Month per emp' : 'MO EMP',
                'Width': 'Width',
                'Unscheduled Events': 'Unscheduled Events',
                'Show weekends': 'Show weekends',
                'Resource': 'Resource',
                'Loading events...': 'Loading events...',
                'Time:' : 'Time:',
                'Start': 'Start',
                'h': 'h',
                'm': 'm',
                'Customer:' : 'Customer:',
                'Address:' : 'Address:',
                'Job status' : 'Job satus',
                'Order status' : 'Order status',
                'Description:' : 'Description:',
                'Employee:': 'Employee',
                'Customer contact': 'Customer contact',
                "No customer": "No customer",
                "No address": "No address",
                "No name": "No job name",
                'No address added': 'No address added',
                'No description added': 'No description added',
                'No comment added': 'No comment added',
                'buttonEventType': 'Event types',
                'etOrder': 'Order',
                'etEmployee': 'Employee',
                'etOther': 'Other',
                'Order statuses': 'Order Statuses',
                'Draft': 'Draft',
                'Quote sent' : 'Quote sent',
                'Accepted by customer': 'Accepted by customer',
                'Confirmed': 'Confirmed',
                'Job started': 'Job started',
                'Job finished': 'Job finished',
                'Sent to payment': 'Sent to payment',
                'Closed': 'Closed',
                'Event': 'Event',
                'Order': 'Order',
                'Open order': 'Open order',
                'Open job': 'Open job',
                'Weekly resources': 'Weekly resources',
                },

                EventEdit : {
                "decade" : "All",
                },

                AgendaView : {
                Agenda : 'Agenda'
                },

                DayView : {
                    Day : 'Day'
                },

                DayResourceView : {
                    Resource : 'Daily resources',
                    Label : 'Daily resources'
                },

                ResourceView : {
                    resourceView : 'Weekly resources',
                    label : 'Weekly resources'
                },

                DateHelper : {
                    weekStartDay : 1, // 0-6 (0: Sunday, 1: Monday etc.)
                    unitNames : {
                        // Used by DateHelper.getUnitAbbreviation() to abbreviate the time units
                        year    : { single : 'year',    plural : 'years',   abbrev : 'yr' },
                        month   : { single : 'month',   plural : 'months',  abbrev : 'mth' },
                        week    : { single : 'week',    plural : 'weeks',   abbrev : 'wk' },
                        day     : { single : 'day',     plural : 'days',    abbrev : 'd' },
                        hour    : { single : 'hour',    plural : 'hours',   abbrev : 'h' },
                        minute  : { single : 'minute',  plural : 'minutes', abbrev : 'min' },
                        second  : { single : 'second',  plural : 'seconds', abbrev : 's' },
                        millisecond : { single : 'millisecond', plural : 'milliseconds', abbrev : 'ms' }
                    }
            }
        }

            LocaleManager.applyLocale('No');

            // Set the default locale based on the current language from ngx-translate
            const currentLang = this.translateService.currentLang;
            if (currentLang === 'en') {
                LocaleManager.applyLocale('En');    // Must match localeName exactly
            } else if (currentLang === 'no') {
                LocaleManager.applyLocale('No');    // Must match localeName exactly
            } else {
                // Default to English
                LocaleManager.applyLocale('En');
            }

            // Subscribe to language change event from ngx-translate
            this.translateService.onLangChange.subscribe((event: LangChangeEvent) => {
                if (event.lang === 'en') {
                    LocaleManager.applyLocale('En');
                } else if (event.lang === 'no') {
                    LocaleManager.applyLocale('No');
                } else {
                    LocaleManager.applyLocale('No');
                }
            });
            }

            beforeEventSave({ editor, eventRecord, values }: { values: any, editor: any, eventRecord: any }): boolean {
                return true;
            }

            beforeEventEditShow({ editor, eventRecord }: { editor: any, eventRecord: any }) {
            this.currentEventRecord = eventRecord;
            // Ensure currentMode is accessible here
            const currentMode = this.calendar.mode;


            // Get the value of the selected eventType from the editor's form data
            const eventTypeId = editor.values.eventTypeId;
            const workOrderId = editor.values.workOrderId;
            const orderIdValue = editor.values.orderId;
            const fromSchedule = editor.values.fromSchedule;
            const company_id = eventRecord.data.company_id;
            const id = eventRecord.data.id;


            // Access widgets in the editor form
            const resourceField = editor.widgetMap.resourceField;
            const customOrderButton = editor.widgetMap.customOrderButton;
            const customWorkOrderButton = editor.widgetMap.customWorkOrderButton;
            const eventType = editor.widgetMap.eventTypeId;
            const orderId = editor.widgetMap.orderId;
            const createOrderField = editor.widgetMap.createOrderField;
            const workOrderTemplateField = editor.widgetMap.workOrderTemplateField;
            const eventTypeWidget = editor.widgetMap.eventTypeField;
            const saveButton = editor.widgetMap.saveButton;
            const deleteButton = editor.widgetMap.deleteButton;
            const eventColorField = editor.widgetMap.colorField;
            const draggable = editor.record.data.draggable;


            eventColorField.hidden = false;



            // Check if workorderTemlats are loaded
            if (this.workOrderTemplates && this.workOrderTemplates.length > 0) {
                // Populate the workorderTemlats with template data
                workOrderTemplateField.store = {
                    fields: ['id', 'name'], // Define fields for the combo store
                    data: this.workOrderTemplates.map(template => ({
                        id: template.template_id,  // Use product_id for value
                        name: template.template_name  // Use product_name for display
                    }))
                };
            } else {
                console.error('Products not loaded');
            }

            if (currentMode !== 'dayresource' && currentMode !== 'resource' && editor.record.meta.isCreating === true) {
                resourceField.value = [];
            }

            // Set the eventTypeId field to 0 if it is not set
            if (eventTypeId === null || eventTypeId === undefined) {
                eventTypeWidget.hidden = false;
                eventTypeWidget.items[0].checked = true;
            }

            if (fromSchedule) {
                customOrderButton.text = 'Gå til jobb';
            }

            // Field setup for new events
            if (orderIdValue === null || orderIdValue === undefined) {
                createOrderField.value = true;
                editor.values.create_order = true;
                customOrderButton.hidden = true;
                customWorkOrderButton.hidden = true;
                saveButton.text = 'Opprett';
                workOrderTemplateField.hidden = false;
            }

            if (orderIdValue) {
                workOrderTemplateField.hidden = true;
            }

            // Show or hide fields based on the selected event type
            if (eventTypeId === 0) {
                customOrderButton.hidden = false;
                customWorkOrderButton.hidden = false;
                deleteButton.hidden = true;
                eventTypeWidget.hidden = true;
                saveButton.text = 'Lagre';
            } else if (eventTypeId === 1) {
                workOrderTemplateField.hidden = true;
                customOrderButton.hidden = true;
                customWorkOrderButton.hidden = true;
                eventTypeWidget.hidden = true;
                saveButton.text = 'Lagre';
            }

            // Setting if you are in subcontractor mode
            if (company_id != this.storageService.getSelectedCompanyId() && editor.record.meta.isCreating != true && !draggable) {
                customOrderButton.hidden = true;
                customWorkOrderButton.hidden = true;
                editor.eventEditFeature.readOnly = true;
            } else if (company_id != this.storageService.getSelectedCompanyId() && editor.record.meta.isCreating != true && draggable) {
                customOrderButton.hidden = true;
                customWorkOrderButton.hidden = false;
            } else {
                editor.eventEditFeature.readOnly = false;
            }
        }
    }
