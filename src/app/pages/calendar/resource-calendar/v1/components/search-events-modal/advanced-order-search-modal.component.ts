import {Component, EventE<PERSON>ter, OnChanges, OnInit, Output, SimpleChanges} from '@angular/core';
import {NgbActiveModal} from "@ng-bootstrap/ng-bootstrap";
import {EventService} from "../../../../../../@shared/services/event.service";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {FormControl, FormGroup, ReactiveFormsModule} from "@angular/forms";
import {_CRM_EVT_17, _CRM_ORD_165} from "../../../../../../@shared/models/input.interfaces";
import {EventResponse} from "../../../../../../@shared/models/events.interfaces";
import {formatDateDMYHHMM, getFormControl} from "../../../../../../@core/utils/utils.service";
import {Column} from "../../../../../../@shared/components/advanced-table/advanced-table.component";
import {AdvancedTableModule} from "../../../../../../@shared/components/advanced-table/advanced-table.module";
import {CommonModule} from "@angular/common";
import {InputComponent} from "../../../../../../@shared/components/input/input.component";


import {OrderService} from "../../../../../../@shared/services/order.service";
import {OrderResponseCompact} from "../../../../../../@shared/models/order.interfaces";

@Component({
  selector: 'app-advanced-order-search-modal',
  templateUrl: './advanced-order-search-modal.component.html',
  styleUrls: ['./advanced-order-search-modal.component.css'],
  standalone: true,
  imports: [
    AdvancedTableModule,
    ReactiveFormsModule,
    TranslateModule,
    CommonModule,
    InputComponent
]
})
export class AdvancedOrderSearchModal implements OnInit, OnChanges {
  searchForm: FormGroup;
  searchResults: OrderResponseCompact[] = [];
  columns: Array<Column> = [];
  loading: boolean = false;
  showNoResults: boolean = false;
  showNoParamsError: boolean = false;

  constructor(public activeModal: NgbActiveModal,
              private orderService: OrderService,
              private translate: TranslateService) { }

  ngOnInit(): void {
    this.searchForm = new FormGroup({
      orderNumber: new FormControl(),
      product: new FormControl(),
      address: new FormControl(),
      customerName: new FormControl(),
      phone: new FormControl(),
      comment: new FormControl(),
      email: new FormControl()
    });
  }

  ngOnChanges(changes: SimpleChanges) {

  }

  searchEvents() {
    let params: _CRM_ORD_165 = {
      order_number: this.searchForm.value.orderNumber,
      product_name: this.searchForm.value.product,
      address: this.searchForm.value.address,
      customer_name: this.searchForm.value.customerName,
      customer_phone: this.searchForm.value.phone,
      comment: this.searchForm.value.comment,
      customer_email: this.searchForm.value.email
    }
    this.activeModal.close(params);
  }

  onEditClick(event: EventResponse) {
    this.activeModal.close(event);
  }


  handlePhoneNumberChangeContact(phoneNumber: string | null): void {
    this.searchForm.patchValue({phone: phoneNumber})
  }

  protected readonly getFormControl = getFormControl;
}
