import {AfterViewInit, ChangeDetectorRef, Component, forwardRef, OnChanges, OnInit, Optional, SimpleChanges, TemplateRef, ViewChild} from '@angular/core';
import {NgbActiveModal, NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {FormControl, FormsModule, ReactiveFormsModule} from "@angular/forms";
import {displayDate, UtilsService, workOrderBadgeStatus} from "../../../@core/utils/utils.service";
import {OrderService} from "../../../@shared/services/order.service";


import {ButtonComponent} from "../../../@shared/components/button/button.component";





import {ActivatedRoute, Router} from "@angular/router";







import {TablerinoColumn, TablerinoSettings} from "../../../@shared/components/tablerino/tablerino.component";
import {_CRM_TTR_24, _CRM_TTR_28, _CRM_TTR_35} from "../../../@shared/models/input.interfaces";

import {EmployeeService} from "../../../@shared/services/employee.service";
import {ResourceService} from "../../../@shared/services/resource.service";
import {InternalUserResponse} from "../../../@shared/models/user.interfaces";
import {PageHeaderComponent} from "../../../@shared/components/page-header/page-header.component";
import {StorageService} from "../../../@core/services/storage.service";
import {TablerinoCompleteComponent} from "../../../@shared/components/tablerino-complete/tablerino-complete.component";
import {SalaryApprovalResponse, WorkingHoursResponse} from 'src/app/@shared/models/timetracking.interfaces';
import {TrackingService} from "../../../@shared/services/tracking.service";
import {ToastService} from "../../../@core/services/toast.service";


import {BehaviorSubject, Subject} from "rxjs";
import {PaginationResponse} from "../../../@shared/models/response.interfaces";
import {WorkOrderCompactResponse} from "../../../@shared/models/order.interfaces";
import {PaginationContainer} from "../../../@shared/models/global.interfaces";
import {HeaderFiltersContainer} from "../../../@shared/components/tablerino-header/tablerino-header.component";
import {WorkOrderRow} from "../../work-orders/work-orders-overview/work-orders-overview.component";
import {SalaryApprovalModal} from "./_modals/salary-approval-modal/salary-approval-modal";
import {VerifyPopupModal} from "../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {StandardImports} from "../../../@shared/global_import";
import {DeleteButtonComponent} from "../../../@shared/components/delete-button/delete-button.component";

interface SalaryApprovalRow extends SalaryApprovalResponse {
  selected: boolean;
}

@Component({
    selector: 'app-salary-approvals',
    templateUrl: './salary-approvals.component.html',
    styleUrls: ['./salary-approvals.component.css'],
    standalone: true,
  imports: [StandardImports, TablerinoCompleteComponent, PageHeaderComponent, DeleteButtonComponent]
})
export class SalaryApprovalsComponent implements OnInit {
  columnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);
  salaryApprovalRows: SalaryApprovalRow[];
  settings: TablerinoSettings = {
    checkboxes: false,
    clickableRows: true,
  }
  accountingLoadingId: number | null = null;
  loading: boolean = false;
  excelLoading: number | null = null;
  paginationSubject: BehaviorSubject<PaginationContainer> = new BehaviorSubject<PaginationContainer>({page: 1, limit: 25, paginate: 1, totalPages: 0, totalItems: 0});
  selectedRowsSubject: BehaviorSubject<SalaryApprovalRow[]> = new BehaviorSubject<SalaryApprovalRow[]>([]);
  headerFiltersContainerSubject: BehaviorSubject<HeaderFiltersContainer> = new BehaviorSubject<HeaderFiltersContainer>({filters: [], init: true});

  @ViewChild('excelButton', {static: true}) excelButton: TemplateRef<any>;

  constructor(public utilsService: UtilsService,
              @Optional() public activeModal: NgbActiveModal,
              private translate: TranslateService,
              private trackingService: TrackingService,
              private modalService: NgbModal
              ) {
  }

  ngOnInit() {
    this.initializeColumns();
    this.getSalaryApprovals();

  }

  getSalaryApprovals() {
    this.trackingService.getCompanySalaryApprovals().subscribe((response) => {
      this.salaryApprovalRows = response.sort((a, b) => a.created_at > b.created_at ? -1 : 1).map((sa) => {
        return {
          ...sa,
          selected: false,
        }
      });
    });
  }

  initializeColumns() {
    this.columnsSubject.next([
      {
        name: 'period',
        labelKey: 'salary.approvedHours.period',
        formatter: (sa: SalaryApprovalResponse) => this.formatPeriod(sa),
        sort: false,
        visible: true,
      },
      {
        name: 'created_at',
        labelKey: 'salary.approvals.list.createdAt',
        formatter: (sa: SalaryApprovalResponse) => displayDate(sa.created_at),
        sort: false,
        visible: true,
      },
      {
        name: 'created_by',
        labelKey: 'salary.approvals.list.createdBy',
        formatter: (sa: SalaryApprovalResponse) => sa.created_by.full_name,
        sort: false,
        visible: true,
      },
      {
        name: 'synced_accounting',
        labelKey: 'salary.approvals.list.syncedAccounting',
        formatter: (sa: SalaryApprovalResponse) => this.formatSentToAccounting(sa),
        sort: false,
        visible: true,
      },
      {
        name: 'excel_button',
        labelKey: 'actions',
        formatter: (sa: SalaryApprovalResponse) => '',
        sort: false,
        visible: true,
        hideHeaderName: true,
        ngTemplate: this.excelButton,
      },
    ]);
  }

  formatSentToAccounting(sa: SalaryApprovalResponse) {
    if (sa.sent_to_accounting_at) {
      return displayDate(sa.sent_to_accounting_at);
    } else {
      return '';
    }
  }

  formatPeriod(sa: SalaryApprovalResponse) {
    return this.translate.instant('MONTHS.' + sa.month) + ' ' + sa.year;
  }

  openSalaryApprovalModal(sa: SalaryApprovalRow) {
    let modalRef = this.modalService.open(SalaryApprovalModal, {size: 'xl'});
    modalRef.componentInstance.salary_approval_id = sa.salary_approval_id;
  }

  exportToExcel(sa: SalaryApprovalRow) {
    this.excelLoading = sa.salary_approval_id;
    this.trackingService.exportSalaryApprovalToExcel(sa.salary_approval_id).subscribe((response) => {
      const url = window.URL.createObjectURL(response);
      const anchor = document.createElement('a');
      anchor.href = url;

      // Set the download attribute with the desired file name
      anchor.download = 'salary-' + sa.year + '-' + sa.month + '.xlsx';
      document.body.appendChild(anchor);
      anchor.click();
      document.body.removeChild(anchor);
      window.URL.revokeObjectURL(url);
      this.excelLoading = null;
    }, error => {
      this.excelLoading = null;
    })
  }

  deleteSalaryApproval(sa: SalaryApprovalRow) {
    this.trackingService.deleteSalaryApproval(sa.salary_approval_id).subscribe(() => {
      this.getSalaryApprovals();
    });
  }

  pushToAccounting(sa: SalaryApprovalRow) {
    let modalRef = this.modalService.open(VerifyPopupModal);
    modalRef.result.then((result) => {
      if (result) {
        this.accountingLoadingId = sa.salary_approval_id;
        this.trackingService.exportSalaryApprovalToAccounting(sa.salary_approval_id).subscribe(() => {
          this.accountingLoadingId = null;
          this.getSalaryApprovals();
        }, error => {
          this.accountingLoadingId = null;
        });
      }
    });
  }
}
