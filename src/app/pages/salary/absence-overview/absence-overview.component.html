<div class="d-flex align-items-center justify-content-between" style="height: 80px;">
  <div class="d-flex align-items-center">
    <h4>{{ 'salary.absence.title' | translate }}</h4>
  </div>
  <div class="me-2">
      <app-selectorini
        #userSelectorini
        [predefinedSearchResults]="employees"
        [searchMainDisplayKeys]="['full_name']"
        [directSelection]="true"
        [placeholderTranslationKey]="'salary.approvedHours.addForEmployee'"
        (itemSelectedEmitter)="addAbsence($event)"
      ></app-selectorini>
    </div>
</div>

<div>
  <app-tablerino
    [tableName]="'salary-absences'"
    [loading]="loading"
    [columnsSubject]="columnsSubject"
    [tableData]="absences"
    (rowClickedEmitter)="openAbsenceModal($event)"
  ></app-tablerino>
</div>

<ng-template #statusColumnTemplate let-row="row">
  <div class="d-flex align-items-center">

    <!--  Not answered  -->
    <div *ngIf="row.declined_at == null && row.approved_at == null">
      <div class="d-flex gap-1">
        <app-button
          [translationKey]="'salary.absence.approve'"
          [loading]="row.entry_id === approvingAbsenceLoadingId"
          [disabled]="row.entry_id === decliningAbsenceLoadingId"
          [small]="true"
          [buttonType]="'nude'"
          (buttonClick)="approveAbsence(row, $event)"
        ></app-button>

        <app-button
          [translationKey]="'salary.absence.decline'"
          [buttonType]="'nude'"
          [loading]="row.entry_id === decliningAbsenceLoadingId"
          [disabled]="row.entry_id === approvingAbsenceLoadingId"
          [small]="true"
          [themeStyle]="'danger'"
          (buttonClick)="declineAbsence(row, $event)"
        ></app-button>
      </div>
    </div>

    <!--  Approved  -->
    <div *ngIf="row.approved_at" class="d-flex align-items-center" [ngbTooltip]="('salary.absence.approvedBy') + ' ' + row.approved_by.full_name + ' ' + displayDate(row.approved_at)">
      <i class="fa-regular b-fa-check fa-lg text-success me-1"></i>
      <div>{{ 'salary.absence.approved' | translate }}</div>
    </div>

    <div *ngIf="row.declined_at" class="d-flex align-items-center" [ngbTooltip]="('salary.absence.declinedBy') + ' ' + row.declined_by.full_name + ' ' + displayDate(row.declined_at)">
      <i class="fa-regular b-fa-xmark fa-lg text-danger me-1"></i>
      <div>{{ 'salary.absence.declined' | translate }}</div>
    </div>

  </div>

</ng-template>
