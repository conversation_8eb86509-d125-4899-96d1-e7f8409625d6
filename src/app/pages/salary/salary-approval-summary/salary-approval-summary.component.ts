import {After<PERSON>iewInit, ChangeDetectorRef, Component, OnInit, Optional, TemplateRef, ViewChild} from '@angular/core';
import {NgbActiveModal, NgbModal, NgbTooltip} from '@ng-bootstrap/ng-bootstrap';
import {BehaviorSubject, combineLatest, forkJoin} from "rxjs";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {formatDateDMY, formatTimeHM, UtilsService} from "../../../@core/utils/utils.service";
import {OrderService} from "../../../@shared/services/order.service";


import {ButtonComponent} from "../../../@shared/components/button/button.component";
import {NgIf} from "@angular/common";




import {ActivatedRoute, Router} from "@angular/router";







import {TablerinoColumn} from "../../../@shared/components/tablerino/tablerino.component";
import {_CRM_TTR_10, _CRM_TTR_22, _CRM_TTR_23} from "../../../@shared/models/input.interfaces";


import {EmployeeService} from "../../../@shared/services/employee.service";
import {ResourceService} from "../../../@shared/services/resource.service";
import {InternalUserResponse} from "../../../@shared/models/user.interfaces";

import {StorageService} from "../../../@core/services/storage.service";

import {TrackingService} from "../../../@shared/services/tracking.service";
import {SummaryTableComponent} from "./summary-table/summary-table.component";
import {ToastService} from "../../../@core/services/toast.service";
import {TimeTrackingDetailsModal} from "../_modals/time-tracking-details-modal/time-tracking-details-modal";
import {VerifyPopupModal} from "../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {UserSalaryTimeTrackingResponse} from "../../../@shared/models/timetracking.interfaces";
import {StandardImports} from "../../../@shared/global_import";

export interface TimeTrackingRow {
  entry_id: number;
  started_at: Date | null;
  stopped_at: Date | null;
  description: string | null;
  ongoing: boolean;
  duration_in_seconds: number;
  comment: string | null;
  time_tracking_status_id: number | null;
  time_tracking_status_name: string | null;
  auto_registered: boolean;
  locked: boolean;
  activity_id: number | null;
  activity_name: string | null;
  week: string | null;
  day: string | null;
  child_entry_ids: number[];
  selected: boolean;
  child_trackings: UserSalaryTimeTrackingResponse[];
  is_pause?: boolean;
  hasSalary: boolean;
}


@Component({
    selector: 'app-salary-approval-summary',
    templateUrl: './salary-approval-summary.component.html',
    styleUrls: ['./salary-approval-summary.component.css'],
    standalone: true,
  imports: [StandardImports, SummaryTableComponent]
})
export class SalaryApprovalSummaryComponent implements OnInit, AfterViewInit {
  loading: boolean = false;
  deleteLoading: boolean = false;
  approveLoading: boolean = false;
  markPeriodLoading: boolean = false;
  columnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);
  tableData: TimeTrackingRow[] = [];
  user: InternalUserResponse | undefined;
  titleDateString: string = '';
  params: _CRM_TTR_22 | null = null;
  employeeHasActiveSalary: boolean = false;
  markedAsFinished: boolean = false;
  anyTimeTrackingRowsSelected: boolean = false;
  totalSeconds: number = 0;

  @ViewChild('flag', { static: true }) flag!: TemplateRef<any>;

  constructor(public utilsService: UtilsService,
              @Optional() public activeModal: NgbActiveModal,
              private orderService: OrderService,
              private route: ActivatedRoute,
              private modalService: NgbModal,
              private translate: TranslateService,
              private employeeService: EmployeeService,
              private resourceService: ResourceService,
              private storageService: StorageService,
              private trackingService: TrackingService,
              private cdr: ChangeDetectorRef,
              private toastService: ToastService,
              private router: Router,
              ) {
  }

  ngOnInit() {
    this.loading = true;
    this.route.paramMap.subscribe(params => {
      let year = params.get('year');
      let month = params.get('month');
      let user_id = params.get('user_id');

      let titleDate = new Date(Number(year), Number(month) - 1);
      let titleDateFirstDate = new Date(titleDate.getFullYear(), titleDate.getMonth(), 1).getDate();
      let titleDateLastDate = new Date(titleDate.getFullYear(), titleDate.getMonth() + 1, 0).getDate();
      this.titleDateString =titleDateFirstDate + '. - ' + titleDateLastDate + '. ' + this.translate.instant('MONTHS.' + (titleDate.getMonth() + 1)).toLowerCase();

      this.params = {
        year: Number(year)!,
        month: Number(month)!,
        user_id: user_id!
      }

      this.employeeService.getEmployees({user_id: user_id!}).subscribe((response) => {
        this.user = response.data[0];
      });

      this.employeeService.getActiveSalaryForEmployee(user_id!, 'hourly').subscribe((response) => {
        this.employeeHasActiveSalary = !!response.salary;
      });

      this.getTimeTrackings();
    });
  }

  ngAfterViewInit() {
    this.initializeColumns();
    this.cdr.detectChanges();
  }

  getTimeTrackings() {
    combineLatest([this.trackingService.getUserSalaryTimeTrackings(this.params!), this.trackingService.getTimeTrackingActivities()]).subscribe(([timeTrackings, activities]) => {
      let activityMap: Map<number, boolean> = new Map();
      activities.forEach((activity) => {
        activityMap.set(activity.activity_id, activity.default_salary_type_id !== null);
      });
      this.tableData = timeTrackings.map((row) => {
        return {
          ...row,
          week: null,
          day: null,
          selected: false,
          child_entry_ids: [],
          hasSalary: activityMap.get(row.activity_id!) || false,
        }
      });
      this.tableData = this.computeRows();
      this.loading = false;
      this.anyTimeTrackingRowsSelected = this.tableData.some((r) => r.selected && r.time_tracking_status_id === 0 && r.entry_id >= 0 && r.stopped_at);
    });
  }

  computeRows() {
    this.totalSeconds = 0;
    // Create week and date map (Week -> Date -> Row)
    let weekMap: Map<number, Map<number, TimeTrackingRow[]>> = new Map();
    this.tableData.forEach((row) => {
      let week = this.getWeekNumber(new Date(row.started_at!));
      let date = new Date(row.started_at!).getDate();
      if (!weekMap.has(week)) {
        weekMap.set(week, new Map());
      }
      if (!weekMap.get(week)!.has(date)) {
        weekMap.get(week)!.set(date, []);
      }
      weekMap.get(week)!.get(date)!.push(row);
    });

    let rows: TimeTrackingRow[] = [];
    weekMap.forEach((dateMap, week) => {
      let weekRow: TimeTrackingRow = {
        entry_id: -1,
        started_at: null,
        stopped_at: null,
        description: null,
        ongoing: false,
        duration_in_seconds: 0,
        comment: null,
        time_tracking_status_id: null,
        time_tracking_status_name: null,
        auto_registered: false,
        locked: false,
        activity_id: null,
        activity_name: null,
        week: this.translate.instant('salary.approval.week') + ': ' + week,
        day: null,
        child_entry_ids: [],
        child_trackings: [],
        selected: false,
        hasSalary: false
      }
      rows.push(weekRow)
      dateMap.forEach((ttRows, date) => {
        let dayRow: TimeTrackingRow = {
          entry_id: -1 * date,
          started_at: null,
          stopped_at: null,
          description: null,
          ongoing: false,
          duration_in_seconds: 0,
          comment: null,
          time_tracking_status_id: null,
          time_tracking_status_name: null,
          auto_registered: false,
          locked: false,
          activity_id: null,
          activity_name: null,
          week: null,
          day: this.formatDateRow(ttRows[0].started_at!),
          child_entry_ids: [],
          child_trackings: [],
          selected: false,
          hasSalary: false
        }
        weekRow.child_entry_ids.push(dayRow.entry_id);
        rows.push(dayRow)
        ttRows.forEach((row) => {
          this.totalSeconds += row.duration_in_seconds;
          weekRow.duration_in_seconds += row.duration_in_seconds;
          weekRow.child_entry_ids.push(row.entry_id);
          dayRow.duration_in_seconds += row.duration_in_seconds;
          dayRow.child_entry_ids.push(row.entry_id);
          rows.push(row);
        });
      });
    });
    return rows;
  }

  initializeColumns() {
    this.columnsSubject.next([
      {
        name: 'datetime',
        labelKey: 'salary.approval.list.time',
        formatter: (row: TimeTrackingRow) => row.week || row.day || formatTimeHM(row.started_at!) + ' - ' + formatTimeHM(row.stopped_at!),
        align: 'middle',
        sort: false,
        visible: true,
      },
      {
        name: 'total',
        labelKey: 'salary.approval.list.total',
        formatter: (row: TimeTrackingRow) => this.formatTotalTime(row),
        sort: false,
        visible: true,
      },
      {
        name: 'activity',
        labelKey: 'salary.approval.list.activity',
        formatter: (row: TimeTrackingRow) => row.activity_name,
        sort: false,
        visible: true,
      },
      {
        name: 'description',
        labelKey: 'salary.approval.list.description',
        formatter: (row: TimeTrackingRow) => row.description || '',
        sort: false,
        visible: true,
      },
      {
        name: 'comment',
        labelKey: 'salary.approval.list.comment',
        formatter: (row: TimeTrackingRow) => row.comment || '',
        sort: false,
        visible: true,
      },
      {
        name: 'flag',
        labelKey: '',
        formatter: (row: TimeTrackingRow) => '',
        sort: false,
        visible: true,
        ngTemplate: this.flag
      },
      {
        name: 'status',
        labelKey: 'salary.approval.list.status',
        formatter: (row: TimeTrackingRow) => this.formatStatus(row),
        sort: false,
        visible: true,
      },
    ]);
  }

  getWeekNumber(date: Date): number {
    const tempDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    tempDate.setUTCDate(tempDate.getUTCDate() + 4 - (tempDate.getUTCDay() || 7));
    const yearStart = new Date(Date.UTC(tempDate.getUTCFullYear(), 0, 1));
    return Math.ceil((((tempDate.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
  }


  formatDateRow(date: Date) {
    return this.translate.instant('WEEKDAYS-SHORT.' + date.getDay()) + ', ' + date.getDate() + '. ' + this.translate.instant('MONTHS-SHORT.' + (date.getMonth() + 1)).toLowerCase() + '.';
  }

  rowSelectClicked(row: TimeTrackingRow) {
    if (row.locked || row.time_tracking_status_id === 1 || (row.entry_id > 0 && !row.stopped_at) || (row.entry_id >= 0 && (!row.hasSalary || row.activity_id == null))) {
      return;
    }

    row.selected = !row.selected;
    if (row.child_entry_ids.length > 0) {
      this.tableData.filter((r) => row.child_entry_ids.includes(r.entry_id) && !r.locked && r.time_tracking_status_id !== 1 && (r.stopped_at || r.entry_id < 0) && !(r.entry_id >= 0 && (!r.hasSalary || r.activity_id == null))).forEach((r) => {
        r.selected = row.selected
      });
    }
    this.anyTimeTrackingRowsSelected = this.tableData.some((r) => r.selected && r.time_tracking_status_id === 0 && r.entry_id >= 0 && r.stopped_at);
  }

  formatStatus(row: TimeTrackingRow) {
    if (row.entry_id > 0 && !row.stopped_at) {
      return `<span class="fw-bold text-danger">${this.translate.instant('salary.approval.list.ongoing')}</span>`;
    }
    if (row.locked) {
      let icon = `<i class="fas fa-lock me-1 text-muted"></i>`;
      let span = `<span class="text-muted">${row.time_tracking_status_name || ''}</span>`;
      return icon + span;
    } else {
      return `<span class="fw-bold">${row.time_tracking_status_name || ''}</span>`
    }
  }

  anyRowsSelected() {
    return this.tableData.some((row) => row.selected);
  }

  approve() {
    this.approveLoading = true;
    let prdPayload: _CRM_TTR_23 = {
      year: Number(this.route.snapshot.paramMap.get('year'))!,
      month: Number(this.route.snapshot.paramMap.get('month'))!,
      user_id: this.route.snapshot.paramMap.get('user_id')!,
      status_id: 1
    }
    this.trackingService.updateWorkingHourPeriod(prdPayload).subscribe((response) => {});

    let selectedRows = this.tableData.filter((row) => row.selected && !row.locked && row.entry_id >= 0);
    let payload: _CRM_TTR_10 = {
      entry_ids: selectedRows.map((row) => row.entry_id)
    }
    this.trackingService.calculateWorkingHours(payload).subscribe((response) => {
      this.approveLoading = false;
      this.toastService.successToast('updated');
      this.getTimeTrackings();
    }, error => {
      this.approveLoading = false;
    });
  }

  markPeriodAsFinished() {
    this.markPeriodLoading = true;
    let payload: _CRM_TTR_23 = {
      year: Number(this.route.snapshot.paramMap.get('year'))!,
      month: Number(this.route.snapshot.paramMap.get('month'))!,
      user_id: this.route.snapshot.paramMap.get('user_id')!,
      status_id: 2
    }
    this.trackingService.updateWorkingHourPeriod(payload).subscribe((response) => {
      this.markPeriodLoading = false;
      this.markedAsFinished = true;
      this.toastService.successToast('updated');
    }, error => {
      this.markPeriodLoading = false;
    });
  }

  openTimeTrackingDetails(row: TimeTrackingRow) {
    if (row.entry_id < 0) {
      return;
    }
    let modalRef = this.modalService.open(TimeTrackingDetailsModal, {size: 'lg'});
    modalRef.componentInstance.timeTracking = row;
    modalRef.componentInstance.user = this.user;
    modalRef.componentInstance.salaryView = true;
    modalRef.componentInstance.timeTrackingUnlocked.subscribe(() => {
      this.getTimeTrackings();
    });

    modalRef.result.then((result) => {
        if (result === 'success') {
          this.getTimeTrackings();
        }
      },
      (reason) => { }
    );
  }

  async deleteTimeTrackings() {
    let modalRef = this.modalService.open(VerifyPopupModal)
    modalRef.result.then((result) => {
      if (result) {
        this.deleteLoading = true;
        let selectedRows = this.tableData.filter((row) => row.selected && !row.locked && row.entry_id >= 0);
        const requests = selectedRows.map((row) => {
          return this.trackingService.deleteTimeTracking(row.entry_id);
        });
        forkJoin(requests).subscribe((response) => {
          this.deleteLoading = false;
          this.toastService.successToast('updated');
          this.getTimeTrackings();
        }, error => {
          this.deleteLoading = false;
        });

      } else {
        return;
      }
    });
  }



  openAddTimeTracking() {
    let modalRef = this.modalService.open(TimeTrackingDetailsModal, {size: 'lg'});
    modalRef.componentInstance.user = this.user;
    modalRef.componentInstance.salaryView = true;
    modalRef.result.then((result) => {
        if (result === 'success') {
          this.getTimeTrackings();
        }
      },
      (reason) => { }
    );
  }

  goBack() {
    this.router.navigate(['/salary/overview']);
  }

  formatTotalTime(row: TimeTrackingRow) {
    if (row.auto_registered && row.entry_id > 0) {
      let mainTime = this.utilsService.formatDurationFromSeconds(row.duration_in_seconds);
      let childTime = 0;
      for (const childRow of row.child_trackings.filter((r) => !r.is_pause)) {
        childTime += childRow.duration_in_seconds;
      }

      let pauseTime = 0;
      for (const childRow of row.child_trackings.filter((r) => r.is_pause)) {
          pauseTime += childRow.duration_in_seconds;
      }

      let childTimeString = childTime > 0 ? this.utilsService.formatDurationFromSeconds(childTime) : '';
      let pauseTimeString = pauseTime > 0 ? this.utilsService.formatDurationFromSeconds(pauseTime) + ' ' + this.translate.instant('salary.pause').toLowerCase() : '';
      let divider = childTime > 0 ? ' + ' : '';
      let subString = childTimeString || pauseTimeString ? `<span class="text-muted">(${childTimeString + divider + pauseTimeString})</span>` : '';

      return mainTime + ' ' + subString
    } else {
      return this.utilsService.formatDurationFromSeconds(row.duration_in_seconds);
    }
  }

}
