<div class="modal-header d-flex justify-content-between align-items-center">
  <h4 class="text-center" style="flex-grow: 1;">{{ (absence ? 'salary.absence.modal.title.edit' : 'salary.absence.modal.title.create') | translate:{ name: user.first_name } }}</h4>
</div>
<div class="modal-body row p-3">

  <div class="">
    <label>{{'salary.absence.modal.absenceType' | translate}}</label>
    <app-selectorini
      [disableFocusOnLoad]="true"
      [customNgTemplate]="activitySelectoriniItem"
      [directSelection]="true"
      [predefinedSearchResults]="activities"
      [selectedItem]="selectedActivity"
      [searchMainDisplayKeys]="['activity_name']"
      [placeholderTranslationKey]="'salary.absence.modal.absenceType.placeholder'"
      (itemSelectedEmitter)="activitySelected($event)"
    ></app-selectorini>
  </div>

  <div class="mt-2">
    <label>{{'salary.absence.modal.description' | translate}}</label>
    <app-input
      [editMode]="true"
      [control]="descriptionControl"
    ></app-input>
  </div>

  <div class="d-flex gap-4">
    <div class="mt-2 position-relative" style="min-width: 85px;">
      <label>{{'salary.activeFrom' | translate}}</label>
      <div *ngIf="!activeFrom" class="clickable-text cursor-pointer" (click)="pickerinoFrom.toggle($event)">{{'salary.selectDate' | translate}}</div>
      <div *ngIf="activeFrom" class="clickable-text cursor-pointer" (click)="pickerinoFrom.toggle($event)" (mouseenter)="activeFromHovered = true;" (mouseleave)="activeFromHovered = false;">
        {{displayDate(activeFrom, false, false)}}
        <i *ngIf="activeFrom !== null && this.activeFromHovered" class="fa-regular fa-xmark cursor-pointer px-1" (click)="activeFromChanged(null)"></i>
      </div>
       <app-datepickerino
        #pickerinoFrom
        [popup]="true"
        [selectedDates]="[activeFrom!]"
        [disableAfterDate]="activeTo"
        (datesSelectedEmitter)="activeFromChanged($event[0])"
      ></app-datepickerino>
    </div>

    <div class="mt-2 position-relative" style="min-width: 85px;">
      <label>{{'salary.activeTo' | translate}}</label>
      <div *ngIf="!activeTo" class="clickable-text cursor-pointer" (click)="pickerinoTo.toggle($event)">{{'salary.selectDate' | translate}}</div>
      <div *ngIf="activeTo" class="clickable-text cursor-pointer" (click)="pickerinoTo.toggle($event)" (mouseenter)="activeToHovered = true;" (mouseleave)="activeToHovered = false;">
        {{displayDate(activeTo, false, false)}}
        <i *ngIf="activeTo !== null && this.activeToHovered" class="fa-regular fa-xmark cursor-pointer px-1" (click)="activeToChanged(null)"></i>
      </div>
       <app-datepickerino
        #pickerinoTo
        [popup]="true"
        [selectedDates]="[activeTo!]"
        [disableBeforeDate]="activeFrom"
        (datesSelectedEmitter)="activeToChanged($event[0])"
      ></app-datepickerino>
    </div>

    <div class="mt-2">
      <label>{{'salary.absence.modal.duration' | translate}}</label>
      <div *ngIf="activeTo && activeFrom" class="">{{getDurationInDays()}} {{'common.days' | translate}}</div>
      <div *ngIf="!activeTo || !activeFrom" class="text-muted">{{'salary.absence.modal.durationSet' | translate}}</div>
    </div>
  </div>

  <!-- Vacation days -->
  <div *ngIf="selectedActivity?.uses_vacation_days" class="mt-2">
    <app-spinner *ngIf="vacationLoading"></app-spinner>

    <div *ngIf="!vacationLoading && !activeVacation" class="text-muted">
      <div>{{'salary.absence.modal.noVacation' | translate}}</div>
      <span class="text-muted clickable-text" (click)="addVacationDays()">({{'salary.absence.modal.noVacationClick' | translate}})</span>
    </div>

    <div *ngIf="!vacationLoading && activeVacation" class="text-muted">
      <div>{{'salary.absence.modal.calculatedVacation' | translate: {daysLeft: activeVacation.initial_days - activeVacation.days_used, initialDays: activeVacation.initial_days, year: activeVacation.year} }}</div>
      <div *ngIf="!absence?.approved_at">({{'salary.absence.modal.notIncludingThisAbsence' | translate}})</div>
    </div>
  </div>


</div>

<div class="modal-footer justify-content-between">
  <div class="d-flex gap-2">
    <app-button
      *ngIf="absence"
      [translationKey]="'common.delete'"
      [themeStyle]="'danger'"
      [disabled]="loading"
      (buttonClick)="deleteAbsence()"
    ></app-button>
    <app-button
      [translationKey]="'salary.absence.modal.checkConflicts'"
      [disabled]="!selectedActivity || !activeFrom || !activeTo || loading"
      (buttonClick)="checkForPeriodEvents()"
    ></app-button>
  </div>
  <div class="d-flex gap-2 pe-2">
    <app-button
      [themeStyle]="'secondary'"
      [translationKey]="'common.cancel'"
      (buttonClick)="cancel()"
    ></app-button>
    <app-button
      [translationKey]="'common.save'"
      [loading]="loading"
      [disabled]="!selectedActivity || !activeFrom || !activeTo"
      (buttonClick)="save()"
    ></app-button>
  </div>
</div>

<ng-template #activitySelectoriniItem let-activity="item">
  <div class="d-flex align-items-center">
    <i *ngIf="activity.color_hex" class="fa-solid fa-circle ms-2" [style.color]="activity.color_hex == '#FFFFFF' ? '#A0A0A0' : activity.color_hex"></i>
    <div class="ms-2">{{activity.activity_name}}</div>
  </div>
</ng-template>
