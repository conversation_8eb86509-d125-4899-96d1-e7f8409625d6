import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {NgbActiveModal, NgbModal} from '@ng-bootstrap/ng-bootstrap';

import {FormControl} from "@angular/forms";
import {firstValueFrom} from "rxjs";
import {StandardImports} from "../../../../@shared/global_import";
import {SelectoriniComponent} from "../../../../@shared/components/selectorini/selectorini.component";
import { DatepickerinoComponent } from 'src/app/@shared/components/datepickerino/datepickerino.component';
import {EmployeeAbsenceResponse, TimeTrackingActivityResponse} from "../../../../@shared/models/timetracking.interfaces";
import {EmployeeService} from "../../../../@shared/services/employee.service";
import {displayDate, UtilsService} from "../../../../@core/utils/utils.service";
import {TrackingService} from "../../../../@shared/services/tracking.service";
import {_CRM_EMP_18, _CRM_EMP_7, _CRM_TTR_47, _CRM_TTR_49} from "../../../../@shared/models/input.interfaces";
import {VerifyPopupModal} from "../../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {EmployeeAvailabilityModalComponent} from "../../../../@shared/components/employee-availability-modal/employee-availability-modal.component";
import {AbsenceConflictModalComponent} from "../absence-conflict-modal/absence-conflict-modal.component";
import {InternalUserResponse} from "../../../../@shared/models/user.interfaces";
import {EmployeeVacationDaysResponse} from "../../../../@shared/models/employee.interfaces";
import {SpinnerComponent} from "../../../../@shared/components/spinner/spinner.component";
import {EmployeeVacationModalComponent} from "../../../employees/edit-employee/_modals/employee-vacation-modal/employee-vacation-modal.component";

@Component({
    selector: 'app-employee-absence-modal',
    templateUrl: './employee-absence-modal.template.html',
    standalone: true,
  imports: [StandardImports, SelectoriniComponent, DatepickerinoComponent, SpinnerComponent]
})

export class EmployeeAbsenceModalComponent implements OnInit {

  @Input() absence: EmployeeAbsenceResponse | null = null;
  @Input() user: InternalUserResponse;
  loading: boolean = false;
  activeFrom: Date | null = null;
  activeTo: Date | null = null;
  activeFromHovered: boolean = false;
  activeToHovered: boolean = false;
  activities: TimeTrackingActivityResponse[] = [];
  selectedActivity: TimeTrackingActivityResponse | null = null;
  descriptionControl: FormControl = new FormControl('');
  activeVacation: EmployeeVacationDaysResponse | null = null;
  vacationLoading: boolean = false;

  @Output() absenceCreated = new EventEmitter<EmployeeAbsenceResponse>();
  @Output() absenceDeleted = new EventEmitter<EmployeeAbsenceResponse>();

  constructor(public activeModal: NgbActiveModal, private modalService: NgbModal, private employeeService: EmployeeService, public utilsService: UtilsService, private trackingService: TrackingService) { }

  ngOnInit(): void {
    this.trackingService.getTimeTrackingActivities().subscribe((activities) => {
      if (this.absence) {
        this.activeFrom = this.absence.active_from;
        this.activeTo = this.absence.active_to;
        this.descriptionControl.setValue(this.absence.description);
        this.selectedActivity = activities.find((act) => {
          return act.activity_id === this.absence!.activity_id;
        })!;

        if (this.selectedActivity.uses_vacation_days) {
          this.getUserVacationDays();
        }

      }
      this.activities = activities.filter((act) => {
        return act.activity_type_id === 2
      });
    });
  }

  activeFromChanged(date: Date | null) {
    this.activeFrom = date;
  }

  activeToChanged(date: Date | null) {
    this.activeTo = date;
  }

  activitySelected(act: TimeTrackingActivityResponse | any) {
    this.selectedActivity = act;
    this.descriptionControl.setValue(act.activity_name);
    if (this.selectedActivity?.uses_vacation_days) {
      this.getUserVacationDays();
    }
  }

  getUserVacationDays() {
    this.vacationLoading = true;
    this.employeeService.getUserVacationDays(this.user.user_id).subscribe((days) => {
      this.activeVacation = days.find(day => {
        return day.year = new Date().getFullYear();
      }) || null;
      this.vacationLoading = false;
    });
  }

  addVacationDays() {
    let modalRef = this.modalService.open(EmployeeVacationModalComponent, {size: 'md'});
    modalRef.componentInstance.userId = this.user.user_id;
    modalRef.result.then((res: EmployeeVacationDaysResponse) => {
      if (res) {
        this.activeVacation = res;
      }
    });
  }

  getDurationInDays() {
    if (!this.activeFrom || !this.activeTo) {
      return 0;
    }
    let start = new Date(this.activeFrom);
    let end = new Date(this.activeTo);
    let duration = Math.abs(end.getTime() - start.getTime());
    let diffDays = Math.ceil(duration / (1000 * 3600 * 24));
    return diffDays + 1;
  }

  async save() {
    if (!this.activeFrom || !this.activeTo) {
      return;
    }
    if (!this.selectedActivity) {
      return;
    }

    this.loading = true;
    // Put
    if (this.absence) {
      let payload: _CRM_TTR_49 = {
        entry_id: this.absence.entry_id,
        active_from: this.activeFrom,
        active_to: this.activeTo,
        activity_id: this.selectedActivity.activity_id,
        description: this.descriptionControl.value,
      }

      this.trackingService.updateAbsence(payload).subscribe((ab) => {
        this.activeModal.close(ab);
        this.loading = false;
      }, error => {
        this.loading = false;
      });
    } else {
      // Post
      let payload: _CRM_TTR_47 = {
        user_id: this.user.user_id,
        active_from: this.activeFrom,
        active_to: this.activeTo,
        activity_id: this.selectedActivity.activity_id,
        description: this.descriptionControl.value,
        approve: true,
      }

      this.trackingService.createAbsence(payload).subscribe((ab) => {
        this.activeModal.close(ab);
        this.loading = false;
      }, error => {
        this.loading = false;
      });
    }
  }

  async checkForPeriodEvents() {
    let activeFrom = this.activeFrom;
    let activeTo = this.activeTo;
    let modalRef = this.modalService.open(AbsenceConflictModalComponent, {size: 'xl'})
    modalRef.componentInstance.dateFrom = activeFrom;
    modalRef.componentInstance.dateTo = activeTo;
    modalRef.componentInstance.user = this.user;
    const modalRes = await modalRef.result;

  }

  async deleteAbsence() {
    this.loading = true;
    let modalRef = this.modalService.open(VerifyPopupModal)
    let continueDeletion = await modalRef.result;

    if (!continueDeletion) {
      this.loading = false;
      return;
    }

    this.trackingService.deleteAbsence(this.absence!.entry_id).subscribe(() => {
      this.loading = false;
      this.absenceDeleted.emit(this.absence!);
      this.activeModal.close(true);
    })
  }

  cancel() {
    this.activeModal.close(false)
  }

  protected readonly displayDate = displayDate;
}
