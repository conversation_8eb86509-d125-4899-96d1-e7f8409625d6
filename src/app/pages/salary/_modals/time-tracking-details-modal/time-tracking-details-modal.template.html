<div *ngIf="timeTracking" class="hidden-id">{{timeTracking.entry_id}}</div>
<div class="modal-header d-flex justify-content-between align-items-center">
  <div *ngIf="!user" id="dummyDiv1" class="col-4"></div>
  <div *ngIf="user" class="d-flex align-items-center col-4">
    <app-colored-initials class="me-2" [user]="user" [hovered]="nameHovered"></app-colored-initials>
    <h4 class="mt-2 mb-1" [class.user-line-hover]="nameHovered">
      <span class="cursor-pointer" (mouseenter)="nameHovered = true;" (mouseleave)="nameHovered = false;" (click)="router.navigate(['employees/edit/' + user.user_id]); activeModal.close()">{{user.full_name}}</span>
    </h4>
  </div>

  <h4 *ngIf="!timeTracking" class="text-center">{{"reports.timeTracking.modal.title" | translate }}</h4>
  <h4 *ngIf="timeTracking" class="text-center">{{"reports.timeTracking.modal.editTitle" | translate }}</h4>

  <div *ngIf="!user" id="dummyDiv" class="col-4"></div>
  <div *ngIf="user" class="col-4 d-flex justify-content-end">
    <i *ngIf="locked" class="fa-solid fa-lock fa-xl cursor-pointer"></i>
  </div>


</div>
<div class="modal-body row">

  <div *ngIf="!user" class="d-flex justify-content-center">
    <div class="col-6">
      <label>{{'reports.timeTracking.modal.selectEmployee' | translate}}</label>
      <app-selectorini
        [disableFocusOnLoad]="true"
        [directSelection]="true"
        [predefinedSearchResults]="users"
        [searchMainDisplayKeys]="['full_name']"
        (itemSelectedEmitter)="userSelected($event)"
      ></app-selectorini>
    </div>
  </div>

  <div *ngIf="user" class="col-8 px-3">

    <!--  Duration and date  -->
    <div class="d-flex gap-2">
      <div class="col-6">
        <label>{{"common.duration" | translate}}</label>
        <app-selectorini
          [directSelection]="true"
          [predefinedSearchResults]="durationChoices"
          [disabledSearchResults]="disabledDurationChoices"
          [searchMainDisplayKeys]="['display']"
          [disabledTranslationKey]="'Over midnatt'"
          [listItemMinHeightPx]="40"
          [selectedItem]="selectedDuration"
          (itemSelectedEmitter)="durationSelected($event)"
        ></app-selectorini>
      </div>

      <div class="col-6 position-relative">
        <label>{{"common.date" | translate}}</label>
        <h4 class="clickable-text cursor-pointer" (click)="pickerinoExecution.toggle($event)">{{displayDate(currentDate, true, false)}}</h4>
        <app-datepickerino
          #pickerinoExecution
          [popup]="true"
          [selectedDates]="[currentDate!]"
          (datesSelectedEmitter)="dateChanged($event[0])"
        ></app-datepickerino>
      </div>
    </div>

    <!--  Start and stop time  -->
    <div class="d-flex mt-3">
      <div class="col-6 pe-1">
        <label>{{ "reports.timeTracking.modal.start" | translate }}</label>
        <app-input
          [type]="'time'"
          [control]="timeFromControl"
          [editMode]="true"
          (valueChange)="timeFromChanged($event)"
        ></app-input>
      </div>

      <div class="col-6 ps-1">
        <label>{{ "reports.timeTracking.modal.stop" | translate }} {{timeTracking && !timeTracking.stopped_at ? '(Pågående)' : ''}}</label>
        <app-input
          [type]="'time'"
          [control]="timeToControl"
          [editMode]="true"
          (valueChange)="timeToChanged($event)"
        ></app-input>
      </div>
    </div>
    <div *ngIf="errorMessage" class="text-danger">{{errorMessage}}</div>

    <!--  Description  -->
    <div class="mt-3">
      <label>{{ "reports.timeTracking.modal.description" | translate }}</label>
      <app-input
        [textArea]="true"
        [control]="descriptionControl"
        [editMode]="true"
      ></app-input>
    </div>

    <div class="mt-3 d-flex gap-2">
      <div class="col">
        <label>{{'reports.timeTracking.modal.job' | translate}}</label>
        <app-selectorini
          [disabled]="locked"
          [multiSelect]="false"
          [selectedItem]="selectedWorkOrder"
          [searchFunction]="searchWorkOrders.bind(this)"
          [predefinedSearchResults]="preloadedWorkOrders"
          [selectedMainDisplayKeys]="['work_order_number', 'work_order_title', 'payment_recipient_name']"
          [selectedDisplaySeparator]="' - '"
          [searchMainDisplayKeys]="['work_order_number', 'work_order_title', 'payment_recipient_name']"
          [searchSubDisplayKeys]="['display_addresses']"
          [placeholderTranslationKey]="'Søk etter jobb'"
          [showCrossButton]="false"
          (itemSelectedEmitter)="onSelectedJobChange($event)"
        ></app-selectorini>
      </div>

      <div class="d-flex align-items-end">
        <app-button
          [buttonWidth]="91"
          [buttonType]="'nude'"
          [translationKey]="'Åpne jobb'"
          [disabled]="!selectedWorkOrder"
          (buttonClick)="openWorkOrder()"
        ></app-button>
      </div>
    </div>

    <div class="mt-3">
      <label>{{'reports.timeTracking.modal.activity' | translate}}</label>
      <app-selectorini
        [customNgTemplate]="activitySelectoriniItem"
        [disabled]="locked"
        [multiSelect]="false"
        [selectedItem]="selectedActivity"
        [directSelection]="true"
        [searchMainDisplayKeys]="['activity_name']"
        [placeholderTranslationKey]="'Velg aktivitet'"
        [predefinedSearchResults]="activities"
        (itemSelectedEmitter)="onSelectedActivityChange($event)"
      ></app-selectorini>
    </div>


    <div *ngIf="projectsEnabled || departmentsEnabled" class="mt-3 d-flex">
      <div class="pe-1" [ngClass]="departmentsEnabled ? 'col-6' : 'col'">
        <label>{{'reports.timeTracking.modal.project' | translate}}</label>
        <app-selectorini
          [disabled]="locked"
          [multiSelect]="false"
          [selectedItem]="selectedProject"
          [directSelection]="true"
          [searchMainDisplayKeys]="['project_name']"
          [placeholderTranslationKey]="'Velg prosjekt'"
          [predefinedSearchResults]="projects"
          (itemSelectedEmitter)="onSelectedProjectChange($event)"
        ></app-selectorini>
      </div>

      <div *ngIf="departmentsEnabled" class="col-6 ps-1">
        <label>{{'reports.timeTracking.modal.department' | translate}}</label>
        <app-selectorini
          [disabled]="locked"
          [multiSelect]="false"
          [selectedItem]="selectedDepartment"
          [directSelection]="true"
          [searchMainDisplayKeys]="['department_name']"
          [placeholderTranslationKey]="'Velg avdeling'"
          [predefinedSearchResults]="departments"
          (itemSelectedEmitter)="onSelectedDepartmentChange($event)"
        ></app-selectorini>
      </div>

    </div>

    <!--  Comment  -->
    <div class="mt-3">
      <label>{{ "reports.timeTracking.modal.comment" | translate }}</label>
      <app-input
        [textArea]="true"
        [control]="commentControl"
        [editMode]="true"
      ></app-input>
    </div>

    <!--  Use in salary  -->
    <div *ngIf="!salaryView" class="mt-3">
      <app-toggle-switch
        [bigSwitch]="true"
        [isDisabled]="locked"
        [labelKey]="'reports.timeTracking.modal.useInSalary'"
        [state]="useInSalary"
        (stateChange)="useInSalary = $event"
      ></app-toggle-switch>
    </div>

  <div *ngIf="timeTracking" class="mt-2">
    <h5 class="my-0">Føringstype</h5>
    <div *ngIf="timeTracking.auto_registered" class="">Automatisk registrert fra oppdrag</div>
    <div *ngIf="!timeTracking.auto_registered && timeTracking.entry_id" class="">Manuell føring</div>
    <div *ngIf="!timeTracking.entry_id" class="">Planlagt arbeidstid, ikke ført enda</div>
  </div>

  <h5 *ngIf="children.length > 0" class="mt-2 mb-0">Registrerte føringer</h5>
  <div *ngFor="let tracking of children" class="child-box mt-1">
    <div class="d-flex">
      <span>{{ tracking.started_at | date:'shortTime' }} - {{ tracking.stopped_at | date:'shortTime' }}</span>
      <span class="mx-1">=</span>
      <span>{{utilsService.formatDurationFromSeconds(tracking.duration_in_seconds)}}</span>
      <span *ngIf="tracking.is_pause" class="ms-1">({{'salary.pause' | translate}}{{tracking.auto_registered ? ', ' + ('reports.timeTracking.modal.autoRegistered' | translate).toLowerCase() : ''}})</span>
    </div>
  </div>


  </div>

  <!-- Logs -->
  <div *ngIf="user" class="col-4" style="border-left: 1px solid #e9ecef; max-height: 540px; overflow-y: scroll;">
    <h4 class="mb-1 mt-0">Endringer</h4>
    <hr class="mb-1 mt-0">
    <div *ngFor="let logEntry of logs" class="my-0 py-0" style="white-space: pre-wrap;">
      <div class="bullet-item mb-1">
        <div class="font-12">{{logEntry.changes[0]}}</div>
        <div class="d-flex justify-content-end font-11">{{logEntry.set_by.user_id.startsWith('MOD') ? 'Scheduler' : logEntry.set_by.full_name}} - {{displayDate(logEntry.set_at)}}</div>
      </div>
    </div>
  </div>

</div>


<div class="modal-footer justify-content-between pe-2">
  <div>
    <app-button
      [themeStyle]="'danger'"
      [translationKey]="'common.delete'"
      *ngIf="timeTracking && !locked"
      (buttonClick)="deleteTimeTracking()"
    ></app-button>
  </div>
  <div class="d-flex gap-1">
    <app-button
      (buttonClick)="activeModal.close(false)"
      [themeStyle]="'secondary'"
      [translationKey]="'common.close'"
      [disabled]="loading"
    ></app-button>
    <app-button
      *ngIf="!locked && user"
      (buttonClick)="save()"
      [themeStyle]="'primary'"
      [disabled]="!!errorMessage"
      [translationKey]="'common.save'"
      [loading]="loading"
    ></app-button>
    <app-button
      *ngIf="locked"
      (buttonClick)="unlockTimeTracking()"
      [themeStyle]="'primary'"
      [translationKey]="'Lås opp'"
      [loading]="unlockLoading"
    ></app-button>
  </div>
</div>


<ng-template #activitySelectoriniItem let-activity="item">
  <div class="d-flex align-items-center">
    <i *ngIf="activity.color_hex" class="fa-solid fa-circle ms-2" [style.color]="activity.color_hex == '#FFFFFF' ? '#F0F0F0' : activity.color_hex"></i>
    <div class="ms-2">{{activity.activity_name}}</div>
  </div>
</ng-template>
