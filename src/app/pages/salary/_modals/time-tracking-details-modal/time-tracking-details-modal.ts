import {Component, EventEmitter, Input, OnInit, Output, TemplateRef, ViewChild} from '@angular/core';
import {NgbActiveModal, NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {TimeTrackingActivityResponse, TimeTrackingLogResponse, TimeTrackingResponse,} from "../../../../@shared/models/timetracking.interfaces";
import {TranslateService} from "@ngx-translate/core";
import {SelectoriniComponent} from "../../../../@shared/components/selectorini/selectorini.component";
import {TrackingService} from "../../../../@shared/services/tracking.service";
import {FormControl} from "@angular/forms";
import {DatepickerinoComponent} from "../../../../@shared/components/datepickerino/datepickerino.component";
import {_CRM_ORD_170, _CRM_TTR_0, _CRM_TTR_2} from "../../../../@shared/models/input.interfaces";
import {displayDate, formatTimeHM, UtilsService} from "../../../../@core/utils/utils.service";

import {ToastService} from "../../../../@core/services/toast.service";
import {InternalUserResponse} from "../../../../@shared/models/user.interfaces";
import {EmployeeService} from "../../../../@shared/services/employee.service";
import {Router} from "@angular/router";
import {VerifyPopupModal} from "../../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {ColoredInitialsComponent} from "../../../../@shared/components/colored-initials/colored-initials.component";
import {WorkOrderCompactResponse, WorkOrderResponse} from "../../../../@shared/models/order.interfaces";
import {OrderService} from "../../../../@shared/services/order.service";
import {ProjectResponse} from "../../../../@shared/models/projects.interfaces";
import {DepartmentResponse} from "../../../../@shared/models/departments.interfaces";
import {WorkOrderDetailsComponent, WorkOrderDetailsModal} from "../../../work-orders/components/work-order-details/work-order-details.component";
import {StandardImports} from "../../../../@shared/global_import";
import {ToggleSwitchComponent} from "../../../../@shared/components/toggle-switch/toggle-switch.component";
import {StorageService} from "../../../../@core/services/storage.service";
import {ProjectsService} from "../../../../@shared/services/projects.service";
import {DepartmentsService} from "../../../../@shared/services/departments.service";
import {combineLatest} from "rxjs";

@Component({
    selector: 'app-working-hours-modal',
    templateUrl: './time-tracking-details-modal.template.html',
    styleUrls: ['./time-tracking-details-modal.component.css'],
    standalone: true,
  imports: [StandardImports, ColoredInitialsComponent, SelectoriniComponent, DatepickerinoComponent, ToggleSwitchComponent]
})

export class TimeTrackingDetailsModal implements OnInit {

  @Input() timeTracking: TimeTrackingResponse | null = null;
  @Input() user: InternalUserResponse;
  @Input() salaryTypeId: number | null = null;
  @Input() supplement: boolean = false;
  @Input() activityId: number | null = null;
  @Input() salaryView: boolean = false;

  activities: TimeTrackingActivityResponse[] = [];
  selectedActivity: TimeTrackingActivityResponse | null = null;
  preloadedWorkOrders: WorkOrderCompactResponse[] = [];
  selectedWorkOrder: WorkOrderResponse | null = null;
  projects: ProjectResponse[] = [];
  selectedProject: ProjectResponse | null = null;
  departments: DepartmentResponse[] = [];
  selectedDepartment: DepartmentResponse | null = null;
  logs: TimeTrackingLogResponse[] = [];
  children: TimeTrackingResponse[] = [];

  timeFromControl = new FormControl();
  timeToControl = new FormControl();
  commentControl = new FormControl();
  descriptionControl = new FormControl();

  durationChoices: {value: number, display: string}[] = [];
  selectedDuration: {value: number, display: string} | null = null;
  disabledDurationChoices: {value: number, display: string}[] = [];
  currentDate = new Date();
  useInSalary: boolean = true;

  loading: boolean = false;
  unlockLoading: boolean = false;
  locked: boolean = false;
  nameHovered: boolean = false;
  users: InternalUserResponse[] = [];
  errorMessage: string | null = null;
  timesChanged: boolean = false;

  projectsEnabled: boolean = false;
  departmentsEnabled: boolean = false;
  projectsAndDepartmentsLoading: boolean = false;

  @Output() timeTrackingUnlocked: EventEmitter<any> = new EventEmitter();
  @Output() timeTrackingUpdated: EventEmitter<TimeTrackingResponse> = new EventEmitter();

  @ViewChild('activitySelectoriniItem') activitySelectoriniItem: TemplateRef<any>;

  constructor(
    private orderService: OrderService,
    public activeModal: NgbActiveModal,
    private trackingService: TrackingService,
    public utilsService: UtilsService,
    private toastService: ToastService,
    private employeeService: EmployeeService,
    public router: Router,
    private modalService: NgbModal,
    private translateService: TranslateService,
    private storageService: StorageService,
    private projectsService: ProjectsService,
    private departmentService: DepartmentsService,
  ) {
  }

  ngOnInit() {
    this.storageService.projectsEnabled$.subscribe((enabled) => {
      this.projectsEnabled = enabled;
    });
    this.storageService.departmentsEnabled$.subscribe((enabled) => {
      this.departmentsEnabled = enabled;
    });

    if (this.projectsEnabled || this.departmentsEnabled) {
      this.projectsAndDepartmentsLoading = true;
      const requests = [];
      if (this.projectsEnabled) {
        requests.push(this.projectsService.getCompanyProjects());
      }
      if (this.departmentsEnabled) {
        requests.push(this.departmentService.getCompanyDepartments());
      }
      combineLatest(requests).subscribe(([projects, departments]) => {
        this.projects = projects as ProjectResponse[];
        this.departments = departments as DepartmentResponse[];
        this.projectsAndDepartmentsLoading = false;
      });
    }

    for (let i = 1; i <= 32; i++) {
      let value = i * 15;
      let hours = Math.floor(value / 60);
      let minutes = value % 60;
      this.durationChoices.push({value: value, display: `${hours}${this.translateService.instant('HOUR-ABBREVIATION')} ${minutes}m`});
    }

    if (!this.timeTracking) {
      this.selectedDuration = this.durationChoices[3];
      this.timeFromControl.setValue('08:00');
      this.timeToControl.setValue('09:00');
    } else {
      this.selectedProject = this.timeTracking.project;
      this.selectedDepartment = this.timeTracking.department;
      this.useInSalary = this.timeTracking.use_in_salary;
      let duration_in_minutes = Math.floor(this.timeTracking.duration_in_seconds / 60);
      this.selectedDuration = {value: duration_in_minutes, display: `${Math.floor(duration_in_minutes / 60)}${this.translateService.instant('HOUR-ABBREVIATION')} ${duration_in_minutes % 60}m`};
      this.currentDate = new Date(this.timeTracking.started_at);
      this.commentControl.setValue(this.timeTracking.comment);
      this.descriptionControl.setValue(this.timeTracking.description);
      this.timeFromControl.setValue(formatTimeHM(this.timeTracking.started_at));
      if (this.timeTracking.stopped_at) {
        this.timeToControl.setValue(formatTimeHM(this.timeTracking.stopped_at));
      }
      this.trackingService.getTrackingLogs(this.timeTracking.entry_id).subscribe((res) => {
        this.logs = [];
        res.map((log) => {
          log.changes.map((change) => {
            this.logs.push({changes: [change], set_by: log.set_by, set_at: log.set_at});
          });
        });
      });
       this.trackingService.getTimeTrackingChildren(this.timeTracking.entry_id).subscribe((res) => {
        this.children = res;
      });
      this.durationChanged(true);
    }

    this.trackingService.getTimeTrackingActivities().subscribe((activities) => {
      this.activities = activities.sort((a, b) => a.index - b.index).map((a) => {
        return {
          ...a,
          ngTemplate: this.activitySelectoriniItem,
        }
      });
      if (this.timeTracking && this.timeTracking.activity_id) {
        this.selectedActivity = this.activities.find((activity) => activity.activity_id === this.timeTracking!.activity_id) || null;
      }
    });

    if (this.timeTracking?.work_order_id) {
      this.orderService.getWorkOrderById(this.timeTracking.work_order_id).subscribe((res) => {
        this.selectedWorkOrder = [res].map((res) => {
          return {
            ...res,
            work_order_number: '#' + res.work_order_number,
            address_display: res.addresses.length > 0 ? res.addresses[0].display : ''
          }
        })[0];
      });
    }

    this.locked = this.timeTracking?.locked || false;
    if (this.locked) {
      this.timeToControl.disable();
      this.timeFromControl.disable();
      this.commentControl.disable();
      this.descriptionControl.disable();
    }

    if (!this.timeTracking?.work_order_id) {
      this.preloadWorkOrders();
    }


    if (!this.user) {
      this.employeeService.getEmployees({}).subscribe(res => {
        this.users = res.data.sort((a, b) => a.full_name.localeCompare(b.full_name));
      });
    }
  }

  timeFromChanged(time: string) {
    let timeFrom = time.split(':');
    let hoursFrom = parseInt(timeFrom[0]);
    let minutesFrom = parseInt(timeFrom[1]);
    let dateFrom = new Date(this.currentDate);
    dateFrom.setHours(hoursFrom, minutesFrom, 0, 0);

    let dateTo = new Date(this.currentDate);
    if (this.timeToControl.value) {
      let timeTo = this.timeToControl.value.split(':');
      let hoursTo = parseInt(timeTo[0]);
      let minutesTo = parseInt(timeTo[1]);
      dateTo.setHours(hoursTo, minutesTo, 0, 0);
      if (dateTo.getTime() < dateFrom.getTime()) {
        this.errorMessage = 'Starttidspunkt kan ikke være etter stopptidspunkt';
        return;
      } else {
        this.errorMessage = null;
      }
    } else {
      dateTo = new Date();
    }


    let duration = Math.floor((dateTo.getTime() - dateFrom.getTime()) / 60000);
    this.selectedDuration = {value: duration, display: `${Math.floor(duration / 60)}${this.translateService.instant('HOUR-ABBREVIATION')} ${duration % 60}m`};
    this.durationChanged();
  }

  timeToChanged(time: string) {
    let timeTo = time.split(':');
    let hoursTo = parseInt(timeTo[0]);
    let minutesTo = parseInt(timeTo[1]);
    let dateTo = new Date(this.currentDate);
    dateTo.setHours(hoursTo, minutesTo, 0, 0);

    let timeFrom = this.timeFromControl.value.split(':');
    let hoursFrom = parseInt(timeFrom[0]);
    let minutesFrom = parseInt(timeFrom[1]);
    let dateFrom = new Date(this.currentDate);
    dateFrom.setHours(hoursFrom, minutesFrom, 0, 0);

    if (dateTo.getTime() < dateFrom.getTime()) {
      this.errorMessage = 'Starttidspunkt kan ikke være etter stopptidspunkt';
      return;
    } else {
      this.errorMessage = null;
    }

    let duration = Math.floor((dateTo.getTime() - dateFrom.getTime()) / 60000);
    this.selectedDuration = {value: duration, display: `${Math.floor(duration / 60)}${this.translateService.instant('HOUR-ABBREVIATION')} ${duration % 60}m`};
    this.durationChanged();
  }

  durationChanged(initial: boolean = false) {
    if (!initial) {
      this.timesChanged = true;
    }
    this.disabledDurationChoices = [];
    let limit = new Date(this.currentDate)
    limit.setHours(23, 59, 59, 999);
    let startTimestamp = new Date(this.currentDate);
    let timeFrom = this.timeFromControl.value.split(':');
    let hoursFrom = parseInt(timeFrom[0]);
    let minutesFrom = parseInt(timeFrom[1]);
    startTimestamp.setHours(hoursFrom, minutesFrom, 0, 0);


    for (let choice of this.durationChoices) {
      let resultDate = this.addMinutes(startTimestamp, choice.value);
        if (resultDate.getTime() > limit.getTime()) {
            this.disabledDurationChoices.push(choice);
        }
    }
  }

  addMinutes(date: Date, minutes: number) {
    return new Date(date.getTime() + minutes * 60000);
  }

  userSelected(user: InternalUserResponse | any) {
    this.user = user;
  }

  dateChanged(date: Date) {
    this.currentDate = new Date(date);
  }

  preloadWorkOrders() {
    this.searchWorkOrders('', true).subscribe((res) => {
      this.preloadedWorkOrders = res.map((wo) => {
        return {
            ...wo,
            work_order_number: '#' + wo.work_order_number,
            address_display: wo.display_addresses.length > 0 ? wo.display_addresses[0] : ''
          }
      });
    });
  }

  durationSelected(duration: {value: number, display: string} | any) {
    this.selectedDuration = duration;
    let durationSeconds = duration.value * 60;
    let timeFrom = this.timeFromControl.value.split(':');
    let hoursFrom = parseInt(timeFrom[0]);
    let minutesFrom = parseInt(timeFrom[1]);
    let dateFrom = new Date(this.currentDate);
    dateFrom.setHours(hoursFrom, minutesFrom, 0, 0);
    let dateTo = new Date(dateFrom.getTime() + durationSeconds * 1000);
    this.timeToControl.setValue(formatTimeHM(dateTo));
    this.timesChanged = true;
  }

  onSelectedJobChange(job: WorkOrderResponse | any) {
    this.selectedWorkOrder = job;
    if (this.selectedWorkOrder?.activity_id) {
      this.selectedActivity = this.activities.find((activity) => activity.activity_id === this.selectedWorkOrder?.activity_id) || this.selectedActivity;
    }
  }

  onSelectedActivityChange(activity: TimeTrackingActivityResponse | any) {
    this.selectedActivity = activity;
  }

  onSelectedProjectChange(project: ProjectResponse | any) {
    this.selectedProject = project;
  }

  onSelectedDepartmentChange(department: DepartmentResponse | any) {
    this.selectedDepartment = department;
  }

  selectedJobRemoved() {
    this.selectedWorkOrder = null;
  }

  searchWorkOrders(searchString: string, useDate: boolean = false) {
    let params: _CRM_ORD_170 = {
      search_string: searchString,
      paginate: 1,
      limit: 10,
      page: 1,
    }

    if (useDate) {
      let dateFrom = new Date(this.currentDate);
      dateFrom.setHours(0, 0, 0, 0);
      let dateTo = new Date(this.currentDate);
      dateTo.setHours(23, 59, 59, 999);
      params.execution_date_from = dateFrom;
      params.execution_date_to = dateTo;
    }
    return this.orderService.selectoriniWorkOrderSearch(params);
  }

  openWorkOrder() {
    const modalRef = this.modalService.open(WorkOrderDetailsComponent, { size: 'xl' });
    modalRef.componentInstance.workOrderId = this.selectedWorkOrder?.work_order_id;
    modalRef.componentInstance.viewSettings = {
      ...WorkOrderDetailsModal,
      workOrderStandaloneView: true
    };
    this.activeModal.close(false);
  }

  deleteTimeTracking() {
    let modalRef = this.modalService.open(VerifyPopupModal)
    modalRef.result.then(
      (result) => {
        if (result) {
          this.trackingService.deleteTimeTracking(this.timeTracking!.entry_id).subscribe(res => {
            this.activeModal.close('success');
          });
        }
      },
      (reason) => { }
    );
  }

  unlockTimeTracking() {
    this.unlockLoading = true;
    this.trackingService.unlockTimeTracking(this.timeTracking!.entry_id).subscribe({
      next: (res) => {
        this.locked = false;
        this.timeToControl.enable();
        this.timeFromControl.enable();
        this.commentControl.enable();
        this.descriptionControl.enable();
        this.timeTrackingUnlocked.emit();
        this.trackingService.getTrackingLogs(this.timeTracking!.entry_id).subscribe((res) => {
          this.logs = [];
          res.map((log) => {
            log.changes.map((change) => {
              this.logs.push({changes: [change], set_by: log.set_by, set_at: log.set_at});
            });
          });
        });
      },
      error: (error) => {
        this.unlockLoading = false;
      },
      complete: () => {
        this.unlockLoading = false;
      },
    });
  }

  save() {
    if (!this.user) {
      return
    }
    this.loading = true;

    let startedAt = new Date(this.currentDate);
    let timeFrom = this.timeFromControl.value.split(':');
    let hoursFrom = parseInt(timeFrom[0]);
    let minutesFrom = parseInt(timeFrom[1]);
    startedAt.setHours(hoursFrom, minutesFrom, 0, 0);

    let stoppedAt: Date | null = null;
    if (this.timeToControl.value) {
      stoppedAt = new Date(this.currentDate);
      let timeTo = this.timeToControl.value.split(':');
      let hoursTo = parseInt(timeTo[0]);
      let minutesTo = parseInt(timeTo[1]);
      stoppedAt.setHours(hoursTo, minutesTo, 0, 0);
    }

    if (!this.timeTracking) {
      let payload: _CRM_TTR_0 = {
        description: this.descriptionControl.value,
        user_id: this.user.user_id,
        started_at: startedAt,
        stopped_at: stoppedAt!,
        work_order_id: this.selectedWorkOrder?.work_order_id || null,
        activity_id: this.selectedActivity?.activity_id!,
        comment: this.commentControl.value,
        use_in_salary: !this.salaryView ? this.useInSalary : true,
        project_id: this.selectedProject?.project_id || null,
        department_id: this.selectedDepartment?.department_id || null
      };

      this.trackingService.createTimeTracking(payload).subscribe({
        next: (res) => {
          this.loading = false;
        },
        error: (error) => {
          this.activeModal.dismiss('cancel');
          this.loading = false;
        },
        complete: () => {
          this.toastService.successToast("timeTracking_created")
          this.activeModal.close('success');
          this.loading = false;
        },
      });
    }

    else {
      let payload: _CRM_TTR_2 = {
        description: this.descriptionControl.value,
        entry_id: this.timeTracking.entry_id,
        work_order_id: this.selectedWorkOrder?.work_order_id || null,
        activity_id: this.selectedActivity?.activity_id!,
        comment: this.commentControl.value,
        use_in_salary: !this.salaryView ? this.useInSalary : true,
        project_id: this.selectedProject?.project_id || null,
        department_id: this.selectedDepartment?.department_id || null
      };

      if (this.timesChanged) {
        payload.started_at = startedAt;
        if (stoppedAt) {
            payload.stopped_at = stoppedAt;
        }
      }

      this.trackingService.editTimeTracking(payload).subscribe({
        next: (res) => {
          this.timeTrackingUpdated.emit(res);
        },
        error: (error) => {
          this.activeModal.dismiss('cancel');
        },
        complete: () => {
          this.toastService.successToast("timeTracking_updated")
          this.activeModal.close('success');
          this.loading = false;
        },
      });
    }
  }

  protected readonly displayDate = displayDate;
}
