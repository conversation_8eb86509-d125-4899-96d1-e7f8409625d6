<div *ngIf="wh" class="hidden-id">{{wh.entry_id}}</div>
<div class="modal-header d-flex justify-content-between align-items-center">
  <h4 class="text-center" style="flex-grow: 1;">{{ (wh ? 'salary.approvedHours.workingHoursModal.edit' : 'salary.approvedHours.workingHoursModal.create') | translate }}</h4>
</div>
<div class="modal-body row p-3">

  <div class="d-flex align-items-center mb-3">
    <div class="initials-box me-2" [class.initials-box-hover]="nameHovered" [style.background-color]="getInitialsBackgroundColor(user.user_id)">{{getUserInitials(user)}}</div>
    <h4 class="mt-2 mb-1 col" [class.user-line-hover]="nameHovered" style="border-bottom: 2px solid #e9ecef;">
      <span class="cursor-pointer" (mouseenter)="nameHovered = true;" (mouseleave)="nameHovered = false;" (click)="router.navigate(['employees/edit/' + user.user_id]); activeModal.close()">{{user.full_name}}</span>
    </h4>
  </div>

  <div class="d-flex">
    <div class="col-6">
      <div *ngIf="!wh" class="mb-2">
        <label>{{'salary.approvedHours.workingHoursModal.type' | translate}}</label>
        <app-selectorini
          [disableFocusOnLoad]="true"
          [predefinedSearchResults]="registrationTypes"
          [selectedItem]="selectedRegistrationType"
          [searchMainDisplayKeys]="['name']"
          [directSelection]="true"
          (itemSelectedEmitter)="onSelectedRegistrationType($event)"
        ></app-selectorini>
      </div>

      <div *ngIf="!supplement" class="">
        <label>{{'salary.approvedHours.workingHoursModal.activity' | translate}}</label>
        <app-selectorini
          [customNgTemplate]="activitySelectoriniItem"
          [disabled]="locked"
          [disableFocusOnLoad]="true"
          [predefinedSearchResults]="activities"
          [selectedItem]="selectedActivity"
          [searchMainDisplayKeys]="['activity_name']"
          [directSelection]="true"
          (itemSelectedEmitter)="onSelectedActivity($event)"
        ></app-selectorini>
      </div>

      <div *ngIf="supplement" class="mt-2">
        <label>{{'salary.approvedHours.workingHoursModal.salaryType' | translate}}</label>
        <app-selectorini
          [disabled]="locked || !!this.wh"
          [predefinedSearchResults]="salaryTypes"
          [selectedItem]="selectedSalaryType"
          [searchMainDisplayKeys]="['salary_type_name']"
          [directSelection]="true"
          (itemSelectedEmitter)="onSelectedSalaryType($event)"
        ></app-selectorini>
      </div>

      <div class="mt-2">
        <label>{{((wh?.calculation_type_id || selectedSalaryType?.calculation_type_id) === 2 ? 'salary.approvedHours.workingHoursModal.quantity' : 'salary.approvedHours.workingHoursModal.hours') | translate}}</label>
        <app-input
          [editMode]="true"
          [control]="durationHoursControl"
          [type]="'number'"
          [maxDecimals]="2"
          (focusin)="quantityFocus()"
        ></app-input>
      </div>

      <div class="mt-2">
        <label>{{'salary.approvedHours.workingHoursModal.comment' | translate}}</label>
        <app-input
          [textArea]="true"
          [control]="commentControl"
        ></app-input>
      </div>

      <div *ngIf="projectsEnabled || departmentsEnabled" class="mt-2 d-flex">
        <div class="pe-1" [ngClass]="departmentsEnabled ? 'col-6' : 'col'">
          <label>{{'reports.timeTracking.modal.project' | translate}}</label>
          <app-selectorini
            [disabled]="locked"
            [multiSelect]="false"
            [selectedItem]="selectedProject"
            [directSelection]="true"
            [searchMainDisplayKeys]="['project_name']"
            [placeholderTranslationKey]="'Velg prosjekt'"
            [predefinedSearchResults]="projects"
            (itemSelectedEmitter)="onSelectedProjectChange($event)"
          ></app-selectorini>
        </div>

        <div *ngIf="departmentsEnabled" class="col-6 ps-1">
          <label>{{'reports.timeTracking.modal.department' | translate}}</label>
          <app-selectorini
            [disabled]="locked"
            [multiSelect]="false"
            [selectedItem]="selectedDepartment"
            [directSelection]="true"
            [searchMainDisplayKeys]="['department_name']"
            [placeholderTranslationKey]="'Velg avdeling'"
            [predefinedSearchResults]="departments"
            (itemSelectedEmitter)="onSelectedDepartmentChange($event)"
          ></app-selectorini>
        </div>
      </div>

      <div *ngIf="wh" class="mt-2">
      <label>{{'salary.approvedHours.workingHoursModal.totalAmount' | translate}}</label>
      <div class="d-flex align-items-center">
        <div>{{wh.quantity}} {{'HOUR-ABBREVIATION' | translate}}</div>
        <i class="fa-regular fa-multiply mx-1"></i>
        <div>{{currencyFormat(wh.rate)}}</div>
        <i class="fa-regular fa-equals mx-1"></i>
        <div class="fw-bold">{{currencyFormat(wh.rate * wh.quantity)}}</div>
      </div>
    </div>

    </div>

    <div class="d-flex col-6 justify-content-center mt-2">
      <app-datepickerino
        [selectedDates]="[this.whDate]"
        [disabled]="locked"
        (datesSelectedEmitter)="selectDate($event[0])"
      ></app-datepickerino>
    </div>

  </div>

  <div *ngIf="wh && ![2, 3].includes(wh.calculation_type_id)" class="mt-2">
    <div class="d-flex">
      <label>{{'salary.approvedHours.workingHoursModal.trackings' | translate}}</label>
      <app-spinner *ngIf="timetrackingsLoading" class="ms-1"></app-spinner>
    </div>
    <div *ngIf="!timetrackingsLoading && timeTrackings.length == 0">{{'salary.approvedHours.workingHoursModal.noTrackings' | translate}}</div>
    <div *ngFor="let tt of timeTrackings" class="activity-card">
      <div class="d-flex align-items-center mb-1" style="border-bottom: 2px solid #e9ecef;">
        <h5 class="my-0" [ngbTooltip]="tt.entry_id.toString()">{{formatFullDayAndDate(tt.started_at)}}</h5>
        <i *ngIf="tt.comment" class="fa-regular fa-comment-lines fa-flip-horizontal ms-1 text-warning fa-lg cursor-pointer" [ngbTooltip]="tt.comment"></i>
      </div>
      <div class="d-grid" style="grid-template-columns: 1.5fr 2fr 1.3fr 1fr">
        <div>
          <label>{{'salary.approvedHours.workingHoursModal.time' | translate}}</label>
          <div class="d-flex gap-1">
            <div>{{formatTimeHM(tt.started_at)}} - {{formatTimeHM(tt.stopped_at)}}</div>
            <div class="d-flex align-items-center"><i class="fa-regular fa-arrow-right"></i></div>
            <div>{{formatHours(tt.duration_in_seconds)}} t</div>
          </div>
          <div *ngIf="tt.pause_duration_in_seconds > 0" class="text-muted">{{formatHours(tt.pause_duration_in_seconds)}} t {{('salary.pause' | translate).toLowerCase()}}</div>
        </div>
        <div>
          <label>{{'salary.approvedHours.workingHoursModal.description' | translate}}</label>
          <div>{{tt.is_pause ? ('salary.pause' | translate) : tt.description}}</div>
        </div>
        <div>
          <label>{{'salary.approvedHours.workingHoursModal.activity' | translate}}</label>
          <div>{{tt.activity_name}}</div>
        </div>
        <div>
          <label>{{'salary.approvedHours.workingHoursModal.source' | translate}}</label>
          <div>{{(tt.auto_registered ? 'salary.approvedHours.workingHoursModal.source.jobHours' : 'salary.approvedHours.workingHoursModal.source.manual') | translate}}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="modal-footer justify-content-between pe-2">
  <div>
    <app-button
      *ngIf="wh"
      [translationKey]="'common.delete'"
      [themeStyle]="'danger'"
      (buttonClick)="deleteWorkingHours()"
    ></app-button>
  </div>
  <div class="d-flex gap-2">
  <button class="btn btn-secondary" style="min-width: 80px;" (click)="cancel()">
    {{ 'common.cancel' | translate}}
  </button>
    <app-button
      [ngbTooltip]="noUserSalary ? ('salary.approvedHours.workingHoursModal.noSalary' | translate) : null"
      [loading]="loading"
      [disabled]="loading || (supplement && !selectedSalaryType) || (!supplement && !selectedActivity) || !durationHoursControl.valid || locked || noUserSalary"
      [translationKey]="'common.save'"
      (buttonClick)="save()">
    </app-button>
  </div>
</div>

<ng-template #activitySelectoriniItem let-activity="item">
  <div class="d-flex align-items-center">
    <i *ngIf="activity.color_hex" class="fa-solid fa-circle ms-2" [style.color]="activity.color_hex == '#FFFFFF' ? '#F0F0F0' : activity.color_hex"></i>
    <div class="ms-2">{{activity.activity_name}}</div>
  </div>
</ng-template>
