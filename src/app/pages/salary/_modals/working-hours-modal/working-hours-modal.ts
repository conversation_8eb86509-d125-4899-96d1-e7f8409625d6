import {Component, HostListener, Input, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {NgbActiveModal, NgbModal, NgbTooltip} from '@ng-bootstrap/ng-bootstrap';
import {CompanySalaryTypeResponse, TimeTrackingActivityResponse, TimeTrackingActivityTypeResponse, UserSalaryTimeTrackingResponse, WorkingHoursResponse} from "../../../../@shared/models/timetracking.interfaces";
import {NgForOf, NgIf} from "@angular/common";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {SelectoriniComponent} from "../../../../@shared/components/selectorini/selectorini.component";
import {TrackingService} from "../../../../@shared/services/tracking.service";
import {FormControl, Validators} from "@angular/forms";
import {InputComponent} from "../../../../@shared/components/input/input.component";
import {DatepickerinoComponent} from "../../../../@shared/components/datepickerino/datepickerino.component";
import {_CRM_TTR_25, _CRM_TTR_26, _CRM_TTR_31, _CRM_TTR_32, CRM_TTR_25} from "../../../../@shared/models/input.interfaces";
import {currencyFormat, formatFullDayAndDate, formatTimeHM, UtilsService} from "../../../../@core/utils/utils.service";
import {SpinnerComponent} from "../../../../@shared/components/spinner/spinner.component";
import {ToastService} from "../../../../@core/services/toast.service";
import {ButtonComponent} from "../../../../@shared/components/button/button.component";
import {InternalUserResponse} from "../../../../@shared/models/user.interfaces";
import {EmployeeService} from "../../../../@shared/services/employee.service";
import {Router} from "@angular/router";
import {VerifyPopupModal} from "../../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {combineLatest} from "rxjs";
import {StandardImports} from "../../../../@shared/global_import";
import {ProjectResponse} from "../../../../@shared/models/projects.interfaces";
import {DepartmentResponse} from "../../../../@shared/models/departments.interfaces";
import {StorageService} from "../../../../@core/services/storage.service";
import {ProjectsService} from "../../../../@shared/services/projects.service";
import {DepartmentsService} from "../../../../@shared/services/departments.service";

interface RegistrationType {
  supplement: boolean;
  name: string;
}

@Component({
    selector: 'app-working-hours-modal',
    templateUrl: './working-hours-modal.template.html',
    styleUrls: ['./working-hours-modal.component.css'],
    standalone: true,
  imports: [StandardImports, SelectoriniComponent, DatepickerinoComponent, SpinnerComponent]
})

export class WorkingHoursModal implements OnInit {

  @Input() wh: WorkingHoursResponse | null = null;
  @Input() date: Date | null = null;
  @Input() user: InternalUserResponse;
  @Input() salaryTypeId: number | null = null;
  @Input() supplement: boolean = false;
  @Input() activityId: number | null = null;

  activities: TimeTrackingActivityResponse[] = [];
  salaryTypes: CompanySalaryTypeResponse[] = [];
  selectedActivity: TimeTrackingActivityResponse | null = null;
  selectedSalaryType: CompanySalaryTypeResponse | null = null;
  commentControl = new FormControl();
  durationHoursControl = new FormControl(0, [Validators.required, Validators.min(0.01)]);
  timeTrackings: UserSalaryTimeTrackingResponse[] = [];
  whDate: Date | null = null;
  timetrackingsLoading: boolean = false;
  loading: boolean = false;
  locked: boolean = false;
  noUserSalary: boolean = false;
  nameHovered: boolean = false;
  registrationTypes: RegistrationType[] = []
  selectedRegistrationType: RegistrationType | null = null;
  projects: ProjectResponse[] = [];
  selectedProject: ProjectResponse | null = null;
  departments: DepartmentResponse[] = [];
  selectedDepartment: DepartmentResponse | null = null;
  projectsEnabled: boolean = false;
  departmentsEnabled: boolean = false;
  projectsAndDepartmentsLoading: boolean = false;

    initialsBackgroundColors = [
    '#7ec0ee', // Sky Blue
    '#f7a8a8', // Soft Pink
    '#98ff98', // Mint Green
    '#e6e6fa', // Lavender
    '#ffdab9', // Peach
    '#f08080', // Light Coral
    '#6495ed', // Cornflower Blue
    '#d8bfd8', // Thistle (Light Purple)
    '#7fffd4', // Aquamarine
    '#fffacd'  // Lemon Chiffon
  ];

  @ViewChild('activitySelectoriniItem') activitySelectoriniItem: TemplateRef<any>;

  constructor(
    public activeModal: NgbActiveModal,
    private trackingService: TrackingService,
    public utilsService: UtilsService,
    private toastService: ToastService,
    private employeeService: EmployeeService,
    public router: Router,
    private modalService: NgbModal,
    private translateService: TranslateService,
    private projectsService: ProjectsService,
    private departmentService: DepartmentsService,
    private storageService: StorageService) {
    this.registrationTypes = [
      {supplement: false, name: this.translateService.instant('timetracking.registrationtypes.activity')},
      {supplement: true, name: this.translateService.instant('timetracking.registrationtypes.supplement')}
    ];
  }

  ngOnInit() {
    this.storageService.projectsEnabled$.subscribe((enabled) => {
      this.projectsEnabled = enabled;
    });
    this.storageService.departmentsEnabled$.subscribe((enabled) => {
      this.departmentsEnabled = enabled;
    });

    if (this.projectsEnabled || this.departmentsEnabled) {
      this.projectsAndDepartmentsLoading = true;
      const requests = [];
      if (this.projectsEnabled) {
        requests.push(this.projectsService.getCompanyProjects());
      }
      if (this.departmentsEnabled) {
        requests.push(this.departmentService.getCompanyDepartments());
      }
      combineLatest(requests).subscribe(([projects, departments]) => {
        this.projects = projects as ProjectResponse[];
        this.departments = departments as DepartmentResponse[];
        this.projectsAndDepartmentsLoading = false;
      });
    }

    if (this.wh) {
      this.selectedProject = this.wh.project;
      this.selectedDepartment = this.wh.department;
      this.user = this.wh.user;
      this.locked = this.wh.salary_approval_id !== null;
      this.supplement = [2, 3].includes(this.wh.calculation_type_id);
      if (this.locked) {
        this.commentControl.disable();
        this.durationHoursControl.disable();
      }
      this.timetrackingsLoading = true;
      this.trackingService.getWorkingHoursTimeTrackings(this.wh.entry_id!).subscribe((response) => {
        this.timeTrackings = response.filter((tt) => !tt.is_pause);
        this.timetrackingsLoading = false;
      }, error => {
        this.timetrackingsLoading = false;
      });
      this.whDate = new Date(this.wh.year, this.wh.month - 1, this.wh.day);
      this.commentControl.setValue(this.wh.comment);
      this.durationHoursControl.setValue(this.wh.quantity);
    } else {
      this.selectedRegistrationType = this.registrationTypes.find((rt) => rt.supplement === this.supplement) || null;
      this.whDate = this.date;
      this.employeeService.getActiveSalaryForEmployee(this.user.user_id, 'hourly').subscribe((response) => {
        this.noUserSalary = !response.salary
      });
    }

    if (!(this.wh && [2, 3].includes(this.wh.calculation_type_id))) {
      this.trackingService.getTimeTrackingActivities().subscribe((activities) => {
        this.activities = activities.filter((a) => a.generates_salary).map((a) => {
          return {
            ...a,
            ngTemplate: this.activitySelectoriniItem,
          };
        });
          if (!this.supplement) {
            if (this.wh) {
              this.selectedActivity = this.activities.find((a) => a.activity_id === this.wh?.activity_id) || null;
            } else {
              this.selectedActivity = this.activities.find((a) => a.activity_id === this.activityId) || this.activities.find((a) => a.is_default) || this.activities[0];
            }
          }
        });
    }

    if (!(this.wh && ![2, 3].includes(this.wh.calculation_type_id))) {
      this.trackingService.getSalaryTypes().subscribe((salaryTypes) => {
        this.salaryTypes = salaryTypes.filter((st) => [2, 3].includes(st.calculation_type_id));
        if (this.supplement) {
          if (this.wh) {
            this.selectedSalaryType = this.salaryTypes.find((st) => st.salary_type_id === this.wh?.salary_type_id) || null;
          } else {
            this.selectedSalaryType = this.salaryTypes.find((st) => st.salary_type_id === this.salaryTypeId) || null;
          }
        }
      });
    }
  }

  quantityFocus() {
    if (this.durationHoursControl.value === 0) {
      this.durationHoursControl.setValue(null);
    }
  }

  onSelectedRegistrationType(regType: RegistrationType | any) {
    this.selectedRegistrationType = regType;
    this.supplement = regType.supplement;
  }

  onSelectedActivity(activity: TimeTrackingActivityResponse | any) {
    this.selectedActivity = activity;
  }

  onSelectedSalaryType(salaryType: CompanySalaryTypeResponse | any) {
    this.selectedSalaryType = salaryType;
  }

  onSelectedProjectChange(project: ProjectResponse | any) {
    this.selectedProject = project;
  }

  onSelectedDepartmentChange(department: DepartmentResponse | any) {
    this.selectedDepartment = department;
  }

  selectDate(date: Date) {
    this.whDate = date;
  }

  formatHours(seconds: number): string {
    return (seconds / 3600).toFixed(2);
  }


  save() {
    this.loading = true;
    if (this.wh) {
      let payload: _CRM_TTR_26 = {
        entry_id: this.wh.entry_id,
        quantity: this.durationHoursControl.value!,
        comment: this.commentControl.value,
        project_id: this.selectedProject?.project_id,
        department_id: this.selectedDepartment?.department_id,
      }
      if (this.supplement) {
        payload.salary_type_id = this.selectedSalaryType?.salary_type_id!;
      } else {
        payload.activity_id = this.selectedActivity?.activity_id!;
      }

      this.trackingService.updateUserWorkingHours(payload).subscribe((response) => {
        this.loading = false;
        this.toastService.successToast('updated');
        this.activeModal.close(response);
      }, error => {
        this.loading = false;
      });
    } else {
      let payload: _CRM_TTR_25 = {
        user_id: this.user?.user_id!,
        day: this.whDate!.getDate(),
        month: this.whDate!.getMonth() + 1,
        year: this.whDate!.getFullYear(),
        quantity: this.durationHoursControl.value!,
        comment: this.commentControl.value,
        project_id: this.selectedProject?.project_id,
        department_id: this.selectedDepartment?.department_id,
      }
      if (this.supplement) {
        payload.salary_type_id = this.selectedSalaryType?.salary_type_id!;
      } else {
        payload.activity_id = this.selectedActivity?.activity_id!;
      }

      this.trackingService.createUserWorkingHours(payload).subscribe((response) => {
        this.loading = false;
        this.toastService.successToast('created');
        this.activeModal.close(response);
      }, error => {
        this.loading = false;
      });
    }
  }

  cancel() {
    this.activeModal.close(false)
  }

    getUserInitials(user: InternalUserResponse) {
    return user.full_name.split(' ').slice(0, 2).map(n => n[0]).join('').toUpperCase();
  }

  getInitialsBackgroundColor(itemId: string | number) {
    // Convert itemId to string in case it's a number

    const itemString = itemId.toString();

    // Simple hash function to convert the itemId string into a number
    let hash = 0;
    for (let i = 0; i < itemString.length; i++) {
      const char = itemString.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32bit integer
    }

    // Ensure the hash code is positive
    hash = Math.abs(hash);

    const backgroundColors = this.initialsBackgroundColors;
    // Use the modulus operator to get an index within the bounds of backgroundColors array
    const index = hash % backgroundColors.length;
    return backgroundColors[index];
  }

  deleteWorkingHours() {
    let modalRef = this.modalService.open(VerifyPopupModal, {centered: true});
    modalRef.result.then((result) => {
      if (result) {
        this.loading = true;
        this.trackingService.deleteUserWorkingHours(this.wh!.entry_id!).subscribe(() => {
          this.loading = false;
          this.toastService.successToast('deleted');
          this.activeModal.close(true);
        }, error => {
          this.loading = false;
        });
      }
    });

  }

  protected readonly currencyFormat = currencyFormat;
  protected readonly formatTimeHM = formatTimeHM;
  protected readonly formatFullDayAndDate = formatFullDayAndDate;
}
