import {AfterViewInit, ChangeDetectorRef, Component, forwardRef, OnChanges, OnInit, Optional, SimpleChanges, TemplateRef, ViewChild} from '@angular/core';
import {NgbActiveModal, NgbModal, NgbTooltip} from '@ng-bootstrap/ng-bootstrap';
import {BehaviorSubject, combineLatest, forkJoin, lastValueFrom, pairwise, Subject} from "rxjs";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {FormControl, FormsModule, ReactiveFormsModule} from "@angular/forms";
import {formatDateYMD, UtilsService} from "../../../@core/utils/utils.service";
import {OrderService} from "../../../@shared/services/order.service";
import {SelectoriniComponent} from "../../../@shared/components/selectorini/selectorini.component";

import {ButtonComponent} from "../../../@shared/components/button/button.component";
import {Decimal<PERSON>ipe, <PERSON><PERSON><PERSON>, <PERSON><PERSON>orOf, NgIf} from "@angular/common";
import {SpinnerComponent} from "../../../@shared/components/spinner/spinner.component";



import {ActivatedRoute, Router} from "@angular/router";







import {TablerinoColumn} from "../../../@shared/components/tablerino/tablerino.component";
import {_CRM_TTR_24, _CRM_TTR_28, _CRM_TTR_35, _CRM_TTR_41, _CRM_TTR_52} from "../../../@shared/models/input.interfaces";

import {EmployeeService} from "../../../@shared/services/employee.service";
import {ResourceService} from "../../../@shared/services/resource.service";
import {InternalUserResponse} from "../../../@shared/models/user.interfaces";

import {StorageService} from "../../../@core/services/storage.service";

import {CompanySalaryTypeResponse, TimeTrackingActivityResponse, WorkingHoursResponse} from 'src/app/@shared/models/timetracking.interfaces';
import {TrackingService} from "../../../@shared/services/tracking.service";
import {ToastService} from "../../../@core/services/toast.service";


import {WorkingHoursModal} from "../_modals/working-hours-modal/working-hours-modal";
import {VerifyPopupModal} from "../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {AccountingPreCheckModal} from "../_modals/accounting-pre-check-modal/accounting-pre-check-modal";
import {SalaryApprovalModal} from "../salary-approvals/_modals/salary-approval-modal/salary-approval-modal";
import {waitForAsync} from "@angular/core/testing";
import {WorkingHoursListModalComponent} from "../_modals/working-hours-list-modal/working-hours-list-modal.component";
import {StandardImports} from "../../../@shared/global_import";

interface WorkingHoursMapDetails {
  type_name: string;
  type_id: number;
  sum: number;
  index: number;
  dayMap: Map<number, DayMap>;
}

interface DayMap {
  daySum: number;
  workingHours: WorkingHoursResponse[];
}

interface UserMap {
  user: InternalUserResponse;
  salaries: Map<number, WorkingHoursMapDetails>;
  activities: Map<number, WorkingHoursMapDetails>;
}

@Component({
    selector: 'app-salary-approved-hours',
    templateUrl: './approved-hours.component.html',
    styleUrls: ['./approved-hours.component.css'],
    standalone: true,
  imports: [StandardImports, SelectoriniComponent, SpinnerComponent]
})
export class ApprovedHoursComponent implements OnInit, AfterViewInit {
  loading: boolean = false;
  approvalLoading: boolean = false;
  responseMap: Map<string, UserMap> | null = null;
  periodOptions: {period: string, display: string}[] = [];
  selectedPeriod: {period: string, display: string} | null = null;
  selectedMonthLength: number;
  hoveredDay: number | null = null;
  hoveredColumnDay: number | null = null;
  userHover: string | null = null;
  salaryAccountingEnabled: boolean = false;
  powerOfficeEnabled: boolean = false;
  employees: InternalUserResponse[] = [];
  hoursAvailableForSalaryApproval: boolean = false;
  salaryTypes: CompanySalaryTypeResponse[] = [];
  activities: TimeTrackingActivityResponse[] = [];
  initValuesLoaded$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  pdfReportLoading: boolean = false;

  @ViewChild('userSelectorini') userSelectorini: SelectoriniComponent;

  initialsBackgroundColors = [
    '#7ec0ee', // Sky Blue
    '#f7a8a8', // Soft Pink
    '#98ff98', // Mint Green
    '#e6e6fa', // Lavender
    '#ffdab9', // Peach
    '#f08080', // Light Coral
    '#6495ed', // Cornflower Blue
    '#d8bfd8', // Thistle (Light Purple)
    '#7fffd4', // Aquamarine
    '#fffacd'  // Lemon Chiffon
  ];

  constructor(public utilsService: UtilsService,
              @Optional() public activeModal: NgbActiveModal,
              private orderService: OrderService,
              private route: ActivatedRoute,
              private modalService: NgbModal,
              private translate: TranslateService,
              private employeeService: EmployeeService,
              private resourceService: ResourceService,
              private storageService: StorageService,
              private trackingService: TrackingService,
              private cdr: ChangeDetectorRef,
              private toastService: ToastService,
              private router: Router,
              private activatedRoute: ActivatedRoute
              ) {
  }

  ngOnInit() {
    combineLatest([this.trackingService.getTimeTrackingActivities(), this.trackingService.getSalaryTypes()]).subscribe(([activities, salaryTypes]) => {
      this.activities = activities.sort((a, b) => a.index - b.index);
      this.salaryTypes = salaryTypes.sort((a, b) => a.index - b.index);
      this.initValuesLoaded$.next(true);
    });

    this.storageService.salaryAccountingEnabled$.subscribe((enabled) => {
      this.salaryAccountingEnabled = enabled;
    });

    this.storageService.powerOfficeEnabled$.subscribe((enabled) => {
      this.powerOfficeEnabled = enabled;
    });

    this.employeeService.getEmployees({}).subscribe((response) => {
      this.employees = response.data.sort((a, b) => {
        if (a.full_name < b.full_name) {
          return -1;
        }
        if (a.full_name > b.full_name) {
          return 1;
        }
        return 0;
      });
    });

    this.periodOptions = Array.from({length: 12}, (_, i) => {
      let date = new Date();
      date.setMonth(date.getMonth() - i);
      return {
        period: `${date.getFullYear()}-${date.getMonth() + 1}`,
        display: `${this.translate.instant('MONTHS.' + (date.getMonth() + 1))} ${date.getFullYear()}`
      }
    });

    this.selectedPeriod = this.periodOptions[0];
    this.selectedMonthLength = new Date(parseInt(this.selectedPeriod.period.split('-')[0]), parseInt(this.selectedPeriod.period.split('-')[1]), 0).getDate();

    let params = this.activatedRoute.snapshot.queryParams;
    let year = params['year'];
    let month = params['month'];
    if (year && month) {
      this.selectedPeriod = {
        period: `${year}-${month}`,
        display: `${this.translate.instant('MONTHS.' + (parseInt(month)))} ${year}`
      }
    }
    this.getWorkingHours();
  }

  ngAfterViewInit() {
  }

  onSelectedPeriod(period: any) {
    this.selectedPeriod = period;
    this.selectedMonthLength = new Date(parseInt(this.selectedPeriod!.period.split('-')[0]), parseInt(this.selectedPeriod!.period.split('-')[1]), 0).getDate();
    this.getWorkingHours();
  }

  getWorkingHours() {
    if (!this.selectedPeriod) {
      return;
    }
    this.loading = true;

    let params: _CRM_TTR_28 = {
      year: parseInt(this.selectedPeriod.period.split('-')[0]),
      month: parseInt(this.selectedPeriod.period.split('-')[1]),
    }

    // If initValuesLoaded$ is true, we can just call the getCompanyWorkingHours method
    if (this.initValuesLoaded$.value) {
      this.trackingService.getCompanyWorkingHours(params).subscribe((response) => {
        this.mapResponse(response);
      }, error => {
        this.loading = false;
      });
    } else {
      // If initValuesLoaded$ is false, we need to wait for it to be true before calling getCompanyWorkingHours
      combineLatest([this.initValuesLoaded$, this.trackingService.getCompanyWorkingHours(params)]).subscribe(([_, response]) => {
        this.mapResponse(response);
      });
    }

  }

  mapResponse(response: WorkingHoursResponse[]) {
    response = response.sort((a, b) => {
      if (a.user.full_name < b.user.full_name) {
        return -1;
      }
      if (a.user.full_name > b.user.full_name) {
        return 1;
      }
      return 0;
    });

    this.hoursAvailableForSalaryApproval = false;
    let responseMap: Map<string, UserMap> = new Map();
    response.forEach((entry: WorkingHoursResponse) => {
      if (entry.salary_approval_id === null) {
        this.hoursAvailableForSalaryApproval = true;
      }
      if (!responseMap.get(entry.user.user_id)) {
        responseMap.set(entry.user.user_id, {user: entry.user, salaries: new Map(), activities: new Map()});
      }

      if (![2, 3].includes(entry.calculation_type_id)) {
        // Skip fixed salaries
        if (entry.calculation_type_id === 4) {
          return;
        }

        // Map activities
        let entryIndex = this.activities.find((activity) => activity.activity_id === entry.activity_id)?.index!;
        if (!responseMap.get(entry.user.user_id)!.activities.get(entry.activity_id)) {
          responseMap.get(entry.user.user_id)!.activities.set(entry.activity_id, {type_name: entry.activity_name, type_id: entry.activity_id, sum: 0, index: entryIndex, dayMap: new Map()});
        }
        if (!responseMap.get(entry.user.user_id)!.activities.get(entry.activity_id)!.dayMap.get(entry.day)) {
          responseMap.get(entry.user.user_id)!.activities.get(entry.activity_id)!.dayMap.set(entry.day, {daySum: 0, workingHours: []});
        }
        responseMap.get(entry.user.user_id)!.activities.get(entry.activity_id)!.dayMap.get(entry.day)!.workingHours.push(entry);
        responseMap.get(entry.user.user_id)!.activities.get(entry.activity_id)!.dayMap.get(entry.day)!.daySum += entry.quantity;
        responseMap.get(entry.user.user_id)!.activities.get(entry.activity_id)!.sum += entry.quantity;
      } else {
        // Map salary types
        let entryIndex = this.salaryTypes.find((salaryType) => salaryType.salary_type_id === entry.salary_type_id)?.index!;
        if (!responseMap.get(entry.user.user_id)!.salaries.get(entry.salary_type_id)) {
          responseMap.get(entry.user.user_id)!.salaries.set(entry.salary_type_id, {type_name: entry.salary_type_name, type_id: entry.salary_type_id, sum: 0, index: entryIndex, dayMap: new Map()});
        }
        if (!responseMap.get(entry.user.user_id)!.salaries.get(entry.salary_type_id)!.dayMap.get(entry.day)) {
          responseMap.get(entry.user.user_id)!.salaries.get(entry.salary_type_id)!.dayMap.set(entry.day, {daySum: 0, workingHours: []});
        }
        responseMap.get(entry.user.user_id)!.salaries.get(entry.salary_type_id)!.dayMap.get(entry.day)!.workingHours.push(entry);
        responseMap.get(entry.user.user_id)!.salaries.get(entry.salary_type_id)!.dayMap.get(entry.day)!.daySum += entry.quantity;
        responseMap.get(entry.user.user_id)!.salaries.get(entry.salary_type_id)!.sum += entry.quantity;
      }

    });

    this.responseMap = responseMap;
    this.loading = false;
  }

  range(length: number): number[] {
    return Array.from({ length }, (_, i) => i + 1);
  }

  getMapValues(map: Map<any, any>, sort: boolean): any[] {
    if (sort) {
      return Array.from(map.values()).sort((a, b) => a.index - b.index);
    } else {
      return Array.from(map.values())
    }
  }

  isWeekend(day: number): boolean {
    let date = new Date(parseInt(this.selectedPeriod!.period.split('-')[0]), parseInt(this.selectedPeriod!.period.split('-')[1]) - 1, day);
    return date.getDay() == 0 || date.getDay() == 6;
  }

  goBack() {
    this.router.navigate(['/salary/overview']);
  }

  getUserInitials(user: InternalUserResponse) {
    return user.full_name.split(' ').slice(0, 2).map(n => n[0]).join('').toUpperCase();
  }

  getInitialsBackgroundColor(itemId: string | number) {
    // Convert itemId to string in case it's a number

    const itemString = itemId.toString();

    // Simple hash function to convert the itemId string into a number
    let hash = 0;
    for (let i = 0; i < itemString.length; i++) {
      const char = itemString.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32bit integer
    }

    // Ensure the hash code is positive
    hash = Math.abs(hash);

    const backgroundColors = this.initialsBackgroundColors;
    // Use the modulus operator to get an index within the bounds of backgroundColors array
    const index = hash % backgroundColors.length;
    return backgroundColors[index];
  }

  openWorkingHours(whs: WorkingHoursResponse[] | undefined, day: number, user: InternalUserResponse, activityId: number | null, salaryTypeId: number | null) {
    if (!whs || whs.length <= 1) {
      let modal = this.modalService.open(WorkingHoursModal, {size: 'lg', centered: true});
      modal.componentInstance.wh = whs && whs.length > 0 ? whs[0] : null;
      modal.componentInstance.date = new Date(parseInt(this.selectedPeriod!.period.split('-')[0]), parseInt(this.selectedPeriod!.period.split('-')[1]) - 1, day);
      modal.componentInstance.user = user;
      modal.componentInstance.activityId = activityId;
      modal.componentInstance.salaryTypeId = salaryTypeId;
      modal.componentInstance.supplement = salaryTypeId !== null;
      modal.result.then((result) => {
        if (result) {
          this.getWorkingHours();
        }
      });
    } else {
      let modal = this.modalService.open(WorkingHoursListModalComponent, {size: 'lg', centered: true});
      modal.componentInstance.workingHours = whs;
      modal.componentInstance.supplement = salaryTypeId !== null;
      modal.result.then((result) => {
        if (result) {
          this.getWorkingHours();
        }
      });
    }
  }

  createWorkingHours(user: InternalUserResponse | any) {
    let modal = this.modalService.open(WorkingHoursModal, {size: 'lg', centered: true});
    modal.componentInstance.user = user;
    modal.componentInstance.date = new Date(parseInt(this.selectedPeriod!.period.split('-')[0]), parseInt(this.selectedPeriod!.period.split('-')[1]) - 1, 1);
    this.userSelectorini.deselectItem(false);
    modal.result.then((result) => {
      if (result) {
        this.getWorkingHours();
      }
    });
  }

  executeCreateSalaryApproval(includeMonthly: boolean) {
    this.approvalLoading = true;
    let payload: _CRM_TTR_35 = {
      year: parseInt(this.selectedPeriod!.period.split('-')[0]),
      month: parseInt(this.selectedPeriod!.period.split('-')[1]),
      include_monthly_salaries: includeMonthly
    }
    this.trackingService.createSalaryApproval(payload).subscribe((response) => {
      let modalRef = this.modalService.open(SalaryApprovalModal, { size: 'lg', centered: true });
      modalRef.componentInstance.salary_approval_id = response.salary_approval_id;
      this.approvalLoading = false;
      this.toastService.successToast('created');
      this.getWorkingHours();
    }, error => {
      this.approvalLoading = false;
    });
  }

  async createSalaryApproval(skipAccountingPreCheck: boolean, includeMonthly: boolean) {
    if (!skipAccountingPreCheck) {
      let checkPayload: _CRM_TTR_41 = {
        year: parseInt(this.selectedPeriod!.period.split('-')[0]),
        month: parseInt(this.selectedPeriod!.period.split('-')[1]),
      };
      const response = await lastValueFrom(this.trackingService.runSalaryApprovalAccountingPreCheck(checkPayload));
      if ((!this.powerOfficeEnabled && response.activities.length > 0) || response.salary_types.length > 0 || response.users.length > 0) {
        let modalRef = this.modalService.open(AccountingPreCheckModal, { size: 'lg', centered: true });
        modalRef.componentInstance.activities = response.activities;
        modalRef.componentInstance.salaryTypes = response.salary_types;
        modalRef.componentInstance.users = response.users;
        let continueCreation = await modalRef.result;
        if (!continueCreation) {
          return;
        } else {
          this.executeCreateSalaryApproval(includeMonthly);
        }
      } else {
        this.executeCreateSalaryApproval(includeMonthly);
      }

    } else {
      this.executeCreateSalaryApproval(includeMonthly);
    }
  }

  downloadPDFReport() {
    this.pdfReportLoading = true;
    let dateFrom = new Date(parseInt(this.selectedPeriod!.period.split('-')[0]), parseInt(this.selectedPeriod!.period.split('-')[1]) - 1, 1);
    dateFrom.setHours(12)
    let dateTo = new Date(parseInt(this.selectedPeriod!.period.split('-')[0]), parseInt(this.selectedPeriod!.period.split('-')[1]) - 1, this.selectedMonthLength);
    dateTo.setHours(12)
    let params: _CRM_TTR_52 = {
      date_from: formatDateYMD(dateFrom),
      date_to: formatDateYMD(dateTo),
    }
    this.trackingService.downloadSalaryReport(params).subscribe((res) => {
      const url = window.URL.createObjectURL(res);
      const anchor = document.createElement('a');
      anchor.href = url;
      anchor.download = 'salary_report.pdf';
      document.body.appendChild(anchor);
      anchor.click();
      document.body.removeChild(anchor);
      window.URL.revokeObjectURL(url);
      this.pdfReportLoading = false;
    }, error => {
      this.pdfReportLoading = false;
    });
  }


}
