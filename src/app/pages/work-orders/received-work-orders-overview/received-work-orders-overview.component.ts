import {AfterViewInit, ChangeDetectorRef, Component, OnChanges, OnInit, Optional, SimpleChanges} from '@angular/core';
import {NgbActiveModal, NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {BehaviorSubject, pairwise, Subject} from "rxjs";
import {TranslateService} from "@ngx-translate/core";
import {WorkOrderResponse} from "../../../@shared/models/order.interfaces";
import {displayDate, formatFullDayAndDate, formatTimeHM, UtilsService} from "../../../@core/utils/utils.service";
import {OrderService} from "../../../@shared/services/order.service";
import {Location} from "@angular/common";
import {TemplateService} from "../../../@shared/services/templates.service";
import {TablerinoColumn, TablerinoComponent, TablerinoSettings} from "../../../@shared/components/tablerino/tablerino.component";
import {WorkOrderDetailsComponent, WorkOrderDetailsModal} from "../components/work-order-details/work-order-details.component";
import {_CRM_ORD_168,} from "../../../@shared/models/input.interfaces";
import {takeUntil} from "rxjs/operators";
import {TableFooterComponent} from "../../../@shared/components/table-footer/table-footer.component";
import {PaginationResponse} from "../../../@shared/models/response.interfaces";
import {PaginationContainer} from "../../../@shared/models/global.interfaces";
import {StorageService} from "../../../@core/services/storage.service";
import {PageHeaderComponent} from "../../../@shared/components/page-header/page-header.component";
import {StandardImports} from "../../../@shared/global_import";

export interface WorkOrderRow extends WorkOrderResponse {
  selected: boolean;
}


@Component({
  selector: 'app-received-work-orders-overview',
  templateUrl: './received-work-orders-overview.component.html',
  styleUrls: ['./received-work-orders-overview.component.css'],
  standalone: true,
  imports: [StandardImports, PageHeaderComponent, TablerinoComponent, TableFooterComponent]
})
export class ReceivedWorkOrdersOverviewComponent implements OnInit, OnChanges, AfterViewInit {
  columnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);
  workOrderRows: WorkOrderRow[];
  settings: TablerinoSettings = {
    checkboxes: false,
    clickableRows: true,
  }
  loading: boolean = false;
  dataResponse: PaginationResponse<WorkOrderResponse[]>;
  paginationSubject: BehaviorSubject<PaginationContainer> = new BehaviorSubject<PaginationContainer>({page: 1, limit: 25, paginate: 1, totalPages: 0, totalItems: 0});
  selectedRowsSubject: BehaviorSubject<WorkOrderRow[]> = new BehaviorSubject<WorkOrderRow[]>([]);

  destroy$ = new Subject<void>();

  constructor(public utilsService: UtilsService,
              @Optional() public activeModal: NgbActiveModal,
              @Optional() private location: Location,
              private orderService: OrderService,
              private modalService: NgbModal,
              private templateService: TemplateService,
              private cdr: ChangeDetectorRef,
              private storageService: StorageService,
              private translate: TranslateService) {
  }

  ngOnInit() {
    this.orderService.refreshWorkOrders$.subscribe(() => {
      this.getWorkOrders();
    });
    this.initializeColumns();
    this.getWorkOrders();
    this.paginationSubject.pipe(pairwise()).subscribe((paginationDetails) => {
      if (JSON.stringify(paginationDetails[0]) !== JSON.stringify(paginationDetails[1])) {
        this.getWorkOrders();
      }
    });
  }

  ngAfterViewInit() {
  }

  ngOnChanges(simpleChanges: SimpleChanges) {
  }

  getWorkOrders() {
    this.destroy$.next();
    this.loading = true;
    let params: _CRM_ORD_168 = {
      paginate: 1,
      accepted: false,
      include_schedules: true,
      page: this.paginationSubject.value.page,
      limit: this.paginationSubject.value.limit,
    }

    this.orderService.getWorkOrdersAsSubContractor(params).pipe(takeUntil(this.destroy$)).subscribe((res) => {
      this.dataResponse = res;
      this.paginationSubject.next({
        ...this.paginationSubject.value,
        totalItems: res.total_items,
        totalPages: res.total_pages
      });
      this.workOrderRows = res.data.map((wo) => {
        return {
          ...wo,
          selected: false,
        }
      });
      this.loading = false;
    }, error => {
      this.loading = false
    });
  }

  initializeColumns() {
    this.columnsSubject.next([
      {
        name: 'id',
        labelKey: 'ID',
        formatter: (wo: WorkOrderRow) => '#' + wo.work_order_number + this.formatWorkOrderIcons(wo),
        sort: true,
        visible: true,
      },
      {
        name: 'title',
        labelKey: 'workOrder.list.column.title',
        formatter: (wo: WorkOrderRow) => wo.work_order_title,
        sort: true,
        visible: true,
      },
      {
        name: 'executionAtDate',
        labelKey: 'workOrder.list.column.date',
        formatter: (wo: WorkOrderRow) => formatFullDayAndDate(wo.execution_at, false),
        sort: true,
        visible: true,
      },
      {
        name: 'executionAtTime',
        labelKey: 'workOrder.list.column.time',
        formatter: (wo: WorkOrderRow) => formatTimeHM(wo.execution_at),
        sort: true,
        visible: true,
      },
      {
        name: 'workOrderStatus',
        labelKey: 'workOrder.list.column.status',
        formatter: (wo: WorkOrderRow) => this.formatContractStatus(wo),
        sort: true,
        visible: true,
      },
      {
        name: 'contractingAuthority',
        labelKey: 'workOrder.receivedList.column.contractingAuthority',
        formatter: (wo: WorkOrderRow) => this.formatContractingAuthority(wo),
        sort: true,
        visible: true,
      },
      {
        name: 'receivedAt',
        labelKey: 'workOrder.receivedList.column.receivedAt',
        formatter: (wo: WorkOrderRow) => displayDate(wo.contractor_relation!.created_at, false),
        sort: true,
        visible: true,
      },
      {
        name: 'declined',
        labelKey: 'workOrder.receivedList.column.declined',
        formatter: (wo: WorkOrderRow) => this.formatDeclined(wo),
        sort: true,
        visible: true,
      },
    ]);
  }

  rowClicked(row: WorkOrderRow) {
    let modalRef = this.modalService.open(WorkOrderDetailsComponent, {size: 'xl'});
    modalRef.componentInstance.workOrderId = row.work_order_id;
    modalRef.componentInstance.viewSettings = {...WorkOrderDetailsModal, workOrderStandaloneView: true, repeatingView: row.schedule_template};
  }

  formatContractingAuthority(wo: WorkOrderRow) {
   return `${wo.company_name} (${wo.contractor_relation!.created_by_name})`;
  }

  formatDeclined(wo: WorkOrderRow) {
    if (!wo.contractor_relation!.declined_at) {
      return '';
    }
    return `${displayDate(wo.contractor_relation!.declined_at, false)} (${wo.contractor_relation!.declined_by_name})`;
  }

  formatWorkOrderIcons(wo: WorkOrderRow) {
    let icons = '';
    if (wo.schedule_template) {
      icons += '<i class="fa fa-repeat ms-1"></i>';
    }
    return icons;
  }

  formatContractStatus(wo: WorkOrderRow) {
    let icon = '';
    if (wo.contractor_relation!.declined_at) {
        icon = `<i class="fa-regular fa-thumbs-down text-danger fa-lg me-1"></i>`;
        return icon + this.translate.instant('workOrder.receivedList.column.declined');
      } else if (!wo.contractor_relation!.accepted_at) {
        icon = `<i class="fa-regular fa-hourglass-clock text-muted fa-lg me-1"></i>`;
        return icon + this.translate.instant('workOrder.receivedList.youHaveNotAnswered');
      } else {
        icon = `<i class="fa-regular fa-thumbs-up text-success fa-lg me-1"></i>`;
        return icon + this.translate.instant('workOrder.receivedList.accepted');
    }

  }

}
