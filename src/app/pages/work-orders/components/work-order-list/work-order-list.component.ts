import {AfterViewInit, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Optional, Output, SimpleChanges, TemplateRef, ViewChild} from '@angular/core';
import {NgbActiveModal, NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {BehaviorSubject, forkJoin, pairwise, Subject} from "rxjs";
import {DetailsViewSettings, WorkOrderResponse, WorkOrderCompactResponse} from "../../../../@shared/models/order.interfaces";
import {formatFullDayAndDate, formatTimeHM, paymentStatusBadge, UtilsService, workOrderBadgeStatus} from "../../../../@core/utils/utils.service";
import {OrderService} from "../../../../@shared/services/order.service";
import {Location} from "@angular/common";
import {TemplateService} from "../../../../@shared/services/templates.service";
import {TablerinoColumn, TablerinoComponent, TablerinoSettings} from "../../../../@shared/components/tablerino/tablerino.component";
import {WorkOrderDetailsComponent, WorkOrderDetailsModal} from "../work-order-details/work-order-details.component";
import {_CRM_ORD_153} from "../../../../@shared/models/input.interfaces";
import {takeUntil} from "rxjs/operators";
import {VerifyPopupModal} from "../../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {TableFooterComponent} from "../../../../@shared/components/table-footer/table-footer.component";
import {PaginationContainer} from "../../../../@shared/models/global.interfaces";
import {StandardImports} from "../../../../@shared/global_import";
import {ProfiledItemListComponent} from "../../../../@shared/components/profiled-item-list/profiled-item-list.component";

export interface WorkOrderRow extends WorkOrderResponse {
  selected: boolean;
}


@Component({
  selector: 'app-work-order-list',
  templateUrl: './work-order-list.component.html',
  styleUrls: ['./work-order-list.component.css'],
  standalone: true,
  imports: [StandardImports, TablerinoComponent, TableFooterComponent, ProfiledItemListComponent]
})
export class WorkOrderListComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() workOrderTemplate?: WorkOrderResponse;
  @Input() viewSettings: DetailsViewSettings = {};
  @Input() showFiveNext: boolean = false;
  @Input() orderId?: number;
  columnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);
  workOrderRows: WorkOrderRow[];
  settings: TablerinoSettings = {
    checkboxes: true,
    clickableRows: true,
  }
  loading: boolean = false;
  selectedRowsSubject: BehaviorSubject<WorkOrderRow[]> = new BehaviorSubject<WorkOrderRow[]>([]);
  paginationSubject: BehaviorSubject<PaginationContainer> = new BehaviorSubject<PaginationContainer>({page: 1, limit: 25, paginate: 1, totalPages: 0, totalItems: 0});

  destroy$ = new Subject<void>();

  @Output() modalOpenedEmitter = new EventEmitter<void>();

  @ViewChild('employees', { static: true }) employees!: TemplateRef<any>;

  constructor(public utilsService: UtilsService,
              @Optional() public activeModal: NgbActiveModal,
              @Optional() private location: Location,
              private orderService: OrderService,
              private modalService: NgbModal,
              private templateService: TemplateService,
              private cdr: ChangeDetectorRef,) {
  }

  ngOnInit() {
    this.paginationSubject.pipe(pairwise()).subscribe((paginationDetails) => {
      if (paginationDetails[0].page !== paginationDetails[1].page) {
        this.getWorkOrders();
      }
    });
    this.orderService.refreshWorkOrders$.subscribe((workOrderId) => {
      this.getWorkOrders();
    });

    this.initializeColumns();
    this.getWorkOrders();
  }

  ngAfterViewInit() {
  }

  ngOnChanges(simpleChanges: SimpleChanges) {
    if (simpleChanges['workOrderTemplate']) {
      this.getWorkOrders();
    }
  }

  getWorkOrders() {
    if (this.orderId) {
      this.orderService.workOrders$.pipe(takeUntil(this.orderService.refreshWorkOrders$)).subscribe((workOrders) => {
        this.workOrderRows = workOrders.map((wo) => {
          return {
            ...wo,
            selected: false,
          }
        });
      }, error => {
        this.loading = false;
      });
    } else if (this.workOrderTemplate) {
      this.destroy$.next();
      this.loading = true;
      let payload: _CRM_ORD_153 = {
        work_order_id: this.workOrderTemplate.work_order_id,
        work_order_status_ids: []
      }

      if (this.viewSettings.contractorView) {
        payload.as_contractor = true;
      }

      if (this.showFiveNext) {
        payload.paginate = 1;
        payload.limit = 5;
        payload.page = 1;
        payload.work_order_status_ids = [0, 1]
        payload.order_by = 'execution_at';
        payload.order_direction = 'asc';
      } else {
        payload.paginate = 1;
        payload.page = this.paginationSubject.value.page;
        payload.limit = this.paginationSubject.value.limit;
      }

      this.orderService.getScheduleWorkOrders(payload).pipe(takeUntil(this.orderService.refreshWorkOrders$)).subscribe((workOrders) => {
        this.workOrderRows = workOrders.data.map((wo) => {
          return {
            ...wo,
            selected: false,
          }
        });
        if (!this.showFiveNext) {
          this.paginationSubject.next({
            ...this.paginationSubject.value,
            totalItems: workOrders.total_items,
            totalPages: workOrders.total_pages
          });
        }
        this.loading = false;
      }, error => {
        this.loading = false
      });
    }
  }

  initializeColumns() {
    this.columnsSubject.next([
      {
        name: 'workOrderNumber',
        labelKey: 'ID',
        formatter: (wo: WorkOrderResponse) => '#' + wo.work_order_number + this.formatWorkOrderIcons(wo),
        sort: false,
        visible: true,
      },
      {
        name: 'title',
        labelKey: 'workOrder.list.column.title',
        formatter: (wo: WorkOrderResponse) => wo.work_order_title,
        sort: false,
        visible: true,
      },
      {
        name: 'executionAtDate',
        labelKey: 'workOrder.list.column.date',
        formatter: (wo: WorkOrderResponse) => this.formatExecutionDate(wo),
        sort: false,
        visible: true,
      },
      {
        name: 'executionAtTime',
        labelKey: 'workOrder.list.column.time',
        formatter: (wo: WorkOrderResponse) => formatTimeHM(wo.execution_at),
        sort: false,
        visible: true,
      },
      {
        name: 'workOrderStatus',
        labelKey: 'workOrder.list.column.status',
        formatter: (wo: WorkOrderResponse) => workOrderBadgeStatus(wo),
        sort: false,
        visible: true,
      },
      {
        name: 'assignees',
        labelKey: 'workOrder.list.column.assignees',
        formatter: (wo: WorkOrderCompactResponse) => wo.users,
        sort: false,
        visible: true,
        ngTemplate: this.employees,
      },
      {
        name: 'workOrderPaymentStatus',
        labelKey: 'workOrder.list.column.paymentStatus',
        formatter: (wo: WorkOrderResponse) => wo.payment ? paymentStatusBadge(wo.payment) : '',
        sort: false,
        visible: true,
      },
    ]);
  }

  rowClicked(row: WorkOrderRow) {
    if (this.viewSettings.contractorNotAcceptedView) return;
    this.modalOpenedEmitter.emit();
    this.orderService.refreshSingleWorkOrder(row, 'workOrderListRowClicked');
    let modalRef = this.modalService.open(WorkOrderDetailsComponent, {size: 'xl'});
    modalRef.componentInstance.workOrderId = row.work_order_id;
    modalRef.componentInstance.viewSettings = WorkOrderDetailsModal;
  }

  async openWorkOrderListModal() {
    const { WorkOrderListModal } = await import('../work-order-list-modal/work-order-list-modal');
    this.modalOpenedEmitter.emit();
    let modalRef = this.modalService.open(WorkOrderListModal, {size: 'xl'});
    modalRef.componentInstance.workOrderTemplate = this.workOrderTemplate;
    modalRef.componentInstance.viewSettings = this.viewSettings;
  }

  formatWorkOrderIcons(wo: WorkOrderResponse) {
    let icons = ''

    if (wo.subcontractors.length > 0) {
      icons += '<i class="fa-regular fa-user-helmet-safety fa-lg ms-1"></i>';
    }
    return icons
  }

  formatExecutionDate(wo: WorkOrderResponse) {
    let text = formatFullDayAndDate(wo.execution_at, false)
    if (wo.execution_at && wo.execution_to && wo.execution_at.getDate() != wo.execution_to.getDate()) {
      text += `<i class="fa-regular fa-calendar-exclamation ms-1"></i>`
    }
    return text;
  }

  deleteWorkOrders() {
    let modalRef = this.modalService.open(VerifyPopupModal);
    modalRef.result.then((result) => {
      if (result) {
        this.loading = true;
        const workOrderIds = this.selectedRowsSubject.value.map(wo => wo.work_order_id);
        const requests = workOrderIds.map(id => this.orderService.deleteWorkOrder({work_order_id: id}));
        forkJoin(requests).subscribe({
          next: () => {
            // If single WO order details view
            if (this.orderId) {
              this.workOrderRows = this.workOrderRows.filter(wo => !workOrderIds.includes(wo.work_order_id));
              this.orderService.fetchAndRefreshOrder(this.orderId, 'workOrdersDeleteFromList');
            } else if (this.workOrderTemplate) {
              // If repeating WO schedule WO list
              this.workOrderRows = this.workOrderRows.filter(wo => !workOrderIds.includes(wo.work_order_id));
            }
            this.orderService.refreshWorkOrders$.next();
            this.loading = false;
          },
          error: (error) => {
            this.loading = false;
          }
        });
      } else {
        this.loading = false;
      }
    });
  }

}
