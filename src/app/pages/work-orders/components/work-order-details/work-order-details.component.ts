import {AfterViewInit, ChangeDetectorRef, Component, ElementRef, Input, OnChanges, OnDestroy, OnInit, Optional, ViewChild} from '@angular/core';
import {NgbActiveModal, NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {BehaviorSubject, combineLatest, firstValueFrom, Subject} from "rxjs";
import {TranslateService} from "@ngx-translate/core";
import {FormControl, Validators} from "@angular/forms";
import {DetailsViewSettings, OrderLineRow, OrderResponse, OrderSubContractorResponse, ServiceRecipientResponse, WorkOrderResponse, WorkOrderStatusResponse} from "../../../../@shared/models/order.interfaces";
import {displayDate, displayPhone, favouriteStar, formatTimeHM, paymentStatusBadge, UtilsService} from "../../../../@core/utils/utils.service";
import {OrderService} from "../../../../@shared/services/order.service";
import {Location} from "@angular/common";
import {SpinnerComponent} from "../../../../@shared/components/spinner/spinner.component";
import {InfoBoxComponent} from "../../../../@shared/components/info-box/info-box.component";
import {ScheduleSetupComponent} from "../../../../@shared/components/schedule-component/schedule-setup.component";
import {OrderLinesComponent} from "../../../orders/order-details-v2/order-lines/order-lines.component";
import {ActivatedRoute, Router, RouterLink} from "@angular/router";
import {OrderAddressComponent} from "../../../orders/order-details-v2/order-address/order-address.component";
import {OrderCrewReportsComponent} from "../../../orders/order-details-v2/order-crew-reports/order-crew-reports.component";
import {OrderTimetrackingComponent} from "../../../orders/order-details-v2/order-timetracking/order-timetracking.component";
import {ChecklistComponent} from "../../../orders/order-details-v2/order-checklist/checklist.component";
import {OrderAttachmentsComponent} from "../../../orders/order-details-v2/order-attachments/order-attachments.component";
import {_CRM_ORD_117, _CRM_ORD_118, _CRM_ORD_119, _CRM_ORD_122, ScheduleInput} from "../../../../@shared/models/input.interfaces";
import {VerifyPopupModal} from "../../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {InternalUserResponse} from "../../../../@shared/models/user.interfaces";
import {ResourceResponse} from "../../../../@shared/models/resources.interfaces";
import {WorkOrderTemplateResponse} from "../../../../@shared/models/templates.interfaces";
import {TemplateService} from "../../../../@shared/services/templates.service";
import {WorkOrderDuplicateModal} from "./_modals/duplicate-modal/work-order-duplicate-modal";

import {PaymentScheduleComponent} from "../../../orders/order-details-v2/order-payment-schedule/payment-schedule.component";
import {DatepickerinoComponent} from "../../../../@shared/components/datepickerino/datepickerino.component";
import {EmployeeResourcesComponent} from "../../../orders/order-details-v2/order-employee-resources/employee-resources.component";
import {PaymentDetailsComponent} from "../../../payments/components/payment-details/payment-details.component";
import {StorageService} from "../../../../@core/services/storage.service";
import {SendToSubContractorModalComponent} from "../../../orders/order-details-v2/components/action-button-group/_modals/send-to-subcontractor-modal/send-to-sub-contractor-modal.component";
import {ContractorDetailsComponent} from "./components/contractor-details/contractor-details.component";
import {OrderNotesDetails} from "../../../orders/order-details-v2/order-notes/components/order-notes-details/order-notes-details";
import {CardComponent} from "../../../../@shared/components/layout/card/card.component";
import {ToastService} from "../../../../@core/services/toast.service";
import {takeUntil} from "rxjs/operators";
import {QuantityProposalModal} from "./_modals/quantity-proposal-modal/quantity-proposal-modal";
import {ColorPickerinoComponent} from "../../../../@shared/components/color-pickerino/color-pickerino.component";
import {StandardImports} from "../../../../@shared/global_import";
import {WorkOrderListComponent} from "../work-order-list/work-order-list.component";
import {OverlayItem, OverlayService} from "../../../../@shared/services/overlay.service";
import {PaymentDetailsV2Component} from "../../../payments/components/payment-details-v2/payment-details-v2.component";

export var WorkOrderDetailsModal: DetailsViewSettings = {
  modalView: true,
  collapsedOrderLines: true,
  workOrderView: true,
}

@Component({
  selector: 'app-work-order-details',
  templateUrl: './work-order-details.component.html',
  styleUrls: ['./work-order-details.component.css'],
  standalone: true,
  imports: [
    StandardImports,
    ContractorDetailsComponent,
    RouterLink,
    ColorPickerinoComponent,
    SpinnerComponent,
    DatepickerinoComponent,
    ScheduleSetupComponent,
    EmployeeResourcesComponent,
    InfoBoxComponent,
    PaymentScheduleComponent,
    OrderLinesComponent,
    OrderNotesDetails,
    OrderTimetrackingComponent,
    CardComponent,
    OrderAddressComponent,
    OrderCrewReportsComponent,
    OrderAttachmentsComponent,
    ChecklistComponent,
    WorkOrderListComponent
  ]
})
export class WorkOrderDetailsComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {
  @Input() workOrderId: number;
  @Input() viewSettings: DetailsViewSettings;
  @Input() orderLinesSubject: BehaviorSubject<OrderLineRow[]> = new BehaviorSubject<OrderLineRow[]>([]);
  workOrder: WorkOrderResponse;
  order?: OrderResponse;
  unfilteredWorkOrderStatuses: WorkOrderStatusResponse[] = [];
  workOrderStatuses: WorkOrderStatusResponse[] = [];
  users: InternalUserResponse[] = [];
  resources: ResourceResponse[] = [];
  serviceRecipient: ServiceRecipientResponse;
  serviceRecipientInitials: string = '';
  multiDayWorkOrder: boolean = false;

  startTimeControl: FormControl = new FormControl();
  endTimeControl: FormControl = new FormControl();
  durationHoursControl: FormControl = new FormControl(1, Validators.min(0));
  durationMinutesControl: FormControl = new FormControl(0, Validators.min(0));
  arrivalToControl: FormControl = new FormControl('');
  arrivalFromControl: FormControl = new FormControl('');
  executionDate: Date | null;
  executionToDate: Date | null;
  workOrderCustomColor: string | null;

  workOrderTitleControl: FormControl = new FormControl('');
  workOrderDescriptionControl: FormControl = new FormControl('');
  descriptionOverflow: boolean = false;
  expandWorkOrderDescription: boolean = false;

  workOrderTemplates: WorkOrderTemplateResponse[] = [];
  topWorkOrderTemplates: WorkOrderTemplateResponse[] = [];
  selectedWorkOrderTemplateId: number | null = null;
  selectedWorkOrderTemplateContainsOrderLines: boolean = false;

  contractor: OrderSubContractorResponse | null;
  contractorDisabledDate: Date | null = null;

  payloadSubject: BehaviorSubject<_CRM_ORD_117> = new BehaviorSubject<_CRM_ORD_117>({} as _CRM_ORD_117);
  scheduleInputSubject: BehaviorSubject<ScheduleInput> = new BehaviorSubject<ScheduleInput>({} as ScheduleInput);

  templatesLoaded: boolean = false;
  showAllTemplates: boolean = false;

  overlayWorkOrderId: number | null = null;

  loading: boolean = false;
  noTemplateOrderLinesLoading: boolean = false;
  titleDescriptionLoading: boolean = false;
  scheduleLoading: boolean = false;
  timeAndDateLoading: boolean = false;

  private destroy$ = new Subject<void>();

  @ViewChild('dropDownButtonsDiv') dropDownButtonsDiv: ElementRef;
  @ViewChild('singleDatepickerino') singleDatepickerino: DatepickerinoComponent;


  constructor(public utilsService: UtilsService,
              @Optional() public activeModal: NgbActiveModal,
              @Optional() private location: Location,
              private orderService: OrderService,
              private modalService: NgbModal,
              private cdr: ChangeDetectorRef,
              private router: Router,
              private storageService: StorageService,
              private toastService: ToastService,
              private translate: TranslateService,
              private overlayService: OverlayService) {
  }

  ngOnInit() {
    if (this.workOrder && this.workOrder?.company_id != this.storageService.getSelectedCompanyId() && this.viewSettings) {
      this.viewSettings.contractorView = true;
    }

    this.overlayService.overlayClosed.pipe(takeUntil(this.destroy$)).subscribe((overlay) => {
      this.handleOverlayClosed(overlay)
    });

    this.overlayService.overlayMinimized.pipe(takeUntil(this.destroy$)).subscribe((overlay) => {
      this.handleOverlayMinimized(overlay)
    });

    this.overlayService.overlayRestored.pipe(takeUntil(this.destroy$)).subscribe((overlay) => {
      this.handleOverlayRestored(overlay)
    })

    combineLatest([
      this.orderService.getWorkOrderById(this.workOrderId),
      this.orderService.getWorkOrderStatuses()
    ]).pipe(takeUntil(this.destroy$)).subscribe(([workOrder, statuses]) => {
      if (workOrder) {
        this.workOrder = workOrder;
        this.unfilteredWorkOrderStatuses = statuses;
        this.mapOrderStatuses();
        this.initWorkOrder();
      }
    });

    if (!this.viewSettings.contractorView) {
      this.orderService.order$.pipe(takeUntil(this.destroy$)).subscribe((order) => {
        this.order = order;
      });
    }


    if (this.workOrder) {
      this.orderService.refreshSingleWorkOrder(this.workOrder, 'workOrderDetailsOnInitIfWorkOrder');
    } else if (this.workOrderId) {
      this.orderService.fetchAndRefreshSingleWorkOrder(this.workOrderId, 'workOrderDetailsOnInitIfNotWorkOrder');
    } else {
      this.scheduleInputSubject.next({
        date: 1,
        every: 1,
        schedule_repeat_type_id: 1,
        schedule_repeat_type_name: this.translate.instant('orderSchedules.weekly'),
        weekdays: [0],
        nth_weekday: 1,
        instances_in_advance: 5,
        active: false,
        start_date: null,
        end_date: null,
        executionAt: new Date(),
        sourceName: 'workOrderDetails',
      });
    }

    this.payloadSubject.pipe(takeUntil(this.destroy$)).subscribe((payload) => {
      this.scheduleInputSubject.next({
        ...this.scheduleInputSubject.value,
        executionAt: payload.execution_at!,
        sourceName: 'workOrderDetails',
      });
    });

    this.orderService.workOrder$.pipe(takeUntil(this.destroy$)).subscribe(workOrder => {
      this.workOrder = workOrder!;
      this.initWorkOrder();
    });
  }

  handleOverlayClosed(overlay: OverlayItem) {
    if (overlay.id.startsWith('WO') && overlay.id.split('-')[1] == this.workOrder?.work_order_id!.toString()) {
      this.ngOnDestroy();
    }
  }

  handleOverlayMinimized(overlay: OverlayItem) {
    if (overlay.id.startsWith('WO') && overlay.id.split('-')[1] == this.workOrder?.work_order_id!.toString()) {
      this.overlayWorkOrderId = this.workOrder?.work_order_id!;
      this.orderService.destroyWorkOrder('workOrderDetailsOverlayMinimized');
    }
  }

  handleOverlayRestored(overlay: OverlayItem) {
    if (this.overlayWorkOrderId && overlay.id.startsWith('WO') && overlay.id.split('-')[1] == this.overlayWorkOrderId.toString()) {
      this.orderService.fetchAndRefreshSingleWorkOrder(this.overlayWorkOrderId, 'workOrderDetailsOverlayRestored');
    }
  }

  ngOnChanges() {
  }

  ngAfterViewInit() {
    this.cdr.detectChanges();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.orderService.destroyWorkOrder('workOrderDetailsNgOnDestroy');
  }

  initWorkOrder() {
    if (this.workOrder && this.workOrder.execution_at && this.workOrder.execution_to) {
      if (this.workOrder.execution_at.getDate() != this.workOrder.execution_to.getDate()) {
        this.multiDayWorkOrder = true;
      }
    }

    this.viewSettings.contractorNotAcceptedView = false;
    this.viewSettings.contractorView = false;
    this.contractorDisabledDate = null;
    if (this.workOrder && this.workOrder?.company_id != this.storageService.getSelectedCompanyId()) {
      this.viewSettings.contractorView = true;
      this.viewSettings.contractorNotAcceptedView = !this.workOrder.contractor_relation?.accepted_at
      this.orderService.getOrderServiceRecipient(this.workOrder.order_id).pipe(takeUntil(this.destroy$)).subscribe((serviceRecipient) => {
        this.serviceRecipient = serviceRecipient;
        if (this.serviceRecipient) {
          if (this.serviceRecipient.name) {
            const nameParts = this.serviceRecipient.name.split(' ');
            this.serviceRecipientInitials = nameParts.map(part => part[0]).join('').toUpperCase();
          } else {
            this.serviceRecipientInitials = 'XX';
          }
        }
      });
    }

    if (this.viewSettings.contractorNotAcceptedView) {
      this.workOrderTitleControl.disable();
      this.workOrderDescriptionControl.disable();
      this.startTimeControl.disable();
      this.endTimeControl.disable();
      this.durationHoursControl.disable();
      this.durationMinutesControl.disable();
      this.arrivalFromControl.disable();
      this.arrivalToControl.disable();
      this.contractorDisabledDate = new Date();
    } else {
      this.workOrderTitleControl.enable();
      this.workOrderDescriptionControl.enable();
      this.startTimeControl.enable();
      this.endTimeControl.enable();
      this.durationHoursControl.enable();
      this.durationMinutesControl.enable();
      this.arrivalFromControl.enable();
      this.arrivalToControl.enable();
    }

    this.workOrderTitleControl.setValue(this.workOrder?.work_order_title);
    this.workOrderDescriptionControl.setValue(this.workOrder?.work_order_description);
    this.workOrderCustomColor = this.workOrder?.custom_color_hex || null;


    // Set start time and duration
    if (!this.viewSettings.createView && !this.viewSettings.createOrderView && this.workOrder?.execution_at) {
      this.executionDate = this.workOrder.execution_at;
      this.executionToDate = this.workOrder.execution_to;
      this.startTimeControl.setValue(formatTimeHM(this.workOrder.execution_at))
      this.endTimeControl.setValue(formatTimeHM(this.workOrder.execution_to));
      const duration = this.workOrder.execution_to.getTime() - this.workOrder.execution_at.getTime();
      this.durationHoursControl.setValue(Math.floor(duration / 1000 / 60 / 60));
      this.durationMinutesControl.setValue(Math.floor(duration / 1000 / 60 % 60));
      this.arrivalToControl.setValue(this.workOrder.arrival_to);
      this.arrivalFromControl.setValue(this.workOrder.arrival_from);
    }

    // Init contractor if exists
    if (this.workOrder?.subcontractors && this.workOrder?.subcontractors.length > 0) {
      this.contractor = this.workOrder.subcontractors[0];
    } else {
      this.contractor = null;
    }

    if (!this.workOrder?.order_lines) {
      this.orderLinesSubject.next([]);
    } else {
      this.orderLinesSubject.next(this.workOrder?.order_lines.sort((a, b) => {
      if (a.locked === b.locked) {return a.index - b.index;}
      return a.locked ? 1 : -1;}).map(orderLine => {
        return {
          ...orderLine,
          checked: true,
        }
      }
    ));
    }
    if (this.workOrder?.schedule) {
      let schedule = this.workOrder.schedule
      this.scheduleInputSubject.next({
        date: schedule.date,
        every: schedule.every,
        schedule_repeat_type_id: schedule.schedule_repeat_type_id,
        schedule_repeat_type_name: schedule.schedule_repeat_type_name,
        weekdays: schedule.weekdays,
        nth_weekday: schedule.nth_weekday,
        instances_in_advance: schedule.instances_in_advance,
        active: schedule.active,
        start_date: schedule.start_date,
        end_date: schedule.end_date,
        sourceName: 'workOrderDetails',
      });
    }
  }

  mapOrderStatuses() {
    if (this.workOrder && this.unfilteredWorkOrderStatuses.length > 0) {
      this.workOrderStatuses = [];
      let orderedStatuses = this.unfilteredWorkOrderStatuses.sort((a, b) => a.work_order_status_id - b.work_order_status_id)
      let exemptOrderStatuses = this.workOrder?.work_order_status_id === 8 ? [] : [8];

      for (let status of orderedStatuses) {
        if (!exemptOrderStatuses.includes(status.work_order_status_id)) {
          this.workOrderStatuses.push(status);
        }
      }
    }
  }

  getActiveOrderStatusIndex(): number {
    return this.workOrderStatuses.findIndex(status => status.work_order_status_id === this.workOrder?.work_order_status_id);
  }

  close() {
    this.activeModal.close();
  }

  deleteWorkOrderContractAsContractor() {
    this.orderService.deleteContract(this.workOrder?.work_order_id!).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.orderService.refreshWorkOrders$.next();
      this.activeModal.close('deleted');
    });
  }

  updateWorkOrderColor(color: string) {
    this.workOrderCustomColor = color;
    if (this.workOrder) {
      let payload: _CRM_ORD_118 = {
        work_order_id: this.workOrder!.work_order_id,
        custom_color_hex: color,
      }
      this.orderService.patchWorkOrder(payload).pipe(takeUntil(this.destroy$)).subscribe((res) => {
        this.toastService.successToast('updated');
        this.workOrder = res;
        this.orderService.refreshSingleWorkOrder(res, 'updateWorkOrderColor');
      });
    }
  }

  deleteWorkOrder() {
    if (this.viewSettings.contractorView) {
      let modalRef = this.modalService.open(VerifyPopupModal, { centered: true });
      modalRef.result.then((result) => {
        if (result) {
          this.deleteWorkOrderContractAsContractor();
        }
      });
      return;
    }

    if (!this.workOrder) return;

    let params: _CRM_ORD_119 = {
      work_order_id: this.workOrder.work_order_id
    }

    if (this.workOrder.payment && ![2, 3].includes(this.workOrder.payment.payment_status_id)) {
      let modalRef = this.modalService.open(VerifyPopupModal, { centered: true });
      modalRef.componentInstance.showBody = true;
      modalRef.componentInstance.titleTranslationKey = 'workOrder.deletePaymentModal.titleTranslationKey';
      modalRef.componentInstance.bodyBoldTranslationKey = 'workOrder.deletePaymentModal.bodyRegularTranslationKey';
      modalRef.componentInstance.bodyRegularTranslationKey = 'workOrder.deletePaymentModal.bodyRegularSecondaryTranslationKey';
      modalRef.result.then((result) => {
        if (result) {
          params.delete_payment = true;
          this.callScheduleDeleteChildrenModal(params);
        } else {
          params.delete_payment = false;
          this.callScheduleDeleteChildrenModal(params);
        }
      });
    } else {
      let modalRef = this.modalService.open(VerifyPopupModal, { centered: true });
      modalRef.result.then((result) => {
        if (result) {
          this.callScheduleDeleteChildrenModal(params);
        }
      });
    }
  }

  callScheduleDeleteChildrenModal(params: _CRM_ORD_119) {
    if (this.workOrder?.schedule && this.workOrder?.schedule?.num_unstarted_work_orders > 0) {
      let modalRef = this.modalService.open(VerifyPopupModal, { centered: true });
      modalRef.componentInstance.showBody = true;
      modalRef.componentInstance.titleTranslationKey = 'workOrder.deleteChildrenModal.titleTranslationKey';
      modalRef.componentInstance.bodyBoldTranslationKey = 'workOrder.deleteChildrenModal.bodyRegularTranslationKey';
      modalRef.componentInstance.bodyRegularTranslationKey = 'workOrder.deleteChildrenModal.bodyRegularSecondaryTranslationKey';
      modalRef.result.then((result) => {
        if (result) {
          params.delete_future_children = true;
          this.callDeleteWorkOrder(params);
        } else {
          params.delete_future_children = false;
          this.callDeleteWorkOrder(params);
        }
      });

    } else {
      this.callDeleteWorkOrder(params);
    }
  }

  callDeleteWorkOrder(params: _CRM_ORD_119) {
    this.loading = true;
    this.orderService.deleteWorkOrder(params).pipe(takeUntil(this.destroy$)).subscribe((res) => {
      this.orderService.fetchAndRefreshOrder(this.order?.order_id!, 'deleteWorkOrder');
      this.orderService.refreshWorkOrders$.next();
      this.loading = false;
      this.activeModal.close('deleted');
    }, error => {
      this.loading = false;
    });
  }

  closeModal() {
    this.activeModal.close(this.workOrder)
  }

  goBack() {
    this.location.back();
  }

  async updateWorkOrderSchedule(initiateSchedule: boolean = false) {
    if (!this.startTimeControl.value || (!this.durationHoursControl.value && !this.durationMinutesControl.value)) return;
    if (!this.viewSettings.repeatingView || this.viewSettings.createView) return;

    this.scheduleLoading = true;

    let executionAt = new Date(this.workOrder!.execution_at ? this.workOrder!.execution_at : new Date());
    let [hours, minutes] = this.startTimeControl.value.split(':').map(Number);
    executionAt.setHours(hours, minutes, 0, 0);
    let executionTo = new Date(executionAt);
    executionTo.setHours(hours + (Number(this.durationHoursControl.value) || 0), minutes + (Number(this.durationMinutesControl.value) || 0));

    let payload: _CRM_ORD_118 = {
      work_order_id: this.workOrder!.work_order_id,
      execution_at: executionAt,
      execution_to: executionTo,
      schedule: {
        date: this.scheduleInputSubject.value.date,
        every: this.scheduleInputSubject.value.every,
        schedule_repeat_type_id: this.scheduleInputSubject.value.schedule_repeat_type_id,
        weekdays: this.scheduleInputSubject.value.weekdays,
        nth_weekday: this.scheduleInputSubject.value.nth_weekday,
        instances_in_advance: this.scheduleInputSubject.value.instances_in_advance,
        start_date: this.scheduleInputSubject.value.start_date,
        end_date: this.scheduleInputSubject.value.end_date,
      }
    }

    if (initiateSchedule) {
      payload.work_order_title = this.workOrderTitleControl.value;
      payload.work_order_description = this.workOrderDescriptionControl.value;
      payload.work_order_status_id = 1
      payload.schedule!.active = true;
    }

    if (this.workOrder?.schedule?.num_unstarted_work_orders! > 1) {
      let modalRef = this.modalService.open(VerifyPopupModal, {backdrop: 'static'});
      modalRef.componentInstance.showBody = true;
      modalRef.componentInstance.titleTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.title';
      modalRef.componentInstance.bodyBoldTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyBoldTranslationKey';
      modalRef.componentInstance.bodyMutedTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyMutedTranslationKey';
      modalRef.componentInstance.bodyRegularTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.executionUpdate';
      try {
        payload.update_children_executions = await modalRef.result;
      } catch (e) {}
    }

    this.orderService.patchWorkOrder(payload).pipe(takeUntil(this.destroy$)).subscribe((workOrder) => {
      if (initiateSchedule) {
        this.orderService.initiateWorkOrderCreationForSchedule(workOrder.schedule?.work_order_schedule_id!).pipe(takeUntil(this.destroy$)).subscribe((res) => {
          this.workOrder = workOrder;
          this.orderService.refreshWorkOrders$.next();
          if (!this.viewSettings.contractorView) {
            this.orderService.fetchAndRefreshOrder(workOrder.order_id, 'updateWorkOrderSchedule');
          }
          this.scheduleLoading = false;
          this.activeModal.close();
        });
      } else {
        if (!this.viewSettings.contractorView) {
          this.orderService.fetchAndRefreshOrder(workOrder.order_id, 'updateWorkOrderSchedule');
        }
        this.workOrder = workOrder;
        this.orderService.refreshSingleWorkOrder(workOrder, 'updateWorkOrderScheduleWorkOrderDetails');
        this.scheduleLoading = false;
      }
    }, error => {
      this.scheduleLoading = false;
    });
  }

  async saveTitleAndDescription() {
    this.titleDescriptionLoading = true;
    let payload: _CRM_ORD_118 = {
      work_order_id: this.workOrder!.work_order_id,
      work_order_title: this.workOrderTitleControl.value,
      work_order_description: this.workOrderDescriptionControl.value,
    }

    if (this.workOrder?.schedule?.num_unstarted_work_orders! > 1) {
      let modalRef = this.modalService.open(VerifyPopupModal, {backdrop: 'static'});
      modalRef.componentInstance.showBody = true;
      modalRef.componentInstance.titleTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.title';
      modalRef.componentInstance.bodyBoldTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyBoldTranslationKey';
      modalRef.componentInstance.bodyMutedTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyMutedTranslationKey';
      modalRef.componentInstance.bodyRegularTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.textUpdate';
      try {
        payload.update_children_text = await modalRef.result;
      } catch (e) {}
    }

    this.orderService.patchWorkOrder(payload).pipe(takeUntil(this.destroy$)).subscribe((workOrder) => {
      this.workOrderTitleControl.markAsPristine()
      this.workOrderDescriptionControl.markAsPristine()
      this.workOrder = workOrder;
      this.orderService.refreshSingleWorkOrder(workOrder, 'updateWorkOrderTitleAndDescription');
      this.titleDescriptionLoading = false;
    }, error => {
      this.titleDescriptionLoading = false;
    });
  }

  setMultiDayExecutionAt(date: Date) {
    if (!this.startTimeControl.value) {
      this.startTimeControl.setValue('08:00');
    }
    this.executionDate = date;
    this.saveTimeAndDateMultiDay();
  }

  setMultiDayExecutionTo(date: Date) {
    if (!this.endTimeControl.value) {
      this.endTimeControl.setValue('08:00');
    }
    this.executionToDate = date;
    this.saveTimeAndDateMultiDay();
  }

  async setExecutionDate(date: Date) {
    let cancel = false;
    if (date.getTime() < new Date().getTime() && !(date.getMonth() == new Date().getMonth() && date.getDate() == new Date().getDate())) {
      let modalRef = this.modalService.open(VerifyPopupModal, {backdrop: 'static'});
      modalRef.componentInstance.showBody = true;
      modalRef.componentInstance.titleTranslationKey = 'workOrderDetails.pastDateModal.title';
      modalRef.componentInstance.bodyBoldTranslationKey = 'workOrderDetails.pastDateModal.bodyBold';
      modalRef.componentInstance.bodyRegularTranslationKey = 'workOrderDetails.pastDateModal.bodyRegular';
      try {
        let result: boolean = await modalRef.result;
        if (!result) {
          cancel = true;
          this.timeAndDateLoading = false;
          this.singleDatepickerino.clearSelectedDates();
        }
      } catch (e) {}
    }

    if (cancel) return;

    if (!this.startTimeControl.value) {
      this.startTimeControl.setValue('08:00');
    }

    // Set default duration to 1 hour if neither hours nor minutes are set
    if (this.durationHoursControl.value === 0 || !this.durationHoursControl.value) {
      if (!this.durationMinutesControl.value) {
        this.durationHoursControl.setValue(1);
      }
    }

    // Set default minutes to 0 if not set
    if (this.durationMinutesControl.value !== 0 && !this.durationMinutesControl.value) {
      this.durationMinutesControl.setValue(0);
    }

    this.executionDate = date;

    this.saveTimeAndDate();
  }

  saveTimeAndDateMultiDay() {
    if (!this.startTimeControl.value || !this.endTimeControl.value) return;
    if (this.startTimeControl.invalid || this.endTimeControl.invalid) return;

    let executionAt = this.executionDate!;
    let [hoursAt, minutesAt] = this.startTimeControl.value.split(':').map(Number);
    executionAt.setHours(hoursAt, minutesAt, 0, 0);
    let executionTo = this.executionToDate!;
    let [hoursTo, minutesTo] = this.endTimeControl.value.split(':').map(Number);
    executionTo.setHours(hoursTo, minutesTo, 0, 0);

    if (executionAt.getDate() == executionTo.getDate()) {
      if (hoursAt > hoursTo || (hoursAt === hoursTo && minutesAt >= minutesTo)) {
        this.toastService.errorToast('multiDayWorkOrderDateAndTimeError');
        executionTo.setDate(executionTo.getDate() + 1);
      } else {
        this.multiDayWorkOrder = false;
      }
    }

    this.timeAndDateLoading = true;

    let payload: _CRM_ORD_118 = {
      work_order_id: this.workOrder!.work_order_id,
      execution_at: executionAt,
      execution_to: executionTo,
    }

    this.orderService.patchWorkOrder(payload).pipe(takeUntil(this.destroy$)).subscribe((workOrder) => {
      this.startTimeControl.markAsPristine()
      this.durationHoursControl.markAsPristine()
      this.durationMinutesControl.markAsPristine()
      this.arrivalFromControl.markAsPristine()
      this.arrivalToControl.markAsPristine()
      this.workOrder = workOrder;
      this.orderService.refreshSingleWorkOrder(workOrder, 'updateWorkOrderDateAndTime');
      this.initWorkOrder();
      this.timeAndDateLoading = false;
    }, error => {
      this.timeAndDateLoading = false;
    });
  }

  saveTimeAndDate() {
    if (!this.startTimeControl.value || (!this.durationHoursControl.value && !this.durationMinutesControl.value)) return;
    if (this.startTimeControl.invalid || this.durationHoursControl.invalid || this.durationMinutesControl.invalid) return;
    let executionAt = this.executionDate!;
    let [hours, minutes] = this.startTimeControl.value.split(':').map(Number);
    executionAt.setHours(hours, minutes, 0, 0);
    let executionTo = new Date(executionAt);
    executionTo.setHours(hours + (Number(this.durationHoursControl.value) || 0), minutes + (Number(this.durationMinutesControl.value) || 0));

    this.timeAndDateLoading = true;

    let payload: _CRM_ORD_118 = {
      work_order_id: this.workOrder!.work_order_id,
      execution_at: executionAt,
      execution_to: executionTo,
      arrival_from: this.arrivalFromControl.value,
      arrival_to: this.arrivalToControl.value,
    }

    this.orderService.patchWorkOrder(payload).pipe(takeUntil(this.destroy$)).subscribe((workOrder) => {
      this.startTimeControl.markAsPristine()
      this.durationHoursControl.markAsPristine()
      this.durationMinutesControl.markAsPristine()
      this.arrivalFromControl.markAsPristine()
      this.arrivalToControl.markAsPristine()
      this.workOrder = workOrder;
      this.orderService.refreshSingleWorkOrder(workOrder, 'updateWorkOrderDateAndTime');
      if (this.workOrder.schedule_template) {
        this.orderService.refreshWorkOrders$.next();
      }
      this.initWorkOrder();
      this.timeAndDateLoading = false;
    }, error => {
      this.timeAndDateLoading = false;
    });
  }

  openDuplicateModal() {
    if (!this.workOrder?.execution_at || !this.workOrder?.execution_to) {
      this.toastService.errorToast('workOrderDuplicateNoExecution');
      return;
    }
    let modalRef = this.modalService.open(WorkOrderDuplicateModal, { size: 'lg', centered: true });
    modalRef.componentInstance.workOrder = this.workOrder;
  }

  openRepeatingPayment() {
    let modalRef = this.modalService.open(PaymentDetailsV2Component, {size: 'lg', centered: true});
    modalRef.componentInstance.workOrderTemplate = this.workOrder;
    modalRef.componentInstance.viewSettings = {
      createView: true,
      modalView: true,
      repeatingView: true,
    };
    modalRef.result.then((result) => {
      if (result) {
        this.orderService.getWorkOrderById(this.workOrder!.work_order_id).pipe(takeUntil(this.destroy$)).subscribe((workOrder) => {
          this.workOrder = workOrder;
          this.initWorkOrder();
          this.orderService.refreshSingleWorkOrder(workOrder, 'repeatingPaymentModalClosed');
        });
      }
    });
  }

  goToOrder() {
    if (!this.workOrder?.order_id) return;
    this.router.navigate(['orders/details/', this.workOrder.order_id]);
    this.activeModal.close();
  }

  copyValue(value: string | null): void {
    if (!value) return;
    navigator.clipboard.writeText(value).then(
      () => {
        this.toastService.successToast('copy')
      },
      (err) => console.error('Could not copy text: ', err)
    );
  }

  sendToSubcontractor() {
    const modalRef = this.modalService.open(SendToSubContractorModalComponent, { size: 'lg' });
    modalRef.componentInstance.workOrder = this.workOrder;
    modalRef.result.then((result: WorkOrderResponse | false) => {
      if (result) {
        this.workOrder = result;
        this.orderService.refreshSingleWorkOrder(result, 'sendToSubcontractor');
        this.initWorkOrder();
      }
    });
  }

  removeContract() {
    this.modalService.open(VerifyPopupModal).result.then((result) => {
      if (result) {
        this.loading = true;
        this.orderService.deleteContract(this.workOrder!.work_order_id).pipe(takeUntil(this.destroy$)).subscribe((workOrder) => {
          this.loading = false;
          this.workOrder = workOrder;
          this.orderService.refreshSingleWorkOrder(workOrder, 'removeContract');
          this.initWorkOrder();
        }, error => {
          this.loading = false;
        });
      }
    });
  }

  toggleShowInOrderLinesList() {
    if (!this.workOrder) return;
    let payload: _CRM_ORD_118 = {
      work_order_id: this.workOrder.work_order_id,
      hide_in_order_lines_list: !this.workOrder.hide_in_order_lines_list
    }
    this.orderService.patchWorkOrder(payload).pipe(takeUntil(this.destroy$)).subscribe((workOrder) => {
      this.workOrder = workOrder;
      this.orderService.refreshSingleWorkOrder(workOrder, 'toggleShowInOrderLinesList');
      this.orderService.fetchAndRefreshOrderLines(this.workOrder.order_id, 'toggleShowInOrderLinesList');
    });
  }

  async finishWorkOrder() {
    if (!this.order && !this.viewSettings.contractorView) {
      this.order = await firstValueFrom(this.orderService.getOrderById(this.workOrder?.order_id!));
    }

    if (!this.viewSettings.contractorView && this.order?.order_status_id! < 2) {
      let modalRef = this.modalService.open(VerifyPopupModal, {centered: true});
      modalRef.componentInstance.showBody = true;
      modalRef.componentInstance.titleTranslationKey = 'workOrderDetails.finishBeforeConfirmedModal.title';
      modalRef.componentInstance.bodyBoldTranslationKey = 'workOrderDetails.finishBeforeConfirmedModal.bodyBold';
      modalRef.componentInstance.bodyRegularTranslationKey = 'workOrderDetails.finishBeforeConfirmedModal.bodyRegular';
      try {
        let result: boolean = await modalRef.result;
        if (!result) return;
      } catch (e) {
        return;
      }
    }

    if (this.workOrder?.order_lines.some(line => line.track_time)) {
      let modalRef = this.modalService.open(QuantityProposalModal, {})
      modalRef.componentInstance.workOrder = this.workOrder;
      let answer = await modalRef.result;
    }

    this.loading = true;
    this.orderService.finishWorkOrder(this.workOrder!.work_order_id).pipe(takeUntil(this.destroy$)).subscribe((workOrder) => {
      this.loading = false;
      this.workOrder = workOrder;
      this.initWorkOrder();
      if (this.workOrder.order_lines.length > 0) {
        this.orderService.fetchAndRefreshOrderLines(this.workOrder.order_id, 'finishWorkOrder');
      }
      this.orderService.refreshSingleWorkOrder(workOrder, 'finishWorkOrder');
      this.orderService.fetchAndRefreshOrder(this.workOrder?.order_id!, 'finishWorkOrder');
    }, error => {
      this.loading = false;
    });
  }

  paymentScheduleUpdated(result: any) {
    if (result) {
      this.orderService.getWorkOrderById(this.workOrder!.work_order_id).pipe(takeUntil(this.destroy$)).subscribe((workOrder) => {
        this.workOrder = workOrder;
        this.initWorkOrder();
        this.orderService.refreshSingleWorkOrder(workOrder, 'paymentScheduleUpdated');
      });
    }
  }

  selectWorkOrderTemplate(template: WorkOrderTemplateResponse | null) {
    if (template) {
      this.selectedWorkOrderTemplateId = template.template_id;
      this.selectedWorkOrderTemplateContainsOrderLines = template.order_lines.length > 0;
    } else {
      this.selectedWorkOrderTemplateId = null;
      this.selectedWorkOrderTemplateContainsOrderLines = false;
    }
  }

  subtractOneDay(date: undefined | Date): Date | null {
    if (!date) return null;
    const d = new Date(date);
    d.setDate(d.getDate() - 1); // Subtract one day
    return d;
  }

  addOneDay(date: undefined | Date): Date | null {
    if (!date) return null;
    const d = new Date(date);
    d.setDate(d.getDate() + 1); // Add one day
    return d
  }

  protected readonly orderBadgePayment = paymentStatusBadge;
  protected readonly Math = Math;
  protected readonly favouriteStar = favouriteStar;
  protected readonly displayDate = displayDate;
  protected readonly displayPhoneNumber = displayPhone;
}
