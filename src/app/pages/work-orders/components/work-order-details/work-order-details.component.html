<!-- Main content -->
<div class="container" [ngClass]="{'mt-3': viewSettings.workOrderStandaloneView}" style="max-width: 1180px;">
<div *ngIf="workOrder" class="hidden-id" [ngStyle]="{'position': 'absolute', 'top': '2px', 'right': viewSettings.fromCalendar ? 'auto' : '3px', 'left': viewSettings.modalView ? '3px' : 'auto', 'text-align': 'right', 'padding': '0 0 0px 0px'}">{{workOrder.work_order_id}}</div>
<div *ngIf="viewSettings.modalView" class="border-bottom" style="padding: 16px 16px 4px 16px;" [ngStyle]="{'padding-bottom.px': !workOrder ? 16 : 4}">
  <div class="d-flex justify-content-between align-items-center">
    <i *ngIf="!viewSettings.fromCalendar" class="fa-regular fa-xmark fa-xl" (click)="closeModal()" style="font-weight: 400; cursor: pointer"></i>
    <h4 class="text-center" style="flex-grow: 1;">{{ this.viewSettings.createView ? this.viewSettings.createOrderView ? ('workOrderDetails.createOrderView.createOrder' | translate) : ('workOrderDetails.modal.create.title' | translate) : '#' + this.workOrder?.work_order_number + (workOrder?.work_order_title ? ' - ' + workOrder?.work_order_title : '') }}</h4>
    <!--  Action buttons for repeating view  -->
<!--    <div *ngIf="!viewSettings.createView && viewSettings.repeatingView">-->
<!--      <ng-container *ngTemplateOutlet="dropdownTemplate"></ng-container>-->
<!--    </div>-->
  </div>

  <!--  Work order status bar  -->
  <div *ngIf="workOrder" class="mb-1" style="display: grid; grid-template-columns: 1fr 4fr 1fr;">
    <div>
      <app-button
        *ngIf="!viewSettings.contractorView && workOrder?.order_id && !viewSettings.repeatingView && viewSettings.workOrderStandaloneView"
        [translationKey]="'workOrderDetails.goToOrder'"
        [buttonWidth]="178"
        (buttonClick)="goToOrder()"
      />
    </div>
    <div *ngIf="viewSettings.repeatingView" id="dummyDivForGridDisplayIfRepeating"></div>
    <div class="status-container justify-content-center" *ngIf="!viewSettings.repeatingView">
      <div
        class="rectangle px-2 d-flex justify-content-center align-items-center"
        *ngFor="let status of workOrderStatuses; let i = index"
        [class.no-start]="i === 0"
        [class.no-end]="i === workOrderStatuses.length - 1"
        [class.active]="(status.work_order_status_id === workOrder.work_order_status_id) || (status.work_order_status_id < workOrder.work_order_status_id && workOrder.work_order_status_id != 8)"
        [class.cancelled]="status.work_order_status_id === 8">
        <span [ngClass]="{
        'rectangle-text':
        status.work_order_status_id != workOrder.work_order_status_id &&
        Math.abs(i - getActiveOrderStatusIndex()) > 1,
        }">{{status.work_order_status_name}}</span>
      </div>
    </div>
    <!--  Action buttons  -->
    <div *ngIf="!viewSettings.createView && !viewSettings.contractorView">
      <ng-container *ngTemplateOutlet="dropdownTemplate"></ng-container>
    </div>

  </div>
</div>

<div [ngClass]="[viewSettings.modalView ? 'modal-body p-1 p-xl-3' : 'card order-details-card', viewSettings.listView ? 'no-border' : '']">
    <div class="d-flex flex-column flex-lg-row">
      <div *ngIf="!this.viewSettings.modalView" class="d-none d-md-block">
        <h4>
          <i class="fa-regular fa-arrow-left" (click)="goBack()" style="cursor: pointer"></i>
        </h4>
      </div>
      <div *ngIf="!viewSettings.modalView && !viewSettings.createView" class="ms-2">
        <h4 class="mb-lg-0">
          <i class="fa-regular fa-arrow-left d-inline d-md-none" routerLink="/orders" style="cursor: pointer"></i>
          <span class="fw-bolder color-header">
            {{workOrder?.work_order_title}}
          </span>
        </h4>
      </div>
    </div>

  <!-- Contractor -->
  <div *ngIf="viewSettings.contractorView" class="mb-2">
    <div class="card py-2 information-container">
      <app-contractor-details *ngIf="workOrder?.contractor_relation" [viewSettings]="viewSettings" [workOrder]="workOrder!"></app-contractor-details>
      <h4 class="text-danger" *ngIf="!workOrder?.contractor_relation && !loading">{{"Contractor view set but not contract found" | translate}}</h4>
    </div>
  </div>

  <!-- Contractor message for owning company  -->
  <div *ngIf="!viewSettings.contractorView && contractor" class="mb-2">
    <div class="card py-2 information-container">
      <div class="d-flex justify-content-between">
        <div class="d-flex align-items-center col-10">
          <i class="fa-regular fa-circle-info font-36 me-2"></i>
          <div>
            <div class="d-flex">
              <div class="fw-bold me-1">{{"workOrderDetails.contractor.asCompany.title" | translate}}</div>
              <div class="">- {{"workOrderDetails.contractor.asCompany.description" | translate}}:</div>
              <div class="ps-1">{{contractor.affiliate_name}}</div>
            </div>
            <div *ngIf="!contractor.accepted_at && !contractor.declined_at">
              <i class="fa-regular fa-hourglass-clock text-muted me-1"></i>
              {{"workOrderDetails.contractor.asCompany.pending" | translate}}
            </div>
            <div *ngIf="contractor.accepted_at" class="d-flex align-items-center">
              <i class="fa-regular fa-thumbs-up text-success me-1"></i>
              <div>{{"workOrderDetails.contractor.acceptedBy" | translate}} {{contractor.accepted_by_name}} ({{displayDate(contractor.accepted_at, false)}})</div>
            </div>

            <div *ngIf="contractor.declined_at" class="d-flex align-items-center">
              <i class="fa-regular fa-thumbs-down text-danger me-1"></i>
              <div>{{"workOrderDetails.contractor.declinedBy" | translate}} {{contractor.declined_by_name}} ({{displayDate(contractor.declined_at, false)}})</div>
            </div>
          </div>
        </div>

        <div class="d-flex align-items-center justify-content-end">
          <app-button
            [translationKey]="'workOrderDetails.contractor.remove'"
            [disabled]="loading"
            [loading]="loading"
            (buttonClick)="removeContract()"
          ></app-button>
        </div>

      </div>


    </div>
  </div>

  <!-- Repeating info box -->
  <div *ngIf="viewSettings.repeatingView && !viewSettings.createOrderView" class="mb-2">
    <div class="card py-2 information-container">
      <div class="d-flex align-items-center">
        <i class="fa-regular fa-circle-info font-36 me-2"></i>
        <div>
          <h4 class="my-0">{{"workOrderDetails.repeatingView.infoBox.title" | translate}}</h4>
          <div>{{"workOrderDetails.repeatingView.infoBox.description" | translate}}</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Desktop -->
  <div class="d-lg-flex">

    <!--  Left column  -->
    <div class=" d-lg-block" [ngClass]="viewSettings.createView ? 'col-12' : 'col-lg-8 pe-lg-2'">

      <!--   Single / Repeating selection   -->
      <div *ngIf="viewSettings.createOrderView" class="d-flex align-items-center mb-4">
        <label class="me-2" style="tab-index: 0;">{{"workOrderDetails.createOrderView.orderType" | translate}}:</label>
        <app-button
          [customClass]="'no-right-border-radius no-shadow'"
          [buttonType]="this.viewSettings.repeatingView ? 'nude' : 'solid'"
          [translationKey]="'workOrderDetails.createOrderView.orderType.single'"
          [boldText]="false"
          (buttonClick)="viewSettings.repeatingView = false;"
        ></app-button>
        <app-button
          [customClass]="'no-left-border-radius no-shadow'"
          [buttonType]="!this.viewSettings.repeatingView ? 'nude' : 'solid'"
          [translationKey]="'workOrderDetails.createOrderView.orderType.repeating'"
          [boldText]="false"
          (buttonClick)="viewSettings.repeatingView = true;"
        ></app-button>
      </div>

      <!--  Title and description card   -->
      <div *ngIf="!viewSettings.createView" class="card order-details-card p-0 mb-2">
        <div class="order-details-header border-bottom d-flex justify-content-between gap-2" style="padding: 0 8px 0 24px !important;">
          <div class="order-details-header-title" style="padding: 16px 0 16px 0 !important;">{{"workOrderDetails.workOrderTitleAndDescription" | translate}}</div>
          <div class="d-flex justify-content-end d-flex align-items-center gap-2">
            <app-color-pickerino [color]="workOrderCustomColor" [showAutoPicker]="true" [disabled]="!!this.viewSettings.contractorNotAcceptedView" (colorChange)="updateWorkOrderColor($event)"></app-color-pickerino>
            <app-spinner *ngIf="titleDescriptionLoading"></app-spinner>
          </div>
        </div>

        <div class="p-2">
          <div class="mb-2">
            <label class="">{{"workOrderDetails.workOrderTitle" | translate}}</label>
            <div id="dummyFocusDiv" [tabIndex]="0"></div>
            <app-input
              [editMode]="true"
              [placeholderKey]="'workOrderDetails.workOrderTitlePlaceholder'"
              [control]="workOrderTitleControl"
              [emitChangeOnBlurOnly]="true"
              (valueChange)="saveTitleAndDescription()"
              (onEnterPressed)="saveTitleAndDescription()"
            ></app-input>
          </div>
          <div [ngClass]="viewSettings.createView ? '' : ''">
            <label class="">{{"workOrderDetails.workOrderDescription" | translate}}</label>
            <app-input
              [editMode]="true"
              [placeholderKey]="'workOrderDetails.workOrderDescriptionPlaceholder'"
              [control]="workOrderDescriptionControl"
              [textArea]="true"
              [textAreaAllowScroll]="true"
              [textAreaMaxRows]="expandWorkOrderDescription ? 100 : 5"
              [emitChangeOnBlurOnly]="true"
              (valueChange)="saveTitleAndDescription()"
              (contentOverflow)="descriptionOverflow = $event"
            ></app-input>
            <div *ngIf="descriptionOverflow && !expandWorkOrderDescription" class="clickable-text between-color ps-1 pt-1" (click)="expandWorkOrderDescription = true;">{{'workOrderDetails.workOrderDescription.showMore' | translate}}</div>
            <div *ngIf="descriptionOverflow && expandWorkOrderDescription" class="clickable-text between-color ps-1 pt-1" (click)="expandWorkOrderDescription = false;">{{'workOrderDetails.workOrderDescription.showLess' | translate}}</div>
          </div>

<!--          <div *ngIf="!viewSettings.createView" class="d-flex justify-content-end">-->
<!--            <app-button-->
<!--              *ngIf="!(viewSettings.repeatingView && workOrder?.work_order_status_id === 0)"-->
<!--              [small]="true"-->
<!--              [translationKey]="'common.save'"-->
<!--              [loading]="titleDescriptionLoading"-->
<!--              [feignDisabled]="workOrderTitleControl.pristine && workOrderDescriptionControl.pristine"-->
<!--              (buttonClick)="saveTitleAndDescription()"-->
<!--            ></app-button>-->
<!--          </div>-->
        </div>


      </div>
      <!--   Date and time   -->
      <div *ngIf="workOrder && !viewSettings.repeatingView && !viewSettings.createOrderView && !viewSettings.createView" class="border-rounded-responsive">
        <div *ngIf="!viewSettings.repeatingView && !viewSettings.createOrderView && !viewSettings.createView" class="mb-2 p-0">
          <div class="order-details-header border-bottom d-flex gap-2">
            <div class="order-details-header-title">{{"workOrderDetails.dateAndTime.title" | translate}}</div>
            <div class="d-flex align-items-center">
              <app-spinner *ngIf="timeAndDateLoading"></app-spinner>
            </div>
          </div>

          <div *ngIf="!multiDayWorkOrder" class="d-flex p-2 flex-xl-row justify-content-between">
          <!--    Left side     -->
            <div class="ps-3">
              <app-datepickerino
                #singleDatepickerino
                [selectedDates]="[this.executionDate!]"
                [referenceDate]="this.executionDate"
                [disableAfterDate]="contractorDisabledDate"
                [disableBeforeDate]="contractorDisabledDate"
                [todayAsReference]="!this.executionDate"
                (datesSelectedEmitter)="setExecutionDate($event[0])"
              ></app-datepickerino>
            </div>

            <!-- Divider -->
            <div class="d-none d-xl-block" style="border-right: 2px solid #E3E3E3; margin: 0 1rem;"></div>

            <!--    Right side      -->
            <div class="pe-3 mt-3 mt-md-0">

              <div class="pe-3">
                <!-- Start time  -->
                <div class="mb-3">
                  <label>{{"workOrderDetails.repeatingView.startTime" | translate}}</label>
                  <app-input
                    [editMode]="true"
                    [type]="'time'"
                    [control]="startTimeControl"
                    [emitChangeOnBlurOnly]="true"
                    (valueChange)="saveTimeAndDate()"
                  ></app-input>
                </div>

                <!--     Arrival       -->
                <div class="mb-3">
                  <div class="d-flex">
                    <label>{{"workOrderDetails.dateAndTime.arrival" | translate}}</label>
                    <i class="fa-regular fa-info-circle ms-1 cursor-pointer" style="padding-top: 4px;" [ngbTooltip]="'workOrderDetails.dateAndTime.arrivalTooltip' | translate"></i>
                  </div>
                  <div class="d-flex justify-content-between gap-2">
                    <app-input
                      [editMode]="true"
                      [type]="'time'"
                      [control]="arrivalFromControl"
                      [emitChangeOnBlurOnly]="true"
                    (valueChange)="saveTimeAndDate()"
                    ></app-input>
                    <div class="d-flex align-items-center">{{"common.to" | translate}}</div>
                    <app-input
                      [editMode]="true"
                      [type]="'time'"
                      [control]="arrivalToControl"
                      [emitChangeOnBlurOnly]="true"
                    (valueChange)="saveTimeAndDate()"
                    ></app-input>
                  </div>
                </div>

                <!--   Duration hours  -->
                <div class="">
                  <label>{{"workOrderDetails.repeatingView.estimatedDuration" | translate}}</label>
                  <div class="d-flex gap-2">
                    <div class="">
                      <app-input
                        [editMode]="true"
                        [type]="'number'"
                        [control]="durationHoursControl"
                        [inputFieldMinWidthPx]="30"
                        [inputFieldMaxWidthPx]="60"
                        [inputSuffix]="'workOrderDetails.repeatingView.hours' | translate"
                        [emitChangeOnBlurOnly]="true"
                        (valueChange)="saveTimeAndDate()"
                      ></app-input>
                    </div>

                    <div class="">
                      <app-input
                        [editMode]="true"
                        [type]="'number'"
                        [inputFieldMinWidthPx]="30"
                        [inputFieldMaxWidthPx]="60"
                        [control]="durationMinutesControl"
                        [inputSuffix]="'workOrderDetails.repeatingView.minutes' | translate"
                        [emitChangeOnBlurOnly]="true"
                        (valueChange)="saveTimeAndDate()"
                      ></app-input>
                    </div>
                  </div>
                </div>

                <div class="mt-3">
                  <app-button
                    [translationKey]="'Flerdagsvisning'"
                    [small]="true"
                    [buttonType]="'nude'"
                    (buttonClick)="multiDayWorkOrder = true;"
                  ></app-button>
                </div>

              </div>

  <!--            <div *ngIf="!viewSettings.createView" class="d-flex flex-row justify-content-end align-items-end mt-4">-->
  <!--              <app-button-->
  <!--                [small]="true"-->
  <!--                [translationKey]="'common.save'"-->
  <!--                [loading]="timeAndDateLoading"-->
  <!--                [feignDisabled]="(this.executionDate === this.workOrder?.execution_at && this.startTimeControl.pristine && this.durationHoursControl.pristine && this.durationMinutesControl.pristine && this.arrivalFromControl.pristine && this.arrivalToControl.pristine)"-->
  <!--                [disabled]="!this.startTimeControl.value || (!this.durationHoursControl.value && !this.durationMinutesControl.value) || !this.executionDate"-->
  <!--                (buttonClick)="saveTimeAndDate()"-->
  <!--              ></app-button>-->
  <!--            </div>-->

            </div>

          </div>

          <div *ngIf="multiDayWorkOrder" class="d-flex flex-xl-row flex-column">

            <div class="ps-3">
              <h4 class="text-center">{{"workOrderDetails.repeatingView.startDate" | translate}}</h4>
              <app-datepickerino
                [selectedDates]="this.workOrder && this.workOrder.execution_at ? [this.workOrder!.execution_at!] : []"
                [referenceDate]="this.workOrder && this.workOrder.execution_at ? this.workOrder!.execution_at! : null"
                [disableAfterDate]="contractorDisabledDate || (this.workOrder && this.workOrder.execution_at) ? this.workOrder.execution_at : null"
                [disableBeforeDate]="contractorDisabledDate"
                [todayAsReference]="!this.executionDate"
                [showWeekNumbers]="false"
                (datesSelectedEmitter)="setMultiDayExecutionAt($event[0])"
              ></app-datepickerino>

              <!-- Start time  -->
              <div class="mb-3">
                <label>{{"workOrderDetails.repeatingView.startTime" | translate}}</label>
                <app-input
                  [editMode]="true"
                  [type]="'time'"
                  [control]="startTimeControl"
                  [emitChangeOnBlurOnly]="true"
                  (valueChange)="saveTimeAndDateMultiDay()"
                ></app-input>
              </div>
            </div>

            <div class="ps-3 ms-xl-3">
              <h4 class="text-center">{{"workOrderDetails.repeatingView.endDate" | translate}}</h4>
              <app-datepickerino
                [selectedDates]="this.workOrder && this.workOrder.execution_to ? [this.workOrder!.execution_to!] : []"
                [referenceDate]="this.workOrder && this.workOrder.execution_to ? this.workOrder!.execution_to! : null"
                [disableAfterDate]="contractorDisabledDate"
                [disableBeforeDate]="contractorDisabledDate || (this.workOrder && this.workOrder.execution_to) ? subtractOneDay(this.workOrder.execution_at) : null"
                [showWeekNumbers]="false"
                (datesSelectedEmitter)="setMultiDayExecutionTo($event[0])"
              ></app-datepickerino>

              <!-- End time  -->
              <div class="mb-3">
                <label>{{"workOrderDetails.repeatingView.endTime" | translate}}</label>
                <app-input
                  [editMode]="true"
                  [type]="'time'"
                  [control]="endTimeControl"
                  [emitChangeOnBlurOnly]="true"
                  (valueChange)="saveTimeAndDateMultiDay()"
                ></app-input>
              </div>
            </div>

            <div class="mt-1 ms-2">
              <i class="fa-regular fa-xmark fa-xl cursor-pointer" (click)="multiDayWorkOrder = false;"></i>
            </div>

          </div>
        </div>
      </div>
      <!--   Schedule   -->
      <div *ngIf="viewSettings.repeatingView && !viewSettings.createView" class="card order-details-card mb-2 p-0">
        <div class="order-details-header border-bottom d-flex gap-2">
          <div class="order-details-header-title">{{"workOrderDetails.repeatingView.title" | translate}}</div>
          <div class="d-flex align-items-center">
            <app-spinner *ngIf="scheduleLoading"></app-spinner>
          </div>
        </div>

        <div class="p-2 ps-3">
          <!--   Execution   -->
          <div class="d-flex flex-column flex-xl-row mb-3">
            <!-- Start time  -->
            <div class="mb-2 mb-lg-0 me-lg-3" style="min-width:200px;">
              <label>{{ "workOrderDetails.repeatingView.startTime" | translate }}</label>
              <app-input
                [editMode]="true"
                [type]="'time'"
                [control]="startTimeControl"
                class="w-100"
              ></app-input>
            </div>

            <!-- Duration hours and minutes -->
            <div>
              <label>{{ "workOrderDetails.repeatingView.estimatedDuration" | translate }}</label>
              <div class="d-flex flex-wrap flex-sm-nowrap gap-2">
                <app-input
                  [editMode]="true"
                  [type]="'number'"
                  [centerWithNoPadding]="true"
                  [control]="durationHoursControl"
                  [inputFieldMinWidthPx]="30"
                  [inputFieldMaxWidthPx]="60"
                  [inputSuffix]="'workOrderDetails.repeatingView.hours' | translate"
                  class="w-auto"
                ></app-input>

                <app-input
                  [editMode]="true"
                  [type]="'number'"
                  [centerWithNoPadding]="true"
                  [inputFieldMinWidthPx]="30"
                  [inputFieldMaxWidthPx]="60"
                  [control]="durationMinutesControl"
                  [inputSuffix]="'workOrderDetails.repeatingView.minutes' | translate"
                  class="w-auto"
                ></app-input>
              </div>
            </div>
          </div>

          <!--    Schedule setup    -->
          <app-schedule-setup
            [scheduleInputSource]="scheduleInputSubject"
            [disabled]="!!this.viewSettings.contractorNotAcceptedView"
          ></app-schedule-setup>

          <div *ngIf="!viewSettings.createView && !viewSettings.contractorNotAcceptedView" class="d-flex justify-content-end align-items-end">
            <app-button
              *ngIf="!(viewSettings.repeatingView && workOrder?.work_order_status_id === 0)"
              [small]="true"
              [disabled]="!this.startTimeControl.value || (!this.durationHoursControl.value && !this.durationMinutesControl.value)"
              [translationKey]="'common.save'"
              [loading]="scheduleLoading"
              (buttonClick)="updateWorkOrderSchedule()"
            ></app-button>
          </div>

        </div>

      </div>

      <!--   Work order list for repeating contractor view   -->
      <div *ngIf="viewSettings.contractorView && viewSettings.repeatingView && workOrder?.schedule?.num_work_orders! > 0" class="mb-2">
        <app-work-order-list [workOrderTemplate]="workOrder" [showFiveNext]="true" [viewSettings]="{contractorView: true, contractorNotAcceptedView: !!viewSettings.contractorNotAcceptedView}" (modalOpenedEmitter)="activeModal.close()"></app-work-order-list>
      </div>

      <!--  Employees and resources    -->
      <div *ngIf="!viewSettings.createView && workOrder" class="mb-2">
        <app-employee-resources [workOrder]="workOrder" [cardView]="true" [payloadSubject]="payloadSubject" [disabled]="!!viewSettings.contractorNotAcceptedView"></app-employee-resources>
      </div>

      <!--   Template selection   -->
      <div *ngIf="viewSettings.createView && workOrderTemplates.length === 0 && templatesLoaded" class="">
        <app-info-box
          [titleTranslationKey]="'workOrderDetails.createFromTemplate.noTemplates.title'"
          [contentTranslationKey]="'workOrderDetails.createFromTemplate.noTemplates.description'"
        ></app-info-box>
      </div>
      <div *ngIf="viewSettings.createView && workOrderTemplates.length > 0" class="">
        <h5 class="mt-0">{{"workOrderDetails.createFromTemplate" | translate}}</h5>
<!--        <div class="">-->
<!--          <div class="d-flex gap-1 flex-wrap">-->
            <div *ngFor="let tmp of showAllTemplates ? workOrderTemplates : topWorkOrderTemplates" class="order-details-card cursor-pointer py-1 d-flex justify-content-lg-between align-items-center mb-1" (click)="selectWorkOrderTemplate(selectedWorkOrderTemplateId === tmp.template_id ? null : tmp);" [ngClass]="{'selected-template': selectedWorkOrderTemplateId === tmp.template_id}">
              <div class="d-flex">
                <span>{{tmp.template_name}}</span>
                <div *ngIf="tmp.favourite" class="ms-1" [innerHTML]="favouriteStar(tmp.favourite, true)"></div>
              </div>
              <i *ngIf="selectedWorkOrderTemplateId === tmp.template_id" class="fa-regular fa-check fa-lg"></i>
            </div>
<!--          </div>-->
<!--        </div>-->
        <app-button
          *ngIf="workOrderTemplates.length > 6 && !showAllTemplates"
          [buttonType]="'link'"
          [translationKey]="'workOrderDetails.createFromTemplate.showAll'"
          (buttonClick)="showAllTemplates = true;"
        ></app-button>
      </div>

      <!--   Repeating payment   -->
      <div *ngIf="!viewSettings.createView && viewSettings.repeatingView && workOrder?.payment?.template" class="mb-2">
        <order-payment-schedule [payment]="workOrder?.payment!" [workOrder]="workOrder" [viewSettings]="viewSettings" (paymentUpdatedEmitter)="paymentScheduleUpdated($event)"></order-payment-schedule>
      </div>

      <!-- Order lines -->
      <div class=" mb-2">
        <app-order-lines [viewSettings]="viewSettings" [workOrder]="workOrder" [orderLinesSubject]="orderLinesSubject" [order]="order"></app-order-lines>
      </div>

      <!--   Contractor order notes   -->
      <div *ngIf="viewSettings.contractorView" class="order-details-card mb-2">
        <app-order-notes-details [workOrder]="workOrder!"></app-order-notes-details>
      </div>

      <!--    Timetracking    -->
      <div class="mb-2" *ngIf="!viewSettings.createView && !(viewSettings.repeatingView && workOrder?.work_order_status_id === 0) && !viewSettings.contractorNotAcceptedView">
        <app-order-timetracking [workOrderView]="true"></app-order-timetracking>
      </div>

      <!--   Left column split   -->
      <div class="d-flex" *ngIf="!viewSettings.createView">

      </div>

    </div>

    <!-- Right column -->
    <div *ngIf="!viewSettings.createView && workOrder" class="col-lg-4 p-0">

      <div *ngIf="viewSettings.contractorView" class="mb-2">
        <app-card
          [labelKey]="'Kunde'"
          [padding]="'0'">
            <ng-container cardcontent>
              <div class="px-3 py-2">
                <div *ngIf="serviceRecipient">
                  <div class="d-flex align-items-center mb-2">
                    <div class="initials-box">
                      <span *ngIf="serviceRecipient.is_private == 1">{{serviceRecipientInitials}}</span>
                      <i *ngIf="serviceRecipient.is_private == 0" class="fa-regular fa-buildings fa-xl"></i>
                    </div>
                    <div class="ps-2 fw-bold">{{ serviceRecipient.name }}</div>
                  </div>

                  <div class="mb-2" style="overflow: hidden;">
                    <i class="fa-regular fa-phone "></i>
                    {{displayPhoneNumber(serviceRecipient.phone)}}
                    <i *ngIf="serviceRecipient.phone" class="fa-regular fa-copy" style="float: right; cursor: pointer;" (click)="copyValue(displayPhoneNumber(serviceRecipient.phone))"></i>
                    <span *ngIf="!(serviceRecipient.phone)" class="ms-1 text-muted"><i class="fa-regular fa-warning text-warning me-1"></i>{{"common.noPhone" | translate}}</span>
                  </div>
                  <div style="overflow: hidden;">
                    <i class="fa-regular fa-envelope"></i>
                    {{ serviceRecipient.email }}
                    <i *ngIf="serviceRecipient.email" class="fa-regular fa-copy" style="float: right; cursor: pointer;" (click)="copyValue(serviceRecipient.email)"></i>
                    <span *ngIf="!(serviceRecipient.email)" class="ms-1 text-muted"><i class="fa-regular fa-warning text-warning me-1"></i>{{"common.noEmail" | translate}}</span>
                  </div>
                  <div class="mt-2" *ngIf="serviceRecipient.organisation_number" style="overflow: hidden;">
                    <i class="fa-regular fa-building"></i>
                    {{ serviceRecipient.organisation_number }}
                    <i class="fa-regular fa-copy" style="float: right; cursor: pointer;" (click)="copyValue(serviceRecipient.organisation_number)"></i>
                  </div>
                </div>

                <div *ngIf="!serviceRecipient">{{"order.orderDetails.customer.noServiceRecipient" | translate}}</div>

              </div>

            </ng-container>
          </app-card>

      </div>

      <!--   Addresses   -->
      <div class="mb-2">
        <app-order-address [workOrderView]="true" [viewSettings]="viewSettings"></app-order-address>
      </div>

      <!--   Reports   -->
      <div *ngIf="!viewSettings.createView && !viewSettings.contractorNotAcceptedView && !workOrder.schedule_template" class="mb-2">
        <app-order-crew-reports [workOrderView]="true"></app-order-crew-reports>
      </div>

      <!--   Attachments   -->
      <div class="mb-2" *ngIf="!viewSettings.contractorNotAcceptedView && !workOrder.schedule_template">
        <app-order-attachments [workOrder]="workOrder"></app-order-attachments>
      </div>

      <!--   Checklists   -->
      <div class="mb-2" *ngIf="!viewSettings.contractorNotAcceptedView">
        <app-order-checklist [workOrder]="workOrder" [viewSettings]="viewSettings" [workOrderView]="true"></app-order-checklist>
      </div>

      <!--   CQ   -->
<!--      <div *ngIf="workOrder?.order_customer_questions" class="">-->
<!--        <app-order-customer-questions [workOrderView]="true"></app-order-customer-questions>-->
<!--      </div>-->

    </div>

  </div>
</div>

<!-- Footer buttons -->
<div class="d-flex justify-content-between p-2 modal-footer">
  <div class="d-flex">
    <button *ngIf="viewSettings.modalView && viewSettings.createView" type="submit" class="btn btn-secondary me-2" (click)="close()">{{ "common.cancel" | translate }}</button>
    <button *ngIf="!viewSettings.createView" [disabled]="loading" type="submit" class="btn btn-danger" (click)="deleteWorkOrder()">
      <span *ngIf="!loading">{{ "common.delete" | translate }}</span>
      <app-spinner *ngIf="loading"></app-spinner>
    </button>
  </div>
  <div class="d-flex gap-2">
    <!--  Schedule un-initiated work order schedule  -->
    <app-button
      *ngIf="viewSettings.repeatingView && workOrder?.work_order_status_id == 0"
      [translationKey]="'workOrderDetails.repeatingView.scheduleButton'"
      [disabled]="!this.startTimeControl.value || (!this.durationHoursControl.value && !this.durationMinutesControl.value) || loading || startTimeControl.invalid || durationHoursControl.invalid || durationMinutesControl.invalid"
      [ngbTooltip]="!this.startTimeControl.value || (!this.durationHoursControl.value && !this.durationMinutesControl.value) ? ('workOrderDetails.repeatingView.scheduleButton.verifySchedule' | translate) : ''"
      [loading]="loading || scheduleLoading"
      (buttonClick)="updateWorkOrderSchedule(true)"
    ></app-button>
  </div>
</div>

<!-- Action buttons-->
<ng-template #dropdownTemplate>
  <div *ngIf="!viewSettings.createView" id="buttonOuterContainerDiv" class="">
    <div class="btn-group d-flex align-items-end me-2">
      <button type="button" class="btn btn-primary" [disabled]="loading" [ngClass]="loading ? '' : 'dropdown-toggle'" style="width: 178px; height: 38px;" data-bs-toggle="dropdown" aria-expanded="false">
        <span *ngIf="!loading">{{ "orders.orderDetails.otherActions" | translate }} <span class="caret"></span></span>
        <app-spinner *ngIf="loading"></app-spinner>
      </button>

      <div #dropDownButtonsDiv *ngIf="!viewSettings.createView" class="dropdown-menu" [hidden]="loading">

        <!--  Duplicate   -->
        <button
          *ngIf="!viewSettings.repeatingView && !viewSettings.contractorView && !viewSettings.repeatingView"
          class="dropdown-item"
          (click)="openDuplicateModal()">
          <i class="fa-regular fa-copy me-1"></i>
          {{ "workOrderDetails.actionButtons.duplicate" | translate }}
        </button>

        <!--  Create repeating payment   -->
        <button
          *ngIf="viewSettings.repeatingView && !viewSettings.contractorView && !viewSettings.repeatingView"
          class="dropdown-item"
          (click)="openRepeatingPayment()">
          <i class="fa-regular fa-arrows-repeat-1 me-1"></i>
          <span >{{"orderDetails.createRepeatingPayment" | translate}}</span>
        </button>

        <button
          *ngIf="workOrder && workOrder.subcontractors.length === 0 && !viewSettings.contractorView"
          class="dropdown-item"
          (click)="sendToSubcontractor()">
          <i class="fa-regular fa-user-helmet-safety me-1"></i>
          {{ "orders.orderDetails.sendToSubContractor" | translate }}
        </button>

        <button
          *ngIf="workOrder && workOrder.work_order_status_id != 2 && !viewSettings.repeatingView"
          class="dropdown-item"
          (click)="finishWorkOrder()">
          <i class="fa-regular fa-check me-1"></i>
          {{ "workOrderDetails.actionButtons.finish" | translate }}
        </button>

        <button
          *ngIf="workOrder && !workOrder.hide_in_order_lines_list && !viewSettings.repeatingView"
          class="dropdown-item"
          (click)="toggleShowInOrderLinesList()">
          <i class="fa-regular fa-eye-slash me-1"></i>
          {{ "workOrderDetails.actionButtons.hideInOrderLinesList" | translate }}
        </button>

        <button
          *ngIf="workOrder && workOrder.hide_in_order_lines_list && !viewSettings.repeatingView"
          class="dropdown-item"
          (click)="toggleShowInOrderLinesList()">
          <i class="fa-regular fa-eye me-1"></i>
          {{ "workOrderDetails.actionButtons.showInOrderLinesList" | translate }}
        </button>

        <button
          *ngIf="!viewSettings.createView && !viewSettings.repeatingView"
          class="dropdown-item"
          (click)="deleteWorkOrder()"
          [disabled]="loading">
          <i class="fa-regular fa-trash-alt me-1"></i>
          {{ "common.delete" | translate }}
        </button>

      </div>

    </div>
  </div>
</ng-template>
</div>

