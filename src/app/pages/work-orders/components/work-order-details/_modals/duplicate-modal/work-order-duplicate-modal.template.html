<div class="modal-header d-flex justify-content-between align-items-center">
  <h4 class="text-center" style="flex-grow: 1;">{{ "workOrderDetails.duplicateModal.title"| translate }}</h4>
</div>
<div  class="modal-body row p-3">
  <div class="d-flex">
    <div class="mb-2 px-3 py-1" style="border-right: 2px solid #E3E3E3">
      <app-datepickerino
        [multiSelect]="true"
        [referenceDate]="workOrder.execution_at"
        (datesSelectedEmitter)="selectedDates = $event"
      ></app-datepickerino>
    </div>
    <div class="ps-3 pt-2">
      <div class="fw-bold pb-2">{{"workOrderDetails.duplicateModal.chosenDates" | translate}}:</div>
      <div *ngFor="let date of selectedDates">{{formatFullDayAndDate(date, false)}}</div>
    </div>

  </div>

  <div class="card order-details-card gap-1">
    <app-toggle-switch
      [labelKey]="'workOrderDetails.duplicateModal.users'"
      [state]="users"
      (stateChange)="users = $event"
    ></app-toggle-switch>

    <app-toggle-switch
      [labelKey]="'workOrderDetails.duplicateModal.resources'"
      [state]="resources"
      (stateChange)="resources = $event"
    ></app-toggle-switch>

    <app-toggle-switch
      [labelKey]="'workOrderDetails.duplicateModal.orderLines'"
      [state]="orderLines"
      (stateChange)="orderLines = $event"
    ></app-toggle-switch>

    <app-toggle-switch
      [labelKey]="'workOrderDetails.duplicateModal.checklists'"
      [state]="checklists"
      (stateChange)="checklists = $event"
    ></app-toggle-switch>


  </div>
</div>

<div class="modal-footer justify-content-end pe-2">
  <button class="btn btn-secondary me-2" style="min-width: 80px;" (click)="cancel()">
    {{ 'common.cancel' | translate}}
  </button>
  <button class="btn btn-primary" [disabled]="selectedDates.length === 0" style="min-width: 80px;" (click)="create()">
    <span *ngIf="!loading">{{ 'common.create' | translate}}</span>
    <app-spinner *ngIf="loading"></app-spinner>
  </button>
</div>
