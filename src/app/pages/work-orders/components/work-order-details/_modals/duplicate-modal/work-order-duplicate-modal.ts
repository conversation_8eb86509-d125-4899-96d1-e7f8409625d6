import {Component, forwardRef, Input} from '@angular/core';
import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Module, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>n<PERSON><PERSON><PERSON>picker, <PERSON><PERSON>Modal} from '@ng-bootstrap/ng-bootstrap';
import {SelectoriniComponent} from "../../../../../../@shared/components/selectorini/selectorini.component";
import {TranslateModule} from "@ngx-translate/core";


import {NgForOf, NgIf} from "@angular/common";
import {SpinnerComponent} from "../../../../../../@shared/components/spinner/spinner.component";

import {ScheduleSetupComponent} from "../../../../../../@shared/components/schedule-component/schedule-setup.component";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";

import {RouterLink} from "@angular/router";
import {WorkOrderCardComponent} from "../../../../../orders/order-details-v2/work-order-card/work-order-card.component";
import {OrderAddressComponent} from "../../../../../orders/order-details-v2/order-address/order-address.component";
import {OrderCrewReportsComponent} from "../../../../../orders/order-details-v2/order-crew-reports/order-crew-reports.component";
import {OrderTimetrackingComponent} from "../../../../../orders/order-details-v2/order-timetracking/order-timetracking.component";
import {ChecklistComponent} from "../../../../../orders/order-details-v2/order-checklist/checklist.component";
import {OrderCustomerQuestionsComponent} from "../../../../../orders/order-details-v2/order-customer-questions/order-customer-questions.component";
import {OrderAttachmentsComponent} from "../../../../../orders/order-details-v2/order-attachments/order-attachments.component";
import {WorkOrderResponse} from "../../../../../../@shared/models/order.interfaces";
import {DayTemplateContext} from "@ng-bootstrap/ng-bootstrap/datepicker/datepicker-day-template-context";
import {DatepickerinoComponent} from "../../../../../../@shared/components/datepickerino/datepickerino.component";
import {formatFullDayAndDate, UtilsService} from "../../../../../../@core/utils/utils.service";
import {_CRM_ORD_117, _CRM_ORD_154} from "../../../../../../@shared/models/input.interfaces";
import {OrderService} from "../../../../../../@shared/services/order.service";
import {firstValueFrom, forkJoin} from "rxjs";
import {ToastService} from "../../../../../../@core/services/toast.service";
import {StandardImports} from "../../../../../../@shared/global_import";
import {ToggleSwitchComponent} from "../../../../../../@shared/components/toggle-switch/toggle-switch.component";

@Component({
    selector: 'app-work-order-duplicate-modal',
    templateUrl: './work-order-duplicate-modal.template.html',
    styleUrls: ['./work-order-duplicate-modal.style.css'],
    standalone: true,
  imports: [StandardImports, DatepickerinoComponent, ToggleSwitchComponent, SpinnerComponent]
})

export class WorkOrderDuplicateModal {
  @Input() workOrder: WorkOrderResponse;
  loading: boolean = false;
  users: boolean = true;
  resources: boolean = true;
  checklists: boolean = false;
  orderLines: boolean = false;
  selectedDates: Date[] = [];


  constructor(public activeModal: NgbActiveModal, public utilsService: UtilsService, private orderService: OrderService, private toastService: ToastService) { }

  async create() {
    this.loading = true;
    if (this.selectedDates.length === 0) {
      return;
    }
    for (const date of this.selectedDates) {
      let payload: _CRM_ORD_154 = {
        work_order_id: this.workOrder.work_order_id,
        execution_at: date,
        copy_users: this.users,
        copy_resources: this.resources,
        copy_tasks: this.checklists,
        copy_order_lines: this.orderLines,
      }
      const res = await firstValueFrom(this.orderService.duplicateWorkOrder(payload));
    }

    this.toastService.successToast('created');
    this.orderService.refreshWorkOrders$.next();
    this.orderService.fetchAndRefreshOrder(this.workOrder.order_id, 'duplicateWorkOrder');
    this.loading = false;
    this.activeModal.close(true);
  }

  cancel() {
    this.activeModal.close(false)
  }

  protected readonly formatFullDayAndDate = formatFullDayAndDate;
}
