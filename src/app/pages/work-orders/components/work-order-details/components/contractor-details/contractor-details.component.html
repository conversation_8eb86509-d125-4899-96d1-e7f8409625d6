<div class="d-flex align-items-center w-100">
  <i class="fa-regular fa-circle-info font-36 me-2"></i>
  <div>
    <div class="d-flex">
      <div class="fw-bold me-1">{{"workOrderDetails.contractor.title" | translate}}</div>
      <div class="">- {{"workOrderDetails.contractor.viewingAs" | translate}}</div>
    </div>

    <div class="d-flex">
      <div>{{"workOrderDetails.contractor.owner" | translate}}: {{workOrder.company_name}} ({{contract.created_by_name}})</div>
    </div>

    <div *ngIf="!contract.accepted_at && !contract.declined_at" class="d-flex align-items-center col-12">
      <i class="fa-regular fa-hourglass-clock text-muted me-1"></i>
      <div>{{"workOrderDetails.contractor.pending" | translate}}</div>
    </div>

    <div *ngIf="contract.accepted_at" class="d-flex align-items-center">
      <i class="fa-regular fa-thumbs-up text-success me-1"></i>
      <div>{{"workOrderDetails.contractor.acceptedBy" | translate}} {{contract.accepted_by_name}} ({{displayDate(contract.accepted_at, false)}})</div>
    </div>

    <div *ngIf="contract.declined_at" class="d-flex align-items-center">
      <i class="fa-regular fa-thumbs-down text-danger me-1"></i>
      <div>{{"workOrderDetails.contractor.declinedBy" | translate}} {{contract.declined_by_name}} ({{displayDate(contract.declined_at, false)}})</div>
    </div>
  </div>
  <div *ngIf="!contract.accepted_at && !contract.declined_at" class="d-flex gap-2 col ms-auto justify-content-end">
    <app-button [disabled]="!!contract.accepted_at" [translationKey]="'workOrderDetails.contractor.accept'" [small]="true" (buttonClick)="acceptContract()"/>
    <app-button [disabled]="!!contract.declined_at || !!contract.accepted_at" [themeStyle]="'danger'" [translationKey]="'workOrderDetails.contractor.decline'" [small]="true" (buttonClick)="declineContract()"/>
  </div>
</div>
