import {AfterViewInit, ChangeDetectorRef, Component, ElementRef, EventEmitter, forwardRef, Input, OnChanges, OnDestroy, OnInit, Optional, Output, SimpleChanges, ViewChild} from '@angular/core';
import {NgbActiveModal, NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {BehaviorSubject} from "rxjs";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {FormControl, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {DetailsViewSettings, OrderLineRow, OrderResponse, OrderSubContractorResponse, WorkOrderResponse, WorkOrderStatusResponse} from "../../../../../../@shared/models/order.interfaces";
import {displayDate, favouriteStar, paymentStatusBadge, UtilsService} from "../../../../../../@core/utils/utils.service";
import {OrderService} from "../../../../../../@shared/services/order.service";


import {ButtonComponent} from "../../../../../../@shared/components/button/button.component";
import {Location, NgIf} from "@angular/common";




import {Router} from "@angular/router";
import {WorkOrderCardComponent} from "../../../../../orders/order-details-v2/work-order-card/work-order-card.component";









import {StorageService} from "../../../../../../@core/services/storage.service";
import {_CRM_ORD_169} from "../../../../../../@shared/models/input.interfaces";
import {ToastService} from "../../../../../../@core/services/toast.service";
import {StandardImports} from "../../../../../../@shared/global_import";

@Component({
  selector: 'app-contractor-details',
  templateUrl: './contractor-details.component.html',
  styleUrls: ['./contractor-details.component.css'],
  standalone: true,
  imports: [StandardImports]
})
export class ContractorDetailsComponent implements OnInit, OnChanges {
  @Input() workOrder: WorkOrderResponse;
  @Input() viewSettings: DetailsViewSettings;
  contract: OrderSubContractorResponse;
  @Output() workOrderUpdated = new EventEmitter<WorkOrderResponse>();

  constructor(public utilsService: UtilsService,
              @Optional() public activeModal: NgbActiveModal,
              @Optional() private location: Location,
              private orderService: OrderService,
              private modalService: NgbModal,
              private cdr: ChangeDetectorRef,
              private router: Router,
              private storageService: StorageService,
              private toastService: ToastService) {
  }

  ngOnInit() {
    this.initWorkOrder();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['workOrder']) {
      this.initWorkOrder();
    }
  }

  acceptContract() {
    let payload: _CRM_ORD_169 = {
      work_order_id: this.workOrder.work_order_id,
      accept: true,
    }
    this.orderService.updateWorkOrderProposalAsSubContractor(payload).subscribe(res => {
      this.toastService.successToast('contractAccepted');
      this.workOrder = res;
      if (this.workOrder.schedule_template) {
        this.orderService.refreshWorkOrders$.next();
      }
      this.initWorkOrder();
      this.orderService.refreshSingleWorkOrder(res, 'contractorDetailsAcceptContract');
      this.storageService.registerContractorWorkOrders();
    })
  }

  declineContract() {
    let payload: _CRM_ORD_169 = {
      work_order_id: this.workOrder.work_order_id,
      decline: true,
    }
    this.orderService.updateWorkOrderProposalAsSubContractor(payload).subscribe(res => {
      this.toastService.successToast('contractDeclined');
      this.workOrder = res;
      this.orderService.refreshWorkOrders$.next();
      this.initWorkOrder();
      this.orderService.refreshSingleWorkOrder(res, 'contractorDetailsDeclineContract');
      this.storageService.registerContractorWorkOrders();
    })
  }

  initWorkOrder() {
    this.contract = this.workOrder.contractor_relation!;
  }

  protected readonly displayDate = displayDate;
}
