.strikethrough {
  text-decoration: line-through;
}

.order-details-card {
  border: 1px solid #DEE2E6;
  border-radius: 10px;
  box-shadow: none;
  margin: 0;
  padding: 24px 12px;
}

.information-container {
  border-radius: 10px;
  padding: 24px 12px;
  /*box-shadow: 1px 1px 5px 0 rgba(0, 0, 0, 0.1);*/
  background-color: #448C7429;
}

.order-details-card.no-border {
  border: none;
}

.no-right-border-radius {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.no-left-border-radius {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.status-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.rectangle {
  position: relative;
  flex: 1;
  height: 40px;
  font-size: 14px;
  background-color: rgb(241, 243, 250); /* Default background color */
  text-align: center;
  font-weight: bold;
  clip-path: polygon(0% 0%, 90% 0%, 100% 50%, 90% 100%, 0% 100%, 10% 50%);
  border-radius: 5px;
  margin-right: -12px;
  overflow: visible;
  line-height: 12px;
  max-width: 200px;
}

@media (max-width: 400px) {
  .rectangle {
    margin-right: 0px;
  }
  .rectangle-text {
    display: none;
  }
}


@media (min-width: 401px) {
  .rectangle {
    font-size: 10px;
    margin-right: -4px;
    padding-right: 0px;
    padding-left: 0px;
  }
}

@media (min-width: 491px) {
  .rectangle {
    font-size: 12px;
    margin-right: -4px;
  }
}

@media (min-width: 768px) {
  .rectangle {
    font-size: 12px;
    margin-right: -4px;
  }
}

@media (min-width: 980px) {
  .rectangle {
    font-size: 14px;
    margin-right: -4px;
  }
}

@media (min-width: 1100px) {
  .rectangle {
    font-size: 14px;
    margin-right: -8px;
  }
}

@media (min-width: 1200px) {
  .rectangle {
    font-size: 14px;
    margin-right: -12px;
  }
}

.rectangle.no-start {
  clip-path: polygon(0% 0%, 90% 0%, 100% 50%, 90% 100%, 0% 100%, 0% 50%);
}

.rectangle.no-end {
  margin-right: 0;
  clip-path: polygon(0% 0%, 100% 0%, 100% 50%, 100% 100%, 0% 100%, 10% 50%);
}

.rectangle.active {
  background-color: rgb(10, 207, 151);
  color: white;
}

.rectangle.cancelled {
  background-color: rgb(235, 145, 145);
  color: white;
}

.selected-template {
  background-color: #448C74;
  color: white;
}

.initials-box {
  width: 40px;
  height: 40px;
  background-color: #f0f0f0;
  color: #80888E;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-size: 15px;
  border-radius: 5px;
  text-overflow: ellipsis;
  overflow: hidden; /* Ensures the text does not overflow the box */
  white-space: nowrap; /* Prevents the text from wrapping */
}

.order-details-header {
  padding: 16px 8px 16px 24px !important;
  font-weight: 600 !important;
}

.order-details-header-title {
  font-size: 18px;
  font-weight: 600 !important;
  color: #2C2C2C;
}

.hidden-id {
  color: white;
}

.hidden-id:hover {
  color: #2c2c2c;
}

.border-rounded-responsive {
  @media (max-width: 1199.98px) {
    border: 1px solid #ddd;
    border-radius: 10px;
    margin-bottom: 16px;
  }
}
