import {Component, Input, OnInit, Optional} from '@angular/core';
import {NgbActiveModal, NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {Subject} from "rxjs";
import {DetailsViewSettings} from "../../../../@shared/models/order.interfaces";
import {favouriteStar, UtilsService} from "../../../../@core/utils/utils.service";
import {OrderService} from "../../../../@shared/services/order.service";
import {Router} from "@angular/router";
import {WorkOrderTemplateResponse} from "../../../../@shared/models/templates.interfaces";

import {StorageService} from "../../../../@core/services/storage.service";
import {takeUntil} from "rxjs/operators";
import {StandardImports} from "../../../../@shared/global_import";
import {SpinnerComponent} from "../../../../@shared/components/spinner/spinner.component";
import {InfoBoxComponent} from "../../../../@shared/components/info-box/info-box.component";
import {_CRM_ORD_117, _CRM_ORD_122} from "../../../../@shared/models/input.interfaces";
import {WorkOrderDetailsComponent} from "../work-order-details/work-order-details.component";


@Component({
  selector: 'app-work-order-details',
  templateUrl: './new-work-order.component.html',
  styleUrls: ['./new-work-order.component.css'],
  standalone: true,
  imports: [
    StandardImports,
    SpinnerComponent,
    InfoBoxComponent,
  ]
})
export class NewWorkOrderComponent implements OnInit {
  @Input() viewSettings: DetailsViewSettings = {};
  @Input() orderId: number | null = null;
  workOrderTemplates: WorkOrderTemplateResponse[] = [];
  topWorkOrderTemplates: WorkOrderTemplateResponse[] = [];
  selectedWorkOrderTemplateId: number | null = null;
  selectedWorkOrderTemplateContainsOrderLines: boolean = false;

  templatesLoaded: boolean = false;
  showAllTemplates: boolean = false;

  createLoading: boolean = false;
  createNoOrderLinesLoading: boolean = false;

  defaultRepeating: boolean = false;

  private destroy$ = new Subject<void>();

  constructor(public utilsService: UtilsService,
              public activeModal: NgbActiveModal,
              private modalService: NgbModal,
              private orderService: OrderService,
              private router: Router,
              private storageService: StorageService,
              ) {
  }

  ngOnInit() {
    this.storageService.defaultRepeatingOrders$.pipe(takeUntil(this.destroy$)).subscribe((defaultRepeating) => {
      this.defaultRepeating = defaultRepeating;
      console.log('defaultRepeating', this.defaultRepeating);
    });

    this.storageService.workOrderTemplates$.pipe(takeUntil(this.destroy$)).subscribe((templates) => {
      this.templatesLoaded = true;
      this.workOrderTemplates = templates.sort((a, b) => {
        if (a.favourite === b.favourite) {
          const nameA = a.template_name || ''; // Default to an empty string if null
          const nameB = b.template_name || '';
          return nameA.localeCompare(nameB);
        }
        return a.favourite ? -1 : 1;
      });
      this.topWorkOrderTemplates = this.workOrderTemplates.slice(0, 6);
    });
  }


  close() {
    this.activeModal.close();
  }

  createWorkOrder(noTemplateOrderLines: boolean) {
    if (noTemplateOrderLines) {
      this.createNoOrderLinesLoading = true;
    } else {
      this.createLoading = true;
    }

    if (this.viewSettings.createOrderView && this.selectedWorkOrderTemplateId === null) {
      this.orderService.createOrderDraft({repeating: this.viewSettings.repeatingView}).pipe(takeUntil(this.destroy$)).subscribe((order) => {
        this.createNoOrderLinesLoading = false;
        this.createLoading = false;
        this.router.navigate(['orders/details/', order.order_id]);
        this.orderService.fetchAndRefreshOrder(order.order_id, 'createOrderDraft');
        this.activeModal.close(order);
      });
    }
    else {
      let payload: _CRM_ORD_117 = {
        order_id: this.orderId,
        execution_at: null,
        execution_to: null,
        work_order_template_id: this.selectedWorkOrderTemplateId,
        create_order: this.viewSettings.createOrderView,
        skip_order_line_creation: (this.viewSettings.repeatingView && !this.viewSettings.createOrderView) || noTemplateOrderLines,
      }

      if (!this.viewSettings.repeatingView) {
        this.orderService.createWorkOrder(payload).pipe(takeUntil(this.destroy$)).subscribe((workOrder) => {
          if (this.viewSettings.createOrderView) {
            this.router.navigate(['orders/details/', workOrder.order_id]);
            this.activeModal.close();
          } else {
            this.orderService.fetchAndRefreshOrder(workOrder.order_id, 'createWorkOrder');
            this.orderService.refreshWorkOrders$.next();
            if (!payload.skip_order_line_creation) {
              this.orderService.fetchAndRefreshOrderLines(workOrder.order_id, 'createWorkOrder');
            }
            let modalRef = this.modalService.open(WorkOrderDetailsComponent, {size: 'xl'});
            modalRef.componentInstance.viewSettings = {
              modalView: true,
              collapsedOrderLines: true,
              workOrderView: true,
            }
            modalRef.componentInstance.workOrderId = workOrder.work_order_id;
            this.activeModal.close();
          }
          this.createNoOrderLinesLoading = false;
          this.createLoading = false;
        }, error => {
          this.createNoOrderLinesLoading = false;
          this.createLoading = false;
        });
      } else {
        let schedulePayload: _CRM_ORD_122 = {
          ...payload,
          date: 1,
          every: 1,
          schedule_repeat_type_id: 1,
          weekdays: [],
          nth_weekday: 1,
          instances_in_advance: 5,
          start_date: null,
          active: false,
          end_date: null,
        }
        this.orderService.createWorkOrderSchedule(schedulePayload).pipe(takeUntil(this.destroy$)).subscribe((workOrder) => {
          if (this.viewSettings.createOrderView) {
            this.router.navigate(['orders/details/', workOrder.order_id]);
            this.activeModal.close(workOrder);
          } else {
            this.orderService.fetchAndRefreshOrder(workOrder.order_id, 'createWorkOrderSchedule');
            if (!payload.skip_order_line_creation) {
              this.orderService.fetchAndRefreshOrderLines(workOrder.order_id, 'createWorkOrderSchedule');
            }
            let modalRef = this.modalService.open(WorkOrderDetailsComponent, {size: 'xl'});
            modalRef.componentInstance.viewSettings = {
              modalView: true,
              collapsedOrderLines: true,
              repeatingView: true,
              workOrderView: true,
            }
            modalRef.componentInstance.workOrderId = workOrder.work_order_id;
            this.activeModal.close();
          }
          this.createNoOrderLinesLoading = false;
          this.createLoading = false;
        }, error => {
          this.createNoOrderLinesLoading = false;
          this.createLoading = false;
        });
      }
    }
  }


  selectWorkOrderTemplate(template: WorkOrderTemplateResponse | null) {
    if (template) {
      this.selectedWorkOrderTemplateId = template.template_id;
      this.selectedWorkOrderTemplateContainsOrderLines = template.order_lines.length > 0;
    } else {
      this.selectedWorkOrderTemplateId = null;
      this.selectedWorkOrderTemplateContainsOrderLines = false;
    }
  }

  protected readonly favouriteStar = favouriteStar;
}
