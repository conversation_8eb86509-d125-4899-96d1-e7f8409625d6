<!-- Main content -->
<div class="container" style="max-width: 1180px;">
<div class="border-bottom" style="padding: 16px 16px 4px 16px;" [ngStyle]="{'padding-bottom.px': 16}">
  <h4 class="text-center" style="flex-grow: 1;">{{ this.viewSettings.createOrderView ? ('workOrderDetails.createOrderView.createOrder' | translate) : this.viewSettings.repeatingView ? ('workOrderDetails.modal.createSchedule.title' | translate) : ('workOrderDetails.modal.create.title' | translate)}}</h4>
</div>

<div class="modal-body p-1 p-xl-3">

  <!-- Desktop -->
  <div class="d-lg-block">

    <!--   Single / Repeating selection   -->
    <div *ngIf="viewSettings.createOrderView && defaultRepeating" class="d-flex align-items-center mb-4">
      <label class="me-2" style="tab-index: 0;">{{"workOrderDetails.createOrderView.orderType" | translate}}:</label>
      <app-button
        [customClass]="'no-right-border-radius no-shadow'"
        [buttonType]="this.viewSettings.repeatingView ? 'nude' : 'solid'"
        [translationKey]="'workOrderDetails.createOrderView.orderType.single'"
        [boldText]="false"
        (buttonClick)="viewSettings.repeatingView = false;"
      ></app-button>
      <app-button
        [customClass]="'no-left-border-radius no-shadow'"
        [buttonType]="!this.viewSettings.repeatingView ? 'nude' : 'solid'"
        [translationKey]="'workOrderDetails.createOrderView.orderType.repeating'"
        [boldText]="false"
        (buttonClick)="viewSettings.repeatingView = true;"
      ></app-button>
    </div>

    <!--   Template selection   -->
    <div *ngIf="workOrderTemplates.length === 0 && templatesLoaded" class="">
      <app-info-box
        [titleTranslationKey]="'workOrderDetails.createFromTemplate.noTemplates.title'"
        [contentTranslationKey]="'workOrderDetails.createFromTemplate.noTemplates.description'"
      ></app-info-box>
    </div>
    <div *ngIf="workOrderTemplates.length > 0" class="">
      <h5 class="mt-0">{{"workOrderDetails.createFromTemplate" | translate}}</h5>
          <div *ngFor="let tmp of showAllTemplates ? workOrderTemplates : topWorkOrderTemplates" class="order-details-card cursor-pointer py-1 d-flex justify-content-lg-between align-items-center mb-1" (click)="selectWorkOrderTemplate(selectedWorkOrderTemplateId === tmp.template_id ? null : tmp);" [ngClass]="{'selected-template': selectedWorkOrderTemplateId === tmp.template_id}">
            <div class="d-flex">
              <span>{{tmp.template_name}}</span>
              <div *ngIf="tmp.favourite" class="ms-1" [innerHTML]="favouriteStar(tmp.favourite, true)"></div>
            </div>
            <i *ngIf="selectedWorkOrderTemplateId === tmp.template_id" class="fa-regular fa-check fa-lg"></i>
          </div>
      <app-button
        *ngIf="workOrderTemplates.length > 6 && !showAllTemplates"
        [buttonType]="'link'"
        [translationKey]="'workOrderDetails.createFromTemplate.showAll'"
        (buttonClick)="showAllTemplates = true;"
      ></app-button>
    </div>


  </div>
</div>

<!-- Footer buttons -->
<div class="d-flex justify-content-between p-2 modal-footer">
  <div class="d-flex">
    <app-button
      [translationKey]="'common.close'"
      [themeStyle]="'secondary'"
      (buttonClick)="close()"
    ></app-button>
  </div>
  <div class="d-flex gap-2">
    <!--  Work order createwithout order lines -->
    <app-button
      *ngIf="selectedWorkOrderTemplateContainsOrderLines"
      [translationKey]="'workOrderDetails.createWithoutOrderLines'"
      [themeStyle]="'secondary'"
      [iconClass]="'fa-regular fa-info-circle ms-1'"
      [iconPlacement]="'right'"
      [iconNgbTooltipTranslationKey]="'workOrderDetails.createWithoutOrderLines.tooltip'"
      [loading]="createNoOrderLinesLoading"
      [disabled]="createLoading"
      (buttonClick)="createWorkOrder(true)"
    ></app-button>

    <!--  Work order create  -->
    <app-button
      [disabled]="createNoOrderLinesLoading"
      [translationKey]="'common.create'"
      [loading]="createLoading"
      (buttonClick)="createWorkOrder(false)"
    ></app-button>

  </div>
</div>

</div>
