import {AfterViewInit, Component, OnChanges, OnDestroy, OnInit, Optional, SimpleChanges, TemplateRef, ViewChild} from '@angular/core';
import {NgbActiveModal, NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {BehaviorSubject, forkJoin, pairwise, Subject} from "rxjs";
import {TranslateService} from "@ngx-translate/core";
import {FormControl} from "@angular/forms";
import {WorkOrderCompactResponse, WorkOrderStatusResponse} from "../../../@shared/models/order.interfaces";
import {formatFullDayAndDate, formatTimeHM, UtilsService, workOrderBadgeStatus} from "../../../@core/utils/utils.service";
import {OrderService} from "../../../@shared/services/order.service";
import {TablerinoColumn, TablerinoSettings} from "../../../@shared/components/tablerino/tablerino.component";
import {WorkOrderDetailsComponent, WorkOrderDetailsModal} from "../components/work-order-details/work-order-details.component";
import {_CRM_ORD_170} from "../../../@shared/models/input.interfaces";
import {takeUntil} from "rxjs/operators";
import {PaginationResponse} from "../../../@shared/models/response.interfaces";
import {PaginationContainer} from "../../../@shared/models/global.interfaces";
import {
  CustomActionButton,
  HeaderFilterComponent,
  HeaderFiltersContainer,
} from "../../../@shared/components/tablerino-header/tablerino-header.component";
import {EmployeeService} from "../../../@shared/services/employee.service";
import {ResourceService} from "../../../@shared/services/resource.service";
import {UserEntityRelationWithUserDataResponse} from "../../../@shared/models/user.interfaces";
import {ResourceResponse} from "../../../@shared/models/resources.interfaces";
import {PageHeaderComponent} from "../../../@shared/components/page-header/page-header.component";
import {StorageService} from "../../../@core/services/storage.service";
import {TablerinoCompleteComponent} from "../../../@shared/components/tablerino-complete/tablerino-complete.component";
import {ProfiledItemListComponent} from "../../../@shared/components/profiled-item-list/profiled-item-list.component";
import {VerifyPopupModal} from "../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {FinishWorkOrderModalComponent} from "./_modals/finish-work-order-modal/finish-work-order-modal.component";
import {StandardImports} from "../../../@shared/global_import";

export interface WorkOrderRow extends WorkOrderCompactResponse {
  selected: boolean;
}


@Component({
  selector: 'app-work-orders-overview',
  templateUrl: './work-orders-overview.component.html',
  styleUrls: ['./work-orders-overview.component.css'],
  standalone: true,
  imports: [StandardImports, PageHeaderComponent, TablerinoCompleteComponent, ProfiledItemListComponent]
})
export class WorkOrdersOverviewComponent implements OnInit, OnChanges, AfterViewInit, OnDestroy {
  columnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);
  workOrderRows: WorkOrderRow[];
  settings: TablerinoSettings = {
    checkboxes: true,
    clickableRows: true,
  }
  loading: boolean = false;
  dataResponse: PaginationResponse<WorkOrderCompactResponse[]>;
  paginationSubject: BehaviorSubject<PaginationContainer> = new BehaviorSubject<PaginationContainer>({page: 1, limit: 25, paginate: 1, totalPages: 0, totalItems: 0});
  selectedRowsSubject: BehaviorSubject<WorkOrderRow[]> = new BehaviorSubject<WorkOrderRow[]>([]);
  headerFiltersContainerSubject: BehaviorSubject<HeaderFiltersContainer> = new BehaviorSubject<HeaderFiltersContainer>({filters: [], init: true});
  cancelCurrentRequest$ = new Subject<void>();
  actionButtonsSubject: BehaviorSubject<CustomActionButton[]> = new BehaviorSubject<CustomActionButton[]>([]);
  destroy$ = new Subject<void>();

  @ViewChild('employees', { static: true }) employees!: TemplateRef<any>;

  constructor(public utilsService: UtilsService,
              @Optional() public activeModal: NgbActiveModal,
              private orderService: OrderService,
              private modalService: NgbModal,
              private translate: TranslateService,
              private employeeService: EmployeeService,
              private resourceService: ResourceService,
              private storageService: StorageService) {
  }

  ngOnInit() {
    this.initializeColumns();
    this.initializeHeaderFilters();
    this.initialiseActionButtons();
    this.getWorkOrders();
    this.paginationSubject.pipe(pairwise()).subscribe((paginationDetails) => {
      if (paginationDetails[0].page !== paginationDetails[1].page) {
        this.getWorkOrders();
      }
    });
    this.headerFiltersContainerSubject.subscribe((headerFilters) => {
      if (!headerFilters.init) {
        this.getWorkOrders();
      }
    });
    this.selectedRowsSubject.subscribe((selectedRows) => {
      if (selectedRows.length > 0) {
        this.actionButtonsSubject.value.forEach((button) => {
          if (button.functionName === 'finishWorkOrder' || button.functionName === 'deleteWorkOrder') {
            button.disabled = false;
          }
        });
      } else {
        this.actionButtonsSubject.value.forEach((button) => {
          if (button.functionName === 'finishWorkOrder' || button.functionName === 'deleteWorkOrder') {
            button.disabled = true;
          }
        });
      }
    });
  }

  ngAfterViewInit() {
  }

  ngOnChanges(simpleChanges: SimpleChanges) {
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getWorkOrders(searchTerm: string = '') {
    this.cancelCurrentRequest$.next();
    this.loading = true;

    let filters = this.headerFiltersContainerSubject.value.filters;
    let sortColumn = this.columnsSubject.value.find(col => col.sortedAsc || col.sortedDesc) || null;
    let sortKey: string = sortColumn?.name || 'created_at';
    let sortDirection: 'asc' | 'desc' = sortColumn ? (sortColumn.sortedAsc ? 'asc' : 'desc') : 'desc';

    let params: _CRM_ORD_170 = {
      paginate: 1,
      page: this.paginationSubject.value.page,
      limit: this.paginationSubject.value.limit,
      work_order_status_ids: filters.find(hf => hf.parameterName === 'work_order_status_ids')!.dropDownOptions!.filter(ddo => ddo.active).map(ddo => Number(ddo.value!)),
      received_work_orders_only: filters.find(hf => hf.parameterName === 'received')!.active,
      sent_work_orders_only: filters.find(hf => hf.parameterName === 'sent')!.active,
      user_ids: filters.find(hf => hf.parameterName === 'user_ids')!.dropDownOptions!.filter(ddo => ddo.active).map(ddo => ddo.value) as string[],
      resource_ids: filters.find(hf => hf.parameterName === 'resource_ids')!.dropDownOptions!.filter(ddo => ddo.active).map(ddo => Number(ddo.value!)),
      order_by: sortKey,
      order_direction: sortDirection,
      execution_date_from: filters.find(hf => hf.parameterName === 'execution_at')!.dateRangeFromControl!.value,
      execution_date_to: filters.find(hf => hf.parameterName === 'execution_at')!.dateRangeToControl!.value,
      search_string: searchTerm,
      search_string_columns: ['work_order_title', 'work_order_number'],
      unplanned_only: filters.find(hf => hf.parameterName === 'unplanned')!.active,
    }

    this.orderService.getCompanyWorkOrders(params).pipe(takeUntil(this.cancelCurrentRequest$)).subscribe((res) => {
      this.dataResponse = res;
      this.paginationSubject.next({
        ...this.paginationSubject.value,
        totalItems: res.total_items,
        totalPages: res.total_pages
      });
      this.workOrderRows = res.data.map((wo) => {
        return {
          ...wo,
          selected: false,
        }
      });
      this.loading = false;
    }, error => {
      this.loading = false
    });
  }

  initializeColumns() {
    this.columnsSubject.next([
      {
        name: 'id',
        labelKey: 'ID',
        formatter: (wo: WorkOrderCompactResponse) => '#' + wo.work_order_number + this.formatWorkOrderIcons(wo),
        sort: false,
        visible: true,
      },
      {
        name: 'work_order_title',
        labelKey: 'workOrder.list.column.title',
        formatter: (wo: WorkOrderCompactResponse) => wo.work_order_title,
        sort: true,
        visible: true,
      },
      {
        name: 'execution_at',
        labelKey: 'workOrder.list.filter.execution',
        formatter: (wo: WorkOrderCompactResponse) => this.formatExecutionDate(wo),
        sort: true,
        visible: true,
      },
      {
        name: 'executionAtTime',
        labelKey: 'workOrder.list.column.time',
        formatter: (wo: WorkOrderCompactResponse) => formatTimeHM(wo.execution_at),
        sort: false,
        visible: true,
      },
      {
        name: 'work_order_status_id',
        labelKey: 'workOrder.list.column.status',
        formatter: (wo: WorkOrderCompactResponse) => workOrderBadgeStatus(wo),
        sort: true,
        visible: true,
      },
      {
        name: 'customerName',
        labelKey: 'workOrder.list.column.customer',
        formatter: (wo: WorkOrderCompactResponse) => wo.payment_recipient_name || '',
        sort: false,
        visible: true,
      },
      {
        name: 'address',
        labelKey: 'workOrder.list.column.address',
        formatter: (wo: WorkOrderCompactResponse) => this.formatAddress(wo),
        sort: false,
        visible: true,
      },
      {
        name: 'assignees',
        labelKey: 'workOrder.list.column.assignees',
        formatter: (wo: WorkOrderCompactResponse) => wo,
        sort: false,
        visible: true,
        ngTemplate: this.employees,
      },
      {
        name: 'subcontractor',
        labelKey: 'workOrder.list.column.subcontractor',
        formatter: (wo: WorkOrderCompactResponse) => this.formatSubContractor(wo),
        sort: false,
        visible: true,
      },
    ]);
  }

  initializeHeaderFilters() {
    let headerFilters: HeaderFilterComponent[] = [
      {
        parameterName: 'execution_at',
        translationKey: 'workOrder.list.filter.execution',
        active: false,
        dateRange: true,
        dateRangeFromControl: new FormControl(),
        dateRangeToControl: new FormControl(),
        dateRangeFromParamKey: 'execution_date_from',
        dateRangeToParamKey: 'execution_date_to',
        excludes: ['unplanned'],
      },
      {
        parameterName: 'unplanned',
        translationKey: 'workOrder.list.filter.unplanned',
        active: false,
        excludes: ['execution_at'],
      },
      {
        parameterName: 'work_order_status_ids',
        translationKey: 'workOrder.list.filter.status',
        multiSelect: true,
        dropDownOptions: [],
        active: false,
      },
      {
        parameterName: 'user_ids',
        translationKey: 'workOrder.list.filter.employees',
        multiSelect: true,
        dropDownOptions: [],
        active: false,
      },
      {
        parameterName: 'resource_ids',
        translationKey: 'workOrder.list.filter.resources',
        multiSelect: true,
        dropDownOptions: [],
        active: false,
      },
      {
        parameterName: 'received',
        translationKey: 'workOrder.list.filter.received',
        active: false,
        excludes: ['sent'],

      },
      {
        parameterName: 'sent',
        translationKey: 'workOrder.list.filter.sent',
        active: false,
        excludes: ['received'],
      },
    ];

    const requests = [
      this.orderService.getWorkOrderStatuses(),
      this.employeeService.getEmployeesInCompany({}),
      this.resourceService.getResources({}),
    ];

    forkJoin(requests).subscribe({
      next: (res) => {
        let workOrderStatuses = res[0] as WorkOrderStatusResponse[];
        let employees = res[1] as UserEntityRelationWithUserDataResponse[];
        let resources = res[2] as PaginationResponse<ResourceResponse[]>;

        // Map work order statuses
        headerFilters.find(hf => hf.parameterName === 'work_order_status_ids')!.dropDownOptions = workOrderStatuses.sort
        ((a, b) => a.work_order_status_id - b.work_order_status_id).map
        ((status: WorkOrderStatusResponse) => {
          return {
            value: status.work_order_status_id,
            translationKey: status.work_order_status_name,
            active: false,
          }
        });

        // Map employees
        headerFilters.find(hf => hf.parameterName === 'user_ids')!.dropDownOptions = employees.map
        ((employee: UserEntityRelationWithUserDataResponse) => {
          return {
            value: employee.user_id,
            translationKey: employee.full_name,
            active: false,
          }
        });

        // Map resources
        headerFilters.find(hf => hf.parameterName === 'resource_ids')!.dropDownOptions = resources.data.map
        ((resource: ResourceResponse) => {
          return {
            value: resource.resource_id,
            translationKey: resource.resource_name,
            active: false,
          }
        });

        this.headerFiltersContainerSubject.next({filters: headerFilters, init: true});
      }
    });

    this.headerFiltersContainerSubject.next({filters: headerFilters, init: true});
  }

  rowClicked(row: WorkOrderRow) {
    const modalRef = this.modalService.open(WorkOrderDetailsComponent, { size: 'xl' });

    // Pass a copy of the selected row to the modal
    modalRef.componentInstance.workOrderId = row.work_order_id;
    modalRef.componentInstance.viewSettings = {
      ...WorkOrderDetailsModal,
      workOrderStandaloneView: true
    };

    modalRef.dismissed.subscribe(() => {
      this.updateSingleWorkOrder(row.work_order_id);

    });
    modalRef.closed.subscribe((res: string) => {
      if (res == 'deleted') {
        this.getWorkOrders();
      } else {
        this.updateSingleWorkOrder(row.work_order_id);
      }
    });
  }


  updateSingleWorkOrder(workOrderId: number) {
    let params: _CRM_ORD_170 = {
        work_order_id: workOrderId,
      }
      this.orderService.getCompanyWorkOrders(params).subscribe((res) => {
        if (res.data.length > 0) {
          let wo = res.data[0];
          let index = this.workOrderRows.findIndex(wor => wor.work_order_id === workOrderId);
          this.workOrderRows[index] = {
            ...wo,
            selected: this.workOrderRows[index].selected,
          }
        }
      });
  }

  formatAssignees(wo: WorkOrderCompactResponse) {
    return wo.users.map(u => u.first_name).join(', ');
  }

  formatAddress(wo: WorkOrderCompactResponse) {
    if (wo.display_addresses.length === 0) {
      return '';
    } else if (wo.display_addresses.length === 1) {
      return wo.display_addresses[0];
    } else {
      return wo.display_addresses[0] + ' + ' + (wo.display_addresses.length - 1);
    }
  }

  formatSubContractor(wo: WorkOrderCompactResponse) {
    if (wo.contractor_relation) {
      return `<i class="fa-regular fa-user-tie-hair fa-lg me-1"></i>` + this.translate.instant('workOrder.list.receivedFrom') + ' ' + wo.company_name;
    }
    else if (wo.subcontractors.length === 0) {
      return '';
    }
    else {
      let sub = wo.subcontractors[0];
      let icon = '';
      if (sub.accepted_at) {
        icon = `<i class="fa-regular fa-thumbs-up text-success fa-lg me-1"></i>`;
      } else if (sub.declined_at) {
        icon = `<i class="fa-regular fa-thumbs-down text-danger fa-lg me-1"></i>`;
      } else {
        icon = `<i class="fa-regular fa-hourglass-clock text-muted fa-lg me-1"></i>`;
      }
      return icon + sub.affiliate_name;
    }
  }

  formatExecutionDate(wo: WorkOrderCompactResponse) {
    let includeYear = wo.execution_at ? (wo.execution_at.getFullYear() !== new Date().getFullYear()) : false;
    let text = formatFullDayAndDate(wo.execution_at, includeYear);
    if (wo.execution_at && wo.execution_to && wo.execution_at.getDate() != wo.execution_to.getDate()) {
      text += `<i class="fa-regular fa-calendar-exclamation ms-1"></i>`
    }
    return text;
  }

  formatWorkOrderIcons(wo: WorkOrderCompactResponse) {
    let icons = ''

    if (wo.parent_work_order_id) {
      icons += '<i class="fa fa-repeat ms-1"></i>';
    }

    if (wo.contractor_relation) {
      icons += '<i class="fa-regular fa-user-tie-hair fa-lg ms-1"></i>';
    }
    return icons
  }

  deleteWorkOrders() {
    this.loading = true;
    const workOrderIds = this.selectedRowsSubject.value.map(wo => wo.work_order_id);
    const requests = workOrderIds.map(id => this.orderService.deleteWorkOrder({work_order_id: id}));
    forkJoin(requests).subscribe({
      next: () => {
        this.workOrderRows = this.workOrderRows.filter(wo => !workOrderIds.includes(wo.work_order_id));
        this.loading = false;
        this.getWorkOrders();
      },
      error: (error) => {
        this.loading = false;
        this.getWorkOrders();
      }
    });
  }

  initialiseActionButtons() {
    this.actionButtonsSubject.next([
      {
        translationKey: 'workOrder.list.finish',
        iconClass: 'fa-regular fa-inbox-in me-1',
        functionName: 'finishWorkOrder',
        placeInDropdown: true,
        disabled: true
      },
      {
        translationKey: 'workOrder.list.delete',
        iconClass: 'fa-regular fa-delete-right me-1',
        functionName: 'deleteWorkOrder',
        placeInDropdown: true,
        disabled: true
      }
    ]);
  }

  // finishWorkOrder() {
  //   let modalRef = this.modalService.open(VerifyPopupModal)
  //   modalRef.result.then((result) => {
  //     if (result) {
  //       const requests = this.selectedRowsSubject.value.map((row) => {
  //         return this.orderService.finishWorkOrder(row.work_order_id);
  //       });
  //       this.loading = true;
  //       forkJoin(requests).subscribe(() => {
  //         this.getWorkOrders();
  //       }, error => {
  //         console.log(error);
  //         this.getWorkOrders();
  //       });
  //     }
  //   });
  // }


  finishWorkOrder() {
    // Open the new finish work order modal
    const modalRef = this.modalService.open(FinishWorkOrderModalComponent, { size: 'lg' });
    // Pass the selected rows (work orders) into the modal
    modalRef.componentInstance.selectedRows = this.selectedRowsSubject.value;

    modalRef.result.then((result) => {
      if (result === true) {
        // If the modal returned success (e.g., user confirmed and quantities are valid),
        // finish each work order by calling the service.
        const requests = this.selectedRowsSubject.value.map((row) => {
          return this.orderService.finishWorkOrder(row.work_order_id);
        });
        this.loading = true;
        forkJoin(requests).subscribe(() => {
          this.getWorkOrders();
          this.loading = false;
        }, error => {
          console.error(error);
          this.getWorkOrders();
          this.loading = false;
        });
      }
    });
  }



  //
  //
  // async finishWorkOrder() {
  //   if (!this.order) {
  //     this.order = await firstValueFrom(this.orderService.getOrderById(this.workOrder?.order_id!));
  //   }
  //
  //   if (this.order.order_status_id < 2) {
  //     let modalRef = this.modalService.open(VerifyPopupModal, {centered: true});
  //     modalRef.componentInstance.showBody = true;
  //     modalRef.componentInstance.titleTranslationKey = 'workOrderDetails.finishBeforeConfirmedModal.title';
  //     modalRef.componentInstance.bodyBoldTranslationKey = 'workOrderDetails.finishBeforeConfirmedModal.bodyBold';
  //     modalRef.componentInstance.bodyRegularTranslationKey = 'workOrderDetails.finishBeforeConfirmedModal.bodyRegular';
  //     try {
  //       let result: boolean = await modalRef.result;
  //       if (!result) return;
  //     } catch (e) {
  //       return;
  //     }
  //   }
  //
  //   if (this.workOrder?.order_lines.some(line => line.track_time)) {
  //     let modalRef = this.modalService.open(QuantityProposalModal, {})
  //     modalRef.componentInstance.workOrder = this.workOrder;
  //     let answer = await modalRef.result;
  //   }
  //
  //   this.loading = true;
  //   this.orderService.finishWorkOrder(this.workOrder!.work_order_id).pipe(takeUntil(this.destroy$)).subscribe((workOrder) => {
  //     this.loading = false;
  //     this.workOrder = workOrder;
  //     this.orderService.workOrderListUpdated$.next();
  //     this.initWorkOrder();
  //     this.orderService.updateWorkOrderInOrder(workOrder);
  //     this.orderService.fetchAndUpdateOrder(this.workOrder?.order_id!, 'finishWorkOrder');
  //   }, error => {
  //     this.loading = false;
  //   });
  // }


  deleteWorkOrder() {
    let modalRef = this.modalService.open(VerifyPopupModal)
    modalRef.result.then((result) => {
      if (result) {
        const requests = this.selectedRowsSubject.value.map((row) => {
          return this.orderService.deleteWorkOrder({ work_order_id: row.work_order_id });
        });
        this.loading = true;
        forkJoin(requests).subscribe(() => {
          this.getWorkOrders();
        }, error => {
          console.log(error);
          this.getWorkOrders();
        });
      }
    });
  }

  actionButtonClicked(functionName: string) {
    if (functionName === 'finishWorkOrder') {
      this.finishWorkOrder();
    }
    if (functionName === 'deleteWorkOrder') {
      this.deleteWorkOrder();
    }
  }

}
