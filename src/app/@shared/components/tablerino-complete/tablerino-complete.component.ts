import {Component, EventEmitter, Input, Output} from '@angular/core';
import {TablerinoColumn, TablerinoComponent, TablerinoSettings} from "../tablerino/tablerino.component";
import {BehaviorSubject, Subject} from "rxjs";


import {TableFooterComponent} from "../table-footer/table-footer.component";
import {CustomActionButton, HeaderFilterComponent, HeaderFiltersContainer, TablerinoHeaderComponent} from "../tablerino-header/tablerino-header.component";
import {PaginationContainer} from "../../models/global.interfaces";
import {PaginationResponse} from "../../models/response.interfaces";
import {StandardImports} from "../../global_import";


@Component({
  selector: 'app-tablerino-complete',
  templateUrl: './tablerino-complete.component.html',
  styleUrls: ['./tablerino-complete.component.css'],
  standalone: true,
  imports: [StandardImports, TablerinoHeaderComponent, TablerinoComponent, TableFooterComponent],
})
export class TablerinoCompleteComponent {
  @Input() showHeader: boolean = true;
  @Input() showFooter: boolean = true;
  @Input() columnsSubject: BehaviorSubject<TablerinoColumn[]>;

  // Tablerino inputs/outputs
  @Input() tableName: string;
  @Input() tableData: any[] = [];
  @Input() settings: TablerinoSettings = {};
  @Input() loading: boolean = false;
  @Input() deleteButton: boolean = false;
  @Input() noResultsTranslationKey: string = 'common.noResults';
  @Input() selectedRowsSubject: BehaviorSubject<any>;
  @Input() disableDrag: boolean = false;
  @Input() disableSort: boolean = false;

  @Output() rowClickedEmitter: EventEmitter<any> = new EventEmitter();
  @Output() selectedRowsChangedEmitter: EventEmitter<any> = new EventEmitter();
  @Output() favouriteToggledEmitter: EventEmitter<any> = new EventEmitter();
  @Output() toggleEmitter: EventEmitter<any> = new EventEmitter();

  // Tablerino header inputs/outputs
  @Input() headerFiltersContainerSubject: Subject<HeaderFiltersContainer> = new Subject<HeaderFiltersContainer>();
  @Input() actionButtonsSubject: BehaviorSubject<CustomActionButton[]>;
  @Input() showAllFilter: boolean = false;
  @Input() fetchFiltersFromUrl: boolean = false;
  @Input() showQuickSearch: boolean = false;
  @Input() quickSearchInProgress: boolean = false;
  @Input() isLoading: boolean = false;
  @Input() showColumnDropdown: boolean = true;
  @Input() resetFiltersOnQuickSearch: boolean = false;
  @Input() showSavedViews: boolean = false;
  @Input() noRounding: boolean = false;

  @Output() actionButtonEmitter = new EventEmitter<string>();
  @Output() quickSearchEmitter = new EventEmitter<string>();
  @Output() sortEmitter: EventEmitter<TablerinoColumn> = new EventEmitter();
  @Output() filterClickedEmitter: EventEmitter<HeaderFilterComponent> = new EventEmitter<HeaderFilterComponent>();



  // Tablerino footer inputs/outputs
  @Input() paginationSubject: BehaviorSubject<PaginationContainer>
  @Input() dataResponse: PaginationResponse<any>;

}
