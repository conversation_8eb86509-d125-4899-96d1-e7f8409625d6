import {Component, Input, OnInit} from '@angular/core';
import {InternalUserResponse} from "../../../../models/user.interfaces";
import {formatFullDayAndDate, formatTimeHM, UtilsService} from "../../../../../@core/utils/utils.service";
import {ResourceResponse} from "../../../../models/resources.interfaces";
import {formatDate, NgIf, NgOptimizedImage} from "@angular/common";
import {EmployeeAvailabilityItemResponse} from "../../../../models/employee.interfaces";
import {StandardImports} from "../../../../global_import";

@Component({
    selector: 'app-notification-box',
    templateUrl: './employee-availability-item.component.html',
    styleUrls: ['./employee-availability-item.component.css'],
    standalone: true,
  imports: [StandardImports, NgOptimizedImage]
})
export class EmployeeAvailabilityItemComponent implements OnInit {
  @Input() userData?: InternalUserResponse
  @Input() resourceData?: ResourceResponse
  @Input() item: EmployeeAvailabilityItemResponse

  sameDate: boolean = false

  initialsKeys: string[] = ['full_name']

  initialsBackgroundColors = [
    '#7ec0ee', // Sky Blue
    '#f7a8a8', // Soft Pink
    '#98ff98', // Mint Green
    '#e6e6fa', // Lavender
    '#ffdab9', // Peach
    '#f08080', // Light Coral
    '#6495ed', // Cornflower Blue
    '#d8bfd8', // Thistle (Light Purple)
    '#7fffd4', // Aquamarine
    '#fffacd'  // Lemon Chiffon
  ];
  previousBackgroundColor: string = '';

  constructor(public utilsService: UtilsService) {
  }

  ngOnInit() {
    if (this.item && this.item.date_from && this.item.date_to) {
      const dateFrom = new Date(this.item.date_from);
      const dateTo = new Date(this.item.date_to);
      this.sameDate = dateFrom.toDateString() === dateTo.toDateString();
    }
  }


  getInitials(item: any): string {
    let initials = ''
    if (item) {
      if (this.initialsKeys.length > 1) {
        let firstName = item[this.initialsKeys[0]];
        let lastName = item[this.initialsKeys[1]];
        firstName = firstName.trim();
        lastName = lastName.trim();
        initials = `${firstName[0]}${lastName[0]}`.toUpperCase();
      } else if (this.initialsKeys.length == 1) {
        let displayName = item[this.initialsKeys[0]];
        displayName = displayName.trim();
        displayName = displayName.replace('  ', ' ')
        if (displayName.includes(' ')) {
          let [firstName, lastName] = displayName.split(' ');
          initials = `${firstName[0]}${lastName[0]}`.toUpperCase();
        } else {
          initials = displayName[0].toUpperCase();
          if (displayName.length > 1) {
            initials += displayName[1].toUpperCase();
          }
        }
      }
    }
    return initials
  }

  protected readonly formatDate = formatDate;
  protected readonly formatTimeHM = formatTimeHM;
  protected readonly formatFullDayAndDate = formatFullDayAndDate;
}
