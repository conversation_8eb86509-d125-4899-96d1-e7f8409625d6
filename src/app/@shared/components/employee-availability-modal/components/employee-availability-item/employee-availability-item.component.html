<div class="rounded-div pb-2 pt-2">
  <div class="d-flex px-1 justify-content-between">

    <div class="">
      <div class="d-flex align-items-center mb-1">
        <i class="fa-regular fa-circle-exclamation fa-lg" style="color: #ff0000;"></i>
        <div class="ms-2" style="font-weight: bold; font-size: larger; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{item.title}}</div>
      </div>

      <div *ngIf="sameDate" class="d-flex align-items-center gap-1">
        <div>{{formatFullDayAndDate(item.date_from, false)}} {{formatTimeHM(item.date_from)}} - {{formatTimeHM(item.date_to)}}</div>
      </div>

      <div *ngIf="!sameDate" class="d-flex align-items-center gap-1">
        <div class="">{{formatFullDayAndDate(item.date_from, false)}}</div>
        <div>{{"common.to" | translate}}</div>
        <div class="">{{formatFullDayAndDate(item.date_to, false).toLowerCase()}}</div>
      </div>

    </div>

    <div class="d-flex align-items-center justify-content-end" *ngIf="userData">
      <div class="d-flex align-items-center">
        <img *ngIf="userData.profile_image_url" draggable="false" loading="lazy" class="item-avatar me-2" alt=""
             ngSrc="{{userData.profile_image_url}}"
             width="30"
             height="30">
        <span *ngIf="!userData.profile_image_url" class="initials item-avatar me-2" [style.background-color]="'light-gray'">{{ getInitials(userData) }}</span>
        <div style="font-weight: bold; font-size: larger;">{{userData.full_name}}</div>
      </div>
    </div>

    <div class="d-flex align-items-center justify-content-end" *ngIf="resourceData">
      <div class="d-flex align-items-center">
        <img *ngIf="resourceData.resource_image_url" draggable="false" loading="lazy" class="item-avatar me-2" alt=""
             ngSrc="{{resourceData.resource_image_url}}"
             width="30"
             height="30">
        <div style="font-weight: bold; font-size: larger;">{{resourceData.resource_name}}</div>
      </div>
    </div>

  </div>
</div>
