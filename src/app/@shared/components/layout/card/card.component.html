<div *ngIf="!hideComponent" [ngClass]="['profile-card', margin, colHeight, ghosted ? 'ghosted' : 'card', hideOverflow ? 'overflow-hidden' : '']" [ngStyle]="{'background-color': backgroundColor, 'max-width': maxWidth}" (click)="toggleGhost()">
  <div class="profile-header-content">
    <div class="profile-header justify-content-between d-flex align-items-center" *ngIf="header || ghosted">
      <div class="d-flex align-items-center" [ngClass]="{'justify-content-between': ghosted}">
        <label *ngIf="!additionalTitleText" class="profile-title">{{ labelKey | translate }}</label>
        <app-spinner *ngIf="loading && !ghosted" class="ms-1" [big]="true"></app-spinner>
        <div *ngIf="additionalTitleElement">
          <ng-content select="[additionalTitleElement]"></ng-content>
        </div>
        <label *ngIf="additionalTitleText" class="profile-title">{{ labelKey | translate }} {{additionalTitleText | translate}}</label>
      </div>
      <div *ngIf="ghosted" class="d-flex align-items-center">
        <i class="fa-regular fa-plus fa-xl" (click)="ghosted = false" style="font-weight: 400; color: #a0a0a0;"></i>
      </div>
      <div *ngIf="buttonHeader && !ghosted">
        <ng-content select="[buttonHeader]"></ng-content>
      </div>
    </div>
  </div>
  <div class="profile-body" [ngStyle]="{ 'padding': padding }" *ngIf="!ghosted">
    <ng-content select="[cardcontent]"></ng-content>
  </div>
</div>
