import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TranslateModule } from "@ngx-translate/core";
import {StandardImports} from "../../../global_import";
import {SpinnerComponent} from "../../spinner/spinner.component";


@Component({
  selector: 'app-card',
  templateUrl: './card.component.html',
  styleUrls: ['./card.component.css'],
  standalone: true,
  imports: [StandardImports, SpinnerComponent]
})
export class CardComponent {
  @Input() hideComponent: boolean = false;
  @Input() maxWidth: string = '';
  @Input() labelKey: string = '';
  @Input() additionalTitleText: string = '';
  @Input() additionalTitleElement: boolean = false;
  @Input() header: boolean = true;
  @Input() ghosted: boolean = false;
  @Input() hideOverflow: boolean = false;
  @Input() loading: boolean = false;

  @Input() buttonHeader: boolean = false;
  @Input() buttonText: string = '';
  @Input() colHeight: string = '';
  @Input() buttonType: 'solid' | 'nude' | 'link' = "solid";
  @Input() backgroundColor: string = '#FFFFFF';
  @Input() margin: string = '';
  @Input() padding: string = '1.5rem 1.5rem 1.5rem 1.5rem';
  @Input() xsmall: boolean = false;

  @Output() ghostChangedEmitter: EventEmitter<boolean> = new EventEmitter<boolean>();

  ngOnInit() {
  }

  toggleGhost() {
    if (!this.ghosted) return;
    this.ghosted = false;
    this.ghostChangedEmitter.emit(this.ghosted);
  }
}
