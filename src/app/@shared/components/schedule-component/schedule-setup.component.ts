import {Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Optional, Output, SimpleChanges} from '@angular/core';
import {formatFullDayAndDate, UtilsService} from "../../../@core/utils/utils.service";
import {NgbActiveModal} from "@ng-bootstrap/ng-bootstrap";
import {FormControl, Validators} from "@angular/forms";
import {DetailsViewSettings, ScheduleRepeatTypeResponse} from "../../models/order.interfaces";
import {_CRM_ORD_85, ScheduleInput} from "../../models/input.interfaces";
import {OrderService} from "../../services/order.service";
import {BehaviorSubject, Subject} from "rxjs";
import {TranslateService} from "@ngx-translate/core";
import {SelectoriniComponent} from "../selectorini/selectorini.component";
import {PaymentService} from "../../services/payment.service";

import {DatepickerinoComponent} from "../datepickerino/datepickerino.component";
import {filter, take, takeUntil} from "rxjs/operators";
import {StandardImports} from "../../global_import";

@Component({
  selector: 'app-schedule-setup',
  templateUrl: './schedule-setup.component.html',
  styleUrls: ['./schedule-setup.component.css'],
  standalone: true,
  imports: [StandardImports, DatepickerinoComponent, SelectoriniComponent]
})
export class ScheduleSetupComponent implements OnInit, OnChanges, OnDestroy {

  @Input() scheduleInputSource: BehaviorSubject<ScheduleInput>;
  @Input() paymentView: boolean = false;
  @Input() viewSettings: DetailsViewSettings = {};
  @Input() disabled: boolean = false;

  @Output() scheduleDescriptionChange: EventEmitter<string> = new EventEmitter<string>();
  @Output() firstInstanceChange: EventEmitter<Date | null> = new EventEmitter<Date | null>();

  scheduleInput: ScheduleInput;
  scheduleUseDate: boolean = true;
  scheduleDate: number | null;
  scheduleEveryControl: FormControl = new FormControl(1, [Validators.required, Validators.min(1)]);
  scheduleInstancesInAdvanceControl: FormControl = new FormControl(5, [Validators.required, Validators.min(1), Validators.max(50)]);
  startDate: Date | null;
  endDate: Date | null;

  scheduleMonthDates: {date: number, display: string}[] = [];
  selectedScheduleMonthDate: {date: number, display: string} | null = null;

  selectedNthWeekdayIndex: number = 0;

  scheduleRepeatTypes: ScheduleRepeatTypeResponse[] = [];
  selectedScheduleRepeatType: ScheduleRepeatTypeResponse;
  scheduleDescription: string;
  scheduleExecutionDates: Date[] = [];
  noWeekdaysSelected: boolean = false;

  nthWeekday: number = 1;
  weekdays: number[];

  startAfterEnabled: boolean = false;
  stopAfterEnabled: boolean = false;

  startDateHovered = false;
  endDateHovered = false;

  repeatSuffixes = ['orderSchedules.day', 'orderSchedules.week', 'orderSchedules.month'];

  days: { name: string, value: number, abbreviation: string, active: boolean }[] = [];

  nthWeeks: { display: string, value: number }[] = [];

  stockWeekdays: { display: string, value: number}[] = [];

  schedulePreviewCancel$: Subject<void> = new Subject<void>();

  uniqueIdPrefix = 'schedule-' + Math.random().toString(36).substring(2, 8);

  destroy$ = new Subject<void>();

  constructor(private orderService: OrderService, private translateService: TranslateService, public utilsService: UtilsService, @Optional() public activeModal: NgbActiveModal, private paymentService: PaymentService) {
    for (let i = 0; i <= 6; i++) {
      this.days.push(
        {
          name: this.translateService.instant("WEEKDAYS-METRIC." + i.toString()),
          value: i,
          abbreviation: this.translateService.instant("WEEKDAYS-SHORT-METRIC." + i.toString())[0],
          active: false
        }
      );
    }

    for (let i = 1; i <= 5; i++) {
      this.nthWeeks.push({display: this.translateService.instant("orderSchedules.nthWeekItem." + i.toString()), value: i});
    }

    for (let i = 0; i <= 6; i++) {
      this.stockWeekdays.push(
        {
          display: this.translateService.instant("WEEKDAYS-METRIC." + i.toString()) + this.translateService.instant("orderSchedules.nthWeekItem.suffix"),
          value: i
        }
      );
    }
  }


  ngOnInit() {
    this.scheduleMonthDates = Array.from({length: 28}, (_, i) => ({date: i + 1, display: (i + 1).toString()}));
    this.scheduleMonthDates.push({date: -1, display: this.translateService.instant('orderSchedules.lastDay')});

    if (this.scheduleInputSource) {
      this.scheduleInputSource.pipe(filter(data => Object.keys(data).length > 0), takeUntil(this.destroy$)).subscribe((data) => {
        if (this.scheduleInput === data) return;
        this.scheduleInput = data;
        this.initValues();
      });
    } else {
      this.initValues();
    }

    this.orderService.getScheduleRepeatTypes().pipe(takeUntil(this.destroy$)).subscribe((res) => {
      this.scheduleRepeatTypes = res;
      if (this.paymentView) {
        this.scheduleRepeatTypes = this.scheduleRepeatTypes.filter((type) => type.schedule_repeat_type_id !== 0);
      }
      if (!this.selectedScheduleRepeatType && this.scheduleInput?.schedule_repeat_type_id !== null && this.scheduleInput?.schedule_repeat_type_id !== undefined) {
        this.selectedScheduleRepeatType = res.find((type) => type.schedule_repeat_type_id === this.scheduleInput.schedule_repeat_type_id)!;
        this.updateScheduleSubject();
        this.getSchedulePreview();
      }
    });

    this.scheduleEveryControl.valueChanges.subscribe(() => {
      this.updateScheduleSubject('scheduleSetup');
      this.getSchedulePreview();
    });

    if (this.disabled) {
      this.scheduleEveryControl.disable();
      this.scheduleInstancesInAdvanceControl.disable();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['disabled']) {
      if (this.disabled) {
        this.scheduleEveryControl.disable();
        this.scheduleInstancesInAdvanceControl.disable();
      } else {
        this.scheduleEveryControl.enable();
        this.scheduleInstancesInAdvanceControl.enable();
      }
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.schedulePreviewCancel$.next();
    this.schedulePreviewCancel$.complete();
  }

  initValues() {
    this.scheduleUseDate = this.scheduleInput.date !== null;
    this.startDate = this.scheduleInput.start_date || null;
    this.endDate = this.scheduleInput.end_date || null;
    this.scheduleEveryControl.setValue(this.scheduleInput.every, {emitEvent: false});
    this.scheduleDate = this.scheduleInput.date;

    this.scheduleInstancesInAdvanceControl.setValue(this.scheduleInput.instances_in_advance, {emitEvent: false});
    this.weekdays = this.scheduleInput.weekdays || [];
    this.nthWeekday = this.scheduleInput.nth_weekday!;
    this.selectedNthWeekdayIndex = this.nthWeeks.findIndex((nth) => nth.value === this.nthWeekday);
    this.selectedScheduleRepeatType = {schedule_repeat_type_id: this.scheduleInput.schedule_repeat_type_id, schedule_repeat_type_name: this.scheduleInput.schedule_repeat_type_name || 'N/A'};
    this.days.forEach((day) => {
      if (this.weekdays.includes(day.value)) {
        day.active = true;
      }
    });

    if (this.scheduleInput.date) {
      this.selectedScheduleMonthDate = this.scheduleMonthDates.find((date) => date.date === this.scheduleInput.date) || null;
    }

    if (this.scheduleInput.start_date) {
      this.startAfterEnabled = true;
    }

    if (this.scheduleInput.end_date) {
      this.stopAfterEnabled = true;
    }

    if (this.scheduleInput.initDescription) {
      this.scheduleDescription = this.scheduleInput.initDescription;
    }
    this.getSchedulePreview();
  }

  getSchedulePreview() {
    this.schedulePreviewCancel$.next();
    let missingData = false;

    if (!this.selectedScheduleRepeatType) {
      return;
    }

    // If missing weekdays for weekly schedule
    if (this.selectedScheduleRepeatType.schedule_repeat_type_id === 1 && this.weekdays.length === 0) {
      missingData = true;
    }

    // If missing date for monthly schedule with use-date
    if (this.selectedScheduleRepeatType.schedule_repeat_type_id === 2 && this.scheduleUseDate && !this.scheduleDate) {
      missingData = true;
    }

    // If missing nth_weekday for monthly schedule without use-date
    if (this.selectedScheduleRepeatType.schedule_repeat_type_id === 2 && !this.scheduleUseDate && !this.nthWeekday) {
      this.noWeekdaysSelected = true;
      missingData = true;
    }

    if (!this.scheduleEveryControl.valid && !this.disabled) {
      this.noWeekdaysSelected = true;
      this.scheduleDescription = this.translateService.instant('orderSchedules.missingEvery');
      return;
    }

    if (missingData) {
      this.noWeekdaysSelected = true;
      this.scheduleDescription = this.translateService.instant('orderSchedules.noWeekdaysSelected');
      return;
    }

    let end_time = this.scheduleInput.executionAt || new Date();
    end_time.setHours(end_time.getHours() + 1);
    let scheduleDate = this.scheduleUseDate ? this.scheduleDate : null;

    let payload: _CRM_ORD_85 = {
      every: parseInt(this.scheduleEveryControl.value),
      nth_weekday: this.nthWeekday || null,
      instances_in_advance: 5,
      schedule_repeat_type_id: this.selectedScheduleRepeatType.schedule_repeat_type_id,
      weekdays: this.weekdays,
      start_time: this.scheduleInput.executionAt || new Date(),
      end_time: end_time,
      start_date: this.startDate,
      end_date: this.endDate,
      date: scheduleDate
    }
    this.orderService.getScheduleExecutionPreview(payload).pipe(takeUntil(this.schedulePreviewCancel$)).subscribe((res) => {
      this.noWeekdaysSelected = false
      this.scheduleExecutionDates = [];
      for (const date of res.execution_dates) {
        this.scheduleExecutionDates.push(date);
      }
      this.scheduleDescription = res.schedule_description;
      if (this.scheduleExecutionDates.length > 0) {
        this.firstInstanceChange.emit(res.execution_dates[0]);
      }
      this.scheduleDescriptionChange.emit(this.scheduleDescription);
    });
  }

  onScheduleRepeatTypeChange(event: ScheduleRepeatTypeResponse | any) {
    this.selectedScheduleRepeatType = this.scheduleRepeatTypes.find((type) => type.schedule_repeat_type_id === event.schedule_repeat_type_id)!;
    if (this.selectedScheduleRepeatType.schedule_repeat_type_id == 1) {
      this.weekdays = [];
      for (const day of this.days) {
        day.active = false;
      }
      this.days[0].active = true;
      this.weekdays.push(this.days[0].value);
    }
    else if (this.selectedScheduleRepeatType.schedule_repeat_type_id == 2) {
      this.nthWeekday = 1;
      this.weekdays = [0];
    }
    this.updateScheduleSubject('scheduleSetup');
    this.getSchedulePreview();
  }

  onNthWeekChange(event: any) {
    this.nthWeekday = event.value;
    this.updateScheduleSubject('scheduleSetup');
    this.getSchedulePreview();
  }

  updateWeekdays(day: { name: string, value: number, abbreviation: string, active: boolean }) {
    this.days[this.days.findIndex((_day) => _day.value === day.value)].active = day.active;

    // If the day is already in the array, remove it
    if (this.weekdays.includes(day.value)) {
      this.weekdays.splice(this.weekdays.indexOf(day.value), 1);
    }

    // If the day is not in the array, add it
    else {
      this.weekdays.push(day.value);
    }

    if (this.weekdays.length > 0) {
      this.updateScheduleSubject('scheduleSetup');
      this.getSchedulePreview();
    }
    else {
      this.noWeekdaysSelected = true;
      this.scheduleDescription = this.translateService.instant('orderSchedules.noWeekdaysSelected');
    }
  }

  onUseDateSwitchChange(): void {
    this.scheduleUseDate = !this.scheduleUseDate;
    if (this.scheduleUseDate) {
      if (this.scheduleDate === null) {
        this.scheduleUseDate = true;
        this.scheduleDate = 1;
        this.selectedScheduleMonthDate = this.scheduleMonthDates[0];
      }
    }
    else {
      this.scheduleUseDate = false;
      this.scheduleDate = null;
      if (!this.nthWeekday) {
        this.nthWeekday = 1;
      }
    }
    this.updateScheduleSubject('scheduleSetup');
    this.getSchedulePreview();
  }

  onNthWeekdayChange(event: any): void {
    this.weekdays = [event.value];
    this.selectedNthWeekdayIndex = event.value;
    this.updateScheduleSubject('scheduleSetup');
    this.getSchedulePreview();
  }

  onScheduleDateChange(event: any): void {
    this.scheduleDate = event['date'];
    this.selectedScheduleMonthDate = event;
    this.updateScheduleSubject('scheduleSetup');
    this.getSchedulePreview();
  }

  onScheduleEveryChange(event: any): void {
    this.updateScheduleSubject('scheduleSetup');
    this.getSchedulePreview();
  }

  startDateChanged(event: Date | null) {
    if (!event) {
      this.startDate = null;
    } else {
      this.startDate = event
    }
    this.updateScheduleSubject('scheduleSetup');
    this.getSchedulePreview();
  }

  endDateChanged(event: Date) {
    if (!event) {
      this.endDate = null;
    } else {
      this.endDate = event;
    }
    this.updateScheduleSubject('scheduleSetup');
    this.getSchedulePreview();
  }

  updateInstancesInAdvance(event: any) {
    this.updateScheduleSubject('scheduleSetup');
    this.getSchedulePreview();
  }

  clearStartDate(event: Event) {
    this.startDate = null;
    event.stopPropagation();
    this.updateScheduleSubject('scheduleSetup');
    this.getSchedulePreview();
  }

  clearEndDate(event: Event) {
    this.endDate = null;
    event.stopPropagation();
    this.updateScheduleSubject('scheduleSetup');
    this.getSchedulePreview();
  }

  updateScheduleSubject(sourceName?: string | null) {
    if (!this.selectedScheduleRepeatType) {
      return;
    }
    if (this.scheduleInstancesInAdvanceControl.invalid) {
      return;
    }
    const scheduleInputValue = this.scheduleInputSource.getValue();
    scheduleInputValue.start_date = this.startDate;
    scheduleInputValue.end_date = this.endDate;
    scheduleInputValue.date = this.scheduleDate;
    scheduleInputValue.every = parseInt(this.scheduleEveryControl.value);
    scheduleInputValue.schedule_repeat_type_id = this.selectedScheduleRepeatType.schedule_repeat_type_id;
    scheduleInputValue.weekdays = this.weekdays;
    scheduleInputValue.nth_weekday = this.nthWeekday;
    scheduleInputValue.instances_in_advance = parseInt(this.scheduleInstancesInAdvanceControl.value);
    scheduleInputValue.sourceName = sourceName;
    this.scheduleInputSource.next(scheduleInputValue);

  }

  protected readonly formatFullDayAndDate = formatFullDayAndDate;
}
