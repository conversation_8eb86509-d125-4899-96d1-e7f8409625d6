<!-- Loading Spinner -->
<div class="table-loader" *ngIf="isLoading || isCancellingOrders">
  <div class="spinner-border my-3" role="status">
    <span class="visually-hidden">Loading...</span>
    <span class="spinner-border-sm"></span>
  </div>
</div>

<div *ngIf="!isLoading && !isCancellingOrders" class="table-scroll-container">
  <ng-container *ngIf="(tableData && tableData.length > 0) && (columns && columns.length > 0); else noDataTemplate">

    <div
      cdkDropList
      cdkDropListOrientation="horizontal"
      (cdkDropListDropped)="drop($event)"
      [cdkDropListDisabled]="isResizing"
      class="table-responsive"
    >
      <!-- Table Header -->
      <div class="table-row table-header">
        <!-- Select All Checkbox -->
        <div      *ngIf="columns && columns.length > 0" class="table-cell header-cell" (click)="$event.stopPropagation();">
          <input
            *ngIf="columns && columns.length > 0"
            class="form-check-input"
            type="checkbox"
            [checked]="allSelected"
            (change)="selectAllRows($event)"
          />
        </div>

        <div
          *ngFor="let column of columns; let i = index"

          [style.display]="columnVisibility[column.name] ? 'table-cell' : 'none'"
          [style.width.px]="columnWidths[i]"
          cdkDrag
          [cdkDragDisabled]="isResizing"
          class="table-cell header-cell"
        >
          <div class="header-content">
            <span>{{ column.label }}</span>
            <i class="fa-regular fa-grip-dots-vertical column-icon fa-xl"></i>
          </div>
        </div>
      </div>

      <!-- Table Body -->
      <div
        *ngFor="let row of tableData; let rowIndex = index"
        class="table-row"
        [routerLink]="clickableRow ? generateLink(row) : null"
      >
<!--        (click)="onRowClick(row)"-->
        <!-- Row Checkbox -->
        <div     *ngIf="columns && columns.length > 0"  class="table-cell" (click)="$event.stopPropagation();">
          <input
            *ngIf="columns && columns.length > 0"
            class="form-check-input"
            type="checkbox"
            [(ngModel)]="row.selected"
            (change)="onRowSelect(row)"
          />
        </div>

        <div
          *ngFor="let column of columns; let i = index"

          [style.display]="columnVisibility[column.name] ? 'table-cell' : 'none'"
          [style.width.px]="columnWidths[i]"
          class="table-cell"
        >
          <!-- Customer Column -->
          <ng-container *ngIf="column.name === 'customer.name'; else serviceCell">
            <div class="cell-content"
              (mouseleave)="hideTooltip()">
              <span [innerHTML]="callFormatter(column, row)"></span>
              <i class="fa fa-chevron-down customer-chevron" (click)="toggleTooltip($event, row, 'payment')"></i>
            </div>
          </ng-container>

          <!-- Service Recipient Column -->
          <ng-template #serviceCell>
            <ng-container *ngIf="column.name === 'service_recipient'; else feedbackCell">
              <div class="cell-content"
                (mouseleave)="hideTooltip()">
                <span [innerHTML]="callFormatter(column, row)"></span>
                <i class="fa fa-chevron-down customer-chevron" (click)="toggleTooltip($event, row, 'service')"></i>
              </div>
            </ng-container>
          </ng-template>

          <ng-template #feedbackCell>
            <ng-container *ngIf="column.name === 'feedback_rating'; else defaultCell">
              <div
                class="cell-content"
                (click)="toggleTooltip($event, row, 'feedback')"
                (mouseleave)="hideTooltip()">
                <div class="title" *ngIf="row.feedback_rating; else noRating">
                  <span class="star" *ngFor="let star of stars">
                    <i class="fa-regular fa-star" *ngIf="star > row.feedback_rating"></i>
                    <i class="fa-solid fa-star" style="color: #FFD700;" *ngIf="star <= row.feedback_rating"></i>
                  </span>
                  <i class="fa-regular fa-comment ms-1" *ngIf="row.feedback_comment"></i>
                </div>
              </div>
              <!-- Fallback for no rating -->
              <ng-template #noRating>
                <span>{{ "orders.orderList.feedbackRating.tooltip.noRating" | translate }}</span>
              </ng-template>
            </ng-container>

            <ng-template #defaultCell>
              <div [innerHTML]="callFormatter(column, row)"></div>
            </ng-template>
          </ng-template>

        </div>
      </div>
    </div>
  </ng-container>

  <!-- No Data Template -->
  <ng-template #noDataTemplate>
    <div class="text-center d-flex justify-content-center align-items-center no-data-row" style="width: 100%; height: 200px;">
      <p>{{ "orders.orderList.noData" | translate }}</p>
    </div>
  </ng-template>
  <!-- Tooltip -->
  <div
    class="customer-tooltip table-font-size"

    *ngIf="
    tooltipVisible &&
    (
      (tooltipType === 'feedback') ||
      (tooltipType === 'service' && tooltipRow?.service_recipient) ||
      (tooltipType === 'payment' && tooltipRow?.payment_recipient)
    )
  "
    [style.top.px]="tooltipPosition.top"
    [style.left.px]="tooltipPosition.left"
    (mouseenter)="keepTooltipVisible()"
    (mouseleave)="hideTooltip()"
  >
    <div *ngIf="tooltipType === 'feedback'">
      <div class="fw-bolder">{{"orders.orderList.feedbackRating.tooltip" | translate}}</div>
      <div class="fs-6 fw-normal text-muted mb-1">
        {{ tooltipRow?.feedback_comment || ("orders.orderList.feedbackRating.tooltip.noComment" | translate) }}
      </div>
    </div>

    <div *ngIf="tooltipType !== 'feedback'">
      <div class="fw-bolder">
        {{ tooltipType === 'payment' ? (tooltipRow?.payment_recipient?.name || ("addOrder.customer.noName" | translate)) : (tooltipRow?.service_recipient?.name || "addOrder.customer.noName" | translate) }}
      </div>
      <div *ngIf="tooltipType === 'payment' && tooltipRow?.payment_recipient?.organisation_number" class="fs-6 fw-normal text-muted mb-1">
        {{ tooltipRow?.payment_recipient?.organisation_number }}
      </div>
      <div *ngIf="tooltipType === 'service' && tooltipRow?.service_recipient?.organisation_number" class="fs-6 fw-normal text-muted mb-1">
        {{ tooltipRow?.service_recipient?.organisation_number }}
      </div>
      <div class="fw-light">
        {{ tooltipType === 'payment' ? displayPhoneNumber(tooltipRow?.payment_recipient?.phone) || ("addOrder.customer.noPhone" | translate) : displayPhoneNumber(tooltipRow?.service_recipient?.phone) || ("addOrder.customer.noPhone" | translate) }}
      </div>
      <div class="d-flex align-items-center justify-content-between">
        <span
          [class.between-color]="tooltipType === 'payment' ? tooltipRow?.payment_recipient?.email : tooltipRow?.service_recipient?.email"
          [class.text-muted]="!(tooltipType === 'payment' ? tooltipRow?.payment_recipient?.email : tooltipRow?.service_recipient?.email)">
          {{ tooltipType === 'payment' ? (tooltipRow?.payment_recipient?.email || ("addOrder.customer.noEmail" | translate)) : (tooltipRow?.service_recipient?.email || ("addOrder.customer.noEmail" | translate)) }}
        </span>

        <i
          *ngIf="tooltipType === 'payment' ? tooltipRow?.payment_recipient?.email : tooltipRow?.service_recipient?.email"
          class="fa-regular fa-copy"
          style="cursor: pointer; color: #6c757d;"
          (click)="copyToClipboard(tooltipType === 'payment' ? tooltipRow?.payment_recipient?.email : tooltipRow?.service_recipient?.email)"
          title="Copy email">
        </i>
      </div>
      <app-button [small]="true" [buttonType]="'nude'" [translationKey]="'orders.orderList.customer.viewCustomer'" [customClass]="'mt-3 custom-button w-100'" (buttonClick)="onTooltipButtonClick(tooltipType === 'payment' ? tooltipRow?.payment_recipient : tooltipRow?.service_recipient)"></app-button>
    </div>
  </div>
</div>
