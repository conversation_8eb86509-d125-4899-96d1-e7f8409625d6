import {ChangeDetectorRef, Component, Input, OnInit} from '@angular/core';
import {CommonModule} from "@angular/common";
import {CdkDragDrop, DragDropModule, moveItemInArray} from "@angular/cdk/drag-drop";
import {Router, RouterModule} from "@angular/router";
import {OrderService} from "../../services/order.service";
import {USM_USR_22} from "../../models/input.interfaces";
import {EndpointService} from "../../services/endpoints.service";
import {OrderListService} from "../../services/order-list.service";
import {ButtonComponent} from "../button/button.component";
import {ToastService} from "../../../@core/services/toast.service";
import {TranslateModule} from "@ngx-translate/core";
import {displayPhone, UtilsService} from "../../../@core/utils/utils.service";
import {FormControl, FormsModule} from "@angular/forms";
import {
  CancelOrderModalComponent
} from "../../../pages/orders/order-details-v2/components/action-button-group/_modals/cancel-order-modal/cancel-order-modal.component";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {environment} from "../../../../environments/environment";
import {OverlayService} from "../../services/overlay.service";
import {OrderDetailsV2Component} from "../../../pages/orders/order-details-v2/order-details-v2.component";


export interface Column {
  name: string;
  label: string;
  formatter: (a: any) => any | string;
  sort?: boolean;
  align?: string;
  width?: number;
  visible?: boolean;
}
@Component({
  selector: 'app-table-v2',
  imports: [CommonModule, DragDropModule, RouterModule, ButtonComponent, TranslateModule, FormsModule],
  templateUrl: './table-v2.component.html',
  standalone: true,
  styleUrls: ['./table-v2.component.css']
})
export class TableV2Component implements OnInit {
  @Input() tableData: any[] = [];
  @Input() clickableRow: boolean = false;
  @Input() rowClickLink: string = '';
  @Input() linkKey: string = '';
  @Input() isLoading: boolean = false;

  stars: number[] = [1, 2, 3, 4, 5];
  columnVisibility: { [key: string]: boolean } = {};
  columnWidths: number[] = [];
  totalTableWidth: number = 0;
  isResizing: boolean = false;
  columns: Column[] = [];
  hiddenColumns: Column[] = [];
  allSelected: boolean = false;
  tooltipType: 'service' | 'payment' | 'feedback';
  paginationDetails = {
    page: 1,
    limit: environment.standardPageSize
  }
  isCancellingOrders: boolean = false;
  tooltipVisible: boolean = false;
  tooltipPosition = { top: 0, left: 0 };
  tooltipRow: any = null;
  hideTimeout: any = null;

  constructor(private orderService: OrderService,
              private endpointService: EndpointService,
              private cdr: ChangeDetectorRef,
              private orderListService: OrderListService,
              private toastService: ToastService,
              private router: Router,
              private overlayService: OverlayService,
              private modalService: NgbModal,
              public utilsService: UtilsService) {}

  ngOnInit() {
    // console.log('app-table-v2 ngOnInit');

    this.orderListService.cancelOrders$.subscribe(() => {
      this.cancelSelectedOrders();
    });

    this.columns.forEach(column => {
      if (!(column.name in this.columnVisibility)) {
        this.columnVisibility[column.name] = true;
      }
    });
    this.orderListService.fetchUserPreferences();
    this.orderListService.columns$.subscribe(columns => {
      this.columns = columns;

      this.calculateTotalWidth();
      this.cdr.detectChanges();
    });

    this.orderListService.columnVisibility$.subscribe(columnVisibility => {
      this.columnVisibility = columnVisibility;
      this.hiddenColumns = this.columns.filter(column => !columnVisibility[column.name]);
      this.adjustColumnWidths();
      this.calculateTotalWidth();
      this.cdr.detectChanges();
    });

    // Initialize column widths
    this.columns.forEach((column, index) => {
      this.columnWidths[index] = column.width || 400;
    });
    this.calculateTotalWidth();
    // console.log('app-table-v2 ngOnInit END');

  }


  toggleTooltip(event: MouseEvent, row: any, type: 'service' | 'payment' | 'feedback'): void {
    event.stopPropagation();

    if (type === 'feedback' && !row.feedback_rating) {
      return;
    }

    if (this.tooltipRow === row && this.tooltipVisible) {
      this.hideTooltip();
    } else {
      this.tooltipType = type;
      this.showTooltip(event, row);
    }
  }

  showTooltip(event: MouseEvent, row: any): void {
    const tooltipOffset = 10;
    const tooltipWidth = 200;
    const tooltipHeight = 50;

    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let top = event.clientY + tooltipOffset;
    let left = event.clientX + tooltipOffset;

    if (left + tooltipWidth > viewportWidth) {
      left = viewportWidth - tooltipWidth - tooltipOffset;
    }

    if (top + tooltipHeight > viewportHeight) {
      top = viewportHeight - tooltipHeight - tooltipOffset;
    }

    this.tooltipPosition = { top, left };
    this.tooltipVisible = true;
    this.tooltipRow = row;
  }
  hideTooltip(): void {
    this.hideTimeout = setTimeout(() => {
      this.tooltipVisible = false;
      this.tooltipRow = null;
    }, 200);
  }

  keepTooltipVisible(): void {
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
    }
  }

  onTooltipButtonClick(payment_recipient: any): void {
    if (payment_recipient.is_private === 1) {
      this.router.navigate([`/customers/private/${payment_recipient.affiliate_id}`]);
    } else if (payment_recipient.is_private === 0) {
      this.router.navigate([`/affiliates/details/${payment_recipient.affiliate_id}`]);
    }
  }

  copyToClipboard(text: string | null): void {
    if (!text) {
      return;
    }
    navigator.clipboard.writeText(text).then(() => {
      this.toastService.infoToast('copy');
    }).catch(err => {
      console.error('Could not copy text: ', err);
    });
  }

  adjustColumnWidths() {
    // console.log("adjustColumnWidths")
    const visibleColumns = this.columns.filter(col => this.columnVisibility[col.name]);

    let totalVisibleWidth = 0;
    visibleColumns.forEach((col, index) => {
      totalVisibleWidth += this.columnWidths[this.columns.indexOf(col)];
      // console.log("adjustColumnWidths",totalVisibleWidth)
    });

    visibleColumns.forEach((col, index) => {
      const columnIndex = this.columns.indexOf(col);
      this.columnWidths[columnIndex] = (this.columnWidths[columnIndex] / totalVisibleWidth) * this.totalTableWidth;
    });
  }

  calculateTotalWidth() {
    this.totalTableWidth = this.columnWidths.reduce((sum, width) => sum + width, 0);
    // console.log("calculateTotalWidth", this.totalTableWidth)
    this.adjustColumnWidths();
  }

  callFormatter(column: Column, data: any): any {
    return column.formatter(data);
  }

  drop(event: CdkDragDrop<string[]>): void {
    if (!this.isResizing) {
      const visibleColumns = this.columns.filter(col => this.columnVisibility[col.name]);
      const draggedColumn = visibleColumns[event.previousIndex];

      const previousIndex = this.columns.indexOf(draggedColumn);
      const targetColumn = visibleColumns[event.currentIndex];
      const currentIndex = this.columns.indexOf(targetColumn);

      moveItemInArray(this.columns, previousIndex, currentIndex);
      moveItemInArray(this.columnWidths, previousIndex, currentIndex);

      this.saveColumnPreferences();
    }
  }

  getOrderListColumns(): string[] {
    return this.columns
      .filter(col => this.columnVisibility[col.name])
      .map(col => col.name); // Map to column names
  }

  saveColumnPreferences() {
    const orderListColumns = this.getOrderListColumns();

    const payload: USM_USR_22 = {
      order_list_columns: orderListColumns,
    };

    this.endpointService.usm_usr_22(payload).subscribe({
      next: () => {
        this.orderListService.setColumns([...this.columns]);
      },
      error: (error) => {
        console.error('Failed to save user preferences:', error);
      }
    });
  }

  generateLink(row: any): string {
    return `${this.rowClickLink}${row[this.linkKey]}`;
  }

  selectAllRows(event: Event): void {
    const isChecked = (event.target as HTMLInputElement).checked;
    this.allSelected = isChecked;
    this.tableData.forEach(row => (row.selected = isChecked));
    this.updateSelectedOrders();
  }

  onRowSelect(row: any): void {
    this.allSelected = this.tableData.every(row => row.selected);
    this.updateSelectedOrders();
  }

  updateSelectedOrders(): void {
    const selectedOrders = this.tableData.filter(row => row.selected);
    this.orderListService.updateSelectedOrders(selectedOrders);
  }

  cancelSelectedOrders(): void {
    const selectedOrders = this.tableData.filter(order => order.selected);

    if (!selectedOrders.length) {
      console.log('No orders selected for cancellation.');
      return;
    }

    const modalRef = this.modalService.open(CancelOrderModalComponent, { size: 'lg' });

    modalRef.result.then((result) => {
      if (result.deleteConfirmed) {
        // Set loading to true when cancellation process begins
        this.isCancellingOrders = true;

        let processedCount = 0;

        selectedOrders.forEach(order => {
          if (order.order_status_id === 7) {
            // console.log(`Order ${order.order_id} is already canceled.`);
            processedCount++;
            this.checkIfAllOrdersProcessed(processedCount, selectedOrders.length);
            return;
          }

          const notify_customer = result.notifyCustomer ? 1 : null;

          this.orderService.cancelOrder(notify_customer, order.order_id).subscribe(
            (res) => {
              this.orderService.fetchAndRefreshOrder(order.order_id, 'cancelOrder');
              processedCount++;
              this.checkIfAllOrdersProcessed(processedCount, selectedOrders.length);
            },
            (error) => {
              console.error(`Failed to cancel order ${order.order_id}:`, error);
              processedCount++;
              this.checkIfAllOrdersProcessed(processedCount, selectedOrders.length);
            }
          );
        });
      }
    }).catch((reason) => {
      // console.log('Cancellation modal dismissed.');
    });
  }

  checkIfAllOrdersProcessed(processedCount: number, totalOrders: number) {
    if (processedCount === totalOrders) {
      this.refreshOrderList();
    }
  }

  refreshOrderList() {
    const params = {
      page: this.paginationDetails  ? this.paginationDetails .page : 1,
      limit: this.paginationDetails ? this.paginationDetails.limit : 10,
      paginate: 1
    };

    this.orderService.getAllOrders(params).subscribe((updatedOrders) => {
      this.tableData = updatedOrders.data;
      this.isCancellingOrders= false;

      this.cdr.detectChanges();
    }, (error) => {
      console.error('Failed to fetch updated orders:', error);
    });
  }

  hasVisibleColumns(): boolean {
    return this.columns.some(column => this.columnVisibility[column.name]);
  }

  protected readonly displayPhoneNumber = displayPhone;
}
