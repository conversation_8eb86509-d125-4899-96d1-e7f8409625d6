import {Component, Input, Output, EventEmitter, NgModule, OnInit} from '@angular/core';
import {FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import {TranslateModule, TranslateService} from '@ngx-translate/core';
import {CommonModule} from "@angular/common";
import {NgbTooltip} from "@ng-bootstrap/ng-bootstrap";
import {StandardImports} from "../../global_import";



@Component({
  selector: 'app-help-icon',
  templateUrl: './help-icon.component.html',
  styleUrls: ['./help-icon.component.css'],
  standalone: true,
  imports: [StandardImports]
})
export class HelpIconComponent implements OnInit {
  @Input() tooltipKey: string;
  @Input() noPadding: boolean = false;
  @Output() iconClicked = new EventEmitter<any>();

  expandOnHover: boolean = false;

  constructor(private translate: TranslateService) {}

  ngOnInit() {
    if (!this.tooltipKey) {
      this.expandOnHover = true;
    }
  }

  onClick() {
    this.iconClicked.emit();
  }


}

