import {Component, EventEmitter, Input, Output} from '@angular/core';
import {TranslateModule} from "@ngx-translate/core";


import {ButtonComponent} from "../button/button.component";
import {StandardImports} from "../../global_import";

@Component({
  selector: 'app-button-double',
  templateUrl: './button-double.component.html',
  styleUrls: ['./button-double.component.css'],
  standalone: true,
  imports: [StandardImports]
})
export class ButtonDoubleComponent {

  @Input() leftButtonTranslationKey: string = '';
  @Input() middleButtonTranslationKey: string = '';
  @Input() rightButtonTranslationKey: string = '';

  @Input() leftButtonDisabled: boolean = false;
  @Input() middleButtonDisabled: boolean = false;
  @Input() rightButtonDisabled: boolean = false;

  @Input() leftButtonActive: boolean = false;
  @Input() middleButtonActive: boolean = false;
  @Input() rightButtonActive: boolean = false;

  @Input() leftButtonNgbTooltip: string | null = null;
  @Input() middleButtonNgbTooltip: string | null = null;
  @Input() rightButtonNgbTooltip: string | null = null;

  @Input() leftButtonCustomClass: string = '';
  @Input() middleButtonCustomClass: string = '';
  @Input() rightButtonCustomClass: string = '';

  @Input() showMiddleButton: boolean = false;
  @Input() small: boolean = false;
  @Input() useMaxWidth: boolean = false;
  @Input() boldText: boolean = false;

  @Output() leftButtonClick: EventEmitter<void> = new EventEmitter<void>();
  @Output() middleButtonClick: EventEmitter<void> = new EventEmitter<void>();
  @Output() rightButtonClick: EventEmitter<void> = new EventEmitter<void>();

  constructor() {
  }

  protected readonly crossOriginIsolated = crossOriginIsolated;
}
