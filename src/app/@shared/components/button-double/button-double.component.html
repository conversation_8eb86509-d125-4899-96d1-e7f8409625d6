<div class="d-flex align-items-center">
  <div class="" [ngClass]="{'d-flex justify-content-end': !useMaxWidth, 'col-4': showMiddleButton, 'col-6': !showMiddleButton}">
    <app-button
      [customClass]="'no-right-border-radius no-shadow' + ' ' + leftButtonCustomClass"
      [small]="small"
      [buttonType]="this.leftButtonActive ? 'solid' : 'nude'"
      [translationKey]="leftButtonTranslationKey"
      [boldText]="boldText"
      [useMaxWidth]="useMaxWidth"
      [ngbTooltip]="leftButtonNgbTooltip"
      [disabled]="leftButtonDisabled"
      (buttonClick)="leftButtonClick.emit()"
    ></app-button>
  </div>
  <div *ngIf="showMiddleButton" class="col-4" [ngClass]="{'d-flex justify-content-center': !useMaxWidth}">
    <app-button
      [customClass]="'no-right-border-radius no-left-border-radius no-shadow' + ' ' + middleButtonCustomClass"
      [small]="small"
      [buttonType]="middleButtonActive ? 'solid' : 'nude'"
      [translationKey]="middleButtonTranslationKey"
      [boldText]="boldText"
      [useMaxWidth]="useMaxWidth"
      [ngbTooltip]="middleButtonNgbTooltip"
      [disabled]="middleButtonDisabled"
      (buttonClick)="middleButtonClick.emit()"
    ></app-button>
  </div>
  <div class="" [ngClass]="{'d-flex justify-content-start': !useMaxWidth, 'col-4': showMiddleButton, 'col-6': !showMiddleButton}">
    <app-button
      [customClass]="'no-left-border-radius no-shadow' + ' ' + rightButtonCustomClass"
      [small]="small"
      [buttonType]="rightButtonActive ? 'solid' : 'nude'"
      [translationKey]="rightButtonTranslationKey"
      [boldText]="boldText"
      [useMaxWidth]="useMaxWidth"
      [ngbTooltip]="rightButtonNgbTooltip"
      [disabled]="rightButtonDisabled"
      (buttonClick)="rightButtonClick.emit()"
    ></app-button>
  </div>
</div>
