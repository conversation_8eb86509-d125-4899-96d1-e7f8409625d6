.no-right-border-radius {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.no-left-border-radius {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.no-shadow {
  box-shadow: none !important;
}

.link-button {
  border: none !important;
}

.link-button:visited {
  border: none !important;
}

.link-button:active {
  border: none !important;
}

.link-button:focus {
  border: none !important;
}

.nude-bold {
  font-weight: bold;
}

.nude {
  background-color: white;
  /*border-color: var(--primary-color);*/
  border-color: #E6E9EC;
}

.nude:hover {
  background-color: #F6F7FB;
}

.dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.6em;
  vertical-align: 0.17em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-left: 0.3em solid transparent;
}

.dropdown-toggle.nude::after {
  border-top-color: #6c757d;
}

.dropdown-toggle.solid::after {
  border-top-color: white;
}

.feigned-disabled {
  background-color: var(--disabled-primary) !important;
  border-color: var(--disabled-primary) !important;
}

button.outline-text-color:hover,
button.outline-text-color:hover .outline-text-primary,
button.outline-text-color:hover .outline-text-warning {
  color: white !important;
}

.outline-text-primary {
  color: var(--primary-color);
}

.outline-text-warning {
  color: var(--warning-color);
}
