import { Injectable } from '@angular/core';
import {EndpointService} from "./endpoints.service";
import {StorageService} from "../../@core/services/storage.service";
import {
  CRM_PAY_7,
  CRM_PAY_2,
  _CRM_PAY_7,
  _CRM_PAY_2,
  CRM_PAY_6,
  CRM_PAY_15,
  CRM_PAY_16,
  CRM_PAY_23,
  CRM_PAY_25,
  _CRM_PAY_26,
  CRM_PAY_26,
  _CRM_PAY_27,
  CRM_PAY_27,
  CRM_PAY_28,
  CRM_PAY_29,
  CRM_PAY_30,
  CRM_PAY_31,
  CRM_PAY_32,
  CRM_COY_25,
  CRM_PAY_33,
  _CRM_PAY_36,
  CRM_PAY_36,
  CRM_PAY_37,
  _CRM_PAY_38,
  CRM_PAY_38,
  _CRM_PAY_41,
  CRM_PAY_41,
  _CRM_PAY_42,
  CRM_PAY_42,
  _CRM_PAY_43, CRM_PAY_43, _CRM_PAY_44, CRM_PAY_44, CRM_PAY_46
} from "../models/input.interfaces";
import {map} from "rxjs/operators";
import {OrderPaymentResponse, PaymentMethodResponse} from "../models/payment.interfaces";
import {BehaviorSubject, Observable} from "rxjs";
import {CompanyPaymentMethodResponse} from "../models/integrations.interfaces";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {DetailsViewSettings, OrderLineRow, WorkOrderResponse} from "../models/order.interfaces";

@Injectable({
  providedIn: 'root'
})
export class PaymentService {

  constructor(private endpointService: EndpointService, private storageService: StorageService, private modalService: NgbModal) { }

  getPaymentStatuses() {
    return this.endpointService.crm_pay_5().pipe(map((response) =>  response.data));
  }

  sendToPayment(payload: _CRM_PAY_7) {
    let _payload: CRM_PAY_7 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_pay_7(_payload).pipe(map((response) => response.data));
  }

  refundOrder(payload: _CRM_PAY_2) {
    let _payload: CRM_PAY_2 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_pay_2(_payload).pipe(map((response) => response.data));
  }

  getInvoiceSendTypes() {
    let params: CRM_PAY_16 = {
      company_id: this.storageService.getSelectedCompanyId()
    }

    return this.endpointService.crm_pay_16(params).pipe(map((response) => response.data))
  }

  creditInvoice(paymentId: number) {
    let payload: CRM_PAY_23 = {
      company_id: this.storageService.getSelectedCompanyId(),
      payment_id: paymentId
    }
    return this.endpointService.crm_pay_23(payload).pipe(map((response) => response.data))
  }

  markPaymentAsPaid(paymentId: number) {
    let payload: CRM_PAY_33 = {
      company_id: this.storageService.getSelectedCompanyId(),
      payment_id: paymentId
    }
    return this.endpointService.crm_pay_33(payload).pipe(map((data) => {
      return data.data
    }))
  }

  checkAccountingPaymentStatus(paymentId: number) {
    let params: CRM_PAY_25 = {
      company_id: this.storageService.getSelectedCompanyId(),
      payment_id: paymentId
    }
    return this.endpointService.crm_pay_25(params).pipe(map((response) => response.data))
  }

  createOrderPayment(payload: _CRM_PAY_26) {
    let _payload: CRM_PAY_26 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_pay_26(_payload).pipe(map((response) => response.data));
  }

  updateOrderPayment(payload: _CRM_PAY_27, raiseErrorToast: boolean = true) {
    let _payload: CRM_PAY_27 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_pay_27(_payload, raiseErrorToast).pipe(map((response) => response.data));
  }

  togglePaymentReminder(paymentId: number, disable: boolean) {
    let payload: CRM_PAY_27 = {
      company_id: this.storageService.getSelectedCompanyId(),
      payment_id: paymentId,
      payment_reminders_disabled: disable
    }
    return this.endpointService.crm_pay_27(payload).pipe(map((response) => response.data));
  }

  deleteOrderPayment(paymentId: number) {
    let payload: CRM_PAY_28 = {
      company_id: this.storageService.getSelectedCompanyId(),
      payment_id: paymentId
    }
    return this.endpointService.crm_pay_28(payload).pipe(map((response) => response));
  }

  getOrderPayment(paymentId: number) {
    let payload: CRM_PAY_29 = {
      company_id: this.storageService.getSelectedCompanyId(),
      payment_id: paymentId
    }
    return this.endpointService.crm_pay_29(payload).pipe(map((response) => response.data));
  }

  getAllOrderPayments(orderId: number, template: boolean = false) {
    let payload: CRM_PAY_30 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_id: orderId,
      template: template
    }
    return this.endpointService.crm_pay_30(payload).pipe(map((response) => response.data));
  }

  retryAccountingSynchronisation(payment_id: number) {
    let payload: CRM_PAY_31 = {
      company_id: this.storageService.getSelectedCompanyId(),
      payment_id: payment_id
    }
    return this.endpointService.crm_pay_31(payload).pipe(map((data) => data.data));
  }

  getPaymentInvoicePdfFromTripletex(paymentId: number) {
    let params: CRM_PAY_32 = {
      company_id: this.storageService.getSelectedCompanyId(),
      payment_id: paymentId
    }
    return this.endpointService.crm_pay_32(params);
  }

  getCompanyPaymentMethods() {
    let params: CRM_COY_25 = {
      company_id: this.storageService.getSelectedCompanyId()
    }
    return this.endpointService.crm_coy_25(params).pipe(map((response) => response.data));
  }

  getSchedulePreview(payload: _CRM_PAY_36) {
    let _payload: CRM_PAY_36 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_pay_36(_payload).pipe(map((response) => response.data));
  }

  manuallyInitiatePaymentSchedule(paymentScheduleId: number, toast: boolean = true) {
    let payload: CRM_PAY_37 = {
      company_id: this.storageService.getSelectedCompanyId(),
      payment_schedule_id: paymentScheduleId
    }
    return this.endpointService.crm_pay_37(payload, toast).pipe(map((response) => response));
  }

  createWorkOrderPayment(payload: _CRM_PAY_38) {
    let _payload: CRM_PAY_38 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_pay_38(_payload).pipe(map((response) => response.data));
  }

  fetchAndUpdatePaymentMethods() {
    this.getCompanyPaymentMethods().subscribe((paymentMethods) => {
      this.paymentMethodsSubject.next(paymentMethods);
    });
  }

  getCompanyPayments(params: _CRM_PAY_41) {
    let _params: CRM_PAY_41 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...params
    }
    return this.endpointService.crm_pay_41(_params).pipe(map((response) => response));
  }

  getCompanyTotals(params: _CRM_PAY_44) {
    let _params: CRM_PAY_44 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...params
    }
    return this.endpointService.crm_pay_44(_params).pipe(map((response) => response.data));
  }

  getCompanyPaymentsCompleteResponse(params: _CRM_PAY_41) {
    let _params: CRM_PAY_41 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...params
    }
    return this.endpointService.crm_pay_45(_params).pipe(map((response) => response));
  }

  initiateConsolidatedInvoice(payload: _CRM_PAY_42) {
    let _payload: CRM_PAY_42 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_pay_42(_payload).pipe(map((response) => response.data));
  }

  sendReceipt(payload: _CRM_PAY_43) {
    let _payload: CRM_PAY_43 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_pay_43(_payload).pipe(map((response) => response.data));
  }

  getPaymentLogs(paymentId: number) {
    let params: CRM_PAY_46 = {
      company_id: this.storageService.getSelectedCompanyId(),
      payment_id: paymentId
    }
    return this.endpointService.crm_pay_46(params).pipe(map((response) => response.data));
  }

  getConsolidatedInvoiceParents(paymentRecipientId: number) {
    let params: CRM_PAY_41 = {
      company_id: this.storageService.getSelectedCompanyId(),
      paginate: 0,
      include_consolidated_invoice_parents: true,
      customer_id: paymentRecipientId
    }
    return this.endpointService.crm_pay_45(params).pipe(map((response) => response.data));
  }

  private paymentMethodsSubject: BehaviorSubject<CompanyPaymentMethodResponse[]> = new BehaviorSubject<CompanyPaymentMethodResponse[]>([]);
  paymentMethods$: Observable<CompanyPaymentMethodResponse[]> = this.paymentMethodsSubject.asObservable();

  async openPaymentDetailsComponent(input: {payment?: OrderPaymentResponse, orderLinesSubject?: BehaviorSubject<OrderLineRow[]>, workOrder?: WorkOrderResponse, viewSettings?: DetailsViewSettings}) {
    const { PaymentDetailsV2Component } = await import('src/app/pages/payments/components/payment-details-v2/payment-details-v2.component');
    const modalRef = this.modalService.open(PaymentDetailsV2Component, { size: 'lg' });
    modalRef.componentInstance.payment = input.payment;
    modalRef.componentInstance.viewSettings = input.viewSettings;
    modalRef.componentInstance.orderLinesSubject = input.orderLinesSubject;
    return modalRef
  }

  getPaymentScheduleOptions() {
    return this.endpointService.crm_pay_48().pipe(map((response) => response.data));
  }

}
