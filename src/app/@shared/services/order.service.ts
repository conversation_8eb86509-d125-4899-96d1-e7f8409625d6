import { Injectable } from '@angular/core';
import {
  CRM_ORD_0,
  CRM_ORD_10,
  CRM_ORD_12,
  CRM_ORD_13,
  CRM_ORD_15,
  CRM_ORD_19,
  CRM_ORD_21,
  CRM_ORD_27,
  CRM_ORD_31,
  CRM_ORD_39,
  CRM_PRD_1,
  CRM_PAY_13,
  CRM_ORD_7,
  _CRM_ORD_2,
  _CRM_ORD_0,
  CRM_ORD_53,
  CRM_ORD_56,
  CRM_ORD_60,
  CRM_ORD_61,
  CRM_ORD_64,
  _CRM_PRD_77,
  CRM_PRD_77,
  _CRM_ORD_66,
  CRM_ORD_66,
  _CRM_ORD_67,
  CRM_ORD_67,
  CRM_PAY_31,
  _CRM_ORD_55,
  CRM_ORD_55,
  CRM_ORD_68,
  CRM_ORD_69,
  CRM_ORD_71,
  CRM_PAY_34,
  _CRM_PAY_34,
  _CRM_ORD_73,
  CRM_ORD_73,
  UnitDetails,
  _CRM_ORD_74,
  CRM_ORD_74,
  CRM_PAY_33,
  _CRM_ORD_76,
  CRM_ORD_76,
  _CRM_ORD_85,
  CRM_ORD_85,
  CRM_ORD_80,
  _CRM_ORD_80,
  CRM_ORD_78,
  CRM_ORD_77,
  _CRM_ORD_79,
  CRM_ORD_79,
  _CRM_ORD_81,
  CRM_ORD_81,
  _CRM_ORD_82,
  CRM_ORD_82,
  CRM_ORD_83,
  _CRM_ORD_83,
  _CRM_ORD_113,
  CRM_ORD_113,
  _CRM_ORD_114,
  CRM_ORD_114,
  _CRM_ORD_115,
  CRM_ORD_115,
  CRM_ORD_116,
  _CRM_ORD_116,
  _CRM_ORD_117,
  CRM_ORD_117,
  _CRM_ORD_118,
  CRM_ORD_118,
  CRM_ORD_119,
  CRM_ORD_120,
  CRM_ORD_121,
  _CRM_ORD_122,
  CRM_ORD_122,
  CRM_ORD_123,
  _CRM_ORD_123,
  CRM_ORD_125,
  CRM_ORD_86,
  _CRM_ORD_61,
  CRM_ORD_87,
  _CRM_ORD_87,
  CRM_ORD_90,
  CRM_ORD_91,
  CRM_ORD_93,
  CRM_ORD_95,
  CRM_ORD_96,
  CRM_ORD_97,
  _CRM_ORD_97,
  CRM_ORD_98,
  CRM_ORD_99,
  CRM_ORD_100,
  _CRM_ORD_101,
  CRM_ORD_101,
  CRM_ORD_102,
  CRM_ORD_103,
  CRM_ORD_104,
  _CRM_ORD_56,
  CRM_ORD_107,
  _CRM_ORD_31,
  CRM_PAY_24,
  CRM_PAY_32,
  CRM_ORD_110,
  _CRM_ORD_110,
  CRM_ORD_22,
  _CRM_ORD_60,
  _CRM_ORD_86,
  _CRM_ORD_12,
  CRM_PAY_30,
  _CRM_ORD_119,
  _CRM_ORD_98,
  CRM_ORD_142,
  CRM_ORD_143,
  _CRM_ORD_144,
  CRM_ORD_144,
  CRM_ORD_153,
  _CRM_ORD_154,
  CRM_ORD_154,
  _CRM_ORD_120,
  _CRM_ORD_153,
  _CRM_ORD_165,
  CRM_ORD_165,
  _CRM_ORD_168,
  CRM_ORD_168,
  _CRM_ORD_169,
  CRM_ORD_169,
  CRM_ORD_170,
  _CRM_ORD_170,
  CRM_ORD_126,
  _CRM_ORD_126,
  _CRM_ORD_128,
  _CRM_ORD_131,
  CRM_ORD_128,
  CRM_ORD_131,
  CRM_ORD_20,
  CRM_ORD_133,
  _CRM_ORD_133,
  _CRM_ORD_136,
  CRM_ORD_136,
  _CRM_ORD_135,
  CRM_ORD_135,
  CRM_ORD_134,
  _CRM_ORD_134,
  _CRM_ORD_132,
  CRM_ORD_132,
  CRM_ORD_127,
  _CRM_ORD_127,
  _CRM_ORD_130, CRM_ORD_130, _CRM_ORD_129, CRM_ORD_129, CRM_ORD_167, CRM_ORD_172, CRM_ORD_150, _CRM_ORD_103, _CRM_ORD_151, CRM_ORD_151, _CRM_ORD_13, _CRM_ORD_177, CRM_ORD_177, CRM_ORD_178, CRM_ORD_171, CRM_ORD_162
} from "../models/input.interfaces";
import {EndpointService} from "./endpoints.service";
import {CRM_ORD_2, CRM_ORD_4, CRM_ORD_5, CRM_ORD_8} from "../models/input.interfaces";
import {filter, map} from "rxjs/operators";
import {BehaviorSubject, Observable, Subject} from "rxjs";
import {StorageService} from "../../@core/services/storage.service";
import {CalculatedOrderResponse} from '../models/product.interfaces';
import {OrderAttachmentResponse, OrderLineResponse, OrderLogResponse, OrderResponse, OrderScheduleResponse, WorkOrderCompactResponse, WorkOrderResponse, WorkOrderStatusResponse} from "../models/order.interfaces";
import {OrderPaymentResponse} from "../models/payment.interfaces";

@Injectable({
  providedIn: 'root'
})
export class OrderService {

  constructor(private endpointService: EndpointService, private storageService: StorageService) { }



  private orderUpdatedSubject = new BehaviorSubject<string>('');
  creationOrderUpdated = this.orderUpdatedSubject.asObservable();
  orderUpdated(updateSource: string) {
    this.orderUpdatedSubject.next(updateSource);
  }

  getOrder(params: CRM_ORD_10): Observable<OrderResponse> {
    return this.endpointService.crm_ord_10(params).pipe(map((data: any)=>{
      return data.data;
    }))
  }

  getOrderById(orderId: number) {
    let params: CRM_ORD_10 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_id: orderId
    }
    return this.endpointService.crm_ord_10(params).pipe(map((response) => response.data));
  }

  getAllOrders(params: _CRM_ORD_2) {
    let _params: CRM_ORD_2 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...params
    }
    return this.endpointService.crm_ord_2(_params).pipe(map((data) => data));
  }

  getAllOrdersCompact(params: _CRM_ORD_2) {
    let _params: CRM_ORD_2 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...params
    }
    return this.endpointService.crm_ord_108(_params).pipe(map((data) => data));

  }

  getOrderLineAggregatedDate(fromDate: Date, toDate: Date) {
    let params: CRM_ORD_4 = {
      company_id: this.storageService.getSelectedCompanyId(),
      execution_date_from: fromDate,
      execution_date_to: toDate,
    };
    return this.endpointService.crm_ord_4(params).pipe(map((data) => data.data));
  }

  getOrdersByCompanyAggregatedDate(fromDate: Date, toDate: Date) {
    let params: CRM_ORD_5 = {
      company_id: this.storageService.getSelectedCompanyId(),
      date_from: fromDate,
      date_to: toDate
    };
    return this.endpointService.crm_ord_5(params).pipe(map((data) =>data.data));
  }

  getCompanyRevenueByDate(fromDate: Date, toDate: Date) {

    let params: CRM_ORD_7 = {
      company_id: this.storageService.getSelectedCompanyId(),
      date_from: fromDate,
      date_to: toDate,
    };
    return this.endpointService.crm_ord_7(params).pipe(map((data) => data.data));
  }

  getCompanyRevenueByMonth(paidOnly: boolean) {
    const currentDate = new Date();
    const fromDate = new Date(currentDate);
    fromDate.setMonth(fromDate.getMonth() - 12);

    let params: CRM_ORD_8 = {
      company_id: this.storageService.getSelectedCompanyId(),
      date_from: fromDate,
      date_to: currentDate,
      paid_only: paidOnly
    };
    return this.endpointService.crm_ord_8(params).pipe(map((data) => data.data));
  }

  getProductsWithServiceType() {
    let params: CRM_PRD_1 = {
      company_id: this.storageService.getSelectedCompanyId(),
      product_type_id: 1,
    };
    return this.endpointService.crm_prd_1(params).pipe(map((data) => data.data));
  }

  createOrderAsCompany(payload: _CRM_ORD_0) {
    let _payload: CRM_ORD_0 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }

    return this.endpointService.crm_ord_0(_payload).pipe(map((response) => response.data));
  }

  createOrderAsCompanyWithEventResponse(payload: _CRM_ORD_0) {
    let _payload: CRM_ORD_0 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }

    return this.endpointService.crm_ord_70(_payload).pipe(map((response) => response.data));
  }

  getOrderConfirmationStatuses() {
    return this.endpointService.crm_ord_44().pipe(map((data) => data.data));
  }

  getOrderStatuses() {
    return this.endpointService.crm_ord_18().pipe(map((data) => data.data));
  }

  acceptOrderAsCompany(orderId: number, sendConfirmation: boolean = false) {
    let payload: CRM_ORD_39 = {
      order_id: orderId,
      company_id: this.storageService.getSelectedCompanyId(),
      send_confirmation: sendConfirmation
    };
    return this.endpointService.crm_ord_39(payload).pipe(map((data) => data.data));
  }

  sendQuote(orderId: number, sms: boolean, email: boolean) {
    let payload: CRM_ORD_27 = {
      order_id: orderId,
      company_id: this.storageService.getSelectedCompanyId(),
      sms: sms ? 1 : 0,
      email: email ? 1 : 0
    };
    return this.endpointService.crm_ord_27(payload).pipe(map(() => {}));
  }

  addOrderLineAsCompany(payload: _CRM_ORD_12) {
    let _payload: CRM_ORD_12 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
      }
    return this.endpointService.crm_ord_12(_payload).pipe(map((data) => data.data));
  }

  addCustomProductInOrder(payload: _CRM_ORD_60) {
    let _payload: CRM_ORD_60 = {
      company_id : this.storageService.getSelectedCompanyId(),
      ...payload
    }

    return this.endpointService.crm_ord_60(_payload).pipe(map((data) => data.data));
  }

  updateOrderLineStageAddress(order_line_stage_id: number, address: UnitDetails) {
    let payload: CRM_ORD_53 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_line_stage_id: order_line_stage_id,
      address: address
    }
    return this.endpointService.crm_ord_53(payload).pipe(map((data) => data.data));
  }

  updateOrderInformation(payload: _CRM_ORD_31) {
    let _payload: CRM_ORD_31 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_31(_payload).pipe(map((data) => data.data));
  }

  updateOrderLineAsCompany(payload: _CRM_ORD_31) {
    let _payload: CRM_ORD_31 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }

    return this.endpointService.crm_ord_31(_payload).pipe(map((data) => data.data));
  }

  // updateOrderLinePriceRules(order_line_id: number, price_rule_ids: number[]) {
  //   let payload: CRM_ORD_31 = {
  //     order_line_id: order_line_id,
  //     company_id: this.storageService.getSelectedCompanyId(),
  //     price_rule_ids: price_rule_ids,
  //   }
  //   return this.endpointService.crm_ord_31(payload).pipe(map((data) => data.data));
  // }

  deleteOrderLineAsCompany(order_line_id: number) {
    let params: CRM_ORD_15 = {
      order_line_id: order_line_id,
      company_id: this.storageService.getSelectedCompanyId()
    }
    return this.endpointService.crm_ord_15(params).pipe(map(() => {}));
  }

  setOrderDiscount(payload: _CRM_ORD_13) {
    let _payload: CRM_ORD_13 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_13(_payload).pipe(map((data) => data.data));
  }

  setOrderLineTotalPrice(order_line_id: number, total_price: number) {
    let payload: CRM_ORD_69 = {
      order_line_id: order_line_id,
      company_id: this.storageService.getSelectedCompanyId(),
      total_price_inc_vat: total_price
    }
    return this.endpointService.crm_ord_69(payload).pipe(map((data) => data.data));
  }

  addOrderNoteAsCompany(order_id: number, note_text: string, internal: number) {
    let payload: CRM_ORD_19 = {
      order_id: order_id,
      company_id: this.storageService.getSelectedCompanyId(),
      note_text: note_text,
      internal: internal
    }
    return this.endpointService.crm_ord_19(payload).pipe(map((data) => data.data));
  }

  getOrderNotes(order_id: number) {
    let params: CRM_ORD_20 = {
      order_id: order_id,
      company_id: this.storageService.getSelectedCompanyId()
    }
    return this.endpointService.crm_ord_20(params).pipe(map((data) => data.data));
  }

  deleteOrderNoteAsCompany(order_note_id: number) {
    let payload: CRM_ORD_21 = {
      order_note_id: order_note_id,
      company_id: this.storageService.getSelectedCompanyId()
    }
    return this.endpointService.crm_ord_21(payload).pipe(map(() => {}));
  }

  editOrderNoteAsCompany(order_note_id: number, note_text: string) {
    let payload: CRM_ORD_22 = {
      order_note_id: order_note_id,
      note_text: note_text,
      company_id: this.storageService.getSelectedCompanyId()
    }
    return this.endpointService.crm_ord_22(payload).pipe(map(() => {}));
  }

  attachPartnerToOrder(payload: _CRM_ORD_56) {
    let _payload: CRM_ORD_56 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_56(_payload).pipe(map((data) => data.data));
  }

  addStageAddressToOrderLine(payload: _CRM_ORD_61) {
    let _payload: CRM_ORD_61 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_61(_payload).pipe(map((data) => data.data));
  }

  deleteAddress(orderLineStagedId: number) {
    let payload: CRM_ORD_64 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_line_stage_id: orderLineStagedId
    }
    return this.endpointService.crm_ord_64(payload).pipe(map((data) => {
      return data.data
    }))
  }

  calculatePreviewPrice(payload: _CRM_PRD_77): Observable<CalculatedOrderResponse> {
    let _payload: CRM_PRD_77 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_prd_77(_payload).pipe(map((data)=> data.data));
  }

  setOrderLineCrewAssignments(payload: _CRM_ORD_66) {
    let _payload: CRM_ORD_66 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_66(_payload).pipe(map((data) => data.data));
  }

  setOrderLineResourceAssignments(payload: _CRM_ORD_67) {
    let _payload: CRM_ORD_67 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_67(_payload).pipe(map((data) => data.data));
  }

  updateOrderStatus(payload: _CRM_ORD_55) {
    let _payload: CRM_ORD_55 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_55(_payload).pipe(map((data) => data.data));
  }

  archiveOrder(order_id: number) {
    let payload: CRM_ORD_68 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_id: order_id,
      archived: 1
    }
    return this.endpointService.crm_ord_68(payload).pipe(map((data) => data.data));
  }

  removeFromArchive(order_id: number) {
    let payload: CRM_ORD_68 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_id: order_id,
      archived: 0,
    }
    return this.endpointService.crm_ord_68(payload).pipe(map((data) => data.data));
  }

  cancelOrder(notify_customer: number | null, order_id: number) {
    let payload: CRM_ORD_55 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_id: order_id,
      order_status_id: 8,
      notify_customer: notify_customer
    }
    return this.endpointService.crm_ord_55(payload).pipe(map((data) => data.data));
  }

  duplicateOrder(order_id: number) {
    let payload: CRM_ORD_71 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_id: order_id,
    }
    return this.endpointService.crm_ord_71(payload).pipe(map((data) => data.data));
  }

  sendPaymentSMS(payload: _CRM_PAY_34){
    let _payload: CRM_PAY_34 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_pay_34(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  createDiscountOrderLine(payload: _CRM_ORD_73){
    let _payload: CRM_ORD_73 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_73(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  updateOrderInvoiceSettings(payload: _CRM_ORD_74) {
    let _payload: CRM_ORD_74 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_74(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  createOrderSchedule(payload: _CRM_ORD_76) {
    let _payload: CRM_ORD_76 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_76(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  deleteOrderSchedule(scheduleId: number, cancelFutureOrders: boolean) {
    let params: CRM_ORD_77 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_schedule_id: scheduleId,
      cancel_future_orders: cancelFutureOrders ? 1 : 0
    }
    return this.endpointService.crm_ord_77(params).pipe(map((data) => {
      return data
    }))
  }

  getOrderScheduleById(scheduleId: number) {
    let params: CRM_ORD_78 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_schedule_id: scheduleId
    }
    return this.endpointService.crm_ord_78(params).pipe(map((data) => {
      return data.data
    }))
  }

  updateOrderSchedule(payload: _CRM_ORD_79) {
    let _payload: CRM_ORD_79 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_79(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  getOrderSchedules(params: _CRM_ORD_80) {
    let _params: CRM_ORD_80 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...params
    }
    return this.endpointService.crm_ord_80(_params).pipe(map((data) => {
      return data
    }))
  }

  createOrderScheduleOrderLine(payload: _CRM_ORD_81) {
    let _payload: CRM_ORD_81 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_81(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  updateOrderScheduleOrderLine(payload: _CRM_ORD_82) {
    let _payload: CRM_ORD_82 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_82(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  deleteOrderScheduleOrderLine(params: _CRM_ORD_83) {
    let _params: CRM_ORD_83 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...params
    }
    return this.endpointService.crm_ord_83(_params).pipe(map((data) => {
      return data.data
    }))
  }

  getScheduleRepeatTypes() {
    return this.endpointService.crm_ord_84().pipe(map((data) => {
      return data.data
    }));
  }

  getScheduleExecutionPreview(payload: _CRM_ORD_85) {
    let _payload: CRM_ORD_85 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_85(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  changeOrderCustomer(payload: _CRM_ORD_86) {
    let _payload: CRM_ORD_86 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_86(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  changeOrderCustomerSetup(payload: _CRM_ORD_103) {
    let _payload: CRM_ORD_103 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_103(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  changeOrderServiceRecipient(order_id: number, affiliate_id: number) {
    let payload: CRM_ORD_104 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_id: order_id,
      affiliate_id: affiliate_id
    }
    return this.endpointService.crm_ord_104(payload).pipe(map((data) => {
      return data.data
    }))
  }

  setAffiliateContactForOrder(order_id: number, affiliate_contact_id: number) {
    let _payload: CRM_ORD_103 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_id: order_id,
      affiliate_contact_id: affiliate_contact_id,
    }
    return this.endpointService.crm_ord_103(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  getTimelineLogs(payload: _CRM_ORD_87){
    let _payload: CRM_ORD_87 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_87(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  getTimelineLogTypes(){
    return this.endpointService.crm_ord_88().pipe(map((data) => {
      return data.data
    }));
  }

  acceptOrderSchedule(scheduleId: number, acceptForCustomer: boolean = false) {
    let payload: CRM_ORD_90 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_schedule_id: scheduleId,
      for_customer: acceptForCustomer ? 1 : 0
    }
    return this.endpointService.crm_ord_90(payload).pipe(map((data) => {
      return data.data
    }))
  }

  sendScheduleQuote(scheduleId: number, sms: boolean, email: boolean) {
    let payload: CRM_ORD_91 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_schedule_id: scheduleId,
      sms: sms ? 1 : 0,
      email: email ? 1 : 0
    }
    return this.endpointService.crm_ord_91(payload).pipe(map((data) => {
      return data.data
    }))
  }

  forceOrderScheduleOrderCreation(scheduleId: number) {
    let payload: CRM_ORD_93 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_schedule_id: scheduleId
    }
    return this.endpointService.crm_ord_93(payload).pipe(map((data) => {
    }))
  }

  setOrderScheduleActiveStatus(scheduleId: number, active: boolean, send_customer_confirmation: number) {
    let payload: CRM_ORD_95 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_schedule_id: scheduleId,
      active: active ? 1 : 0,
      send_customer_confirmation: send_customer_confirmation
    }
    return this.endpointService.crm_ord_95(payload).pipe(map((data) => {
      return data.data
    }))
  }

  setOrderSchedulePaymentMethod(scheduleId: number, paymentMethodId: number) {
    let payload: CRM_ORD_96 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_schedule_id: scheduleId,
      payment_method_id: paymentMethodId
    }
    return this.endpointService.crm_ord_96(payload).pipe(map((data) => {
      return data.data
    }))
  }

  patchOrder(payload: _CRM_ORD_97) {
    let _payload: CRM_ORD_97 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_97(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  addOrderAttachment(attachment: File, orderId: number) {
    let payload: CRM_ORD_98 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_id: orderId,
      attachment: attachment
    }
    return this.endpointService.crm_ord_98(payload).pipe(map((data) => {
      return data.data
    }))
  }

  addWorkOrderAttachment(attachment: File, workOrderId: number) {
    let payload: CRM_ORD_142 = {
      company_id: this.storageService.getSelectedCompanyId(),
      work_order_id: workOrderId,
      attachment: attachment
    }
    return this.endpointService.crm_ord_142(payload).pipe(map((data) => {
      return data.data
    }))
  }

  updateOrderAttachment(payload: _CRM_ORD_110) {
    let _payload: CRM_ORD_110 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_110(_payload).pipe(map((data) => {
      return data.data
    }))
  }


  deleteOrderAttachment(attachmentId: number) {
    let payload: CRM_ORD_99 = {
      company_id: this.storageService.getSelectedCompanyId(),
      attachment_id: attachmentId
    }
    return this.endpointService.crm_ord_99(payload).pipe(map((data) => {
      return data
    }))
  }

  getAttachmentFile(attachmentId: number, orderId: number) {
    let payload: CRM_ORD_100 = {
      company_id: this.storageService.getSelectedCompanyId(),
      attachment_id: attachmentId,
      order_id: orderId
    }
    return this.endpointService.crm_ord_100(payload).pipe(map((data) => {
      return data
    }))
  }

  sendOrderToSubContractors(params: _CRM_ORD_101) {
    let _params: CRM_ORD_101 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...params
    }
    return this.endpointService.crm_ord_101(_params).pipe(map((data) => {
      return data.data
    }))
  }

  removeSubContractorFromOrder(order_id: number, affiliate_id: number) {
    let params: CRM_ORD_102 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_id: order_id,
      affiliate_id: affiliate_id
    }
    return this.endpointService.crm_ord_102(params).pipe(map((data) => {
      return;
    }))
  }

  sendOrderConfirmation(orderId: number) {
    let payload: CRM_ORD_107 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_id: orderId
    }
    return this.endpointService.crm_ord_107(payload).pipe(map((data) => {
      return data.data
    }))
  }

  getVatRates() {
    return this.endpointService.crm_pay_11().pipe(map((data)=> data.data));
  }

  capturePaymentManually(paymentId: number) {
    let payload: CRM_PAY_24 = {
      company_id: this.storageService.getSelectedCompanyId(),
      payment_id: paymentId
    }
    return this.endpointService.crm_pay_24(payload).pipe(map((data) => data.data));
  }

  assignUserToWorkOrder(payload: _CRM_ORD_113) {
    let _payload: CRM_ORD_113 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_113(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  removeUserFromWorkOrder(params: _CRM_ORD_114) {
    let _params: CRM_ORD_114 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...params
    }
    return this.endpointService.crm_ord_114(_params).pipe(map((data) => {
      return data.data
    }))
  }

  assignResourceToWorkOrder(payload: _CRM_ORD_115) {
    let _payload: CRM_ORD_115 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_115(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  removeResourceFromWorkOrder(params: _CRM_ORD_116) {
    let _params: CRM_ORD_116 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...params
    }
    return this.endpointService.crm_ord_116(_params).pipe(map((data) => {
      return data.data
    }))
  }

  createWorkOrder(payload: _CRM_ORD_117) {
    if (!payload.execution_at && payload.execution_to) {
      throw new Error('execution_at must be provided if execution_to is to be set')
    }

    let _payload: CRM_ORD_117 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_117(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  patchWorkOrder(payload: _CRM_ORD_118) {
    let _payload: CRM_ORD_118 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_118(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  deleteWorkOrder(params: _CRM_ORD_119) {
    let _params: CRM_ORD_119 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...params
    }
    return this.endpointService.crm_ord_119(_params).pipe(map((data) => {
      return data
    }))
  }

  createOrderDraft(payload: _CRM_ORD_120) {
    let _payload: CRM_ORD_120 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_120(_payload).pipe(map((data) => {
      return data.data
    }));
  }

  initiateOrderDraft(orderId: number) {
    let payload: CRM_ORD_121 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_id: orderId
    }
    return this.endpointService.crm_ord_121(payload).pipe(map((data) => {
      return data.data
    }));
  }

  createWorkOrderSchedule(payload: _CRM_ORD_122) {
    let _payload: CRM_ORD_122 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_122(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  updateWorkOrderSchedule(payload: _CRM_ORD_123) {
    let _payload: CRM_ORD_123 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_123(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  deleteWorkOrderSchedule(scheduleId: number) {
    let params: CRM_ORD_123 = {
      company_id: this.storageService.getSelectedCompanyId(),
      work_order_schedule_id: scheduleId
    }
    return this.endpointService.crm_ord_123(params).pipe(map((data) => {
      return data.data
    }))
  }

  initiateWorkOrderCreationForSchedule(scheduleId: number) {
    let payload: CRM_ORD_125 = {
      company_id: this.storageService.getSelectedCompanyId(),
      work_order_schedule_id: scheduleId
    }
    return this.endpointService.crm_ord_125(payload).pipe(map((data) => {
      return data.data
    }))
  }

  assignTaskTemplateToWorkOrder(payload: _CRM_ORD_126) {
    let _payload: CRM_ORD_126 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_126(_payload).pipe(map((data) => {
      return data.data
    }))
  }


  createWorkOrderTask(payload: _CRM_ORD_127) {
    let _payload: CRM_ORD_127 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_127(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  editWorkOrderTask(payload: _CRM_ORD_128) {
    let _payload: CRM_ORD_128 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_128(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  deleteWorkOrderTask(payload: _CRM_ORD_129) {
    let _payload: CRM_ORD_129 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_129(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  createWorkOrderTaskGroup(payload: _CRM_ORD_130) {
    let _payload: CRM_ORD_130 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_130(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  editWorkOrderTaskGroup(payload: _CRM_ORD_131) {
    let _payload: CRM_ORD_131 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_131(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  deleteTaskGroup(payload: _CRM_ORD_132) {
    let _payload: CRM_ORD_132 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_132(_payload).pipe(map((data) => {
      return data.data
    }))
  }


  assignCustomerQuestionsTemplateToOrder(payload: _CRM_ORD_133) {
    let _payload: CRM_ORD_133 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_133(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  createCustomerQuestion(payload: _CRM_ORD_134) {
    let _payload: CRM_ORD_134 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_134(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  updateCustomerQuestiom(payload: _CRM_ORD_135){
    let _payload: CRM_ORD_135 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_135(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  deleteCustomerQuestion(payload: _CRM_ORD_136) {
    let _payload: CRM_ORD_136 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_136(_payload).pipe(map((data) => {
      return data.data
    }))
  }


  getOrderPayments(orderId: number, template: boolean = false) {
    let payload: CRM_PAY_30 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_id: orderId,
      template: template
    }
    return this.endpointService.crm_pay_30(payload).pipe(map((data) => {
      return data.data
    }))
  }

  getOrderRefundPayments(orderId: number) {
    let payload: CRM_PAY_30 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_id: orderId,
      refund: true
    }
    return this.endpointService.crm_pay_30(payload).pipe(map((data) => {
      return data.data
    }))
  }

  getOrderLineDraft(payload: _CRM_ORD_12) {
    let _payload: CRM_ORD_12 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_140(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  getWorkOrderStatuses() {
    return this.endpointService.crm_ord_141().pipe(map((data) => {
      return data.data
    }))
  }

  getWorkOrderById(workOrderId: number) {
    let payload: CRM_ORD_143 = {
      company_id: this.storageService.getSelectedCompanyId(),
      work_order_id: workOrderId
    }
    return this.endpointService.crm_ord_143(payload).pipe(map((data) => {
      return data.data
    }))
  }

  addAddressToMultipleWorkOrders(payload: _CRM_ORD_144) {
    let _payload: CRM_ORD_144 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_144(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  getScheduleWorkOrders(params: _CRM_ORD_153) {
    let _params: CRM_ORD_153 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...params
    }
    return this.endpointService.crm_ord_153(_params).pipe(map((data) => {
      return data
    }))
  }

  duplicateWorkOrder(payload: _CRM_ORD_154) {
    let _payload: CRM_ORD_154 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_154(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  advancedSearch(params: _CRM_ORD_165) {
    let _params: CRM_ORD_165 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...params
    }
    return this.endpointService.crm_ord_165(_params).pipe(map((data) => {
      return data
    }))
  }

  getWorkOrdersAsSubContractor(params: _CRM_ORD_168) {
    let _params: CRM_ORD_168 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...params
    }
    return this.endpointService.crm_ord_168(_params).pipe(map((data) => {
      return data
    }))
  }

  updateWorkOrderProposalAsSubContractor(payload: _CRM_ORD_169) {
    let _payload: CRM_ORD_169 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_169(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  deleteContract(workOrderId: number) {
    let params: CRM_ORD_172 = {
      company_id: this.storageService.getSelectedCompanyId(),
      work_order_id: workOrderId
    }
    return this.endpointService.crm_ord_172(params).pipe(map((data) => {
      return data.data
    }))
  }

  getCompanyWorkOrders(params: _CRM_ORD_170) {
    let _params: CRM_ORD_170 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...params
    }
    return this.endpointService.crm_ord_170(_params).pipe(map((data) => {
      return data
    }))
  }

  getCompanyWorkOrdersWithFullResponse(params: _CRM_ORD_170) {
    let _params: CRM_ORD_170 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...params
    }
    return this.endpointService.crm_ord_185(_params).pipe(map((data) => {
      return data
    }))
  }

  selectoriniWorkOrderSearch(params: _CRM_ORD_170): Observable<WorkOrderCompactResponse[]> {
    let _params: CRM_ORD_170 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...params
    }
    return this.endpointService.crm_ord_170(_params).pipe(map((response) => {
      return response.data.map((workOrder) => {
        return {
          ...workOrder,
          work_order_number: '#' + workOrder.work_order_number
        }
      });

    }, (error: any) => {
      return [];
    }));
  }

  getOrderServiceRecipient(order_id: number) {
    let params: CRM_ORD_167 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_id: order_id
    }
    return this.endpointService.crm_ord_167(params).pipe(map((data) => {
      return data.data
    }))
  }

  finishWorkOrder(workOrderId: number) {
    let payload: CRM_ORD_150 = {
      company_id: this.storageService.getSelectedCompanyId(),
      work_order_id: workOrderId
    }
    return this.endpointService.crm_ord_150(payload).pipe(map((data) => {
      return data.data
    }))
  }

  updateCustomerQuestionChoiceAsCompany(payload: _CRM_ORD_151) {
    let _payload: CRM_ORD_151 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_151(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  updateWorkOrderAddress(payload: _CRM_ORD_177) {
    let _payload: CRM_ORD_177 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...payload
    }
    return this.endpointService.crm_ord_177(_payload).pipe(map((data) => {
      return data.data
    }))
  }

  updateWorkOrderScheduleFutureChildrenChecklists(workOrderId: number) {
    let payload: CRM_ORD_178 = {
      company_id: this.storageService.getSelectedCompanyId(),
      work_order_id: workOrderId
    }
    return this.endpointService.crm_ord_178(payload).pipe(map((data) => {
      return data
    }))
  }

  getWorkOrderQuantityProposal(workOrderId: number) {
    let params: CRM_ORD_171 = {
      company_id: this.storageService.getSelectedCompanyId(),
      work_order_id: workOrderId
    }
    return this.endpointService.crm_ord_171(params).pipe(map((data) => {
      return data.data
    }))
  }

  getOrderLinesForOrder(orderId: number) {
    let params: CRM_ORD_162 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_id: orderId
    }
    return this.endpointService.crm_ord_162(params).pipe(map((data) => {
      return data.data
    }))
  }


  // --------------------------------------- | Declarations | ---------------------------------------
  private orderSource: BehaviorSubject<OrderResponse> = new BehaviorSubject<any>(null)
  order$: Observable<OrderResponse> = this.orderSource.asObservable()
  private orderLogs = new BehaviorSubject<OrderLogResponse[]>([]);
  orderLogs$ = this.orderLogs.asObservable();

  private workOrderSource: BehaviorSubject<WorkOrderResponse | undefined> = new BehaviorSubject<any>(undefined)
  workOrder$: Observable<WorkOrderResponse | undefined> = this.workOrderSource.asObservable().pipe(filter((value) => value !== undefined))

  public refreshWorkOrders$: Subject<void> = new Subject<void>()

  private workOrderSchedulesSource: BehaviorSubject<WorkOrderResponse[]> = new BehaviorSubject<WorkOrderResponse[]>([]);
  workOrderSchedules$: Observable<WorkOrderResponse[]> = this.workOrderSchedulesSource.asObservable();

  private workOrdersSource: BehaviorSubject<WorkOrderResponse[]> = new BehaviorSubject<WorkOrderResponse[]>([]);
  workOrders$: Observable<WorkOrderResponse[]> = this.workOrdersSource.asObservable();

  private orderPaymentsSource: BehaviorSubject<OrderPaymentResponse[]> = new BehaviorSubject<OrderPaymentResponse[]>([]);
  orderPayments$: Observable<OrderPaymentResponse[]> = this.orderPaymentsSource.asObservable();

  private orderPaymentSchedulesSource: BehaviorSubject<OrderPaymentResponse[]> = new BehaviorSubject<OrderPaymentResponse[]>([]);
  orderPaymentSchedules$: Observable<OrderPaymentResponse[]> = this.orderPaymentSchedulesSource.asObservable();

  private orderRefundPaymentsSource: BehaviorSubject<OrderPaymentResponse[]> = new BehaviorSubject<OrderPaymentResponse[]>([]);
  orderRefundPayments$: Observable<OrderPaymentResponse[]> = this.orderRefundPaymentsSource.asObservable();

  private orderLinesSource: BehaviorSubject<OrderLineResponse[]> = new BehaviorSubject<OrderLineResponse[]>([]);
  orderLines$: Observable<OrderLineResponse[]> = this.orderLinesSource.asObservable();

  // --------------------------------------- | Refresh | ---------------------------------------

  // ------ Order ------
  refreshOrder(order: OrderResponse, source: string){
    this.setOrder(order, source)
  }

  fetchAndRefreshOrder(orderId: number, source: string, fetchPayments: boolean = true){
    if (!orderId) {
      return;
    }

    let payload: CRM_ORD_10 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_id: orderId
    }
    this.getOrder(payload).subscribe((data) => {
      this.setOrder(data, source)
      if (fetchPayments) {
        this.fetchAndRefreshOrderPayments(orderId)
        this.fetchAndRefreshOrderPaymentSchedules(orderId)
      }
    })
  }

  refreshOrderLogs(orderId: number){
    if (!orderId) return;
    let params: CRM_ORD_87 = {
      company_id: this.storageService.getSelectedCompanyId(),
      order_id: orderId
    }
    this.getTimelineLogs(params).subscribe((data) => {
      this.orderLogs.next(data)
    });
  }

  removeAttachmentInOrderSubject(attachmentId: number) {
    if (this.orderSource.value) {
      let order: OrderResponse = this.orderSource.value;
      order.attachments = order.attachments.filter((attachment) => attachment.attachment_id !== attachmentId);
      this.orderSource.next(order);
    }
  }

  // ------ Order Lines ------
  refreshOrderLines(orderLines: OrderLineResponse[], source: string) {
    this.orderLinesSource.next(orderLines);
  }

  fetchAndRefreshOrderLines(orderId: number, source: string) {
    if (!orderId) {
      return;
    }

    this.getOrderLinesForOrder(orderId).subscribe((data) => {
      this.refreshOrderLines(data, 'orderService.fetchAndRefreshOrderLines');
    });
  }

  refreshOrderLineInOrderLines(orderLine: OrderLineResponse, operation: 'post' | 'put' | 'delete') {
    if (this.orderLinesSource.value) {
      let orderLines = this.orderLinesSource.value;
      if (operation == 'post') {
        orderLines.push(orderLine);
      } else if (operation == 'put') {
        let orderLineMatch = false;
        orderLines = orderLines.map((ol) => {
          if (ol.order_line_id === orderLine.order_line_id) {
            orderLineMatch = true;
            return orderLine;
          }
          return ol;
        });

        if (!orderLineMatch) {
          orderLines.push(orderLine);
        }
      } else if (operation == 'delete') {
        orderLines = orderLines.filter((ol) => ol.order_line_id !== orderLine.order_line_id);
      }
      this.refreshOrderLines(orderLines, 'orderService.refreshOrderLineInOrderLines');
    }
  }

  // ------ Payments ------
  fetchAndRefreshOrderPayments(orderId: number){
    this.getOrderPayments(orderId).subscribe((data) => {
      this.orderPaymentsSource.next(data)
    });
  }

  fetchAndRefreshOrderRefundPayments(orderId: number){
    this.getOrderRefundPayments(orderId).subscribe((data) => {
      this.orderRefundPaymentsSource.next(data)
    });
  }

  fetchAndRefreshOrderPaymentSchedules(orderId: number){
    this.getOrderPayments(orderId, true).subscribe((data) => {
      this.orderPaymentSchedulesSource.next(data)
    });
  }

  // ------ Work Orders ------
  refreshSingleWorkOrder(workOrder: WorkOrderResponse, source: string){
    this.workOrderSource.next(workOrder)
    this.refreshSingleWorkOrderInWorkOrders(workOrder, source);
  }

  refreshSingleWorkOrderInWorkOrders(workOrder: WorkOrderResponse, source: string) {
    // Determine which subject to use based on whether the work order is a schedule template
    let woSourceSubject: BehaviorSubject<WorkOrderResponse[]>;
    if (workOrder.schedule_template) {
      woSourceSubject = this.workOrderSchedulesSource;
    } else {
      woSourceSubject = this.workOrdersSource;
    }
    let workOrdersFromSubject = this.workOrdersSource.value;

    // Check if the work order exists in the current subject. If it does, update it
    let match = false;
    workOrdersFromSubject = workOrdersFromSubject.map((wo) => {
      if (wo.work_order_id === workOrder.work_order_id) {
        match = true;
        return workOrder;
      }
      return wo;
    });

    // If the work order exists in the current subject, update the subject with the modified array
    if (match) {
      woSourceSubject.next(workOrdersFromSubject);
    }
  }

  fetchAndRefreshSingleWorkOrder(workOrderId: number, source: string){
    this.getWorkOrderById(workOrderId).subscribe((data) => {
      this.refreshSingleWorkOrder(data, 'fetchAndUpdateWorkOrder')
      this.refreshSingleWorkOrderInWorkOrders(data, 'fetchAndUpdateWorkOrder')
    })
  }

  refreshWorkOrders(workOrders: WorkOrderResponse[], source: string) {
    this.workOrdersSource.next(workOrders);
  }

  fetchAndRefreshWorkOrders(orderId: number, source: string) {
    this.getCompanyWorkOrdersWithFullResponse({order_id: orderId}).subscribe((data) => {
      this.refreshWorkOrders(data.data, source);
    });
  }

  destroyWorkOrder(source: string){
    this.workOrderSource.next(undefined)
  }

  // ------ Work Order Schedules ------
  refreshWorkOrderSchedules(workOrders: WorkOrderResponse[], source: string) {
    this.workOrderSchedulesSource.next(workOrders);
  }

// --------------------------------------- | Setters | ---------------------------------------

  setOrder(value: OrderResponse, source: string){
    if (value.company_id && value.company_id != this.storageService.getSelectedCompanyId()) {
      value.contractorView = true;
    }
    value.source_tag = source;
    this.orderSource.next(value);
  }



}
