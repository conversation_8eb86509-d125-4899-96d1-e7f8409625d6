import { LoginResponse } from "../models/authentication.interfaces";
import { HttpClient, HttpErrorResponse, HttpHeaders, HttpParams } from "@angular/common/http";
import { JwtService } from "../../@core/services/jwt.service";
import {InternalUserResponse, InvitationTokenVerificationResponse, RoleResponse, UserEntityRelationWithoutUserDataResponse, UserEntityRelationWithUserDataResponse, UserOptionsResponse, UserResponse, UserSimpleResponse} from "../models/user.interfaces";
import { DeleteResponse, GetResponse, PaginationResponse, PostResponse, PutResponse } from "../models/response.interfaces";
import {
  CRM_ADR_1,
  CRM_ADR_3,
  CRM_ADR_4,
  CRM_ADR_5,
  CRM_ADR_6,
  CRM_ADR_7,
  CRM_ADR_8,
  CRM_ADR_9,
  CRM_COY_0,
  CRM_COY_1,
  CRM_COY_10,
  CRM_COY_11,
  CRM_COY_12,
  CRM_COY_15,
  CRM_COY_16,
  CRM_COY_17,
  CRM_COY_19,
  CRM_COY_2,
  CRM_COY_20,
  CRM_COY_21,
  CRM_COY_22,
  CRM_COY_23,
  CRM_COY_25,
  CRM_COY_26,
  CRM_COY_3,
  CRM_COY_4,
  CRM_COY_5,
  CRM_COY_6,
  CRM_COY_7,
  CRM_COY_8,
  CRM_COY_9,
  CRM_CUS_0,
  CRM_CUS_1,
  CRM_CUS_14,
  CRM_EMP_0,
  CRM_EMP_1,
  CRM_EMP_2,
  CRM_EMP_4,
  CRM_EMP_5,
  CRM_EMP_6,
  CRM_INT_11,
  CRM_INT_12,
  CRM_INT_13,
  CRM_INT_14,
  CRM_INT_7,
  CRM_INT_8,
  CRM_ORD_0,
  CRM_ORD_10,
  CRM_ORD_12,
  CRM_ORD_13,
  CRM_ORD_15,
  CRM_ORD_19,
  CRM_ORD_2,
  CRM_ORD_21,
  CRM_ORD_22,
  CRM_ORD_27,
  CRM_ORD_31,
  CRM_ORD_39,
  CRM_ORD_4,
  CRM_ORD_5,
  CRM_ORD_53,
  CRM_ORD_56,
  CRM_ORD_60,
  CRM_ORD_64,
  CRM_ORD_61,
  CRM_ORD_7,
  CRM_ORD_8,
  CRM_PAY_2,
  CRM_PAY_6,
  CRM_PAY_7,
  CRM_PAY_8,
  CRM_PCG_0,
  CRM_PCG_1,
  CRM_PCG_2,
  CRM_PRD_0,
  CRM_PRD_1,
  CRM_PRD_11,
  CRM_PRD_12,
  CRM_PRD_13,
  CRM_PRD_14,
  CRM_PRD_15,
  CRM_PRD_16,
  CRM_PRD_17,
  CRM_PRD_18,
  CRM_PRD_2,
  CRM_PRD_29,
  CRM_PRD_3,
  CRM_PRD_30,
  CRM_PRD_32,
  CRM_PRD_35,
  CRM_PRD_36,
  CRM_PRD_37,
  CRM_PRD_38,
  CRM_TMP_13,
  CRM_TMP_14,
  CRM_TMP_15,
  CRM_TMP_16,
  CRM_TMP_17,
  CRM_TMP_18,
  CRM_PRD_45,
  CRM_PRD_46,
  CRM_PRD_47,
  CRM_PRD_48,
  CRM_PRD_49,
  CRM_PRD_50,
  CRM_PRD_51,
  CRM_PRD_52,
  CRM_PRD_53,
  CRM_PRD_54,
  CRM_PRD_55,
  CRM_PRD_56,
  CRM_PRD_57,
  CRM_PRD_59,
  CRM_PRD_60,
  CRM_PRD_61,
  CRM_PRD_62,
  CRM_PRD_64,
  CRM_PRD_65,
  CRM_PRD_66,
  CRM_PRD_67,
  CRM_PRD_68,
  CRM_PRD_69,
  CRM_REP_0,
  CRM_SER_8,
  CRM_STG_10,
  CRM_STG_11,
  CRM_STG_12,
  CRM_STG_15,
  CRM_STG_16,
  CRM_STG_4,
  CRM_STG_5,
  CRM_STG_6,
  CRM_STG_7,
  GEO_ADR_0,
  USM_ENT_0,
  USM_ENT_1,
  USM_ENT_2,
  USM_ENT_3,
  USM_USR_0,
  USM_USR_10,
  USM_USR_13,
  USM_USR_14,
  USM_USR_16,
  USM_USR_17,
  USM_USR_18,
  USM_USR_19,
  USM_USR_2,
  USM_USR_20,
  USM_USR_22,
  USM_USR_6,
  USM_USR_7,
  USM_USR_8,
  USM_USR_9,
  CRM_CUS_15,
  CRM_CUS_12,
  CRM_EMP_7,
  CRM_EMP_8,
  CRM_EMP_9,
  CRM_EMP_10,
  CRM_EMP_11,
  CRM_EMP_12,
  CRM_EMP_13,
  CRM_EVT_0,
  CRM_EVT_1,
  CRM_EVT_2,
  CRM_EVT_3,
  CRM_EVT_5,
  CRM_EVT_6,
  CRM_EVT_7,
  CRM_RSC_0,
  CRM_RSC_1,
  CRM_ORD_55,
  CRM_RSC_2,
  CRM_RSC_3,
  CRM_RSC_5,
  CRM_SER_12,
  USM_USR_24,
  CRM_PRD_77,
  CRM_RSC_6,
  CRM_ORD_66,
  CRM_ORD_67,
  CRM_PAY_31,
  CRM_INT_16,
  CRM_INT_17,
  CRM_INT_18,
  CRM_EVT_8,
  CRM_EVT_9,
  CRM_ORD_68,
  CRM_SER_13,
  USM_USR_25,
  CRM_EVT_12,
  CRM_PRD_78,
  CRM_ORD_69,
  CRM_ORD_71,
  CRM_PAY_34,
  CRM_ORD_73,
  UnitDetails,
  CRM_PAY_15,
  CRM_PAY_16,
  CRM_SER_15,
  CRM_ORD_74,
  CRM_EMB_0,
  CRM_EMB_1,
  CRM_EMB_2,
  CRM_EMB_3,
  CRM_EMB_4,
  CRM_EMB_5,
  CRM_EMB_6,
  CRM_EMB_7,
  CRM_EMB_8,
  CRM_EMB_9,
  CRM_EMB_10,
  CRM_EMB_11,
  CRM_EMB_12,
  CRM_EMB_13,
  CRM_COY_27,
  CRM_EVT_17,
  CRM_PAY_33,
  CRM_ORD_76,
  CRM_ORD_85,
  CRM_ORD_80,
  CRM_ORD_77,
  CRM_ORD_78,
  CRM_ORD_79,
  CRM_ORD_81,
  CRM_ORD_82,
  CRM_ORD_83,
  CRM_PRD_82,
  CRM_PRD_83,
  CRM_AFF_0,
  CRM_AFF_1,
  CRM_AFF_2,
  CRM_AFF_3,
  CRM_AFF_4,
  CRM_AFF_5,
  CRM_AFF_6,
  CRM_AFF_7,
  CRM_AFF_8,
  CRM_PRD_85,
  CRM_ORD_86,
  CRM_ORD_87,
  CRM_APP_0,
  CRM_APP_1,
  CRM_APP_2,
  CRM_COY_31,
  CRM_COY_32,
  CRM_PRD_86,
  CRM_EMB_14,
  CRM_EMB_15,
  CRM_EMB_16,
  CRM_EMB_17,
  CRM_ORD_90,
  CRM_ORD_91,
  CRM_ADR_10,
  CRM_ORD_93,
  CRM_ORD_95,
  CRM_ORD_96,
  CRM_ORD_97,
  CRM_CUS_16,
  CRM_TTR_0,
  CRM_TTR_1,
  CRM_TTR_2,
  CRM_TTR_3,
  CRM_TTR_4,
  CRM_TTR_5,
  CRM_TTR_6,
  CRM_TTR_7,
  CRM_TTR_8,
  CRM_RSC_7,
  REP_TMP_0,
  REP_TMP_1,
  REP_TMP_2,
  REP_TMP_3,
  REP_TMP_4,
  REP_TMP_5,
  REP_ELM_0,
  REP_ELM_1,
  REP_ELM_2,
  REP_ELM_3,
  REP_ELM_4,
  REP_ELM_5,
  REP_ELM_6,
  REP_ELM_7,
  REP_ELM_8,
  REP_ELM_9,
  REP_ELM_10,
  REP_ELM_11,
  REP_ELM_12,
  REP_TMP_6,
  REP_OBJ_2,
  REP_OBJ_0,
  CRM_ORD_99,
  CRM_ORD_101,
  CRM_ORD_102,
  CRM_ORD_103,
  CRM_ORD_104,
  CRM_ORD_107,
  CRM_ORD_98,
  CRM_PRD_84,
  CRM_PRD_87,
  CRM_PRD_88,
  CRM_PAY_23,
  CRM_AFF_9,
  CRM_CUS_19,
  CRM_ADR_12,
  REP_ELM_17,
  REP_ELM_18,
  REP_ELM_19,
  REP_ELM_20,
  REP_ELM_21,
  REP_ELM_22,
  REP_TMP_7,
  REP_TMP_8,
  REP_TMP_9,
  REP_REP_0,
  REP_REP_1,
  REP_REP_2,
  REP_REP_3,
  REP_REP_4,
  REP_REP_5,
  REP_REP_6,
  REP_REP_7,
  REP_REP_8,
  REP_REP_9,
  REP_REP_10,
  REP_REP_11,
  REP_REP_12,
  REP_REP_13,
  REP_REP_16,
  CRM_PAY_24,
  REP_REP_18,
  CRM_EVT_20,
  REP_REP_19,
  CRM_PAY_25,
  CRM_ORD_110,
  REP_REP_14,
  REP_REP_17,
  REP_REP_20,
  REP_REP_21,
  REP_REP_22,
  REP_REP_23,
  REP_REP_25,
  REP_REP_24,
  REP_REP_26,
  REP_REP_27,
  REP_REP_28,
  REP_REP_29,
  REP_REP_30,
  REP_REP_31,
  REP_REP_32,
  REP_REP_33,
  REP_REP_34,
  REP_REP_35,
  REP_REP_36,
  REP_REP_37,
  CRM_ORD_113,
  CRM_ORD_114,
  CRM_ORD_115,
  CRM_ORD_116,
  CRM_ORD_117,
  CRM_ORD_118,
  CRM_ORD_119,
  CRM_ORD_120,
  CRM_ORD_121,
  CRM_ORD_122,
  CRM_ORD_123,
  CRM_ORD_124,
  CRM_ORD_125,
  CRM_TMP_19,
  CRM_TMP_20,
  CRM_TMP_21,
  CRM_TMP_22,
  CRM_TMP_23,
  CRM_TMP_0,
  CRM_TMP_1,
  CRM_TMP_2,
  CRM_TMP_3,
  CRM_TMP_4,
  CRM_TMP_5,
  CRM_TMP_6,
  CRM_TMP_7,
  CRM_TMP_8,
  CRM_TMP_9,
  CRM_TMP_10,
  CRM_TMP_11,
  CRM_TMP_12,
  CRM_TMP_24,
  CRM_ORD_126,
  CRM_ORD_127,
  CRM_ORD_128,
  CRM_ORD_129,
  CRM_ORD_130,
  CRM_ORD_131,
  CRM_ORD_132,
  CRM_ORD_133,
  CRM_ORD_134,
  CRM_ORD_135,
  CRM_ORD_136,
  CRM_ORD_137,
  CRM_ORD_138,
  CRM_ORD_139,
  CRM_PAY_26,
  CRM_PAY_27,
  CRM_PAY_29,
  CRM_PAY_28,
  CRM_PAY_30,
  CRM_PAY_36,
  CRM_PAY_37,
  CRM_ORD_142,
  CRM_ORD_143,
  CRM_ORD_144,
  CRM_TMP_25,
  CRM_TMP_26,
  CRM_TMP_27,
  CRM_TMP_28,
  CRM_TMP_29,
  CRM_TMP_30,
  CRM_ORD_153,
  CRM_ORD_154,
  CRM_TMP_31,
  CRM_PAY_38,
  CRM_SER_32,
  REP_REP_39,
  REP_REP_40,
  REP_REP_41,
  REP_REP_42,
  REP_REP_43,
  REP_REP_44,
  REP_REP_45,
  REP_REP_46,
  REP_REP_47,
  REP_REP_48,
  REP_REP_49,
  REP_REP_50,
  REP_REP_51,
  REP_REP_52,
  REP_REP_53,
  REP_REP_54,
  REP_REP_38,
  CRM_TMP_34,
  CRM_TMP_35,
  CRM_TMP_36,
  CRM_TMP_37,
  CRM_TMP_38,
  CRM_TMP_39,
  CRM_ORD_163,
  CRM_ORD_164,
  CRM_ORD_165,
  CRM_ORD_168,
  CRM_ORD_169,
  CRM_ORD_170,
  CRM_ORD_20,
  CRM_PAY_41,
  CRM_PAY_42,
  CRM_PAY_43,
  CRM_PRD_90,
  CRM_PAY_44,
  CRM_AFF_10,
  CRM_AFF_11,
  CRM_ORD_167,
  CRM_ORD_172,
  CRM_ORD_150,
  CRM_ORD_151,
  CRM_PAY_46,
  CRM_INT_19,
  CRM_INT_20,
  CRM_INT_21,
  CRM_INT_22,
  CRM_ORD_177,
  REP_TMP_10,
  REP_TMP_11,
  REP_TMP_12,
  REP_TMP_13,
  USM_USR_26,
  CRM_NOT_0,
  CRM_NOT_1,
  CRM_TTR_10,
  CRM_TTR_11,
  CRM_TTR_12,
  CRM_TTR_13,
  CRM_TTR_14,
  CRM_TTR_15,
  CRM_TTR_16,
  CRM_TTR_17,
  CRM_TTR_18,
  CRM_TTR_19,
  CRM_TTR_20,
  CRM_TTR_21,
  CRM_TTR_22,
  CRM_TTR_23,
  CRM_TTR_24,
  CRM_TTR_25,
  CRM_TTR_26,
  CRM_TTR_27,
  CRM_TTR_28,
  CRM_TTR_29,
  CRM_TTR_30,
  CRM_TTR_31,
  CRM_TTR_32,
  CRM_TTR_33,
  CRM_TTR_35,
  CRM_TTR_36,
  CRM_TTR_37,
  CRM_TTR_38,
  CRM_TTR_39,
  CRM_TTR_40,
  CRM_TTR_41,
  CRM_EMP_14,
  CRM_INT_29,
  CRM_INT_30,
  CRM_TTR_42,
  CRM_TTR_43,
  CRM_TTR_44,
  CRM_TTR_45,
  CRM_ORD_178,
  CRM_ORD_171,
  CRM_EMP_15,
  CRM_INT_33,
  CRM_PCG_3,
  CRM_TTR_47,
  CRM_TTR_48,
  CRM_TTR_49,
  CRM_TTR_50,
  CRM_PAY_17,
  CRM_COY_33,
  CRM_COY_34,
  CRM_TTR_51,
  CRM_INT_31,
  CRM_INT_32,
  CRM_PRJ_0,
  CRM_PRJ_1,
  CRM_PRJ_2,
  CRM_PRJ_3,
  CRM_DEP_0,
  CRM_DEP_1,
  CRM_DEP_2,
  CRM_DEP_3,
  CRM_EMB_18,
  CRM_AFF_13,
  CRM_AFF_14,
  CRM_AFF_15,
  CRM_AFF_16,
  CRM_AFF_17,
  CRM_AFF_18,
  CRM_AFF_19,
  CRM_COY_35, CRM_COY_36, CRM_COY_37, CRM_COY_38,
  CRM_EMP_19,
  CRM_EMP_18,
  CRM_EMP_17,
  CRM_EMP_16, CRM_ORD_162
} from "../models/input.interfaces";
import { environment } from "../../../environments/environment";
import { Injectable, Injector } from '@angular/core';
import {
  ConfirmationStatusResponse,
  DashboardOrderLinesByDateResponse,
  DashboardOrdersByDateResponse,
  DashboardRevenueByDateResponse,
  DashboardRevenueByMonthResponse,
  IncidentResponse,
  OrderLineResponse,
  OrderCustomerQuestionChoiceResponse,
  OrderLineStageResponse,
  WorkOrderTaskResponse,
  OrderLogResponse,
  OrderLogTypeResponse,
  OrderNoteResponse,
  OrderResponse,
  OrderResponseCompact,
  OrderScheduleResponse,
  OrderStatusResponse,
  PriceRuleResponse,
  ScheduleExecutionDatePreviewResponse,
  ScheduleRepeatTypeResponse,
  WorkOrderResponse,
  OrderCustomerQuestionResponse,
  WorkOrderStatusResponse,
  ServiceRecipientResponse,
  WorkOrderCompactResponse, CustomerAggregatedDataResponse, QuantityProposalOrderLineResponse,
} from "../models/order.interfaces";
import {TranslateService} from "@ngx-translate/core";
import {
  AccountingProductResponse,
  CalculatedOrderResponse,
  PartnerPriceRuleGroupResponse,
  PartnerPriceRuleResponse,
  PriceRuleCalculationTypeResponse,
  PriceRuleGroupResponse,
  ProductAttributeChoicesResponse,
  ProductBaseResponse,
  ProductCategoryResponse,
  ProductCompactResponse,
  ProductInformationResponse,
  ProductQuantityCalculationResponse,
  ProductQuantityCalculationTestResponse,
  ProductQuantityCalculationTypeResponse,
  ProductQuantityCalculationValueSourceResponse,
  ProductResponse,
  ProductStageResponse,
  ProductTypeResponse,
  PropertyTypeResponse,
  SimulatedOrderDataResponse,
  TriggerTypeResponse,
  UnitTypeResponse,
  UpsellProductResponse
} from "../models/product.interfaces";
import {
  CompanyDefaultAddressValuesResponse,
  CompanyEmbedSettingsResponse, CompanyEmployeeAttributesResponse, CompanyGeneralSettingsResponse, CompanyNotificationSettingsResponse, CompanyOpeningHoursResponse, CompanyPaymentCredentialsResponse,
  CompanyResponse, CompanyThresholdResponse, CompanyTOCResponse, CompanyTypeResponse
} from "../models/company.interfaces";
import {CompanyTableSetupResponse, InvoiceSendTypeResponse, OrderPaymentResponse, OrderPaymentResponseCompact, PaymentLogEntryResponse, PaymentMethodResponse, PaymentResponse, PaymentScheduleOptionResponse, PaymentStatusResponse, VatRateResponse} from "../models/payment.interfaces";
import { IncidentTypeResponse } from "../models/stage.interfaces";
import { CargoTypeResponse } from "../models/cargo.interfaces";
import { ReportFinancialSummaryResponse } from "../models/report.interfaces";
import { convertPayloadDatetime } from "../../@core/utils/utils.service";
import { convertResponseDatetime } from "../../@core/utils/utils.service";
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, filter, finalize, map, switchMap, take } from 'rxjs/operators';
import {AccountingEmployeeResponse, EmployeeActiveSalaryResponse, EmployeeAttributesResponse, EmployeeAvailabilityItemResponse, EmployeeResponse, EmployeeSalaryResponse, EmployeeVacationDaysResponse} from "../models/employee.interfaces";
import {CustomerAccountingResponse, CustomerEditableResponse, CustomerResponse} from "../models/customer.interfaces";
import jwtDecode from "jwt-decode";
import {AuthService} from "../../@core/services/auth.service";
import {AddressDetailsResponse, AddressSearchResultItemResponse, GeoAddressResponse, PropertyOwnershipTypeResponse} from "../models/address.interfaces";
import {CompanyAccountResponse, CompanyPaymentMethodResponse, ExternalActivityResponse, ExternalDepartmentResponse, ExternalEmployeeResponse, ExternalProjectResponse, ExternalSalaryTypeResponse, FikenAccountResponse, PogoAccountResponse, TripletexPaymentMethodResponse} from "../models/integrations.interfaces";
import {BryntumChangeSetResponse, EventAvailabilityResponse, EventCountResponse, EventResponse, EventTypeResponse} from "../models/events.interfaces";
import {EmployeeSalesResponse, ResourceResponse, ResourceTypeResponse} from "../models/resources.interfaces";
import {PeppolEHFCheckResponse, TopBarSetupResponse, VehicleRegistrationDataResponse} from "../models/global.interfaces";
import {ToastService} from "../../@core/services/toast.service";
import {EmbedProductScheduleTemplateResponse, EmbedResponse} from "../models/embed.interfaces";
import {ApplicationTypeResponse, CompanyApplicationResponse} from "../models/applications.interfaces";
import {
  CompanySalaryRuleResponse,
  CompanySalaryTypeResponse,
  CompanyTimeTrackingResponse, EmployeeAbsenceResponse,
  SalaryApprovalAccountingPreCheckResponse,
  SalaryApprovalResponse, SalaryCalculationTypeResponse,
  SalaryRuleTypeResponse,
  TimeTrackingActivityResponse,
  TimeTrackingActivityTypeResponse, TimeTrackingLogResponse,
  TimeTrackingOccurrenceResponse, TimeTrackingResponse,
  UserSalaryTimeTrackingResponse,
  UserWorkingHourPeriodResponse,
  WorkingHoursResponse
} from "../models/timetracking.interfaces";
import {AffiliateContactResponse, AffiliateNoteResponse, AffiliateResponse, AffiliateSearchResponse, ReversedAffiliateResponse} from "../models/affiliate.interfaces";
import {
  AssessmentTypeResponse,
  AttributeResponse,
  ElementResponse,
  ElementTypeResponse,
  FloorResponse,
  PhraseResponse,
  PhraseRuleFilterTypeResponse,
  ReportElementFloorSetupResponse,
  ReportElementResponse,
  ReportElementRoomSetupResponse, ReportElementStatusResponse, ReportForHtmlResponse, ReportPlotDataResponse,
  ReportResponse, ReportSourceResponse, ReportUpgradeDocumentTypeResponse, ReportUpgradeResponse,
  RevisionResponse,
  RoomResponse, SewageConnectionTypeResponse,
  TemplateElementResponse, TemplatePrerequisiteResponse,
  TemplateResponse, WaterConnectionTypeResponse
} from "../models/reportinator.interfaces";
import {CustomerQuestionResponse, CustomerQuestionTemplateResponse, ImportantInformationResponse, SpecificationChoiceResponse, SpecificationResponse, TaskGroupResponse, TaskResponse, TaskTemplateResponse, WorkOrderTemplateResponse} from "../models/templates.interfaces";
import {NotificationsResponse} from "../models/notification";
import {ProjectResponse} from "../models/projects.interfaces";
import {DepartmentResponse} from "../models/departments.interfaces";


export interface EndpointOptions {
  auth?: boolean,
  content_type?: string | null,
  platform?: boolean,
  raiseErrorToast?: boolean
  language?: string
}

@Injectable({
  providedIn: 'root',
})
export class EndpointService {
  constructor(private http: HttpClient,
              private jwtService: JwtService,
              private translateService: TranslateService,
              private injector: Injector,
              private toastService: ToastService
  ) {}


  private refreshTokenSubject: BehaviorSubject<any | null> = new BehaviorSubject<any | null>(null);
  private refreshTokenInProgress = false;

  refreshToken(): Observable<string> {
    const authService = this.injector.get(AuthService);

    if (this.refreshTokenInProgress) {
      return this.refreshTokenSubject.pipe(
        filter((token) => token !== null),
        take(1)
      );
    }

    this.refreshTokenInProgress = true;
    this.refreshTokenSubject.next(null);

    return authService.refreshToken().pipe(
      switchMap((data) => {
        const newToken = data.access_token;
        this.refreshTokenSubject.next(newToken);
        return of(newToken); // Return the new token
      }),
      catchError((error) => {
        // Handle token refresh error here
        return throwError(error);
      }),
      finalize(() => {
        this.refreshTokenInProgress = false;
      })
    );
  }

  /**
  *
  * Default HTTP functions
  *
  */

  private handleResponse<T>(observable: Observable<T>): Observable<T> {
  return observable.pipe(
    map((res) => {
      if (res) {
        convertResponseDatetime(res);
      }
      return res;
    })
  )};

  private handlePayload(data: any): any {
    if (data) {
      data = convertPayloadDatetime(data);
    }
    return data;
  }

  getDefaultHeaders(auth: boolean = true, content_type: string | null = 'application/json', platform: boolean = false, language: string | undefined = undefined): Observable<HttpHeaders> {
    // Accept-Language
    let headers = new HttpHeaders({
      'Accept-Language': language ? language : (this.translateService.currentLang ? this.translateService.currentLang : 'en'),
    });

    // Content-Type
    if (content_type != null) {
      headers = headers.append('Content-Type', content_type);
    }

    // Platform
    if (platform) {
      headers = headers.append('Platform', 'core');
    }

    // Authorization
    if (auth) {
      if (this.jwtService.getAccessToken() == null || this.jwtService.getAccessToken() == undefined || this.jwtService.getAccessToken() == '') {
        throw new Error('No access token found')
      }
      const decodedToken: {[key: string]: any} = jwtDecode(this.jwtService.getAccessToken());
      const currentTimestamp = Math.floor(Date.now() / 1000); // Get current Unix timestamp in seconds
      const tokenExpiration = decodedToken["exp"];

      // Check if token is expired or about to expire in the next 60 seconds
      const timeRemaining = tokenExpiration - currentTimestamp;
      if (tokenExpiration && (tokenExpiration < currentTimestamp || timeRemaining < 60)) {
        return this.refreshToken().pipe(
          switchMap((newToken) => {
            headers = headers.append('Authorization', 'Bearer ' + newToken);
            return of(headers);
          })
        );
      } else {
        headers = headers.append('Authorization', 'Bearer ' + this.jwtService.getAccessToken());
        return of(headers);
      }
    } else {
      return of(headers);
    }
  }

  // post<R>(url: string, payload: any, auth = true, content_type?: string | null, platform: boolean = false, raiseErrorToast: boolean = true): Observable<R> {
  post<R>(url: string, payload: any, options: EndpointOptions): Observable<R> {
    return this.getDefaultHeaders(options.auth, options.content_type, options.platform, options.language).pipe(
      switchMap((headers) => {
        payload = this.handlePayload(payload);
        return this.http.post<R>(url, payload, {headers: headers}).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && options.raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
          switchMap((response: R) => {
            return this.handleResponse<R>(of(response));
          })
        );
      })
    );
  }

  put<R>(url: string, payload: any, options: EndpointOptions): Observable<R> {
    return this.getDefaultHeaders(options.auth, options.content_type, options.platform, options.language).pipe(
      switchMap((headers) => {
        payload = this.handlePayload(payload);
        return this.http.put<R>(url, payload, {headers: headers}).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && options.raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
          switchMap((response: R) => {
            return this.handleResponse<R>(of(response));
          })
        );
      })
    );
  }

  patch<R>(url: string, payload: any, options: EndpointOptions): Observable<R> {
    return this.getDefaultHeaders(options.auth, options.content_type, options.platform, options.language).pipe(
      switchMap((headers) => {
        payload = this.handlePayload(payload);
        return this.http.patch<R>(url, payload, {headers: headers}).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && options.raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
          switchMap((response: R) => {
            return this.handleResponse<R>(of(response));
          })
        );
      })
    );
  }

  get<R>(url: string, queryParams: { [key: string]: any } | undefined, options: EndpointOptions): Observable<R> {
    return this.getDefaultHeaders(options.auth, options.content_type, options.platform, options.language).pipe(
      switchMap((headers) => {
        queryParams = this.handlePayload(queryParams);
        let httpParams = new HttpParams();
        if (queryParams) {
          Object.keys(queryParams).forEach(key => {
            const value = queryParams?.[key];
            if (value !== undefined && value !== null) {
              httpParams = httpParams.set(key, value);
            }
          });
        }

        return this.http.get<R>(url, { headers: headers, params: httpParams }).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && options.raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
          switchMap((response: R) => {
            return this.handleResponse<R>(of(response));
          })
        );
      })
    );
  }

  delete<R>(url: string, queryParams: { [key: string]: any } | undefined, options: EndpointOptions): Observable<R> {
    return this.getDefaultHeaders(options.auth, options.content_type, options.platform, options.language).pipe(
      switchMap((headers) => {
        queryParams = this.handlePayload(queryParams);
        let httpParams = new HttpParams();
        if (queryParams) {
          Object.keys(queryParams).forEach(key => {
            const value = queryParams?.[key];
            if (value !== undefined && value !== null) {
              httpParams = httpParams.set(key, value);
            }
          });
        }
        return this.http.delete<R>(url, { headers: headers, params: httpParams }).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && options.raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
          switchMap((response: R) => {
            return this.handleResponse<R>(of(response));
          })
        );
      })
    );
  }


  /**
  *
  * Endpoint definitions
   * USM
   * Users
  *
  */

  usm_usr_0(payload: USM_USR_0, raiseErrorToast: boolean = true): Observable<LoginResponse> {
    let options: EndpointOptions = {
      auth: false,
      content_type: null,
      platform: true,
      raiseErrorToast: raiseErrorToast
    }
    return this.post(environment.usermanagementApiUrl + 'users/login', payload, options)
  }

  usm_usr_2(payload: USM_USR_2, raiseErrorToast: boolean = true): Observable<PostResponse<UserResponse>> {
    return this.post(environment.usermanagementApiUrl + 'users/', payload, {raiseErrorToast: raiseErrorToast})
  }

  usm_usr_3(raiseErrorToast: boolean = true): Observable<PostResponse<UserResponse>>  {
    return this.get(environment.usermanagementApiUrl + 'users/', undefined, {raiseErrorToast: raiseErrorToast})
  }

  usm_usr_6(payload: USM_USR_6, raiseErrorToast: boolean = true): Observable<PostResponse<UserEntityRelationWithUserDataResponse>> {
    return this.post(environment.usermanagementApiUrl + 'users/invite/to-entity', payload, {raiseErrorToast: raiseErrorToast})
  }

  usm_usr_7(payload: USM_USR_7, raiseErrorToast: boolean = true): Observable<GetResponse<null>> {
    return this.post(environment.usermanagementApiUrl + 'users/create-with-otp', payload, {raiseErrorToast: raiseErrorToast})
  }

  usm_usr_8(payload: USM_USR_8, raiseErrorToast: boolean = true): Observable<LoginResponse> {
    let options: EndpointOptions = {
      auth: false,
      raiseErrorToast: raiseErrorToast
    }
    return this.post(environment.usermanagementApiUrl + 'users/login/otp', payload, options)
  }

  usm_usr_9(payload: USM_USR_9, raiseErrorToast: boolean = true): Observable<GetResponse<null>> {
    let options: EndpointOptions = {
      auth: false,
      raiseErrorToast: raiseErrorToast
    }
    return this.post(environment.usermanagementApiUrl + 'users/login/otp/initiate', payload, options)
  }

  usm_usr_10(payload: USM_USR_10, raiseErrorToast: boolean = true): Observable<PaginationResponse<UserResponse>> {
    return this.post(environment.usermanagementApiUrl + 'users/all', payload, {raiseErrorToast: raiseErrorToast})
  }

  usm_usr_12(raiseErrorToast: boolean = true): Observable<LoginResponse> {
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept-Language': this.translateService.currentLang || 'en',
      'Authorization': 'Bearer ' + this.jwtService.getRefreshToken(),
      'Platform': 'core'
    })
    let options = {headers: headers}
    return this.http.post<LoginResponse>(environment.usermanagementApiUrl + 'users/refresh-token', {}, options).pipe(
      catchError((error: any) => {
        if (error instanceof HttpErrorResponse && raiseErrorToast) {
          // this.toastService.errorToast(error.error.asset_id);
        }
        return throwError(error);
      }),
    );
  }

  usm_usr_13(payload: USM_USR_13, raiseErrorToast: boolean = true): Observable<GetResponse<null>> {
    let options: EndpointOptions = {
      auth: false,
      raiseErrorToast: raiseErrorToast
    }
    return this.post(environment.usermanagementApiUrl + 'users/reset-password/initiate', payload, options)
  }

  usm_usr_14(payload: USM_USR_14, raiseErrorToast: boolean = true): Observable<PostResponse<null>> {
    let options: EndpointOptions = {
      auth: false,
      raiseErrorToast: raiseErrorToast
    }
    return this.post(environment.usermanagementApiUrl + 'users/reset-password/token', payload, options)
  }

  usm_usr_15(payload: null, raiseErrorToast: boolean = true): Observable<null> {
    let options: EndpointOptions = {
      auth: true,
      platform: true,
      raiseErrorToast: raiseErrorToast
    }
    return this.post(environment.usermanagementApiUrl + 'users/logout', payload, options)
  }

  usm_usr_16(payload: USM_USR_16, raiseErrorToast: boolean = true): Observable<PutResponse<UserResponse>> {
    return this.getDefaultHeaders(true, null).pipe(
      switchMap((headers) => {
        let options = { headers: headers };

        let formData = new FormData();
        formData.append('image', payload.image);

        return this.http.put<PutResponse<UserResponse>>(environment.usermanagementApiUrl + 'users/profile-image', formData, options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
        );
      })
    );
  }

  usm_usr_17(payload: USM_USR_17, raiseErrorToast: boolean = true): Observable<PutResponse<UserResponse>> {
    return this.getDefaultHeaders(true, null).pipe(
      switchMap((headers) => {
        let params = { 'entity_id': payload.entity_id, 'user_id': payload.user_id };
        let options = { headers: headers, params: params };

        let formData = new FormData();
        formData.append('image', payload.image);

        return this.http.put<PutResponse<UserResponse>>(environment.usermanagementApiUrl + 'users/profile-image/admin', formData, options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
        );
      })
    );
  }

  usm_usr_18(payload: USM_USR_18, raiseErrorToast: boolean = true): Observable<PostResponse<null>> {
    return this.post<PostResponse<null>>(environment.usermanagementApiUrl + 'users/reset-password', payload, {raiseErrorToast: raiseErrorToast})
  }

  usm_usr_19(payload: USM_USR_19, raiseErrorToast: boolean = true): Observable<LoginResponse> {
    let options: EndpointOptions = {
      auth: false,
      raiseErrorToast: raiseErrorToast
    }
  return this.post(environment.usermanagementApiUrl + 'users/register/invitation-token', payload, options)
  }

  usm_usr_20(params: USM_USR_20, raiseErrorToast: boolean = true): Observable<GetResponse<UserSimpleResponse>> {
    return this.get(environment.usermanagementApiUrl + 'users/by-data', params, {raiseErrorToast: raiseErrorToast})
  }

  usm_usr_21(raiseErrorToast: boolean = true): Observable<GetResponse<UserOptionsResponse>> {
    return this.get(environment.usermanagementApiUrl + 'users/options', undefined, {raiseErrorToast: raiseErrorToast})
  }

  usm_usr_22(payload: USM_USR_22, raiseErrorToast: boolean = true): Observable<PutResponse<UserOptionsResponse>> {
    return this.put(environment.usermanagementApiUrl + 'users/options', payload, {raiseErrorToast: raiseErrorToast})
  }

  usm_usr_24(payload: USM_USR_24, raiseErrorToast: boolean = true): Observable<PostResponse<UserEntityRelationWithUserDataResponse>> {
    return this.post(environment.usermanagementApiUrl + 'users/resend-user-invitation', payload, {raiseErrorToast: raiseErrorToast})
  }

  usm_usr_25(payload: USM_USR_25, raiseErrorToast: boolean = true): Observable<PostResponse<InvitationTokenVerificationResponse>> {
    let options: EndpointOptions = {
      auth: false,
      raiseErrorToast: raiseErrorToast
    }
    return this.post(environment.usermanagementApiUrl + 'users/register/invitation-token/verify', payload, options)
  }

  usm_usr_26(payload: USM_USR_26, raiseErrorToast: boolean = true): Observable<PutResponse<null>> {
    let options: EndpointOptions = {
      platform: true,
      raiseErrorToast: raiseErrorToast
    }
    return this.put(environment.usermanagementApiUrl + 'users/fcm-token', payload, options)
  }

  /**
  *
  * Endpoint definitions
   * USM
   * Entities
  *
  */

  usm_ent_0(params: USM_ENT_0, raiseErrorToast: boolean = true): Observable<PaginationResponse<UserEntityRelationWithUserDataResponse[]>>{
    return this.get(environment.usermanagementApiUrl + 'entities/users', params, {raiseErrorToast: raiseErrorToast})
  }


  usm_ent_1(params: USM_ENT_1, raiseErrorToast: boolean = true): Observable<PaginationResponse<RoleResponse[]>> {
    return this.get(environment.usermanagementApiUrl + 'entities/roles', params, {raiseErrorToast: raiseErrorToast})
  }

  usm_ent_2(payload: USM_ENT_2, raiseErrorToast: boolean = true): Observable<PutResponse<UserEntityRelationWithUserDataResponse>> {
    return this.patch(environment.usermanagementApiUrl + 'entities/users', payload, {raiseErrorToast: raiseErrorToast})
  }

  usm_ent_3(params?: USM_ENT_3, raiseErrorToast: boolean = true): Observable<PaginationResponse<UserEntityRelationWithoutUserDataResponse[]>> {
    return this.get(environment.usermanagementApiUrl + 'entities/user-relations', params, {raiseErrorToast: raiseErrorToast})
  }

  /**
  *
  * Endpoint definitions
   * CRM
   * Employees
  *
  */

  crm_emp_0(payload: CRM_EMP_0, raiseErrorToast: boolean = true): Observable<PostResponse<UserEntityRelationWithUserDataResponse>> {
    return this.post(environment.crmApiUrl + 'employees/', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_emp_1(payload: CRM_EMP_1, raiseErrorToast: boolean = true): Observable<PutResponse<UserEntityRelationWithUserDataResponse>> {
    return this.put(environment.crmApiUrl + 'employees/', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_emp_2(params: CRM_EMP_2, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'employees/', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_emp_4(params: CRM_EMP_4, raiseErrorToast: boolean = true): Observable<GetResponse<EmployeeAttributesResponse>> {
    return this.get(environment.crmApiUrl + 'employees/attributes', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_emp_5(payload: CRM_EMP_5, raiseErrorToast: boolean = true): Observable<PutResponse<EmployeeAttributesResponse>> {
    return this.patch(environment.crmApiUrl + 'employees/attributes', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_emp_6(params: CRM_EMP_6, raiseErrorToast: boolean = true): Observable<GetResponse<EmployeeAttributesResponse>> {
    return this.get(environment.crmApiUrl + 'employees/attributes/as-user', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_emp_7(params: CRM_EMP_7, raiseErrorToast: boolean = true): Observable<GetResponse<EmployeeAvailabilityItemResponse[]>> {
    return this.get(environment.crmApiUrl + 'employees/availability', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_emp_8(params: CRM_EMP_8, raiseErrorToast: boolean = true): Observable<GetResponse<AccountingEmployeeResponse[]>> {
    return this.get(environment.crmApiUrl + 'employees/accounting', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_emp_9(params: CRM_EMP_9, raiseErrorToast: boolean = true): Observable<PutResponse<null>> {
    return this.put(environment.crmApiUrl + 'employees/accounting', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_emp_10(payload: CRM_EMP_10, raiseErrorToast: boolean = true): Observable<GetResponse<EmployeeSalaryResponse[]>> {
    return this.get(environment.crmApiUrl + 'employees/salaries', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_emp_11(payload: CRM_EMP_11, raiseErrorToast: boolean = true): Observable<PostResponse<EmployeeSalaryResponse>> {
    return this.post(environment.crmApiUrl + 'employees/salaries', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_emp_12(params: CRM_EMP_12, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'employees/salaries', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_emp_13(payload: CRM_EMP_13, raiseErrorToast: boolean = true): Observable<PutResponse<EmployeeSalaryResponse>> {
    return this.put(environment.crmApiUrl + 'employees/salaries', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_emp_14(params: CRM_EMP_14, raiseErrorToast: boolean = true): Observable<GetResponse<EmployeeActiveSalaryResponse>> {
    return this.get(environment.crmApiUrl + 'employees/salaries/active', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_emp_15(params: CRM_EMP_15, raiseErrorToast: boolean = true): Observable<GetResponse<EmployeeResponse[]>> {
    return this.get(environment.crmApiUrl + 'employees/company', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_emp_16(params: CRM_EMP_16, raiseErrorToast: boolean = true): Observable<GetResponse<EmployeeVacationDaysResponse[]>> {
    return this.get(environment.crmApiUrl + 'employees/vacation-days', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_emp_17(payload: CRM_EMP_17, raiseErrorToast: boolean = true): Observable<PutResponse<EmployeeVacationDaysResponse>> {
    return this.put(environment.crmApiUrl + 'employees/vacation-days', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_emp_18(payload: CRM_EMP_18, raiseErrorToast: boolean = true): Observable<PostResponse<EmployeeVacationDaysResponse>> {
    return this.post(environment.crmApiUrl + 'employees/vacation-days', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_emp_19(params: CRM_EMP_19, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'employees/vacation-days', params, {raiseErrorToast: raiseErrorToast})
  }

  /**
  *
  * Endpoint definitions
   * CRM
   * Events
  *
  */

  crm_evt_0(payload: CRM_EVT_0, raiseErrorToast: boolean = true): Observable<PostResponse<EventResponse>> {
    return this.post(environment.crmApiUrl + 'events/', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_evt_1(params: CRM_EVT_1, raiseErrorToast: boolean = true): Observable<any> {
  return this.get(environment.crmApiUrl + 'events/', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_evt_2(payload: CRM_EVT_2, raiseErrorToast: boolean = true): Observable<BryntumChangeSetResponse> {
    return this.patch(environment.crmApiUrl + 'events/', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_evt_3(params: CRM_EVT_3, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
  return this.delete(environment.crmApiUrl + 'events/', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_evt_4(raiseErrorToast: boolean = true): Observable<GetResponse<EventTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'events/types', undefined, {raiseErrorToast: raiseErrorToast})
  }

  crm_evt_5(payload: CRM_EVT_5, raiseErrorToast: boolean = true): Observable<PostResponse<EventResponse>> {
    return this.post(environment.crmApiUrl + 'events/notes', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_evt_6(payload: CRM_EVT_6, raiseErrorToast: boolean = true): Observable<PutResponse<null>> {
  return this.put(environment.crmApiUrl + 'events/notes', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_evt_7(params: CRM_EVT_7, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
  return this.delete(environment.crmApiUrl + 'events/notes', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_evt_8(params: CRM_EVT_8, raiseErrorToast: boolean = true): Observable<PostResponse<EventResponse>> {
    return this.post(environment.crmApiUrl + 'events/assignments/users', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_evt_9(params: CRM_EVT_9, raiseErrorToast: boolean = true): Observable<PutResponse<EventResponse>> {
    return this.delete(environment.crmApiUrl +  'events/assignments/users', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_evt_12(payload: CRM_EVT_12, raiseErrorToast: boolean = true): Observable<PostResponse<EventAvailabilityResponse>> {
    return this.post(environment.crmApiUrl + 'events/availability', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_evt_17(params: CRM_EVT_17, raiseErrorToast: boolean = true): Observable<GetResponse<EventResponse[]>> {
    return this.get(environment.crmApiUrl + 'events/search', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_evt_18(params: CRM_EVT_1, raiseErrorToast: boolean = true): Observable<GetResponse<EventCountResponse>> {
    return this.get(environment.crmApiUrl + 'events/count', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_evt_20(payload: CRM_EVT_20, raiseErrorToast: boolean = true): Observable<PostResponse<OrderResponse>> {
    return this.post(environment.crmApiUrl + 'events/order/add', payload, {raiseErrorToast: raiseErrorToast})
  }

  /**
  *
  * Endpoint definitions
   * CRM
   * Resources
  *
  */

  crm_rsc_0(payload: CRM_RSC_0, raiseErrorToast: boolean = true): Observable<PostResponse<ResourceResponse>> {
    return this.post(environment.crmApiUrl + 'resources/', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_rsc_1(payload: CRM_RSC_1, raiseErrorToast: boolean = true): Observable<PutResponse<ResourceResponse>> {
    return this.patch(environment.crmApiUrl + 'resources/', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_rsc_2(params: CRM_RSC_2, raiseErrorToast: boolean = true): Observable<PaginationResponse<ResourceResponse[]>> {
    return this.get(environment.crmApiUrl + 'resources/', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_rsc_3(params: CRM_RSC_3, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'resources/', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_rsc_4(raiseErrorToast: boolean = true): Observable<GetResponse<ResourceTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'resources/types', undefined, {raiseErrorToast: raiseErrorToast})
  }

  crm_rsc_5(params: CRM_RSC_5, raiseErrorToast: boolean = true): Observable<PutResponse<UserResponse>> {
      return this.getDefaultHeaders(true, null).pipe(
        switchMap((headers) => {
          let _params = { 'company_id': params.company_id, 'resource_id': params.resource_id };
          let options = { headers: headers, params: _params };

          let formData = new FormData();
          formData.append('image', params.image);

          return this.http.put<PutResponse<UserResponse>>(environment.crmApiUrl + 'resources/image', formData, options).pipe(
            catchError((error: any) => {
              if (error instanceof HttpErrorResponse && raiseErrorToast) {
                this.toastService.errorToast(error.error.asset_id);
              }
              return throwError(error);
            }),
          );
        })
      );
  }

  crm_rsc_6(params: CRM_RSC_6, raiseErrorToast: boolean = true): Observable<GetResponse<ResourceResponse>> {
    return this.get(environment.crmApiUrl + 'resources/id', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_rep_5(params: CRM_RSC_7, raiseErrorToast: boolean = true): Observable<GetResponse<EmployeeSalesResponse[]>> {
    return this.get(environment.crmApiUrl + 'reports/sales/employee', params, {raiseErrorToast: raiseErrorToast})
  }


  /**
  *
  * Endpoint definitions
   * CRM
   * Orders
  *
  */

  crm_ord_0(payload: CRM_ORD_0, raiseErrorToast: boolean = true): Observable<PostResponse<OrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/as-company', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_2(params: CRM_ORD_2, raiseErrorToast: boolean = true): Observable<PaginationResponse<OrderResponse[]>> {
    return this.get(environment.crmApiUrl + 'orders/company', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ord_4(params: CRM_ORD_4, raiseErrorToast: boolean = true): Observable<GetResponse<DashboardOrderLinesByDateResponse>> {
    return this.get(environment.crmApiUrl + 'orders/order-lines/company/aggregated/date', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ord_5(params: CRM_ORD_5, raiseErrorToast: boolean = true): Observable<GetResponse<DashboardOrdersByDateResponse>> {
    return this.get(environment.crmApiUrl + 'orders/company/aggregated/date', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ord_7(params: CRM_ORD_7, raiseErrorToast: boolean = true): Observable<GetResponse<DashboardRevenueByDateResponse>> {
    return this.get(environment.crmApiUrl + 'orders/company/revenue/date', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ord_8(params: CRM_ORD_8, raiseErrorToast: boolean = true): Observable<GetResponse<DashboardRevenueByMonthResponse>> {
    return this.get(environment.crmApiUrl + 'orders/company/revenue/month', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ord_10(params: CRM_ORD_10, raiseErrorToast: boolean = true): Observable<GetResponse<OrderResponse>> {
    return this.get(environment.crmApiUrl + 'orders/', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_12(payload: CRM_ORD_12, raiseErrorToast: boolean = true): Observable<PostResponse<OrderLineResponse>> {
    return this.post(environment.crmApiUrl + 'orders/order-lines', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_13(payload: CRM_ORD_13, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.put(environment.crmApiUrl + 'orders/discount', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_15(params: CRM_ORD_15, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'orders/order-lines', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_18(raiseErrorToast: boolean = true): Observable<GetResponse<OrderStatusResponse[]>> {
    return this.get<GetResponse<OrderStatusResponse[]>>(environment.crmApiUrl + 'orders/order-status', undefined, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_19(payload: CRM_ORD_19, raiseErrorToast: boolean = true): Observable<PostResponse<OrderNoteResponse>> {
    return this.post(environment.crmApiUrl + 'orders/order-notes/company', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_20(params: CRM_ORD_20, raiseErrorToast: boolean = true): Observable<GetResponse<OrderNoteResponse[]>> {
    return this.get(environment.crmApiUrl + 'orders/order-notes/company', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_21(payload: CRM_ORD_21, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'orders/order-notes/company', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_22(payload: CRM_ORD_22, raiseErrorToast: boolean = true): Observable<PutResponse<OrderNoteResponse>> {
    return this.put(environment.crmApiUrl + 'orders/order-notes/company', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_27(payload: CRM_ORD_27, raiseErrorToast: boolean = true): Observable<PostResponse<null>> {
    return this.post(environment.crmApiUrl + 'orders/send-quote', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_31(payload: CRM_ORD_31, raiseErrorToast: boolean = true): Observable<PutResponse<OrderLineResponse>> {
    return this.patch(environment.crmApiUrl + 'orders/order-lines', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_39(payload: CRM_ORD_39, raiseErrorToast: boolean = true): Observable<PostResponse<OrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/accept/company', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_44(raiseErrorToast: boolean = true): Observable<GetResponse<ConfirmationStatusResponse[]>> {
    return this.get(environment.crmApiUrl + 'orders/confirmation-statuses', undefined, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_53(payload: CRM_ORD_53, raiseErrorToast: boolean = true): Observable<PutResponse<UnitDetails>> {
    return this.put(environment.crmApiUrl + 'orders/order-lines/stages/address', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_55(payload: CRM_ORD_55, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.put(environment.crmApiUrl + 'orders/order-status', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_56(payload: CRM_ORD_56, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.put(environment.crmApiUrl + 'orders/partner', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_61(payload: CRM_ORD_61, raiseErrorToast: boolean = true): Observable<PostResponse<OrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/order-lines/stages/address', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_64(payload: CRM_ORD_64, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'orders/order-lines/stages/address', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_60(payload: CRM_ORD_60, raiseErrorToast: boolean = true): Observable<PostResponse<OrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/order-lines/custom', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_66(payload: CRM_ORD_66, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.put(
      environment.crmApiUrl + 'orders/order-lines/assignments/crew', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_67(payload: CRM_ORD_67, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.put(
      environment.crmApiUrl + 'orders/order-lines/assignments/resources', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_68(payload: CRM_ORD_68, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.put(
      environment.crmApiUrl + 'orders/set-archive-status', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_69(payload: CRM_ORD_69, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.put(
      environment.crmApiUrl + 'orders/order-lines/total-price', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_prd_69(payload: CRM_PRD_69, raiseErrorToast: boolean = true): Observable<PutResponse<ProductResponse>> {
    return this.put(environment.crmApiUrl + 'products/affiliates/price', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_70(payload: CRM_ORD_0, raiseErrorToast: boolean = true): Observable<PostResponse<EventResponse>> {
    return this.post(environment.crmApiUrl + 'orders/as-company/as-event', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_71(payload: CRM_ORD_71, raiseErrorToast: boolean = true): Observable<PostResponse<OrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/duplicate', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_73(payload: CRM_ORD_73, raiseErrorToast: boolean = true): Observable<PostResponse<OrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/order-lines/discount', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_74(payload: CRM_ORD_74, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.put(environment.crmApiUrl + 'orders/invoice/settings', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_76(payload: CRM_ORD_76, raiseErrorToast: boolean = true): Observable<PostResponse<OrderScheduleResponse>> {
    return this.post(environment.crmApiUrl + 'orders/schedules', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_77(params: CRM_ORD_77, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'orders/schedules', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_78(params: CRM_ORD_78, raiseErrorToast: boolean = true): Observable<GetResponse<OrderScheduleResponse>> {
    return this.get(environment.crmApiUrl + 'orders/schedules', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_79(payload: CRM_ORD_79, raiseErrorToast: boolean = true): Observable<PutResponse<OrderScheduleResponse>> {
    return this.patch(environment.crmApiUrl + 'orders/schedules', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_80(params: CRM_ORD_80, raiseErrorToast: boolean = true): Observable<PaginationResponse<OrderScheduleResponse[]>> {
    return this.get(environment.crmApiUrl + 'orders/schedules/all', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_81(payload: CRM_ORD_81, raiseErrorToast: boolean = true): Observable<PostResponse<OrderScheduleResponse>> {
    return this.post(environment.crmApiUrl + 'orders/schedules/order-lines', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_82(payload: CRM_ORD_82, raiseErrorToast: boolean = true): Observable<PutResponse<OrderScheduleResponse>> {
    return this.patch(environment.crmApiUrl + 'orders/schedules/order-lines', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_83(params: CRM_ORD_83, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'orders/schedules/order-lines', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_84(raiseErrorToast: boolean = true): Observable<GetResponse<ScheduleRepeatTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'orders/schedules/repeat-types', undefined, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_85(payload: CRM_ORD_85, raiseErrorToast: boolean = true): Observable<PostResponse<ScheduleExecutionDatePreviewResponse>> {
    return this.post(environment.crmApiUrl + 'orders/schedules/execution-date-preview', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_86(payload: CRM_ORD_86, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.put(environment.crmApiUrl + 'orders/customer', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_87(params: CRM_ORD_87, raiseErrorToast: boolean = true): Observable<GetResponse<OrderLogResponse[]>> {
    return this.get(environment.crmApiUrl + 'orders/logs', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_88(raiseErrorToast: boolean = true): Observable<GetResponse<OrderLogTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'orders/logs/log-types', undefined, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_90(payload: CRM_ORD_90, raiseErrorToast: boolean = true): Observable<PostResponse<OrderScheduleResponse>> {
    return this.post(environment.crmApiUrl + 'orders/schedules/accept', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_91(payload: CRM_ORD_91, raiseErrorToast: boolean = true): Observable<PostResponse<OrderScheduleResponse>> {
    return this.post(environment.crmApiUrl + 'orders/schedules/send-quote', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_93(payload: CRM_ORD_93, raiseErrorToast: boolean = true): Observable<PostResponse<null>> {
    return this.post(environment.crmApiUrl + 'orders/schedules/force-order-creation', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_95(payload: CRM_ORD_95, raiseErrorToast: boolean = true): Observable<PutResponse<OrderScheduleResponse>> {
    return this.put(environment.crmApiUrl + 'orders/schedules/set-active-status', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_96(payload: CRM_ORD_96, raiseErrorToast: boolean = true): Observable<PutResponse<OrderScheduleResponse>> {
    return this.put(environment.crmApiUrl + 'orders/schedules/payment-method', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_97(payload: CRM_ORD_97, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.patch(environment.crmApiUrl + 'orders/', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_98(payload: CRM_ORD_98, raiseErrorToast: boolean = true): Observable<PostResponse<OrderResponse>> {
    if (payload.work_order_id) {
      throw new Error('Cannot use work_order_id for crm_ord_98, use crm_ord_98_wo instead');
    }
    return this.getDefaultHeaders(true, null).pipe(
      switchMap((headers) => {
        let params = { 'company_id': payload.company_id, 'order_id': payload.order_id };
        let options = { headers: headers, params: params };

        let formData = new FormData();
        formData.append('attachment', payload.attachment);

        return this.http.post<PostResponse<OrderResponse>>(environment.crmApiUrl + 'orders/attachments', formData, options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
          switchMap((response: PostResponse<OrderResponse>) => {
            return this.handleResponse<PostResponse<OrderResponse>>(of(response));
          })
        );

      })
    );
  }

  crm_ord_99(payload: CRM_ORD_99, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'orders/attachments', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_100(params: { [key: string]: any }, raiseErrorToast: boolean = true): Observable<Blob> {
    return this.getDefaultHeaders(true, 'application/octet-stream').pipe(
      switchMap((headers) => {
        headers.set('Accept', 'application/octet-stream');
        let httpParams = new HttpParams();
        Object.keys(params).forEach(key => {
          const value = params[key];
          if (value !== undefined && value !== null) {
            httpParams = httpParams.set(key, value);
          }
        });
        let options = { headers: headers, params: httpParams, responseType: 'blob' as 'json' };
        return this.http.get<Blob>(environment.crmApiUrl + 'orders/attachments', options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
        );
      })
    );
  }

  crm_ord_101(payload: CRM_ORD_101, raiseErrorToast: boolean = true): Observable<PostResponse<WorkOrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/subcontractors', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_102(params: CRM_ORD_102, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'orders/subcontractors', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_103(payload: CRM_ORD_103, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.put(environment.crmApiUrl + 'orders/payment-recipient', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_104(payload: CRM_ORD_104, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.put(environment.crmApiUrl + 'orders/service-recipient', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_107(payload: CRM_ORD_107, raiseErrorToast: boolean = true): Observable<PostResponse<OrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/order-confirmation', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_108(params: CRM_ORD_2, raiseErrorToast: boolean = true): Observable<PaginationResponse<OrderResponseCompact[]>> {
    return this.get(environment.crmApiUrl + 'orders/company/compact', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_110(payload: CRM_ORD_110, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.put(environment.crmApiUrl + 'orders/attachments', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_113(payload: CRM_ORD_113, raiseErrorToast: boolean = true): Observable<PostResponse<WorkOrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/work-orders/users', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_114(params: CRM_ORD_114, raiseErrorToast: boolean = true): Observable<GetResponse<WorkOrderResponse>> {
    return this.delete(environment.crmApiUrl + 'orders/work-orders/users', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_115(payload: CRM_ORD_115, raiseErrorToast: boolean = true): Observable<PostResponse<WorkOrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/work-orders/resources', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_116(params: CRM_ORD_116, raiseErrorToast: boolean = true): Observable<GetResponse<WorkOrderResponse>> {
    return this.delete(environment.crmApiUrl + 'orders/work-orders/resources', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_117(payload: CRM_ORD_117, raiseErrorToast: boolean = true): Observable<PostResponse<WorkOrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/work-orders', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_118(payload: CRM_ORD_118, raiseErrorToast: boolean = true): Observable<PutResponse<WorkOrderResponse>> {
    return this.put(environment.crmApiUrl + 'orders/work-orders', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_119(params: CRM_ORD_119, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'orders/work-orders', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_120(params: CRM_ORD_120, raiseErrorToast: boolean = true): Observable<PostResponse<OrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/draft', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_121(payload: CRM_ORD_121, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/draft/initiate', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_122(payload: CRM_ORD_122, raiseErrorToast: boolean = true): Observable<PostResponse<WorkOrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/work-orders/schedules', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_123(payload: CRM_ORD_123, raiseErrorToast: boolean = true): Observable<PutResponse<WorkOrderResponse>> {
    return this.put(environment.crmApiUrl + 'orders/work-orders/schedules', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_124(params: CRM_ORD_124, raiseErrorToast: boolean = true): Observable<GetResponse<WorkOrderResponse>> {
    return this.delete(environment.crmApiUrl + 'orders/work-orders/schedules', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_125(payload: CRM_ORD_125, raiseErrorToast: boolean = true): Observable<PostResponse<WorkOrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/work-orders/schedules/initiate', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_126(payload: CRM_ORD_126, raiseErrorToast: boolean = true): Observable<PutResponse<WorkOrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/work-orders/tasks/template', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_127(payload: CRM_ORD_127, raiseErrorToast: boolean = true): Observable<PutResponse<WorkOrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/work-orders/tasks', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_128(payload: CRM_ORD_128, raiseErrorToast: boolean = true): Observable<PutResponse<WorkOrderResponse>> {
    return this.put(environment.crmApiUrl + 'orders/work-orders/tasks', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_129(params: CRM_ORD_129, raiseErrorToast: boolean = true): Observable<PutResponse<WorkOrderResponse>> {
    return this.delete(environment.crmApiUrl + 'orders/work-orders/tasks', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_130(payload: CRM_ORD_130, raiseErrorToast: boolean = true): Observable<PutResponse<WorkOrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/work-orders/tasks/groups', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_131(payload: CRM_ORD_131, raiseErrorToast: boolean = true): Observable<PutResponse<WorkOrderResponse>> {
    return this.put(environment.crmApiUrl + 'orders/work-orders/tasks/groups', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_132(params: CRM_ORD_132, raiseErrorToast: boolean = true): Observable<PutResponse<WorkOrderResponse>> {
    return this.delete(environment.crmApiUrl + 'orders/work-orders/tasks/groups', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_133(payload: CRM_ORD_133, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/customer-questions/template', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_134(payload: CRM_ORD_134, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/customer-questions', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_135(payload: CRM_ORD_135, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.put(environment.crmApiUrl + 'orders/customer-questions', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_136(params: CRM_ORD_136, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.delete(environment.crmApiUrl + 'orders/customer-questions', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_137(payload: CRM_ORD_137, raiseErrorToast: boolean = true): Observable<PutResponse<OrderCustomerQuestionResponse>> {
    return this.post(environment.crmApiUrl + 'orders/customer-questions/choices', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_138(payload: CRM_ORD_138, raiseErrorToast: boolean = true): Observable<PutResponse<OrderCustomerQuestionResponse>> {
    return this.put(environment.crmApiUrl + 'orders/customer-questions/choices', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_139(params: CRM_ORD_139, raiseErrorToast: boolean = true): Observable<PutResponse<OrderCustomerQuestionResponse>> {
    return this.delete(environment.crmApiUrl + 'orders/customer-questions/choices', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_140(payload: CRM_ORD_12, raiseErrorToast: boolean = true): Observable<PostResponse<OrderLineResponse>> {
    return this.post(environment.crmApiUrl + 'orders/order-lines/draft', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_141(raiseErrorToast: boolean = true): Observable<PutResponse<WorkOrderStatusResponse[]>> {
    return this.get(environment.crmApiUrl + 'orders/work-order-statuses', undefined, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_142(payload: CRM_ORD_142, raiseErrorToast: boolean = true): Observable<PostResponse<WorkOrderResponse>> {
    return this.getDefaultHeaders(true, null).pipe(
      switchMap((headers) => {
        let params = { 'company_id': payload.company_id, 'work_order_id': payload.work_order_id! };
        let options = { headers: headers, params: params };

        let formData = new FormData();
        formData.append('attachment', payload.attachment);

        return this.http.post<PostResponse<WorkOrderResponse>>(environment.crmApiUrl + 'orders/work-orders/attachments', formData, options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
          switchMap((response: PostResponse<WorkOrderResponse>) => {
            return this.handleResponse<PostResponse<WorkOrderResponse>>(of(response));
          })
        );

      })
    );
  }

  crm_ord_143(payload: CRM_ORD_143, raiseErrorToast: boolean = true): Observable<GetResponse<WorkOrderResponse>> {
    return this.get(environment.crmApiUrl + 'orders/work-orders/id', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_144(payload: CRM_ORD_144, raiseErrorToast: boolean = true): Observable<PostResponse<{address_id: number}>> {
    return this.post(environment.crmApiUrl + 'orders/work-orders/addresses', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_150(payload: CRM_ORD_150, raiseErrorToast: boolean = true): Observable<PutResponse<WorkOrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/work-orders/finish', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_151(payload: CRM_ORD_151, raiseErrorToast: boolean = true): Observable<PutResponse<OrderCustomerQuestionChoiceResponse>> {
    return this.put(environment.crmApiUrl + 'orders/work-orders/customer-questions/set-choice', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_153(params: CRM_ORD_153, raiseErrorToast: boolean = true): Observable<PaginationResponse<WorkOrderResponse[]>> {
    return this.get(environment.crmApiUrl + 'orders/work-orders/schedules/work-orders', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_154(payload: CRM_ORD_154, raiseErrorToast: boolean = true): Observable<PostResponse<WorkOrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/work-orders/duplicate', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_162(params: CRM_ORD_162, raiseErrorToast: boolean = true): Observable<GetResponse<OrderLineResponse[]>> {
    return this.get(environment.crmApiUrl + 'orders/order-lines', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_163(payload: CRM_ORD_163, raiseErrorToast: boolean = true): Observable<PostResponse<OrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/important-information', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_164(params: CRM_ORD_164, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'orders/important-information', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_165(params: CRM_ORD_165, raiseErrorToast: boolean = true): Observable<PaginationResponse<OrderResponseCompact[]>> {
    return this.get(environment.crmApiUrl + 'orders/advanced-search', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_167(params: CRM_ORD_167, raiseErrorToast: boolean = true): Observable<GetResponse<ServiceRecipientResponse>> {
    return this.get(environment.crmApiUrl + 'orders/service-recipient', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_168(params: CRM_ORD_168, raiseErrorToast: boolean = true): Observable<PaginationResponse<WorkOrderResponse[]>> {
    return this.get(environment.crmApiUrl + 'orders/work-orders/sub-contractors/assignments', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_169(payload: CRM_ORD_169, raiseErrorToast: boolean = true): Observable<PutResponse<WorkOrderResponse>> {
    return this.put(environment.crmApiUrl + 'orders/work-orders/sub-contractors/assignments', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_170(params: CRM_ORD_170, raiseErrorToast: boolean = true): Observable<PaginationResponse<WorkOrderCompactResponse[]>> {
    return this.get(environment.crmApiUrl + 'orders/work-orders', params, {raiseErrorToast: raiseErrorToast})
  }


  crm_ord_171(params: CRM_ORD_171, raiseErrorToast: boolean = true): Observable<GetResponse<QuantityProposalOrderLineResponse[]>> {
      return this.get(environment.crmApiUrl + 'orders/work-orders/quantity-proposal', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_172(params: CRM_ORD_172, raiseErrorToast: boolean = true): Observable<PutResponse<WorkOrderResponse>> {
    return this.delete(environment.crmApiUrl + 'orders/work-orders/sub-contractors/assignments', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_177(payload: CRM_ORD_177, raiseErrorToast: boolean = true): Observable<PutResponse<null>> {
    return this.put(environment.crmApiUrl + 'orders/work-orders/addresses', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_178(payload: CRM_ORD_178, raiseErrorToast: boolean = true): Observable<PutResponse<null>> {
    return this.post(environment.crmApiUrl + 'orders/work-orders/schedules/work-orders/update-checklists', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_185(params: CRM_ORD_170, raiseErrorToast: boolean = true): Observable<PaginationResponse<WorkOrderResponse[]>> {
    return this.get(environment.crmApiUrl + 'orders/work-orders/full-response', params, {raiseErrorToast: raiseErrorToast})
  }

  /**
  *
  * Endpoint definitions
   * CRM
   * Embed
  *
  */

  crm_emb_0(payload: CRM_EMB_0, raiseErrorToast: boolean = true): Observable<PostResponse<EmbedResponse>> {
    return this.post(environment.crmApiUrl + 'embed/', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_emb_1(params: CRM_EMB_1, raiseErrorToast: boolean = true): Observable<GetResponse<EmbedResponse[]>> {
    return this.get(environment.crmApiUrl + 'embed/', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_emb_2(payload: CRM_EMB_2, raiseErrorToast: boolean = true): Observable<PutResponse<EmbedResponse>> {
    return this.patch(environment.crmApiUrl + 'embed/', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_emb_3(params: CRM_EMB_3, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'embed/', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_emb_4(params: CRM_EMB_4, raiseErrorToast: boolean = true): Observable<GetResponse<EmbedResponse>> {
    return this.get(environment.crmApiUrl + 'embed/id', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_emb_5(payload: CRM_EMB_5, raiseErrorToast: boolean = true): Observable<PostResponse<EmbedResponse>> {
    return this.post(environment.crmApiUrl + 'embed/products', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_emb_6(payload: CRM_EMB_6, raiseErrorToast: boolean = true): Observable<PutResponse<EmbedResponse>> {
    return this.patch(environment.crmApiUrl + 'embed/products', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_emb_7(params: CRM_EMB_7, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'embed/products', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_emb_8(payload: CRM_EMB_8, raiseErrorToast: boolean = true): Observable<PostResponse<SpecificationResponse>> {
    return this.post(environment.crmApiUrl + 'embed/products/specifications', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_emb_9(payload: CRM_EMB_9, raiseErrorToast: boolean = true): Observable<PutResponse<SpecificationResponse>> {
    return this.patch(environment.crmApiUrl + 'embed/products/specifications', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_emb_10(params: CRM_EMB_10, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'embed/products/specifications', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_emb_11(payload: CRM_EMB_11, raiseErrorToast: boolean = true): Observable<PostResponse<SpecificationChoiceResponse>> {
    return this.post(environment.crmApiUrl + 'embed/products/specifications/choices', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_emb_12(payload: CRM_EMB_12, raiseErrorToast: boolean = true): Observable<PutResponse<SpecificationChoiceResponse>> {
    return this.patch(environment.crmApiUrl + 'embed/products/specifications/choices', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_emb_13(params: CRM_EMB_13, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'embed/products/specifications/choices', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_emb_14(payload: CRM_EMB_14, raiseErrorToast: boolean = true): Observable<PostResponse<EmbedProductScheduleTemplateResponse>> {
    return this.post(environment.crmApiUrl + 'embed/products/schedule-templates', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_emb_15(params: CRM_EMB_15, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'embed/products/schedule-templates', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_emb_16(params: CRM_EMB_16, raiseErrorToast: boolean = true): Observable<GetResponse<EmbedProductScheduleTemplateResponse[]>> {
    return this.get(environment.crmApiUrl + 'embed/products/schedule-templates', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_emb_17(payload: CRM_EMB_17, raiseErrorToast: boolean = true): Observable<PutResponse<EmbedProductScheduleTemplateResponse>> {
    return this.patch(environment.crmApiUrl + 'embed/products/schedule-templates', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_emb_18(params: CRM_EMB_18, raiseErrorToast: boolean = true): Observable<GetResponse<InternalUserResponse | null>> {
    return this.get(environment.crmApiUrl + 'embed/seller', params, {raiseErrorToast: raiseErrorToast});
  }

  /**
  *
  * Endpoint definitions
   * CRM
   * Companies
  *
  */

  crm_coy_0(payload: CRM_COY_0, raiseErrorToast: boolean = true): Observable<PostResponse<CompanyResponse>> {
    return this.post(environment.crmApiUrl + 'companies/', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_1(params: CRM_COY_1, raiseErrorToast: boolean = true): Observable<GetResponse<CompanyResponse>> {
    return this.get(environment.crmApiUrl + 'companies/', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_2(params: CRM_COY_2, raiseErrorToast: boolean = true): Observable<PaginationResponse<CompanyResponse[]>> {
    return this.get(environment.crmApiUrl + 'companies/all', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_3(payload: CRM_COY_3, raiseErrorToast: boolean = true): Observable<PutResponse<CompanyResponse>> {
    return this.put(environment.crmApiUrl + 'companies/', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_4(payload: CRM_COY_4, raiseErrorToast: boolean = true): Observable<PutResponse<CompanyOpeningHoursResponse>> {
    return this.put(environment.crmApiUrl + 'companies/opening-hours', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_5(params: CRM_COY_5, raiseErrorToast: boolean = true): Observable<GetResponse<CompanyOpeningHoursResponse>> {
    return this.get(environment.crmApiUrl + 'companies/opening-hours', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_6(payload: CRM_COY_6, raiseErrorToast: boolean = true): Observable<PutResponse<CompanyThresholdResponse>> {
    return this.put(environment.crmApiUrl + 'companies/order-thresholds', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_7(params: CRM_COY_7, raiseErrorToast: boolean = true): Observable<GetResponse<CompanyThresholdResponse>> {
    return this.get(environment.crmApiUrl + 'companies/order-thresholds', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_8(payload: CRM_COY_8, raiseErrorToast: boolean = true): Observable<PutResponse<CompanyResponse>> {
    return this.getDefaultHeaders(true, null).pipe(
      switchMap((headers) => {
        let params = { 'company_id': payload.company_id };
        let options = { headers: headers, params: params };

        let formData = new FormData();
        formData.append('image', payload.image);

        return this.http.put<PutResponse<CompanyResponse>>(environment.crmApiUrl + 'companies/logo', formData, options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
        );
      })
    );
  }

  crm_coy_9(payload: CRM_COY_9, raiseErrorToast: boolean = true): Observable<PutResponse<CompanyTOCResponse>> {
    return this.put(environment.crmApiUrl + 'companies/terms-and-conditions', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_10(params: CRM_COY_10, raiseErrorToast: boolean = true): Observable<GetResponse<CompanyTOCResponse>> {
    return this.get(environment.crmApiUrl + 'companies/terms-and-conditions', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_11(payload: CRM_COY_11, raiseErrorToast: boolean = true): Observable<PutResponse<CompanyNotificationSettingsResponse[]>> {
    return this.put(environment.crmApiUrl + 'companies/settings/notifications', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_12(params: CRM_COY_12, raiseErrorToast: boolean = true): Observable<GetResponse<CompanyNotificationSettingsResponse[]>> {
    return this.get(environment.crmApiUrl + 'companies/settings/notifications', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_15(params: CRM_COY_15, raiseErrorToast: boolean = true): Observable<PostResponse<CompanyPaymentCredentialsResponse>> {
    return this.post(environment.crmApiUrl + 'companies/payment-credentials', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_16(params: CRM_COY_16, raiseErrorToast: boolean = true): Observable<GetResponse<CompanyPaymentCredentialsResponse[]>> {
    return this.get(environment.crmApiUrl + 'companies/payment-credentials', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_17(params: CRM_COY_17, raiseErrorToast: boolean = true): Observable<PutResponse<CompanyPaymentCredentialsResponse>> {
    return this.put(environment.crmApiUrl + 'companies/payment-credentials', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_19(params: CRM_COY_19, raiseErrorToast: boolean = true): Observable<GetResponse<CompanyEmployeeAttributesResponse>> {
    return this.get(environment.crmApiUrl + 'companies/employee-attributes', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_20(params: CRM_COY_20, raiseErrorToast: boolean = true): Observable<GetResponse<CompanyEmbedSettingsResponse>> {
    return this.get(environment.crmApiUrl + 'companies/embed-settings', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_21(payload: CRM_COY_21, raiseErrorToast: boolean = true): Observable<PutResponse<CompanyEmbedSettingsResponse>> {
    return this.put(environment.crmApiUrl + 'companies/embed-settings', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_22(payload: CRM_COY_22, raiseErrorToast: boolean = true): Observable<PutResponse<CompanyGeneralSettingsResponse>> {
    return this.patch(environment.crmApiUrl + 'companies/settings/general', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_23(params: CRM_COY_23, raiseErrorToast: boolean = true): Observable<GetResponse<CompanyGeneralSettingsResponse>> {
    return this.get(environment.crmApiUrl + 'companies/settings/general', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_25(params: CRM_COY_25, raiseErrorToast: boolean = true): Observable<GetResponse<CompanyPaymentMethodResponse[]>> {
    return this.get(environment.crmApiUrl + 'companies/payment-methods', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_26(payload: CRM_COY_26, raiseErrorToast: boolean = true): Observable<PutResponse<CompanyPaymentMethodResponse>> {
    return this.patch(environment.crmApiUrl + 'companies/payment-methods', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_27(params: CRM_COY_27, raiseErrorToast: boolean = true): Observable<GetResponse<InternalUserResponse[]>> {
    return this.get(environment.crmApiUrl + 'companies/contacts', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_29(raiseErrorToast: boolean = true): Observable<GetResponse<CompanyTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'companies/company-types', undefined, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_31(params: CRM_COY_31, raiseErrorToast: boolean = true): Observable<GetResponse<CompanyDefaultAddressValuesResponse[]>> {
    return this.get(environment.crmApiUrl + 'companies/default-address-values', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_32(payload: CRM_COY_32, raiseErrorToast: boolean = true): Observable<PutResponse<CompanyDefaultAddressValuesResponse>> {
    return this.patch(environment.crmApiUrl + 'companies/default-address-values', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_33(payload: CRM_COY_33, raiseErrorToast: boolean = true): Observable<GetResponse<OrderPaymentResponse>> {
    return this.get(environment.crmApiUrl + 'companies/admin/subscription/payment/main', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_34(payload: CRM_COY_34, raiseErrorToast: boolean = true): Observable<PaginationResponse<OrderPaymentResponse[]>> {
    return this.get(environment.crmApiUrl + 'companies/admin/subscription/payment/all', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_35(params: CRM_COY_35, raiseErrorToast: boolean = true): Observable<GetResponse<CompanyTableSetupResponse[]>> {
    return this.get(environment.crmApiUrl + 'companies/table-setups', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_36(payload: CRM_COY_36, raiseErrorToast: boolean = true): Observable<PostResponse<CompanyTableSetupResponse>> {
    return this.post(environment.crmApiUrl + 'companies/table-setups', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_coy_37(payload: CRM_COY_37, raiseErrorToast: boolean = true): Observable<PutResponse<CompanyTableSetupResponse>> {
    return this.put(environment.crmApiUrl + 'companies/table-setups', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_38(params: CRM_COY_38, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'companies/table-setups', params, {raiseErrorToast: raiseErrorToast});
  }

  /**
  *
  * Endpoint definitions
   * CRM
   * Products
  *
  */

  crm_prd_0(payload: CRM_PRD_0, raiseErrorToast: boolean = true): Observable<PostResponse<ProductBaseResponse>> {
    return this.post(environment.crmApiUrl + 'products/', payload, {raiseErrorToast: raiseErrorToast});
  }


  crm_prd_1(params: CRM_PRD_1, raiseErrorToast: boolean = true): Observable<PaginationResponse<ProductResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_2(payload: CRM_PRD_2, raiseErrorToast: boolean = true): Observable<PutResponse<ProductBaseResponse>> {
    return this.put(environment.crmApiUrl + 'products/', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_65(payload: CRM_PRD_65, raiseErrorToast: boolean = true): Observable<PutResponse<ProductResponse>> {
    return this.patch(environment.crmApiUrl + 'products/', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_3(params: CRM_PRD_3, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'products/', params, {raiseErrorToast: raiseErrorToast});
  }


  crm_prd_8(raiseErrorToast: boolean = true): Observable<GetResponse<PropertyTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/property-types', undefined, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_9(raiseErrorToast: boolean = true): Observable<GetResponse<UnitTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/units', undefined, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_11(payload: CRM_PRD_11, raiseErrorToast: boolean = true): Observable<PostResponse<PriceRuleResponse>> {
    return this.post(environment.crmApiUrl + 'products/price-rules', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_12(params: CRM_PRD_12, raiseErrorToast: boolean = true): Observable<GetResponse<PriceRuleResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/price-rules', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_13(payload: CRM_PRD_13, raiseErrorToast: boolean = false): Observable<PutResponse<PriceRuleResponse>> {
    return this.put(environment.crmApiUrl + 'products/price-rules', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_14(params: CRM_PRD_14, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'products/price-rules', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_15(payload: CRM_PRD_15, raiseErrorToast: boolean = true): Observable<PostResponse<PriceRuleGroupResponse>> {
    return this.post(environment.crmApiUrl + 'products/price-rules/groups', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_16(params: CRM_PRD_16, raiseErrorToast: boolean = true): Observable<GetResponse<PriceRuleGroupResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/price-rules/groups', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_17(payload: CRM_PRD_17, raiseErrorToast: boolean = true): Observable<PutResponse<PriceRuleGroupResponse>> {
    return this.put(environment.crmApiUrl + 'products/price-rules/groups', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_18(params: CRM_PRD_18, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'products/price-rules/groups', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_29(payload: CRM_PRD_29, raiseErrorToast: boolean = true): Observable<PostResponse<UpsellProductResponse>> {
    return this.post(environment.crmApiUrl + 'products/upsell', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_30(params: CRM_PRD_30, raiseErrorToast: boolean = true): Observable<GetResponse<UpsellProductResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/upsell', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_32(params: CRM_PRD_32, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'products/upsell', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_33(raiseErrorToast: boolean = true): Observable<GetResponse<ProductTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/product-types', undefined, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_34(raiseErrorToast: boolean = true): Observable<GetResponse<UpsellProductResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/upsell/customer', undefined, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_35(params: CRM_PRD_35, raiseErrorToast: boolean = true): Observable<GetResponse<ProductStageResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/stages', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_36(payload: CRM_PRD_36, raiseErrorToast: boolean = true): Observable<PostResponse<ProductStageResponse>> {
    return this.post(environment.crmApiUrl + 'products/stages', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_37(payload: CRM_PRD_37, raiseErrorToast: boolean = true): Observable<PutResponse<ProductStageResponse>> {
      return this.put(environment.crmApiUrl + 'products/stages', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_38(params: CRM_PRD_38, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'products/stages', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_45(payload: CRM_PRD_45, raiseErrorToast: boolean = true): Observable<PostResponse<ProductInformationResponse>> {
    return this.post(environment.crmApiUrl + 'products/information', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_46(payload: CRM_PRD_46, raiseErrorToast: boolean = true): Observable<PutResponse<ProductInformationResponse>> {
    return this.put(environment.crmApiUrl + 'products/information', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_47(params: CRM_PRD_47, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'products/information', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_48(params: CRM_PRD_48, raiseErrorToast: boolean = true): Observable<GetResponse<ProductInformationResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/information', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_49(params: CRM_PRD_49, raiseErrorToast: boolean = true): Observable<GetResponse<TriggerTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/trigger-types', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_50(payload: CRM_PRD_50, raiseErrorToast: boolean = true): Observable<PostResponse<SpecificationResponse>> {
    return this.post(environment.crmApiUrl + 'products/specifications', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_51(payload: CRM_PRD_51, raiseErrorToast: boolean = true): Observable<PutResponse<SpecificationResponse>> {
    return this.put(environment.crmApiUrl + 'products/specifications', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_52(params: CRM_PRD_52, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'products/specifications', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_53(params: CRM_PRD_53, raiseErrorToast: boolean = true): Observable<GetResponse<SpecificationResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/specifications', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_54(payload: CRM_PRD_54, raiseErrorToast: boolean = true): Observable<PostResponse<SpecificationChoiceResponse>> {
    return this.post(environment.crmApiUrl + 'products/specifications/choices', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_55(payload: CRM_PRD_55, raiseErrorToast: boolean = true): Observable<PutResponse<SpecificationChoiceResponse>> {
    return this.put(environment.crmApiUrl + 'products/specifications/choices', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_56(params: CRM_PRD_56, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'products/specifications/choices', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_57(params: CRM_PRD_57, raiseErrorToast: boolean = true): Observable<PaginationResponse<ProductBaseResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/base', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_58(raiseErrorToast: boolean = true): Observable<GetResponse<PriceRuleCalculationTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/price-rules/calculation-types', undefined, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_59(params: CRM_PRD_59, raiseErrorToast: boolean = true): Observable<GetResponse<ProductAttributeChoicesResponse>> {
    return this.get(environment.crmApiUrl + 'products/attribute-choices', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_60(params: CRM_PRD_60, raiseErrorToast: boolean = true): Observable<GetResponse<UpsellProductResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/upsell/company', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_61(payload: CRM_PRD_61, raiseErrorToast: boolean = true): Observable<PutResponse<ProductBaseResponse>> {
    return this.put(environment.crmApiUrl + 'products/inventory', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_62(params: CRM_PRD_62, raiseErrorToast: boolean = true): Observable<GetResponse<TriggerTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/trigger-types/product', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_64(payload: CRM_PRD_64, raiseErrorToast: boolean = true): Observable<PostResponse<SimulatedOrderDataResponse[]>> {
    return this.post(environment.crmApiUrl + 'products/price-rules/test', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_66(payload: CRM_PRD_66, raiseErrorToast: boolean = true): Observable<GetResponse<PartnerPriceRuleGroupResponse[]>>{
    return this.get(environment.crmApiUrl + 'products/affiliates/price-rules', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_prd_67(payload: CRM_PRD_67, raiseErrorToast: boolean = true): Observable<PutResponse<PartnerPriceRuleResponse>> {
    return this.put(environment.crmApiUrl + 'products/affiliates/price-rules', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_68(params: CRM_PRD_68, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'products/affiliates/price-rules', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_77(payload: CRM_PRD_77, raiseErrorToast: boolean = true): Observable<PostResponse<CalculatedOrderResponse>> {
    return this.post(environment.crmApiUrl + 'products/price-calculations', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_78(payload: CRM_PRD_78, raiseErrorToast: boolean = true): Observable<GetResponse<AccountingProductResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/accounting', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_80(raiseErrorToast: boolean = true): Observable<GetResponse<ProductQuantityCalculationTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/quantity-calculations/calculation-types', undefined, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_81(raiseErrorToast: boolean = true): Observable<GetResponse<ProductQuantityCalculationValueSourceResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/quantity-calculations/value-sources', undefined, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_82(params: CRM_PRD_82, raiseErrorToast: boolean = true): Observable<GetResponse<ProductQuantityCalculationResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/quantity-calculations', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_83(payload: CRM_PRD_83, raiseErrorToast: boolean = true): Observable<PostResponse<ProductQuantityCalculationResponse>> {
    return this.post(environment.crmApiUrl + 'products/quantity-calculations', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_84(payload: CRM_PRD_84, raiseErrorToast: boolean = true): Observable<PutResponse<ProductQuantityCalculationResponse>> {
    return this.patch(environment.crmApiUrl + 'products/quantity-calculations', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_85(params: CRM_PRD_85, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'products/quantity-calculations', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_86(payload: CRM_PRD_86, raiseErrorToast: boolean = true): Observable<PostResponse<ProductQuantityCalculationTestResponse>> {
    return this.post(environment.crmApiUrl + 'products/quantity-calculations/test', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_87(params: CRM_PRD_87, raiseErrorToast: boolean = true): Observable<GetResponse<ProductResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/affiliate/products', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_88(params: CRM_PRD_88, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'products/affiliate/products', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_90(params: CRM_PRD_90, raiseErrorToast: boolean = true): Observable<GetResponse<ProductCompactResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/compact', params, {raiseErrorToast: raiseErrorToast});
  }


  /**
  *
  * Endpoint definitions
   * CRM
   * Product Categories
  *
  */

  crm_pcg_0(payload: CRM_PCG_0, raiseErrorToast: boolean = true): Observable<PostResponse<ProductCategoryResponse>> {
    return this.post(environment.crmApiUrl + 'product-categories/', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_pcg_1(params: CRM_PCG_1, raiseErrorToast: boolean = true): Observable<PaginationResponse<ProductCategoryResponse[]>> {
    return this.get(environment.crmApiUrl + 'product-categories/company', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_pcg_2(params: CRM_PCG_2, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'product-categories/', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_pcg_3(payload: CRM_PCG_3, raiseErrorToast: boolean = true): Observable<PutResponse<ProductCategoryResponse>> {
    return this.put(environment.crmApiUrl + 'product-categories/', payload, {raiseErrorToast: raiseErrorToast});
  }

  /**
  *
  * Endpoint definitions
   * CRM
   * Stages
  *
  */

  crm_stg_4(params: CRM_STG_4, raiseErrorToast: boolean = true): Observable<GetResponse<IncidentResponse>> {
    return this.get(environment.crmApiUrl + 'stages/incidents', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_stg_5(payload: CRM_STG_5, raiseErrorToast: boolean = true): Observable<PostResponse<IncidentResponse>> {
    return this.post(environment.crmApiUrl + 'stages/incidents', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_stg_6(params: CRM_STG_6, raiseErrorToast: boolean = true): Observable<GetResponse<IncidentResponse>> {
    return this.get(environment.crmApiUrl + 'stages/incidents/order', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_stg_7(payload: CRM_STG_7, raiseErrorToast: boolean = true): Observable<PutResponse<WorkOrderTaskResponse>> {
    return this.patch(environment.crmApiUrl + 'stages/tasks/status', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_stg_8(raiseErrorToast: boolean = true): Observable<GetResponse<IncidentTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'stages/incidents/types', undefined, {raiseErrorToast: raiseErrorToast});
  }

  crm_stg_10(payload: CRM_STG_10, raiseErrorToast: boolean = true): Observable<PostResponse<null>> {
    return this.getDefaultHeaders(true, null).pipe(
      switchMap((headers) => {
        let params = { 'incident_id': payload.incident_id, 'company_id': payload.company_id };
        let options = { headers: headers, params: params };

        let formData = new FormData();
        formData.append('image', payload.image);

        return this.http.post<PostResponse<null>>(environment.crmApiUrl + 'stages/incidents/image', formData, options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
        );
      })
    );
  }

  crm_stg_11(payload: CRM_STG_11, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'stages/incidents', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_stg_12(payload: CRM_STG_12, raiseErrorToast: boolean = true): Observable<PutResponse<OrderCustomerQuestionChoiceResponse>> {
    return this.patch(environment.crmApiUrl + 'stages/stage-specifications/set-choice', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_stg_15(payload: CRM_STG_15, raiseErrorToast: boolean = true): Observable<PutResponse<OrderCustomerQuestionChoiceResponse>> {
    return this.patch(environment.crmApiUrl + 'stages/stage-specifications/set-choice/customer', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_stg_16(payload: CRM_STG_16, raiseErrorToast: boolean = true): Observable<PutResponse<OrderLineStageResponse>> {
    return this.put(environment.crmApiUrl + 'stages/pause-unpause', payload, {raiseErrorToast: raiseErrorToast});
  }

  /**
  *
  * Endpoint definitions
   * CRM
   * Cargo
  *
  */

  crm_cgo_0(raiseErrorToast: boolean = true): Observable<GetResponse<CargoTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'cargo/', undefined, {raiseErrorToast: raiseErrorToast});
  }

  /**
  *
  * Endpoint definitions
   * CRM
   * Payment
  *
  */

  crm_pay_2(payload: CRM_PAY_2, raiseErrorToast: boolean = true): Observable<PostResponse<OrderResponse>> {
    return this.post(environment.crmApiUrl + 'payment/refund', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_5(raiseErrorToast: boolean = true): Observable<GetResponse<PaymentStatusResponse[]>> {
    return this.get(environment.crmApiUrl + 'payment/payment-statuses', undefined, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_6(params: CRM_PAY_6, raiseErrorToast: boolean = true): Observable<GetResponse<PaymentMethodResponse[]>> {
    return this.get(environment.crmApiUrl + 'payment/payment-methods', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_7(payload: CRM_PAY_7, raiseErrorToast: boolean = true): Observable<PostResponse<OrderPaymentResponse>> {
    return this.post(environment.crmApiUrl + 'payment/send-to-payment', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_8(payload: CRM_PAY_8, raiseErrorToast: boolean = true): Observable<GetResponse<PaymentMethodResponse[]>> {
    return this.get(environment.crmApiUrl + 'payment/payment-methods/customer', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_11(raiseErrorToast: boolean = true): Observable<GetResponse<VatRateResponse[]>> {
    return this.get(environment.crmApiUrl + 'payment/vat-rates', undefined, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_16(params: CRM_PAY_16, raiseErrorToast: boolean = true): Observable<GetResponse<InvoiceSendTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'payment/invoice/send-types', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_pay_17(payload: CRM_PAY_17, raiseErrorToast: boolean = true): Observable<PostResponse<{url: string}>> {
    return this.post<PostResponse<{url: string}>>(environment.crmApiUrl + 'payment/subscriptions', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_23(payload: CRM_PAY_23, raiseErrorToast: boolean = true): Observable<PostResponse<OrderPaymentResponse>> {
    return this.post(environment.crmApiUrl + 'payment/credit-invoice', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_24(payload: CRM_PAY_24, raiseErrorToast: boolean = true): Observable<PostResponse<OrderPaymentResponse>> {
    return this.post(environment.crmApiUrl + 'payment/capture/manually', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_25(params: CRM_PAY_25, raiseErrorToast: boolean = true): Observable<GetResponse<OrderPaymentResponse>> {
    return this.get(environment.crmApiUrl + 'payment/accounting/check-payment-status', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_26(payload: CRM_PAY_26, raiseErrorToast: boolean = true): Observable<PostResponse<OrderPaymentResponse>> {
    return this.post(environment.crmApiUrl + 'payment/order', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_27(payload: CRM_PAY_27, raiseErrorToast: boolean = true): Observable<PutResponse<OrderPaymentResponse>> {
    return this.put(environment.crmApiUrl + 'payment/order', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_28(params: CRM_PAY_28, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'payment/order', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_29(params: CRM_PAY_29, raiseErrorToast: boolean = true): Observable<GetResponse<OrderPaymentResponse>> {
    return this.get(environment.crmApiUrl + 'payment/order', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_30(payload: CRM_PAY_30, raiseErrorToast: boolean = true): Observable<GetResponse<OrderPaymentResponse[]>> {
    return this.get(environment.crmApiUrl + 'payment/order/all', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_31(payload: CRM_PAY_31, raiseErrorToast: boolean = true): Observable<PutResponse<OrderPaymentResponse>> {
    return this.post(environment.crmApiUrl + 'payment/accounting/synchronise', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_pay_32(params: { [key: string]: any }, raiseErrorToast: boolean = true): Observable<Blob> {
    return this.getDefaultHeaders(true, 'application/pdf').pipe(
      switchMap((headers) => {
        headers.set('Accept', 'application/pdf');
        let httpParams = new HttpParams();
        Object.keys(params).forEach(key => {
          const value = params[key];
          if (value !== undefined && value !== null) {
            httpParams = httpParams.set(key, value);
          }
        });
        let options = { headers: headers, params: httpParams, responseType: 'blob' as 'json' };
        return this.http.get<Blob>(environment.crmApiUrl + 'payment/invoice/pdf', options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
        );
      })
    );
  }

  crm_pay_33(payload: CRM_PAY_33, raiseErrorToast: boolean = true): Observable<PutResponse<OrderPaymentResponse>> {
    return this.put(environment.crmApiUrl + 'payment/mark-as-paid', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_pay_34(payload: CRM_PAY_34, raiseErrorToast: boolean = true): Observable<PostResponse<null>> {
    return this.post(environment.crmApiUrl + 'payment/send-payment-sms', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_pay_35() {
    // Used in customer
    return 'payments/order-payments/as-user'
  }

  crm_pay_36(payload: CRM_PAY_36, raiseErrorToast: boolean = true): Observable<PostResponse<ScheduleExecutionDatePreviewResponse>> {
    return this.post(environment.crmApiUrl + 'payment/schedules/execution-date-preview', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_pay_37(payload: CRM_PAY_37, raiseErrorToast: boolean = true): Observable<PostResponse<null>> {
    return this.post(environment.crmApiUrl + 'payment/schedules/initiate', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_pay_38(payload: CRM_PAY_38, raiseErrorToast: boolean = true): Observable<PostResponse<OrderPaymentResponse>> {
    return this.post(environment.crmApiUrl + 'payment/schedules/execute', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_pay_41(params: CRM_PAY_41, raiseErrorToast: boolean = true): Observable<PaginationResponse<OrderPaymentResponseCompact[]>> {
    return this.get(environment.crmApiUrl + 'payment/company', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_42(payload: CRM_PAY_42, raiseErrorToast: boolean = true): Observable<GetResponse<null>> {
    return this.post(environment.crmApiUrl + 'payment/initiate-consolidation-invoice', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_43(payload: CRM_PAY_43, raiseErrorToast: boolean = true): Observable<GetResponse<null>> {
    return this.post(environment.crmApiUrl + 'payment/send-receipt', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_44(params: CRM_PAY_44, raiseErrorToast: boolean = true): Observable<GetResponse<{total: number}>> {
    return this.get(environment.crmApiUrl + 'payment/total', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_45(params: CRM_PAY_41, raiseErrorToast: boolean = true): Observable<PaginationResponse<OrderPaymentResponse[]>> {
    return this.get(environment.crmApiUrl + 'payment/company/complete-response', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_46(params: CRM_PAY_46, raiseErrorToast: boolean = true): Observable<GetResponse<PaymentLogEntryResponse[]>> {
    return this.get(environment.crmApiUrl + 'payment/logs', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_48(raiseErrorToast: boolean = true): Observable<GetResponse<PaymentScheduleOptionResponse[]>> {
    return this.get(environment.crmApiUrl + 'payment/schedule-options', undefined, {raiseErrorToast: raiseErrorToast});
  }

  /**
   * Endpoint definitions
   * CRM
   * Affiliates
   */

  crm_aff_0(payload: CRM_AFF_0, raiseErrorToast: boolean = true): Observable<PostResponse<AffiliateResponse>> {
    return this.post(environment.crmApiUrl + 'affiliates/', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_aff_1(params: CRM_AFF_1, raiseErrorToast: boolean = true): Observable<PaginationResponse<AffiliateResponse[]>> {
    return this.get(environment.crmApiUrl + 'affiliates/', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_aff_2(payload: CRM_AFF_2, raiseErrorToast: boolean = true): Observable<PutResponse<AffiliateResponse>> {
    return this.patch(environment.crmApiUrl + 'affiliates/', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_aff_3(params: CRM_AFF_3, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'affiliates/', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_aff_4(params: CRM_AFF_4, raiseErrorToast: boolean = true): Observable<GetResponse<AffiliateResponse>> {
    return this.get(environment.crmApiUrl + 'affiliates/id', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_aff_5(payload: CRM_AFF_5, raiseErrorToast: boolean = true): Observable<PostResponse<AffiliateContactResponse>> {
    return this.post(environment.crmApiUrl + 'affiliates/contacts', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_aff_6(params: CRM_AFF_6, raiseErrorToast: boolean = true): Observable<GetResponse<AffiliateContactResponse[]>> {
    return this.get(environment.crmApiUrl + 'affiliates/contacts', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_aff_7(payload: CRM_AFF_7, raiseErrorToast: boolean = true): Observable<PutResponse<AffiliateContactResponse>> {
    return this.patch(environment.crmApiUrl + 'affiliates/contacts', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_aff_8(params: CRM_AFF_8, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'affiliates/contacts', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_aff_9(params: CRM_AFF_9, raiseErrorToast: boolean = true): Observable<GetResponse<AffiliateSearchResponse[]>> {
    return this.get(environment.crmApiUrl + 'affiliates/search', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_aff_10(params: CRM_AFF_10, raiseErrorToast: boolean = true): Observable<GetResponse<ReversedAffiliateResponse[]>> {
    return this.get(environment.crmApiUrl + 'affiliates/contractor-relations', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_aff_11(payload: CRM_AFF_11, raiseErrorToast: boolean = true): Observable<PutResponse<ReversedAffiliateResponse>> {
    return this.patch(environment.crmApiUrl + 'affiliates/contractor-relations', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_aff_13(params: CRM_AFF_13, raiseErrorToast: boolean = true): Observable<GetResponse<UnitDetails[]>> {
    return this.get(environment.crmApiUrl + 'affiliates/addresses', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_aff_14(payload: CRM_AFF_14, raiseErrorToast: boolean = true): Observable<PostResponse<UnitDetails>> {
    return this.post(environment.crmApiUrl + 'affiliates/addresses', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_aff_15(payload: CRM_AFF_15, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'affiliates/addresses', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_aff_16(payload: CRM_AFF_16, raiseErrorToast: boolean = true): Observable<PostResponse<AffiliateNoteResponse>> {
    return this.post(environment.crmApiUrl + 'affiliates/notes', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_aff_17(params: CRM_AFF_17, raiseErrorToast: boolean = true): Observable<GetResponse<AffiliateNoteResponse[]>> {
    return this.get(environment.crmApiUrl + 'affiliates/notes', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_aff_18(params: CRM_AFF_18, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'affiliates/notes', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_aff_19(payload: CRM_AFF_19, raiseErrorToast: boolean = true): Observable<PutResponse<AffiliateNoteResponse>> {
    return this.put(environment.crmApiUrl + 'affiliates/notes', payload, {raiseErrorToast: raiseErrorToast});
  }

  /**
   * Endpoint definitions
   * CRM
   * Customers
   */

  crm_cus_0(params: CRM_CUS_0, raiseErrorToast: boolean = true): Observable<PaginationResponse<CustomerResponse[]>> {
    return this.get(environment.crmApiUrl + 'customers/', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_cus_1(payload: CRM_CUS_1, raiseErrorToast: boolean = true): Observable<PostResponse<AffiliateResponse>> {
    return this.post(environment.crmApiUrl + 'customers/', payload, {raiseErrorToast: raiseErrorToast});
  }

  // crm_cus_4(payload: CRM_CUS_4, raiseErrorToast: boolean = true): Observable<PostResponse<BusinessCustomerResponse>> {
  //   return this.post(environment.crmApiUrl + 'customers/business', payload, {raiseErrorToast: raiseErrorToast});
  // }

  // crm_cus_5(payload: CRM_CUS_5, raiseErrorToast: boolean = true): Observable<PutResponse<BusinessCustomerResponse>> {
  //   return this.patch(environment.crmApiUrl + 'customers/business', payload, {raiseErrorToast: raiseErrorToast});
  // }

  // crm_cus_6(params: CRM_CUS_6, raiseErrorToast: boolean = true): Observable<PaginationResponse<BusinessCustomerResponse[]>> {
  //   return this.get(environment.crmApiUrl + 'customers/business', params, {raiseErrorToast: raiseErrorToast});
  // }

  // crm_cus_8(params: CRM_CUS_8, raiseErrorToast: boolean = true): Observable<GetResponse<BusinessCustomerResponse>> {
  //   return this.get(environment.crmApiUrl + 'customers/business/id', params, {raiseErrorToast: raiseErrorToast});
  // }

  // crm_cus_10(params: CRM_CUS_10, raiseErrorToast: boolean = true): Observable<PaginationResponse<PrivateCustomerResponse[]>> {
  //   return this.get(environment.crmApiUrl + 'customers/private', params, {raiseErrorToast: raiseErrorToast});
  // }

  // crm_cus_11(payload: CRM_CUS_11, raiseErrorToast: boolean = true): Observable<PostResponse<PrivateCustomerResponse>> {
  //   return this.post(environment.crmApiUrl + 'customers/private', payload, {raiseErrorToast: raiseErrorToast});
  // }
  crm_cus_12(payload: CRM_CUS_12, raiseErrorToast: boolean = true): Observable<PutResponse<AffiliateResponse>> {
    return this.patch(environment.crmApiUrl + 'customers/private', payload, {raiseErrorToast: raiseErrorToast});
  }
  crm_cus_14(params: CRM_CUS_14, raiseErrorToast: boolean = true): Observable<GetResponse<CustomerAggregatedDataResponse>> {
    return this.get(environment.crmApiUrl + 'customers/orders/aggregated', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_cus_15(payload: CRM_CUS_15, raiseErrorToast: boolean = true): Observable<PostResponse<AffiliateResponse>> {
    return this.post(environment.crmApiUrl + 'customers/as-company', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_cus_16(params: CRM_CUS_16, raiseErrorToast: boolean = true): Observable<GetResponse<CustomerAccountingResponse[]>> {
    return this.get(environment.crmApiUrl + 'customers/accounting', params, {raiseErrorToast: raiseErrorToast});
  }

  // crm_cus_18(params: CRM_CUS_18, raiseErrorToast: boolean = true): Observable<GetResponse<PrivateCustomerResponse>> {
  //   return this.get(environment.crmApiUrl + 'customers/private/id', params, {raiseErrorToast: raiseErrorToast});
  // }

  crm_cus_19(params: CRM_CUS_19, raiseErrorToast: boolean = true): Observable<GetResponse<CustomerEditableResponse>> {
    return this.get(environment.crmApiUrl + 'customers/editable', params, {raiseErrorToast: raiseErrorToast});
  }

  /**
   * Endpoint definitions
   * CRM
   * Reports
   */

  crm_rep_0(params: CRM_REP_0, raiseErrorToast: boolean = true): Observable<GetResponse<ReportFinancialSummaryResponse>> {
    return this.get(environment.crmApiUrl + 'reports/financial-summary', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_rep_1(params: { [key: string]: any }, raiseErrorToast: boolean = true): Observable<Blob> {
    return this.getDefaultHeaders(true, 'application/pdf').pipe(
      switchMap((headers) => {
        headers.set('Accept', 'application/pdf');
        let httpParams = new HttpParams();
        Object.keys(params).forEach(key => {
          const value = params[key];
          if (value !== undefined && value !== null) {
            httpParams = httpParams.set(key, value);
          }
        });
        let options = { headers: headers, params: httpParams, responseType: 'blob' as 'json' };
        return this.http.get<Blob>(environment.crmApiUrl + 'reports/sales-reports/pdf', options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
        );
      })
    );
  }

  crm_rep_2(params: { [key: string]: any }, raiseErrorToast: boolean = true): Observable<Blob> {
    return this.getDefaultHeaders(true, 'application/pdf').pipe(
      switchMap((headers) => {
        headers.set('Accept', 'application/pdf');
        let httpParams = new HttpParams();
        Object.keys(params).forEach(key => {
          const value = params[key];
          if (value !== undefined && value !== null) {
            httpParams = httpParams.set(key, value);
          }
        });
        let options = { headers: headers, params: httpParams, responseType: 'blob' as 'json' };
        return this.http.get<Blob>(environment.crmApiUrl + 'reports/transaction-reports/pdf', options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
        );
      })
    );
  }

  crm_rep_3(params: { [key: string]: any }, raiseErrorToast: boolean = true): Observable<Blob> {
    return this.getDefaultHeaders(true, 'application/xlsx').pipe(
      switchMap((headers) => {
        headers.set('Accept', 'application/xlsx');
        let httpParams = new HttpParams();
        Object.keys(params).forEach(key => {
          const value = params[key];
          if (value !== undefined && value !== null) {
            httpParams = httpParams.set(key, value);
          }
        });
        let options = { headers: headers, params: httpParams, responseType: 'blob' as 'json' };
        return this.http.get<Blob>(environment.crmApiUrl + 'reports/transaction-reports/xlsx', options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
        );
      })
    );
  }

  /**
   * Endpoint definitions
   * CRM
   * Addresses
   */

  crm_adr_1(params: CRM_ADR_1, raiseErrorToast: boolean = true): Observable<GetResponse<AddressSearchResultItemResponse[]>> {
    return this.get(environment.crmApiUrl + 'addresses/search/address/addresses', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_adr_3(params: CRM_ADR_3, raiseErrorToast: boolean = true): Observable<GetResponse<AddressDetailsResponse>> {
    return this.get(environment.crmApiUrl + 'addresses/unit/id', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_adr_4(params: CRM_ADR_4, raiseErrorToast: boolean = true): Observable<GetResponse<AddressDetailsResponse>> {
    return this.get(environment.crmApiUrl + 'addresses/unit/section', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_adr_5(params: CRM_ADR_5, raiseErrorToast: boolean = true): Observable<GetResponse<AddressDetailsResponse>> {
    return this.get(environment.crmApiUrl + 'addresses/search/address/direct', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_adr_6(params: CRM_ADR_6, raiseErrorToast: boolean = false): Observable<GetResponse<AddressSearchResultItemResponse[]>> {
    return this.get(environment.crmApiUrl + 'addresses/ambita/address/search', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_adr_7(params: CRM_ADR_7, raiseErrorToast: boolean = false): Observable<GetResponse<AddressDetailsResponse>> {
    return this.get(environment.crmApiUrl + 'addresses/ambita/unit/details', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_adr_8(params: CRM_ADR_8, raiseErrorToast: boolean = true): Observable<PostResponse<UnitDetails>> {
    return this.post(environment.crmApiUrl + 'addresses/entry', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_adr_9(params: CRM_ADR_9, raiseErrorToast: boolean = true): Observable<GetResponse<UnitDetails>> {
    return this.get(environment.crmApiUrl + 'addresses/entry', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_adr_10(payload: CRM_ADR_10, raiseErrorToast: boolean = true): Observable<PutResponse<UnitDetails>> {
    return this.put(environment.crmApiUrl + 'addresses/entry', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_adr_12(params: CRM_ADR_12, raiseErrorToast: boolean = true): Observable<GetResponse<UnitDetails | null>> {
    return this.get(environment.crmApiUrl + 'addresses/affiliate/last-used', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_adr_13(raiseErrorToast: boolean = true): Observable<GetResponse<PropertyOwnershipTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'addresses/property-ownership-types', undefined, {raiseErrorToast: raiseErrorToast});
  }

   /**
   * Endpoint definitions
   * CRM
   * Integrations
   */

 crm_int_7(payload: CRM_INT_7, raiseErrorToast: boolean = false): Observable<PutResponse<null>> {
  return this.put(environment.crmApiUrl + 'integrations/accounting/tripletex', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_int_8(params: CRM_INT_8, raiseErrorToast: boolean = true): Observable<GetResponse<CompanyAccountResponse[]>> {
    return this.get(environment.crmApiUrl + 'integrations/accounting/accounts', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_int_11(payload: CRM_INT_11, raiseErrorToast: boolean = true): Observable<PostResponse<null>> {
    return this.post(environment.crmApiUrl + 'integrations/accounting/start-date', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_int_12(params: CRM_INT_12, raiseErrorToast: boolean = true): Observable<GetResponse<TripletexPaymentMethodResponse[]>> {
    return this.get(environment.crmApiUrl + 'integrations/accounting/tripletex/payment-methods', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_int_13(payload: CRM_INT_13, raiseErrorToast: boolean = true): Observable<PutResponse<CompanyPaymentMethodResponse[]>> {
    return this.put(environment.crmApiUrl + 'integrations/accounting/tripletex/payment-methods', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_int_14(params: CRM_INT_14, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'integrations/accounting/disable', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_int_16(params: CRM_INT_16, raiseErrorToast: boolean = true): Observable<GetResponse<PogoAccountResponse[]>> {
    return this.get(environment.crmApiUrl + 'integrations/accounting/pogo/accounts', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_int_17(payload: CRM_INT_17, raiseErrorToast: boolean = false): Observable<PutResponse<null>> {
    return this.put(environment.crmApiUrl + 'integrations/accounting/pogo', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_int_18(payload: CRM_INT_18, raiseErrorToast: boolean = true): Observable<PutResponse<PogoAccountResponse[]>> {
    return this.put(environment.crmApiUrl + 'integrations/accounting/pogo/accounts', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_int_19(payload: CRM_INT_19, raiseErrorToast: boolean = true): Observable<PutResponse<null>> {
    return this.put(environment.crmApiUrl + 'integrations/accounting/fiken', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_int_20(params: CRM_INT_20, raiseErrorToast: boolean = true): Observable<GetResponse<FikenAccountResponse[]>> {
    return this.get(environment.crmApiUrl + 'integrations/accounting/fiken/accounts', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_int_21(payload: CRM_INT_21, raiseErrorToast: boolean = true): Observable<PutResponse<CompanyPaymentMethodResponse[]>> {
    return this.put(environment.crmApiUrl + 'integrations/accounting/fiken/accounts', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_int_22(payload: CRM_INT_22, raiseErrorToast: boolean = true): Observable<PutResponse<null>> {
    return this.put(environment.crmApiUrl + 'integrations/accounting/fiken/accounts/clearhaus', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_int_29(params: CRM_INT_29, raiseErrorToast: boolean = true): Observable<GetResponse<ExternalActivityResponse[]>> {
    return this.get(environment.crmApiUrl + 'integrations/accounting/activities', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_int_30(params: CRM_INT_30, raiseErrorToast: boolean = true): Observable<GetResponse<ExternalSalaryTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'integrations/accounting/salary-types', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_int_31(params: CRM_INT_31, raiseErrorToast: boolean = true): Observable<GetResponse<ExternalProjectResponse[]>> {
    return this.get(environment.crmApiUrl + 'integrations/accounting/projects', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_int_32(params: CRM_INT_32, raiseErrorToast: boolean = true): Observable<GetResponse<ExternalDepartmentResponse[]>> {
    return this.get(environment.crmApiUrl + 'integrations/accounting/departments', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_int_33(params: CRM_INT_33, raiseErrorToast: boolean = true): Observable<GetResponse<ExternalEmployeeResponse[]>> {
    return this.get(environment.crmApiUrl + 'integrations/accounting/employees', params, {raiseErrorToast: raiseErrorToast});
  }

  /**
   * Endpoint definitions
   * CRM
   * Applications
   */

  crm_app_0(payload: CRM_APP_0, raiseErrorToast: boolean = true): Observable<PostResponse<CompanyApplicationResponse>> {
    return this.post(environment.crmApiUrl + 'applications/company', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_app_1(params: CRM_APP_1, raiseErrorToast: boolean = true): Observable<GetResponse<CompanyApplicationResponse[]>> {
    return this.get(environment.crmApiUrl + 'applications/company', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_app_2(params: CRM_APP_2, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'applications/company', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_app_3(raiseErrorToast: boolean = true): Observable<GetResponse<ApplicationTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'applications/types', undefined, {raiseErrorToast: raiseErrorToast});
  }

  /**
   * Endpoint definitions
   * CRM
   * Templates
   */

  crm_tmp_0(payload: CRM_TMP_0, raiseErrorToast: boolean = true): Observable<PostResponse<CustomerQuestionResponse>> {
    return this.post(environment.crmApiUrl + 'templates/customer-questions', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_1(params: CRM_TMP_1, raiseErrorToast: boolean = true): Observable<GetResponse<CustomerQuestionResponse[]>> {
    return this.get(environment.crmApiUrl + 'templates/customer-questions', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_2(payload: CRM_TMP_2, raiseErrorToast: boolean = true): Observable<PutResponse<CustomerQuestionResponse>> {
    return this.put(environment.crmApiUrl + 'templates/customer-questions', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_3(params: CRM_TMP_3, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'templates/customer-questions', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_4(payload: CRM_TMP_4, raiseErrorToast: boolean = true): Observable<PostResponse<CustomerQuestionTemplateResponse>> {
    return this.post(environment.crmApiUrl + 'templates/customer-questions/templates', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_5(params: CRM_TMP_5, raiseErrorToast: boolean = true): Observable<GetResponse<CustomerQuestionTemplateResponse[]>> {
    return this.get(environment.crmApiUrl + 'templates/customer-questions/templates', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_6(payload: CRM_TMP_6, raiseErrorToast: boolean = true): Observable<PutResponse<CustomerQuestionTemplateResponse>> {
    return this.put(environment.crmApiUrl + 'templates/customer-questions/templates', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_7(params: CRM_TMP_7, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'templates/customer-questions/templates', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_8(payload: CRM_TMP_8, raiseErrorToast: boolean = true): Observable<PostResponse<CustomerQuestionResponse>> {
    return this.post(environment.crmApiUrl + 'templates/customer-questions/choices', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_9(params: CRM_TMP_9, raiseErrorToast: boolean = true): Observable<PutResponse<CustomerQuestionResponse>> {
    return this.put(environment.crmApiUrl + 'templates/customer-questions/choices', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_10(params: CRM_TMP_10, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'templates/customer-questions/choices', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_11(payload: CRM_TMP_11, raiseErrorToast: boolean = true): Observable<PutResponse<ProductResponse>> {
    return this.post(environment.crmApiUrl + 'templates/customer-questions/templates/products', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_12(params: CRM_TMP_12, raiseErrorToast: boolean = true): Observable<PutResponse<ProductResponse>> {
    return this.delete(environment.crmApiUrl + 'templates/customer-questions/templates/products', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_13(payload: CRM_TMP_13, raiseErrorToast: boolean = true): Observable<PostResponse<TaskResponse>> {
    return this.post(environment.crmApiUrl + 'templates/tasks', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_14(params: CRM_TMP_14, raiseErrorToast: boolean = true): Observable<PutResponse<TaskResponse>> {
    return this.put(environment.crmApiUrl + 'templates/tasks', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_15(params: CRM_TMP_15, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'templates/tasks', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_16(payload: CRM_TMP_16, raiseErrorToast: boolean = true): Observable<PostResponse<TaskGroupResponse>> {
    return this.post(environment.crmApiUrl + 'templates/tasks/groups', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_17(params: CRM_TMP_17, raiseErrorToast: boolean = true): Observable<PutResponse<TaskGroupResponse>> {
    return this.put(environment.crmApiUrl + 'templates/tasks/groups', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_18(params: CRM_TMP_18, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'templates/tasks/groups', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_19(payload: CRM_TMP_19, raiseErrorToast: boolean = true): Observable<PostResponse<TaskTemplateResponse>> {
    return this.post(environment.crmApiUrl + 'templates/tasks/templates', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_20(params: CRM_TMP_20, raiseErrorToast: boolean = true): Observable<PutResponse<TaskTemplateResponse>> {
    return this.put(environment.crmApiUrl + 'templates/tasks/templates', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_21(params: CRM_TMP_21, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'templates/tasks/templates', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_22(payload: CRM_TMP_22, raiseErrorToast: boolean = true): Observable<PutResponse<ProductResponse>> {
    return this.post(environment.crmApiUrl + 'templates/tasks/templates/products', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_23(params: CRM_TMP_23, raiseErrorToast: boolean = true): Observable<PutResponse<ProductResponse>> {
    return this.delete(environment.crmApiUrl + 'templates/tasks/templates/products', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_24(payload: CRM_TMP_24, raiseErrorToast: boolean = true): Observable<GetResponse<TaskTemplateResponse[]>> {
    return this.get(environment.crmApiUrl + 'templates/tasks/templates', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_25(payload: CRM_TMP_25, raiseErrorToast: boolean = true): Observable<PostResponse<WorkOrderTemplateResponse>> {
    return this.post(environment.crmApiUrl + 'templates/work-orders', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_26(params: CRM_TMP_26, raiseErrorToast: boolean = true): Observable<GetResponse<WorkOrderTemplateResponse[]>> {
    return this.get(environment.crmApiUrl + 'templates/work-orders', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_27(payload: CRM_TMP_27, raiseErrorToast: boolean = true): Observable<PutResponse<WorkOrderTemplateResponse>> {
    return this.put(environment.crmApiUrl + 'templates/work-orders', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_28(params: CRM_TMP_28, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'templates/work-orders', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_29(payload: CRM_TMP_29, raiseErrorToast: boolean = true): Observable<PostResponse<ProductResponse>> {
    return this.post(environment.crmApiUrl + 'templates/work-orders/products', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_30(params: CRM_TMP_30, raiseErrorToast: boolean = true): Observable<PutResponse<ProductResponse>> {
    return this.delete(environment.crmApiUrl + 'templates/work-orders/products', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_31(payload: CRM_TMP_31, raiseErrorToast: boolean = true): Observable<PostResponse<OrderLineResponse>> {
    return this.post(environment.crmApiUrl + 'templates/work-orders/order-lines', payload, {raiseErrorToast: raiseErrorToast});
  }


  crm_tmp_34(payload: CRM_TMP_34, raiseErrorToast: boolean = true): Observable<PutResponse<ImportantInformationResponse>> {
    return this.post(environment.crmApiUrl + 'templates/important-information', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_35(params: CRM_TMP_35, raiseErrorToast: boolean = true): Observable<GetResponse<ImportantInformationResponse[]>> {
    return this.get(environment.crmApiUrl + 'templates/important-information', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_36(payload: CRM_TMP_36, raiseErrorToast: boolean = true): Observable<PutResponse<ImportantInformationResponse>> {
    return this.put(environment.crmApiUrl + 'templates/important-information', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_37(params: CRM_TMP_37, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'templates/important-information', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_38(payload: CRM_TMP_38, raiseErrorToast: boolean = true): Observable<PostResponse<ProductResponse>> {
    return this.post(environment.crmApiUrl + 'templates/important-information/products', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_tmp_39(params: CRM_TMP_39, raiseErrorToast: boolean = true): Observable<GetResponse<ProductResponse>> {
    return this.delete(environment.crmApiUrl + 'templates/important-information/products', params, {raiseErrorToast: raiseErrorToast});
  }



  /**
   * Endpoint definitions
   * CRM
   * Time tracking
   */

  crm_ttr_0(payload: CRM_TTR_0, raiseErrorToast: boolean = true): Observable<PostResponse<TimeTrackingResponse>> {
    return this.post(environment.crmApiUrl + 'timetrackings/', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_1(params: CRM_TTR_1, raiseErrorToast: boolean = true): Observable<GetResponse<CompanyTimeTrackingResponse[]>> {
    return this.get(environment.crmApiUrl + 'timetrackings/', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_2(payload: CRM_TTR_2, raiseErrorToast: boolean = true): Observable<PutResponse<TimeTrackingResponse>> {
    return this.patch(environment.crmApiUrl + 'timetrackings/', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_3(params: CRM_TTR_3, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'timetrackings/', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_4(payload: CRM_TTR_4, raiseErrorToast: boolean = true): Observable<PostResponse<TimeTrackingResponse>> {
    return this.post(environment.crmApiUrl + 'timetrackings/crew', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_5(params: CRM_TTR_5, raiseErrorToast: boolean = true): Observable<GetResponse<TimeTrackingResponse[]>> {
    return this.get(environment.crmApiUrl + 'timetrackings/crew', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_6(payload: CRM_TTR_6, raiseErrorToast: boolean = true): Observable<PutResponse<TimeTrackingResponse>> {
    return this.put(environment.crmApiUrl + 'timetrackings/crew', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_7(params: CRM_TTR_7, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'timetrackings/crew', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_8(params: CRM_TTR_8, raiseErrorToast: boolean = true): Observable<PostResponse<TimeTrackingOccurrenceResponse>> {
    return this.post(environment.crmApiUrl + 'timetrackings/occurrences', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_9(params: { [key: string]: any }, raiseErrorToast: boolean = true): Observable<Blob> {
    return this.getDefaultHeaders(true, 'application/xlsx').pipe(
      switchMap((headers) => {
        headers.set('Accept', 'application/xlsx');
        let httpParams = new HttpParams();
        Object.keys(params).forEach(key => {
          const value = params[key];
          if (value !== undefined && value !== null) {
            httpParams = httpParams.set(key, value);
          }
        });
        let options = { headers: headers, params: httpParams, responseType: 'blob' as 'json' };
        return this.http.get<Blob>(environment.crmApiUrl + 'timetrackings/export', options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
        );
      })
    );
  }

  crm_ttr_10(payload: CRM_TTR_10, raiseErrorToast: boolean = true): Observable<PostResponse<null>> {
    return this.post(environment.crmApiUrl + 'timetrackings/calculate-working-hours', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_11(params: CRM_TTR_11, raiseErrorToast: boolean = true): Observable<GetResponse<SalaryRuleTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'timetrackings/salary-rule-types', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_12(params: CRM_TTR_12, raiseErrorToast: boolean = true): Observable<GetResponse<CompanySalaryRuleResponse[]>> {
    return this.get(environment.crmApiUrl + 'timetrackings/salary-rules', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_13(payload: CRM_TTR_13, raiseErrorToast: boolean = true): Observable<PostResponse<CompanySalaryRuleResponse>> {
    return this.post(environment.crmApiUrl + 'timetrackings/salary-rules', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_14(payload: CRM_TTR_14, raiseErrorToast: boolean = true): Observable<PutResponse<CompanySalaryRuleResponse>> {
    return this.patch(environment.crmApiUrl + 'timetrackings/salary-rules', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_15(params: CRM_TTR_15, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'timetrackings/salary-rules', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_16(params: CRM_TTR_16, raiseErrorToast: boolean = true): Observable<GetResponse<CompanySalaryTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'timetrackings/salary-types', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_17(payload: CRM_TTR_17, raiseErrorToast: boolean = true): Observable<PostResponse<CompanySalaryTypeResponse>> {
    return this.post(environment.crmApiUrl + 'timetrackings/salary-types', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_18(payload: CRM_TTR_18, raiseErrorToast: boolean = true): Observable<PutResponse<CompanySalaryTypeResponse>> {
    return this.patch(environment.crmApiUrl + 'timetrackings/salary-types', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_19(params: CRM_TTR_19, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'timetrackings/salary-types', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_20(params: CRM_TTR_20, raiseErrorToast: boolean = true): Observable<GetResponse<TimeTrackingActivityTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'timetrackings/activity-types', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_21(payload: CRM_TTR_21, raiseErrorToast: boolean = true): Observable<GetResponse<UserWorkingHourPeriodResponse[]>> {
    return this.get(environment.crmApiUrl + 'timetrackings/working-hour-periods', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_22(params: CRM_TTR_22, raiseErrorToast: boolean = true): Observable<GetResponse<UserSalaryTimeTrackingResponse[]>> {
    return this.get(environment.crmApiUrl + 'timetrackings/salary/user', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_23(payload: CRM_TTR_23, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.put(environment.crmApiUrl + 'timetrackings/working-hour-periods', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_24(params: CRM_TTR_24, raiseErrorToast: boolean = true): Observable<GetResponse<WorkingHoursResponse[]>> {
    return this.get(environment.crmApiUrl + 'timetrackings/working-hours/user', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_25(payload: CRM_TTR_25, raiseErrorToast: boolean = true): Observable<PostResponse<WorkingHoursResponse>> {
    return this.post(environment.crmApiUrl + 'timetrackings/working-hours/user', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_26(payload: CRM_TTR_26, raiseErrorToast: boolean = true): Observable<PutResponse<WorkingHoursResponse>> {
    return this.put(environment.crmApiUrl + 'timetrackings/working-hours/user', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_27(params: CRM_TTR_27, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'timetrackings/working-hours/user', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_28(params: CRM_TTR_28, raiseErrorToast: boolean = true): Observable<GetResponse<WorkingHoursResponse[]>> {
    return this.get(environment.crmApiUrl + 'timetrackings/working-hours/company', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_29(params: CRM_TTR_29, raiseErrorToast: boolean = true): Observable<GetResponse<UserSalaryTimeTrackingResponse[]>> {
    return this.get(environment.crmApiUrl + 'timetrackings/working-hours/timetrackings', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_30(params: CRM_TTR_30, raiseErrorToast: boolean = true): Observable<GetResponse<TimeTrackingActivityResponse[]>> {
    return this.get(environment.crmApiUrl + 'timetrackings/activities', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_31(payload: CRM_TTR_31, raiseErrorToast: boolean = true): Observable<PostResponse<TimeTrackingActivityResponse>> {
    return this.post(environment.crmApiUrl + 'timetrackings/activities', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_32(payload: CRM_TTR_32, raiseErrorToast: boolean = true): Observable<PutResponse<TimeTrackingActivityResponse>> {
    return this.put(environment.crmApiUrl + 'timetrackings/activities', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_33(params: CRM_TTR_33, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'timetrackings/activities', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_34(params: { [key: string]: any }, raiseErrorToast: boolean = true): Observable<Blob> {
    return this.getDefaultHeaders(true, 'application/xlsx').pipe(
      switchMap((headers) => {
        headers.set('Accept', 'application/xlsx');
        let httpParams = new HttpParams();
        Object.keys(params).forEach(key => {
          const value = params[key];
          if (value !== undefined && value !== null) {
            httpParams = httpParams.set(key, value);
          }
        });
        let options = { headers: headers, params: httpParams, responseType: 'blob' as 'json' };
        return this.http.get<Blob>(environment.crmApiUrl + 'timetrackings/salary-approvals/export/xlsx', options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
        );
      })
    );
  }

  crm_ttr_35(payload: CRM_TTR_35, raiseErrorToast: boolean = true): Observable<PostResponse<SalaryApprovalResponse>> {
    return this.post(environment.crmApiUrl + 'timetrackings/salary-approvals', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_36(params: CRM_TTR_36, raiseErrorToast: boolean = true): Observable<GetResponse<SalaryApprovalResponse>> {
    return this.get(environment.crmApiUrl + 'timetrackings/salary-approvals', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_37(params: CRM_TTR_37, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'timetrackings/salary-approvals', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_38(params: CRM_TTR_38, raiseErrorToast: boolean = true): Observable<GetResponse<SalaryApprovalResponse[]>> {
    return this.get(environment.crmApiUrl + 'timetrackings/salary-approvals/company', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_39(payload: CRM_TTR_39, raiseErrorToast: boolean = true): Observable<PostResponse<null>> {
    return this.post(environment.crmApiUrl + 'timetrackings/salary-approvals/export/accounting', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_40(params: CRM_TTR_40, raiseErrorToast: boolean = true): Observable<GetResponse<WorkingHoursResponse[]>> {
    return this.get(environment.crmApiUrl + 'timetrackings/salary-approvals/working-hours', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_41(payload: CRM_TTR_41, raiseErrorToast: boolean = true): Observable<PostResponse<SalaryApprovalAccountingPreCheckResponse>> {
    return this.post(environment.crmApiUrl + 'timetrackings/salary-approvals/accounting-pre-check', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_42(params: CRM_TTR_42, raiseErrorToast: boolean = true): Observable<GetResponse<TimeTrackingLogResponse[]>> {
    return this.get(environment.crmApiUrl + 'timetrackings/log', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_43(params: CRM_TTR_43, raiseErrorToast: boolean = true): Observable<GetResponse<TimeTrackingResponse[]>> {
    return this.get(environment.crmApiUrl + 'timetrackings/children', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_44(payload: CRM_TTR_44, raiseErrorToast: boolean = true): Observable<GetResponse<TimeTrackingResponse>> {
    return this.post(environment.crmApiUrl + 'timetrackings/unlock', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_45(params: CRM_TTR_45, raiseErrorToast: boolean = true): Observable<GetResponse<SalaryCalculationTypeResponse[]>> {
    return this.get(environment.crmApiUrl + 'timetrackings/salary-calculation-types', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_47(payload: CRM_TTR_47, raiseErrorToast: boolean = true): Observable<PostResponse<EmployeeAbsenceResponse>> {
    return this.post(environment.crmApiUrl + 'timetrackings/absences', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_48(params: CRM_TTR_48, raiseErrorToast: boolean = true): Observable<GetResponse<EmployeeAbsenceResponse[]>> {
    return this.get(environment.crmApiUrl + 'timetrackings/absences', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_49(payload: CRM_TTR_49, raiseErrorToast: boolean = true): Observable<PutResponse<EmployeeAbsenceResponse>> {
    return this.put(environment.crmApiUrl + 'timetrackings/absences', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_50(params: CRM_TTR_50, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'timetrackings/absences', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_51(params: CRM_TTR_51, raiseErrorToast: boolean = true): Observable<GetResponse<EmployeeAbsenceResponse[]>> {
    return this.get(environment.crmApiUrl + 'timetrackings/absences/company', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ttr_52(params: { [key: string]: any }, raiseErrorToast: boolean = true): Observable<Blob> {
    return this.getDefaultHeaders(true, 'application/octet-stream').pipe(
      switchMap((headers) => {
        headers.set('Accept', 'application/octet-stream');
        let httpParams = new HttpParams();
        Object.keys(params).forEach(key => {
          const value = params[key];
          if (value !== undefined && value !== null) {
            httpParams = httpParams.set(key, value);
          }
        });
        let options = { headers: headers, params: httpParams, responseType: 'blob' as 'json' };
        return this.http.get<Blob>(environment.crmApiUrl + 'timetrackings/report/pdf', options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
        );
      })
    );
  }

  /**
   * Endpoint definitions
   * CRM
   * Projects
   */

  crm_prj_0(params: CRM_PRJ_0, raiseErrorToast: boolean = true): Observable<GetResponse<ProjectResponse[]>> {
    return this.get(environment.crmApiUrl + 'projects/', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prj_1(payload: CRM_PRJ_1, raiseErrorToast: boolean = true): Observable<PostResponse<ProjectResponse>> {
    return this.post(environment.crmApiUrl + 'projects/', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prj_2(payload: CRM_PRJ_2, raiseErrorToast: boolean = true): Observable<PutResponse<ProjectResponse>> {
    return this.put(environment.crmApiUrl + 'projects/', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_prj_3(params: CRM_PRJ_3, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'projects/', params, {raiseErrorToast: raiseErrorToast});
  }

   /**
   * Endpoint definitions
   * CRM
   * Departments
   */

  crm_dep_0(params: CRM_DEP_0, raiseErrorToast: boolean = true): Observable<GetResponse<DepartmentResponse[]>> {
    return this.get(environment.crmApiUrl + 'departments/', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_dep_1(payload: CRM_DEP_1, raiseErrorToast: boolean = true): Observable<PostResponse<DepartmentResponse>> {
    return this.post(environment.crmApiUrl + 'departments/', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_dep_2(payload: CRM_DEP_2, raiseErrorToast: boolean = true): Observable<PutResponse<DepartmentResponse>> {
    return this.put(environment.crmApiUrl + 'departments/', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_dep_3(params: CRM_DEP_3, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'departments/', params, {raiseErrorToast: raiseErrorToast});
  }


  /**
   * Endpoint definitions
   * CRM
   * Services
   */

  crm_ser_8(params: CRM_SER_8, raiseErrorToast: boolean = true): Observable<GetResponse<{ datetime: string }>> {
    return this.get(environment.crmApiUrl + 'services/get-time', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ser_12(params: CRM_SER_12, raiseErrorToast: boolean = true): Observable<GetResponse<VehicleRegistrationDataResponse>> {
    return this.get(environment.crmApiUrl + 'services/vehicle-registration-data', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ser_13(params: CRM_SER_13, raiseErrorToast: boolean = true): Observable<GetResponse<null>> {
    return this.get(environment.crmApiUrl + 'services/test-response', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ser_15(params: CRM_SER_15, raiseErrorToast: boolean = true): Observable<GetResponse<PeppolEHFCheckResponse>> {
    return this.get(environment.crmApiUrl + 'services/peppol/ehf', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_ser_32(payload: CRM_SER_32, raiseErrorToast: boolean = true): Observable<GetResponse<TopBarSetupResponse>> {
    return this.put(environment.crmApiUrl + 'services/top-bar-message', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ser_33(raiseErrorToast: boolean = true): Observable<GetResponse<TopBarSetupResponse>> {
    return this.get(environment.crmApiUrl + 'services/top-bar-message', undefined, {raiseErrorToast: raiseErrorToast});
  }

  /**
   * Endpoint definitions
   * CRM
   * Notifications
   */

  crm_not_0(params: CRM_NOT_0, raiseErrorToast: boolean = true): Observable<GetResponse<NotificationsResponse[]>> {
    return this.get(environment.crmApiUrl + 'notifications/user', params, {raiseErrorToast: raiseErrorToast});
  }

  // crm_not_0(params: CRM_NOT_0, raiseErrorToast: boolean = true): Observable<GetResponse<NotificationsResponse[]>> {
  //   return this.get(environment.crmApiUrl + 'notifications/user', params, {raiseErrorToast: raiseErrorToast});
  // }

  crm_not_1(params: CRM_NOT_1, raiseErrorToast: boolean = true): Observable<PutResponse<NotificationsResponse[]>> {
    const options: EndpointOptions = {raiseErrorToast: raiseErrorToast};
    return this.post(environment.crmApiUrl + 'notifications/user', params, options);
  }

  /**
   * Endpoint definitions
   * External
   */

  geo_adr_0(params: GEO_ADR_0, raiseErrorToast: boolean = true): Observable<GeoAddressResponse> {
    let options: EndpointOptions = {
      auth: false,
      raiseErrorToast: raiseErrorToast,
    };
    return this.get('https://ws.geonorge.no/adresser/v1/sok', params, options);
  }


  /**
   * Endpoint definitions
   * Reportinator
   */

  rep_tmp_0(payload: REP_TMP_0, raiseErrorToast: boolean = true): Observable<PostResponse<TemplateResponse>> {
    return this.post(environment.reportinatorApiUrl + 'templates/', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_tmp_1(params: REP_TMP_1, raiseErrorToast: boolean = true): Observable<PaginationResponse<TemplateResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'templates/', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_tmp_2(payload: REP_TMP_2, raiseErrorToast: boolean = true): Observable<PutResponse<TemplateResponse>> {
    return this.put(environment.reportinatorApiUrl + 'templates/', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_tmp_3(params: REP_TMP_3, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.reportinatorApiUrl + 'templates/', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_tmp_4(payload: REP_TMP_4, raiseErrorToast: boolean = true): Observable<PostResponse<TemplateElementResponse>> {
    return this.post(environment.reportinatorApiUrl + 'templates/elements', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_tmp_5(params: REP_TMP_5, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.reportinatorApiUrl + 'templates/elements', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_tmp_6(params: REP_TMP_6, raiseErrorToast: boolean = true): Observable<GetResponse<TemplateResponse>> {
    return this.get(environment.reportinatorApiUrl + 'templates/id', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_tmp_7(params: REP_TMP_7, raiseErrorToast: boolean = true): Observable<PutResponse<TemplateResponse>> {
    return this.put(environment.reportinatorApiUrl + 'templates/locations', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_tmp_8(params: REP_TMP_8, raiseErrorToast: boolean = true): Observable<GetResponse<TemplateElementResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'templates/elements', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_tmp_9(payload: REP_TMP_9, raiseErrorToast: boolean = true): Observable<PutResponse<TemplateElementResponse>> {
    return this.put(environment.reportinatorApiUrl + 'templates/elements', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_tmp_10(payload: REP_TMP_10, raiseErrorToast: boolean = true): Observable<PostResponse<TemplatePrerequisiteResponse>> {
    return this.post(environment.reportinatorApiUrl + 'templates/prerequisites', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_tmp_11(params: REP_TMP_11, raiseErrorToast: boolean = true): Observable<GetResponse<TemplatePrerequisiteResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'templates/prerequisites', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_tmp_12(params: REP_TMP_12, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.reportinatorApiUrl + 'templates/prerequisites', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_tmp_13(payload: REP_TMP_13, raiseErrorToast: boolean = true): Observable<PutResponse<TemplatePrerequisiteResponse>> {
    return this.put(environment.reportinatorApiUrl + 'templates/prerequisites', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_0(payload: REP_ELM_0, raiseErrorToast: boolean = true): Observable<PostResponse<ElementResponse>> {
    return this.post(environment.reportinatorApiUrl + 'elements/', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_1(params: REP_ELM_1, raiseErrorToast: boolean = true): Observable<PaginationResponse<ElementResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'elements/', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_2(payload: REP_ELM_2, raiseErrorToast: boolean = true): Observable<PutResponse<ElementResponse>> {
    return this.put(environment.reportinatorApiUrl + 'elements/', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_3(params: REP_ELM_3, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.reportinatorApiUrl + 'elements/', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_4(params: REP_ELM_4, raiseErrorToast: boolean = true): Observable<GetResponse<ElementResponse>> {
    return this.get(environment.reportinatorApiUrl + 'elements/id', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_5(payload: REP_ELM_5, raiseErrorToast: boolean = true): Observable<PostResponse<ElementResponse>> {
    return this.post(environment.reportinatorApiUrl + 'elements/attributes', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_6(params: REP_ELM_6, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.reportinatorApiUrl + 'elements/attributes', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_7(payload: REP_ELM_7, raiseErrorToast: boolean = true): Observable<PostResponse<ElementResponse>> {
    return this.post(environment.reportinatorApiUrl + 'elements/attributes/phrases', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_8(payload: REP_ELM_8, raiseErrorToast: boolean = true): Observable<PutResponse<ElementResponse>> {
    return this.put(environment.reportinatorApiUrl + 'elements/attributes/phrases', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_9(params: REP_ELM_9, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.reportinatorApiUrl + 'elements/attributes/phrases', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_10(payload: REP_ELM_10, raiseErrorToast: boolean = true): Observable<PostResponse<PhraseResponse>> {
    return this.post(environment.reportinatorApiUrl + 'elements/attributes/phrases/rules', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_11(payload: REP_ELM_11, raiseErrorToast: boolean = true): Observable<PutResponse<PhraseResponse>> {
    return this.put(environment.reportinatorApiUrl + 'elements/attributes/phrases/rules', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_12(params: REP_ELM_12, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.reportinatorApiUrl + 'elements/attributes/phrases/rules', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_17(params: REP_ELM_17, raiseErrorToast: boolean = true): Observable<PostResponse<ElementResponse>> {
    return this.post(environment.reportinatorApiUrl + 'elements/subelements', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_18(params: REP_ELM_18, raiseErrorToast: boolean = true): Observable<GetResponse<ElementResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'elements/subelements', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_19(params: REP_ELM_19, raiseErrorToast: boolean = true): Observable<PutResponse<ElementResponse>> {
    return this.put(environment.reportinatorApiUrl + 'elements/subelements', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_20(params: REP_ELM_20, raiseErrorToast: boolean = true): Observable<PutResponse<ElementResponse>> {
    return this.delete(environment.reportinatorApiUrl + 'elements/subelements', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_21(payload: REP_ELM_21, raiseErrorToast: boolean = true): Observable<PutResponse<ElementResponse>> {
    return this.put(environment.reportinatorApiUrl + 'elements/attributes', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_22(params: REP_ELM_22, raiseErrorToast: boolean = true): Observable<GetResponse<PhraseResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'elements/attributes/phrases', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_elm_23(raiseErrorToast: boolean = true): Observable<GetResponse<ElementResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'elements/standard', undefined, {raiseErrorToast: raiseErrorToast});
  }

  rep_obj_1(raiseErrorToast: boolean = true): Observable<GetResponse<PhraseRuleFilterTypeResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'objects/rules/filter-types', undefined, {raiseErrorToast: raiseErrorToast});
  }

  rep_obj_0(params: REP_OBJ_0 = {}, raiseErrorToast: boolean = true): Observable<GetResponse<AttributeResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'objects/attributes', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_obj_2(params: REP_OBJ_2, raiseErrorToast: boolean = true): Observable<GetResponse<ElementTypeResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'objects/element-types', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_obj_3(raiseErrorToast: boolean = true): Observable<GetResponse<AssessmentTypeResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'objects/assessment-types', undefined, {raiseErrorToast: raiseErrorToast});
  }

  rep_obj_4(raiseErrorToast: boolean = true): Observable<PostResponse<FloorResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'objects/floors', undefined, {raiseErrorToast: raiseErrorToast});
  }

  rep_obj_6(raiseErrorToast: boolean = true): Observable<GetResponse<RoomResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'objects/rooms', undefined, {raiseErrorToast: raiseErrorToast});
  }

  rep_obj_7(raiseErrorToast: boolean = true): Observable<GetResponse<WaterConnectionTypeResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'objects/water-connection-types', undefined, {raiseErrorToast: raiseErrorToast});
  }

  rep_obj_8(raiseErrorToast: boolean = true): Observable<GetResponse<SewageConnectionTypeResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'objects/sewage-connection-types', undefined, {raiseErrorToast: raiseErrorToast});
  }

  rep_obj_9(raiseErrorToast: boolean = true): Observable<GetResponse<ReportElementStatusResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'objects/element-statuses', undefined, {raiseErrorToast: raiseErrorToast});
  }

  rep_obj_10(raiseErrorToast: boolean = true): Observable<GetResponse<ReportUpgradeDocumentTypeResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'objects/upgrade-documentation-types', undefined, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_0(payload: REP_REP_0, raiseErrorToast: boolean = true): Observable<PostResponse<ReportResponse>> {
    return this.post(environment.reportinatorApiUrl + 'reports/', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_1(params: REP_REP_1, raiseErrorToast: boolean = true): Observable<PaginationResponse<ReportResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'reports/', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_2(payload: REP_REP_2, raiseErrorToast: boolean = true): Observable<PutResponse<ReportResponse>> {
    return this.put(environment.reportinatorApiUrl + 'reports/', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_3(params: REP_REP_3, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.reportinatorApiUrl + 'reports/', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_4(params: REP_REP_4, raiseErrorToast: boolean = true): Observable<GetResponse<ReportResponse>> {
    return this.get(environment.reportinatorApiUrl + 'reports/id', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_5(payload: REP_REP_5, raiseErrorToast: boolean = true): Observable<PostResponse<RevisionResponse>> {
    return this.post(environment.reportinatorApiUrl + 'reports/revisions', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_6(params: REP_REP_6, raiseErrorToast: boolean = true): Observable<PaginationResponse<RevisionResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'reports/revisions', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_7(payload: REP_REP_7, raiseErrorToast: boolean = true): Observable<PutResponse<RevisionResponse>> {
    return this.put(environment.reportinatorApiUrl + 'reports/revisions', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_8(params: REP_REP_8, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.reportinatorApiUrl + 'reports/revisions', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_9(params: REP_REP_9, raiseErrorToast: boolean = true): Observable<GetResponse<RevisionResponse>> {
    return this.get(environment.reportinatorApiUrl + 'reports/revisions/id', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_10(payload: REP_REP_10, raiseErrorToast: boolean = true): Observable<PostResponse<ReportElementResponse>> {
    return this.post(environment.reportinatorApiUrl + 'reports/revisions/elements', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_11(params: REP_REP_11, raiseErrorToast: boolean = true): Observable<GetResponse<ReportElementResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'reports/revisions/elements', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_12(payload: REP_REP_12, raiseErrorToast: boolean = true): Observable<PutResponse<ReportElementResponse>> {
    return this.put(environment.reportinatorApiUrl + 'reports/revisions/elements', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_13(params: REP_REP_13, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.reportinatorApiUrl + 'reports/revisions/elements', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_14(payload: REP_REP_14, raiseErrorToast: boolean = true): Observable<PostResponse<ReportElementResponse>> {
    return this.post(environment.reportinatorApiUrl + 'reports/revisions/elements/attributes', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_16(payload: REP_REP_16, raiseErrorToast: boolean = true): Observable<PutResponse<ReportElementResponse>> {
    return this.put(environment.reportinatorApiUrl + 'reports/revisions/elements/attributes', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_17(params: REP_REP_17, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.reportinatorApiUrl + 'reports/revisions/elements/attributes', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_18(params: REP_REP_18, raiseErrorToast: boolean = true): Observable<GetResponse<ReportResponse>> {
    return this.get(environment.reportinatorApiUrl + 'reports/order-id', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_19(payload: REP_REP_19, raiseErrorToast: boolean = true): Observable<PutResponse<ReportElementResponse>> {
    return this.put(environment.reportinatorApiUrl + 'reports/revisions/elements/locations/measurements', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_20(payload: REP_REP_20, raiseErrorToast: boolean = true): Observable<GetResponse<ReportElementResponse>> {
    return this.post(environment.reportinatorApiUrl + 'reports/revisions/elements/locations/setup/floors', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_21(params: REP_REP_21, raiseErrorToast: boolean = true): Observable<GetResponse<ReportElementResponse>> {
    return this.delete(environment.reportinatorApiUrl + 'reports/revisions/elements/locations/setup/floors', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_22(payload: REP_REP_22, raiseErrorToast: boolean = true): Observable<PutResponse<ReportElementResponse>> {
    return this.put(environment.reportinatorApiUrl + 'reports/revisions/elements/locations/setup/floors', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_23(payload: REP_REP_23, raiseErrorToast: boolean = true): Observable<PostResponse<ReportElementResponse>> {
    return this.post(environment.reportinatorApiUrl + 'reports/revisions/elements/locations/setup/rooms', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_24(params: REP_REP_24, raiseErrorToast: boolean = true): Observable<GetResponse<ReportElementResponse>> {
    return this.delete(environment.reportinatorApiUrl + 'reports/revisions/elements/locations/setup/rooms', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_25(payload: REP_REP_25, raiseErrorToast: boolean = true): Observable<PutResponse<ReportElementResponse>> {
    return this.put(environment.reportinatorApiUrl + 'reports/revisions/elements/locations/setup/rooms', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_26(payload: REP_REP_26, raiseErrorToast: boolean = true): Observable<PostResponse<ReportElementResponse>> {
    return this.post(environment.reportinatorApiUrl + 'reports/revisions/elements/locations/floors', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_27(params: REP_REP_27, raiseErrorToast: boolean = true): Observable<GetResponse<ReportElementResponse>> {
    return this.delete(environment.reportinatorApiUrl + 'reports/revisions/elements/locations/floors', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_28(payload: REP_REP_28, raiseErrorToast: boolean = true): Observable<PostResponse<ReportElementResponse>> {
    return this.post(environment.reportinatorApiUrl + 'reports/revisions/elements/locations/rooms', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_29(params: REP_REP_29, raiseErrorToast: boolean = true): Observable<GetResponse<ReportElementResponse>> {
    return this.delete(environment.reportinatorApiUrl + 'reports/revisions/elements/locations/rooms', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_30(params: REP_REP_30, raiseErrorToast: boolean = true): Observable<GetResponse<ReportElementFloorSetupResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'reports/revisions/elements/locations/floors/available', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_31(params: REP_REP_31, raiseErrorToast: boolean = true): Observable<GetResponse<ReportElementRoomSetupResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'reports/revisions/elements/locations/rooms/available', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_32(params: REP_REP_32, raiseErrorToast: boolean = true): Observable<GetResponse<ReportForHtmlResponse>> {
    let options: EndpointOptions = {
      raiseErrorToast: raiseErrorToast,
      auth: false,
      language: 'no'
    }
    return this.get(environment.reportinatorApiUrl + 'reports/html', params, options);
  }

  rep_rep_33(payload: REP_REP_33, raiseErrorToast: boolean = true): Observable<PutResponse<ReportResponse>> {
    return this.put(environment.reportinatorApiUrl + 'reports/present-users', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_34(payload: REP_REP_34, raiseErrorToast: boolean = true): Observable<PutResponse<ReportResponse>> {
    return this.getDefaultHeaders(true, null).pipe(
      switchMap((headers) => {
        let params = { 'company_id': payload.company_id, 'report_id': payload.report_id };
        let options = { headers: headers, params: params };

        let formData = new FormData();
        formData.append('image', payload.image);

        return this.http.put<PutResponse<ReportResponse>>(environment.reportinatorApiUrl + 'reports/front-page-image', formData, options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
        );
      })
    );
  }

  rep_rep_35(payload: REP_REP_35, raiseErrorToast: boolean = true): Observable<PostResponse<ReportElementResponse>> {
    return this.getDefaultHeaders(true, null).pipe(
      switchMap((headers) => {
        let params = { 'company_id': payload.company_id, 'report_element_id': payload.report_element_id };
        let options = { headers: headers, params: params };

        let formData = new FormData();
        formData.append('image', payload.image);

        return this.http.post<PostResponse<ReportElementResponse>>(environment.reportinatorApiUrl + 'reports/revisions/elements/images', formData, options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
        );
      })
    );
  }

  rep_rep_36(payload: REP_REP_36, raiseErrorToast: boolean = true): Observable<PutResponse<ReportElementResponse>> {
    return this.put(environment.reportinatorApiUrl + 'reports/revisions/elements/images', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_37(payload: REP_REP_37, raiseErrorToast: boolean = true): Observable<PutResponse<ReportElementResponse>> {
    return this.delete(environment.reportinatorApiUrl + 'reports/revisions/elements/images', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_38(params: REP_REP_38, raiseErrorToast: boolean = true): Observable<GetResponse<{ [key: number]: PhraseResponse[] }>> {
    return this.get(environment.reportinatorApiUrl + 'reports/revisions/phrases/all', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_39(payload: REP_REP_39, raiseErrorToast: boolean = true): Observable<PostResponse<ReportUpgradeResponse>> {
    return this.post(environment.reportinatorApiUrl + 'reports/upgrades', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_40(params: REP_REP_40, raiseErrorToast: boolean = true): Observable<GetResponse<ReportUpgradeResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'reports/upgrades', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_41(payload: REP_REP_41, raiseErrorToast: boolean = true): Observable<PutResponse<ReportUpgradeResponse>> {
    return this.put(environment.reportinatorApiUrl + 'reports/upgrades', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_42(params: REP_REP_42, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.reportinatorApiUrl + 'reports/upgrades', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_43(params: REP_REP_43, raiseErrorToast: boolean = true): Observable<PutResponse<ReportResponse>> {
    return this.post(environment.reportinatorApiUrl + 'reports/present-users', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_44(payload: REP_REP_44, raiseErrorToast: boolean = true): Observable<PutResponse<ReportResponse>> {
    return this.put(environment.reportinatorApiUrl + 'reports/present-users', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_45(params: REP_REP_45, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.reportinatorApiUrl + 'reports/present-users', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_46(payload: REP_REP_46, raiseErrorToast: boolean = true): Observable<PutResponse<null>> {
    return this.post(environment.reportinatorApiUrl + 'reports/revisions/elements/duplicate', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_47(params: REP_REP_47, raiseErrorToast: boolean = true): Observable<GetResponse<ReportPlotDataResponse>> {
    return this.get(environment.reportinatorApiUrl + 'reports/plot', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_48(payload: REP_REP_48, raiseErrorToast: boolean = true): Observable<PutResponse<ReportPlotDataResponse>> {
    return this.put(environment.reportinatorApiUrl + 'reports/plot', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_49(payload: REP_REP_49, raiseErrorToast: boolean = true): Observable<PostResponse<ReportSourceResponse>> {
    return this.post(environment.reportinatorApiUrl + 'reports/sources', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_50(params: REP_REP_50, raiseErrorToast: boolean = true): Observable<GetResponse<ReportSourceResponse[]>> {
    return this.get(environment.reportinatorApiUrl + 'reports/sources', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_51(payload: REP_REP_51, raiseErrorToast: boolean = true): Observable<PutResponse<ReportSourceResponse>> {
    return this.put(environment.reportinatorApiUrl + 'reports/sources', payload, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_52(params: REP_REP_52, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.reportinatorApiUrl + 'reports/sources', params, {raiseErrorToast: raiseErrorToast});
  }

  rep_rep_53(payload: REP_REP_53, raiseErrorToast: boolean = true): Observable<PutResponse<ReportSourceResponse>> {
    return this.getDefaultHeaders(true, null).pipe(
      switchMap((headers) => {
        let params = { 'company_id': payload.company_id, 'report_id': payload.report_id, 'source_id': payload.source_id };
        let options = { headers: headers, params: params };

        let formData = new FormData();
        formData.append('image', payload.file);

        return this.http.post<PutResponse<ReportSourceResponse>>(environment.reportinatorApiUrl + 'reports/sources/attachments', formData, options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.errorToast(error.error.asset_id);
            }
            return throwError(error);
          }),
        );
      })
    );
  }

  rep_rep_54(params: REP_REP_54, raiseErrorToast: boolean = true): Observable<PutResponse<ReportSourceResponse>> {
    return this.delete(environment.reportinatorApiUrl + 'reports/sources/attachments', params, {raiseErrorToast: raiseErrorToast});
  }



}

