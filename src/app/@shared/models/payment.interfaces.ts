import {OrderLineResponse, PaymentRecipientResponse, RefundOrderLineResponse} from "./order.interfaces";
import {InternalUserResponse} from "./user.interfaces";
import {ProjectResponse} from "./projects.interfaces";
import {DepartmentResponse} from "./departments.interfaces";
import {AffiliateContactResponse} from "./affiliate.interfaces";

export interface VatRateResponse {
  vat_rate_id: number;
  vat_rate: number;
  vat_rate_display_name: string;
  vatRateDisplay?: string;
}

export interface PaymentResponse {
  undefined: string;
}

export interface PaymentStatusResponse {
  payment_status_id: number;
  payment_status_name: string;
}

export interface PaymentMethodResponse {
  payment_method_id: number;
  payment_method_name: string;
  disabledMessage?: string;
}

export interface InvoiceSendTypeResponse {
  invoice_send_type_id: number;
  invoice_send_type_name: string;
}

export interface PaymentScheduleOptionResponse {
  option_id: number;
  name: string;
  subtext: string | null;
  description: string | null;
  auto_send: boolean;
  custom_schedule: boolean;
  consolidated: boolean;
  fixed: boolean;
  requires_schedule: boolean;
}

export interface OrderPaymentResponse {
  payment_id: number;
  payment_number: string;
  order_id: number | null;
  order_number: string | null;
  company_id: string;
  work_order_ids: number[];
  work_order_number: string | null;
  work_order_title: string | null;
  payment_recipient: PaymentRecipientResponse;
  affiliate_contact: AffiliateContactResponse | null;
  payment_method_id: number;
  payment_method_name: string;
  provider_payment_method_name: string | null;
  payment_status_id: number;
  payment_status_name: string;
  payment_reminder_status: number;
  accounting_transfer_at: Date | null;
  accounting_payment_transfer_at: Date | null;
  accounting_transfer_status_id: number;
  accounting_transfer_status_name: string;
  accounting_payment_transfer_status_id: number;
  accounting_payment_transfer_status_name: string;
  payment_sent_at: Date | null;
  auto_send_at: Date | null;
  captured_at: Date | null;
  paid_amount: number;
  invoice_send_type_id: number;
  invoice_send_type_name: string;
  invoice_sent_at: Date | null;
  invoice_due_date_days: number;
  invoice_email: string;
  invoice_reference_text: string;
  invoice_eligible: number;
  invoice_id: string | null;
  actual_invoice_due_date_days: number | null;
  actual_invoice_due_date_text: string | null;
  invoice_date: Date | null;
  total_discount_amount_inc_vat: number;
  total_amount_inc_vat: number;
  total_amount_ex_vat: number;
  refund_amount: number;
  refunded_amount: number;
  payment_name: string;
  comment: string | null;
  refund: boolean;
  refund_reason: string | null;
  parent_payment_id: number | null;
  next_payment: {payment_id: number, auto_send_at: Date} | null;
  previous_payment: {payment_id: number, auto_send_at: Date} | null;
  total_payments: number;
  total_unpaid_sent_payments: number;
  payment_reminders_disabled: boolean;
  payment_reminder_1_scheduled_at: Date | null;
  payment_reminder_2_scheduled_at: Date | null;
  order_lines: OrderLineResponse[];
  refund_order_lines: RefundOrderLineResponse[];
  payment_schedule: PaymentScheduleResponse | null;
  template: boolean;
  subscription_active: boolean;
  payment_reference: string | null;
  is_consolidated_invoice_container: boolean;
  is_parent_consolidated_invoice: boolean;
  consolidation_container_id: number | null;
  consolidated_invoice_payment_id: number | null;
  card_expiration: string | null;
  card_last4: string | null;
  project: ProjectResponse | null;
  department: DepartmentResponse | null;
  disable_customer_portal: boolean;
  customer_portal_sent_at: Date | null;
  schedule_option: PaymentScheduleOptionResponse | null;
  consolidated_parent_payment: OrderPaymentResponse | null;
  auto_capture: boolean;
  auto_send: boolean;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
}

export interface OrderPaymentResponseCompact {
  payment_id: number;
  payment_number: string;
  payment_name: string;
  order_id: number | null;
  payment_recipient: PaymentRecipientResponse;
  payment_method_id: number;
  payment_method_name: string;
  payment_status_id: number;
  payment_status_name: string;
  payment_sent_at: Date | null;
  captured_at: Date | null;
  total_discount_amount_inc_vat: number;
  total_amount_inc_vat: number;
  total_amount_ex_vat: number;
  comment: string | null;
  created_at: Date;
  refund: boolean;
  auto_send_at: Date | null;
  disable_customer_portal: boolean;
  customer_portal_sent_at: Date | null;
}

export interface PaymentScheduleResponse {
  payment_schedule_id: number;
  payment_id: number;
  start_date: Date;
  end_date: Date | null;
  schedule_repeat_type_id: number;
  schedule_repeat_type_name: string;
  weekdays: number[];
  every: number;
  date: number | null;
  nth_weekday: number | null;
  active: boolean;
  deactivated_at: Date | null;
  schedule_description: string;
}

export interface PaymentLogEntryResponse {
  entry_id: number;
  entry_type_id: number;
  entry_type_name: string;
  log_title: string;
  data: {[key: string]: any};
  dataStr: string | null;
  request_payload: {[key: string]: any};
  requestStr: string | null;
  url: string | null;
  set_by: string;
  set_by_user: InternalUserResponse | null;
  set_at: Date;
  system_initiated: boolean;
}

export interface CompanyTableSetupResponse {
  entry_id: number;
  user_id: string;
  table_name: string;
  table_config: any;
}
