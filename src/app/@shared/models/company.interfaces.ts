import {AddressCompactResponse} from "./address.interfaces";

export interface CompanyResponse {
  company_id: string;
  company_name: string;
  organisation_number: string;
  company_color: string;
  address: AddressCompactResponse;
  city: string;
  postal_code: string;
  country: string;
  phone: string;
  email: string;
  updated_by: string;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
  logo_url: string;
  invitee: {
    first_name : string;
    last_name : string;
    phone : string;
    email : string;
  }
}

export interface CompanyOpeningHoursResponse {
  start_time: Date;
  end_time: Date;
  open_from: string;
  open_to: string;
  days: {
    '0': 1 | 0;
    '1': 1 | 0;
    '2': 1 | 0;
    '3': 1 | 0;
    '4': 1 | 0;
    '5': 1 | 0;
    '6': 1 | 0;
  }
}

export interface CompanyThresholdResponse {
  threshold_0: number;
  threshold_1: number;
  threshold_2: number;
}

export interface CompanyTOCResponse {
  company_id: string;
  toc: string;
}

export interface CompanyNotificationSettingsResponse {
  notification_type_id: number;
  notification_type_name: string;
  notification_type_description: string;
  example: string;
  time_delta_description: string;
  time_delta_hours: number;
  instant: number;
  enabled: number;
  visible: number;
}

export interface CompanyPaymentCredentialsResponse {
  payment_method_id: number;
  active: number;
  credentials: {[key: string]: string | number};
  verified: number;
  business_customer_available: number;
  private_customer_available: number;
}

export interface CompanyEmployeeAttributesResponse {
  add_product: number;
  edit_quantity: number;
  make_external_notes: number;
  make_external_reports: number;
  send_to_payment: number;
  edit_discount: number;
  view_prices: number;
  receive_order_accepted_notification: number;
  receive_order_finished_notification: number;
  payment_page_access: number;
  set_payment_to_external: number;
  receive_accounting_integration_failed_notification: number;
  view_all_events_as_crew: number;
  view_all_orders_as_crew: number;
  unpaid_order_notification: number;
  failed_schedule_order_creation_notification: number;
  receive_upcoming_order_not_accepted_notification: number;
  receive_sub_contractor_order_notification: number;
}

export interface CompanyEmbedSettingsResponse {
  primary_color_hex: string;
  secondary_color_hex: string;
  content: {[key: string]: string | number | boolean | null};
}

export interface CompanyGeneralSettingsResponse {
  order_line_quantity_calculation_resolution: number;
  company_order_number_starting_number: number;
  company_order_number_prefix: string;
  accounting_system_id: number | null;
  accounting_system_start_date: Date | null;
  transfer_external_orders_to_accounting: number;
  transfer_free_orders_to_accounting: number;
  include_vat_in_price_business: number;
  include_vat_in_price_private: number;
  archive_order_when_finished: number;
  calendar_start_hour: number;
  calendar_end_hour: number;
  auto_synchronise_accounting: number;
  auto_send_invoice: number;
  mark_external_orders_as_paid_automatically: number;
  sms_name: string;
  standard_property_type_id_for_unspecified_properties: number;
  include_transport_in_duration_calculation: number;
  geo_lock_range: number | null;
  default_business_customers_due_date: number;
  default_private_customers_due_date: number;
  manual_order_confirmation_email: number;
  split_interday_orders_in_multiple_events: number;
  set_invoice_date_to_sent_to_payment: number;
  auto_sync_on_send_to_payment: number;
  show_employee_initials_only: number;
  color_hex_job_not_started?: string;
  color_hex_job_started?: string;
  color_hex_job_finished?: string;
  color_hex_no_order?: string;
  color_hex_unconfirmed?: string;
  event_title_tags?: string[],
  customer_can_only_accept?: number;
  operate_ex_vat: boolean;
  default_activity_id: number | null;
  default_transport_activity_id: number | null;
  default_fixed_salary_salary_type_id: number | null;
  require_manual_accounting_push: boolean;
  auto_finish_work_orders: boolean;
  auto_finish_all_work_orders: boolean;
  planned_registered_ratio_warning_percentage: number | null;
  quote_expiry_days: number | null;
  convert_tracked_time_to_working_hours: boolean;
  default_department_id: number | null;
  customer_cancel_work_order: number;
  default_vacation_activity_id: number | null;
  default_repeating_orders: boolean;
  require_crew_work_order_confirmation: boolean;
}

export interface CompanyTypeResponse {
  company_type_id: number;
  company_type_name: string;
}

export interface CompanyDefaultAddressValuesResponse {
  property_type_id: number;
  property_type_name: string;
  livable_area: number;
  floor: number | null;
  number_of_floors: number | null;
  number_of_rooms: number;
  number_of_bathrooms: number;
  shed_area: number;
  [key: string]: number | string | null;
}
