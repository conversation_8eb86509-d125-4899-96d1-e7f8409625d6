/**
  *
  * Standard Query Parameter Interfaces
  *
  */
import {ProductStageResponse, ProductWorkOrderData} from "./product.interfaces";
import {InternalUserResponse} from "./user.interfaces";
import {ResourceResponse} from "./resources.interfaces";
import {Subject} from "rxjs";
import {CustomerResponse} from "./customer.interfaces";
import {AffiliateResponse} from "./affiliate.interfaces";

export interface pagination_input {
  page?: number | null;
  limit?: number | null;
  paginate?: number | null;
}

export interface sorting_input {
  order_by?: string | null;
  order_direction?: string | null;
}

export interface search_input {
  search_string?: string | number | null;
  search_string_columns?: string[] | null;
}

export interface date_input {
  date_from?: null | Date | string;
  date_to?: null | Date | string;
}

export interface full_select extends pagination_input, sorting_input, search_input, date_input {}


/**
  *
  * Input Interfaces
   * USM
   * Nested Interfaces
  *
  */

/**
  *
  * Input Interfaces
   * USM
   * Endpoint Interfaces
  *
  */


export interface USM_USR_0 {
    phone: string;
    password: string;
}

export interface USM_USR_2 {
  email: string;
  first_name: string;
  last_name: string;
  phone: string;
}

export interface USM_USR_3 {}

export interface USM_USR_6 {
    email: string;
    first_name: string;
    last_name: string;
    phone: string;
    role_id: number;
    identificator: string | null;
    taskable: number;
    entity_id: string;
    entity_name: string;
    entity_email: string;
    entity_phone: string;
}

export interface USM_USR_7 {
    email: string;
    first_name: string;
    last_name: string;
    phone: string;
}

export interface USM_USR_8 {
  phone: string;
  otp: number;
}

export interface USM_USR_9 {
  phone: string;
}

export interface USM_USR_10 extends full_select {
  entity_id: string;
}

export interface USM_USR_13 {
  phone: number;
}

export interface USM_USR_14 {
  reset_token: string;
  new_password: string;
}

export interface USM_USR_16 {
  image: File;
}

export interface USM_USR_17 {
  entity_id: string;
  user_id: string;
  image: File;
}

export interface _USM_USR_17 extends Omit<USM_USR_17, 'entity_id'> {}

export interface USM_USR_18 {
  old_password: string;
  new_password: string;
}

export interface USM_USR_19 {
  invitation_token: string;
  password: string;
  email: string;
}

export interface USM_USR_20 {
  entity_id: string;
  email?: string | null;
  phone?: string | null;
}

export interface USM_USR_22 {
  language?: string;
  calendar_view_mode?: string;
  calendar_show_weekends?: number;
  calendar_view_port_from?: string;
  calendar_view_port_to?: string;
  calendar_order_statuses?: number[];
  last_selected_company_id?: string | null;
  order_list_columns?: string[];
  list_columns?: {
    table_name: string;
    columns: string[]
  };
}

export interface USM_USR_24 {
  user_id: string;
  entity_id: string;
  entity_name: string;
  entity_email: string;
  entity_phone: string;
}

export interface USM_USR_25 {
  invitation_token: string;
}

export interface USM_USR_26 {
  fcm_token: string;
}

export interface USM_ENT_0 extends full_select {
  entity_id: string;
  taskable?: number | null;
  role_id?: number | null;
  include_customers?: number | null;
  user_id?: string;
}

export interface _USM_ENT_0 extends Omit<USM_ENT_0, 'entity_id'>, full_select {} {}

export interface USM_ENT_1 extends full_select {
  entity_specific_only?: number | null;
  entity_id: string;
}

export interface USM_ENT_2 {
  user_id: string;
  entity_id: string;
  role_id: number;
  identificator: string;
  taskable: number;
}

export interface USM_ENT_3 extends full_select {
  entity_tag?: string;
  role_id_list?: number[];
}

/**
  *
  * Input Interfaces
   * CRM
   * Nested Interfaces
  *
  */

export interface AddressInput {
    address_id: number | null;
    external_id?: string | null;

    city?: string | null;
    lat?: number | null;
    lng?: number | null;
    municipality_id?: number | null;
    municipality_name?: string | null;
    street_id?: string | null;
    street?: string | null;
    number?: string | null;
    letter?: string | null;
    postal_code?: string | null;
    area_id?: number | null;
    area?: string | null;
    display?: string | null;
    section_id?: string | null;

    cadastre?: CadastreInput | null;

    business_manager?: string | null;
    homeowners_name?: string | null;
    ownership_fraction?: string | null;
    ownership_type_id?: number;
    share_number?: string | null;
    stock_number?: string | null;
    organisation_number?: string | null;
    organisation_name?: string | null;
    apartment_number?: string | null;
    section_number?: string | null;
    leasehold?: number | null;
    property_type_id?: number | null;
    property_type_name?: string,

    floor?: number | null;
    radon_level?: number | null;
    energy_score?: null | string;
    heating_score?: null | number;
    primary_area?: number | null;
    livable_area?: number | null;
    plot_area?: number | null;
    number_of_floors?: number | null;
    number_of_rooms?: number | null;
    number_of_bathrooms?: number | null;
    number_of_bedrooms?: number | null;
    number_of_units_on_address?: number | null;
    has_elevator?: number | null;
    has_parking?: number | null;
    has_garage?: number | null;
    build_year?: number | null;
    shed_area?: number | null;
    water_connection_type_id?: number | null;
    sewage_connection_type_id?: number | null;
    plot_description?: string | null;
}

export interface UnitDetails {
    address_id: number | null;
    address_name: string | null;
    external_id: string | null;
    index: number | null;
    display?: string | null;
    created_at: string | null;
    city: string | null;
    lat: number | null;
    lng: number | null;
    municipality_id: number | null;
    municipality_name: string | null;
    street_id: string | null;
    street: string | null;
    number: string | null;
    letter: string | null;
    postal_code: string | null;
    area_id: number | null;
    area: string | null;
    section_id: string | null;

    cadastre: CadastreInput | null;

    business_manager: string | null;
    homeowners_name: string | null;
    ownership_fraction: string | null;
    ownership_type_id?: number;
    share_number: string | null;
    stock_number: string | null;
    organisation_number: string | null;
    organisation_name: string | null;
    apartment_number: string | null;
    section_number: string | null;
    leasehold: number | null;
    property_type_id?: number | null;

    floor: number | null;
    radon_level: number | null;
    energy_score: null | string;
    heating_score: null | number;
    primary_area: number | null;
    livable_area: number | null;
    plot_area: number | null;
    leasehold_number: number | null;
    leasehold_start_year: number | null;
    leasehold_regulation_year: number | null;
    leasehold_expiry_year: number | null;
    number_of_floors: number | null;
    number_of_rooms: number | null;
    number_of_bathrooms: number | null;
    number_of_bedrooms: number | null;
    number_of_units_on_address: number | null;
    has_elevator: number | null;
    has_parking: number | null;
    has_garage: number | null;
    property_type_name?: string,
    build_year: number | null;
    ownership_type_name?: string;
    shed_area: number | null;
    cadastre_section_id?: string | null;
    custom_tag?: string | null;
}

export interface CadastreInput {
    kommunenr: number;
    gardsnr: number;
    bruksnr: number;
    festenr: number;
    seksjonsnr: number;
}

export interface OrderLineStageInput {
  stage_id: number;
  address: UnitDetails | null;
  sets_quantity: number;
}

export interface EmbedOrderLineInput {
  product_id: number;
  quantity: number;
  is_taskable: number;
  stages: OrderLineStageInput[];
  execution_at: Date;
  price_rule_ids: number[];
}

export interface OrderLineInput {
  product_id: number | null;
  order_line_name?: string | null;
  unit_price_inc_vat?: number | null;
  vat_rate_id?: number | null;
  quantity: number;
  price_rule_ids: number[];
  comment?: string | null;
  discount_percentage?: number | null;
  work_order_id?: number | null;
}

export interface OrderLineAsJobInput {
  order_line_id: number;
  execution_at: Date;
  execution_to: Date;
  lane: number;
  crew: string[];
}

/**
  *
  * Input Interfaces
   * CRM
   * Endpoint Interfaces
  *
  */

export interface CRM_AFF_0 {
  company_id: string;
  organisation_number: string;
  company_name: string;
  phone: string | null;
  email: string | null;
  address?: AddressInput;
  is_customer?: number;
  is_partner?: number;
  is_subcontractor?: number;
  accounting_id?: string;
  invoice_send_type_id?: number;
  invoice_due_date_days?: number;
  invoice_email?: string;
  comment?: string;
  sub_ledger_account_id?: number | null;
}

export interface _CRM_AFF_0 extends Omit<CRM_AFF_0, 'company_id'> {}

export interface CRM_AFF_1 extends full_select {
  company_id: string;
  customers_only?: number;
  partners_only?: number;
  subcontractors_only?: number;
  private_only?: number;
  business_only?: number;
  calendar_available?: boolean
}

export interface _CRM_AFF_1 extends Omit<CRM_AFF_1, 'company_id'> {}

export interface CRM_AFF_2 {
  company_id: string;
  affiliate_id: number;
  is_partner?: number;
  is_customer?: number;
  is_subcontractor?: number;
  invoice_send_type_id?: number;
  invoice_due_date_days?: number;
  accounting_id?: string | null;
  preferred_payment_method_id?: number;
  _phone?: string | null;
  _email?: string | null;
  _invoice_email?: string;
  hide_payment_data?: number;
  comment?: string;
  address?: AddressInput;
  disable_sms?: number;
  disable_email?: number;
  sub_contractor_hide_prices?: boolean;
  sub_ledger_account_id?: number | null;
  can_fetch_parent_products_as_contractor?: boolean
}

export interface _CRM_AFF_2 extends Omit<CRM_AFF_2, 'company_id'> {}

export interface CRM_AFF_3 {
  company_id: string;
  affiliate_id: number;
}

export interface CRM_AFF_4 {
  company_id: string;
  affiliate_id: number;
}

export interface CRM_AFF_5 {
  company_id: string;
  affiliate_id: number;
  user_id?: string | null;
  email?: string | null;
  phone?: string | null;
  first_name?: string | null;
  last_name?: string | null;
  role_description?: string | null;
}

export interface _CRM_AFF_5 extends Omit<CRM_AFF_5, 'company_id'> {}

export interface CRM_AFF_6 {
  company_id: string;
  affiliate_id?: number;
}

export interface CRM_AFF_7 {
  company_id: string;
  affiliate_contact_id: number;
  first_name?: string;
  last_name?: string;
  role_description?: string;
  comment?: string;
  _email?: string;
  _phone?: string;
}

export interface _CRM_AFF_7 extends Omit<CRM_AFF_7, 'company_id'> {}

export interface CRM_AFF_8 {
  company_id: string;
  affiliate_contact_id: number;
}

export interface CRM_AFF_9 {
  company_id: string;
  search_term: string | null;
  customers_only?: number;
  partners_only?: number;
  subcontractors_only?: number;
  private_only?: number;
  business_only?: number;
}

export interface CRM_AFF_10 {
  company_id: string;
}

export interface CRM_AFF_11 {
  company_id: string;
  affiliate_id: number;
  show_contractor_calendar?: boolean;
}

export interface _CRM_AFF_11 extends Omit<CRM_AFF_11, 'company_id'> {}

export interface CRM_AFF_13 {
  company_id: string;
  affiliate_id: number;
  include_affiliate_address?: boolean;
}

export interface CRM_AFF_14 extends UnitDetails {
  company_id: string;
  affiliate_id: number;
}

export interface _CRM_AFF_14 extends Omit<CRM_AFF_14, 'company_id'> {}

export interface CRM_AFF_15 {
  company_id: string;
  affiliate_id: number;
  address_id: number;
}

export interface _CRM_AFF_15 extends Omit<CRM_AFF_15, 'company_id'> {}

export interface CRM_AFF_16 {
  company_id: string;
  affiliate_id: number;
  note_text: string;
  copy_to_order: boolean
}

export interface _CRM_AFF_16 extends Omit<CRM_AFF_16, 'company_id'> {}

export interface CRM_AFF_17 {
  company_id: string;
  affiliate_id: number;
}

export interface CRM_AFF_18 {
  company_id: string;
  affiliate_id: number;
  note_id: number;
}

export interface _CRM_AFF_18 extends Omit<CRM_AFF_18, 'company_id'> {}

export interface CRM_AFF_19 {
  company_id: string;
  affiliate_id: number;
  note_id: number;
  note_text?: string;
  copy_to_order?: boolean;
}

export interface _CRM_AFF_19 extends Omit<CRM_AFF_19, 'company_id'> {}


export interface CRM_EMP_0 {
  company_id: string;
  role_id: number;
  skip_invitation: boolean
  invitee: {
    email: string;
    first_name: string;
    last_name: string;
    phone: string | null;
    accounting_id?: string | null;
  } | null,
}

export interface _CRM_EMP_0 extends Omit<CRM_EMP_0, 'company_id'> {}

export interface CRM_EMP_1 {
  company_id: string;
  user_id: string;
  first_name: string;
  last_name: string;
  phone: string;
  email: string;
  identificator: string | null;
  role_id: number;
  taskable: number;
}

export interface _CRM_EMP_1 extends Omit<CRM_EMP_1, 'company_id'> {}

export interface CRM_EMP_2 {
  company_id: string;
  user_id: string;
}

export interface CRM_EMP_4 {
  company_id: string;
  user_id: string;
}

export interface _CRM_EMP_4 extends Omit<CRM_EMP_4, 'company_id'> {}

export interface CRM_EMP_5 {
  user_id: string;
  company_id: string;
  add_product: number;
  edit_quantity: number;
  make_external_notes: number;
  make_external_reports: number;
  send_to_payment: number;
  edit_discount: number;
  view_prices: number;
  view_order_lines: number;
  ntf_order_accepted: number;
  ntf_order_finished: number;
  payment_page_access: number;
  set_payment_to_external: number;
  ntf_accounting_integration: number;
  view_all_events_as_crew?: number;
  view_all_orders_as_crew?: number;
  unpaid_order_notification: number;
  failed_schedule_order_creation_notification: number;
  ntf_upcoming_order_not_accepted: number;
  ntf_accounting_integration_fail: number;
  checkin_geo_lock: number;
  ntf_order_rating: number;
  ntf_embed_order: number;
  ntf_customer_email_error: number;
  edit_own_event: number;
  confirm_order: number;
  edit_work_order_execution_time: number;
  edit_order_lines: number;
  complete_job: number;
  cancel_order: number;
  create_order: number;
  view_unconfirmed_orders: number;
  create_event: number;
  edit_company_events: number;
  edit_time_tracking: number;
  create_manual_time_tracking: number;
  edit_employees: number;
  add_payment_recipient: number;
  edit_resources: number;
  ntf_new_customer_msg: number;
  ntf_internal_note?: {
    email: boolean;
    sms: boolean;
    core_push: boolean;
    crew_push: boolean;
  };
  order_scope: number;
  ntf_sub_contractor_order: number;
  ntf_emp_assign_job: number;
  start_work_order_outside_execution_date: number;
  checkout_geo_lock: number;
  ntf_customer_cancel_work_order: number;
}

export interface _CRM_EMP_5 extends Omit<CRM_EMP_5, 'company_id'> {}

export interface CRM_EMP_6 {
  company_id: string;
}

export interface CRM_EMP_7 extends date_input {
  company_id: string;
  user_id: string;
}

export interface _CRM_EMP_7 extends Omit<CRM_EMP_7, 'company_id'> {}

export interface CRM_EMP_8 {
  company_id: string;
}

export interface _CRM_EMP_8 extends Omit<CRM_EMP_8, 'company_id'> {}

export interface CRM_EMP_9 {
  company_id: string;
  user_id: string;
  accounting_id: string;
}

export interface _CRM_EMP_9 extends Omit<CRM_EMP_9, 'company_id'> {}


export interface CRM_EMP_10 {
  company_id: string;
  user_id: string;
}

export interface CRM_EMP_11 {
  company_id: string;
  user_id: string;
  salary_type: 'hourly' | 'monthly';
  active_from: Date | null;
  active_to: Date | null;
  hourly_rate?: number;
  monthly_rate?: number;
}

export interface _CRM_EMP_11 extends Omit<CRM_EMP_11, 'company_id'> {}

export interface CRM_EMP_12 {
  company_id: string;
  user_id: string;
  entry_id: number;
}

export interface _CRM_EMP_12 extends Omit<CRM_EMP_12, 'company_id'> {}

export interface CRM_EMP_13 {
  company_id: string;
  user_id: string;
  entry_id: number;
  active_from?: Date | null;
  active_to?: Date | null;
  hourly_rate?: number;
  monthly_rate?: number;
}

export interface _CRM_EMP_13 extends Omit<CRM_EMP_13, 'company_id'> {}

export interface CRM_EMP_14 {
  company_id: string;
  user_id: string;
  salary_type: 'hourly' | 'monthly';
}

export interface CRM_EMP_15 {
  company_id: string;
}

export interface CRM_EMP_16 {
  company_id: string;
  user_id: string;
}

export interface CRM_EMP_17 {
  company_id: string;
  user_id: string;
  entry_id: number;
  days_used?: number;
  initial_days?: number;
}

export interface _CRM_EMP_17 extends Omit<CRM_EMP_17, 'company_id'> {}

export interface CRM_EMP_18 {
  company_id: string;
  user_id: string;
  year: number;
  days_used?: number;
  initial_days?: number;
}

export interface _CRM_EMP_18 extends Omit<CRM_EMP_18, 'company_id'> {}

export interface CRM_EMP_19 {
  company_id: string;
  user_id: string;
  entry_id: number;
}

export interface _CRM_EMP_19 extends Omit<CRM_EMP_19, 'company_id'> {}

export interface OrderLineAddressStage extends OrderLineStageInput, ProductStageResponse {
  childStages: OrderLineAddressStage[];
  tempId: number;
  setLater?: boolean;
  clearObservable: Subject<void>;
}

export interface TempOrderLineStage extends OrderLineStageInput {
  tempId: number;
  stage_name?: string;
  setLater?: boolean;
}

export interface TempOrderLineInput extends OrderLineInput {
  tempId: number;
  product_name: string | null;
  custom_product_name?: string | null;
  icon_id: number | null;
  price_inc_vat: number;
  stock_quantity: number | null;
  unit_abbreviation: string | null;
  productStages: ProductStageResponse[];
  tempOrderLineStages: TempOrderLineStage[];
  addressStages: OrderLineAddressStage[];
  serviceSelected?: boolean;
  user_ids: string[];
  resource_ids: number[];
}


export interface CRM_ORD_0 {
  company_id: string;
  order_lines: OrderLineInput[];
  payment_recipient_id: number;
  service_recipient_id: number | null;
  affiliate_contact_id: number | null;
  order_status_id?: number | null;
  confirmation_status_id?: number | null;
  payment_method_id?: number | null;
  partner_id?: number | null;
  partner_contact_id?: number | null;
  use_duration_calculation?: number;
  notes: {note_text: string; internal: number;}[]
  invoice_reference_text?: string | null;
  comment?: string | null;
  specifications?: [{
    choice_id: number;
    value: number;
    input: string
  }],
  order_schedule?: NestedOrderScheduleInput;
}

export interface _CRM_ORD_0 extends Omit<CRM_ORD_0, 'company_id'> {}

export interface NestedOrderScheduleInput {
  schedule_repeat_type_id: number;
  weekdays: number[];
  every: number;
  use_date: number;
  date: number | null;
  nth_weekday: number | null;
  orders_in_advance: number;
  enable_customer_notifications: number;
  order_schedule_order_lines: NestedOrderScheduleOrderLine[];
  fixed_payment: NestedFixedPayment | null;
}

export interface NestedFixedPayment {
  date: number,
  every: number,
  total_price_inc_vat: number,
  vat_rate_id: number,
  service_description: string,
  invoice_reference_text: string,
}

export interface TempTaskableOrderLine extends TempOrderLineInput {
  duration_hours: number | null;
  serviceSelected: boolean;
  customerSelected: boolean;
  unit_id?: number;
  execution_at: Date;
  execution_to: Date;
  arrival_from: string | null;
  arrival_to: string | null;

}

export interface TempOrder extends _CRM_ORD_0 {
  taskableOrderLine: TempTaskableOrderLine;
  order_lines: TempOrderLineInput[],
  tempAddress: UnitDetails | null;
  paymentRecipient?: CustomerResponse | null;
  serviceRecipient?: CustomerResponse | null;
  singleAffiliate: boolean;
  paymentRecipientSelected: boolean;
  serviceRecipientSelected: boolean;
  partnerSelected: boolean;
  partner?: AffiliateResponse | null;
  users: InternalUserResponse[];
  resources: ResourceResponse[];
  useDurationCalculation: boolean;
  durationCalculationProductSelected: boolean;
  creationReady: boolean;
  showOrderNotes: boolean;
  multiDay: boolean;
  creationReadyReasons: string[];
  errors: {
    addressError: boolean;
    timeError: boolean;
    dateError: boolean;
  };
  enableSchedule: boolean;
  scheduleInput: NestedOrderScheduleInput;
  manualHours: boolean;
}

export interface CRM_ORD_1 {
  company_id: string;
  payment_recipient_id: number;
  order_lines: EmbedOrderLineInput[];
  specifications?: [{
    choice_id: number;
    value: number;
    input: string
  }]
}

export interface CRM_ORD_2 extends full_select {
  company_id: string;
  include_order_lines?: number | null;
  partner_id?: number | null;
  partner_contact_id?: number | null;
  order_status_ids?: number[] | null;
  execution_date_from?: Date | null;
  execution_date_to?: Date | null;
  captured_at_from?: Date | null;
  captured_at_to?: Date | null;
  confirmation_status_id?: number | null;
  payment_status_ids?: number[] | null;
  archived?: number | null;
  not_posted_in_accounting?: number | null;
  order_schedule_id?: number | null;
  payment_recipient_id?: number | null;
  service_recipient_id?: number | null;
  quote_sent?: number | null;
  user_ids?: string[] | null;
  contains_schedules?: boolean | null;
  product_ids?: number[];
}

export interface _CRM_ORD_2 extends Omit<CRM_ORD_2, 'company_id'> {}

export interface CRM_ORD_3 extends pagination_input, sorting_input, search_input {
  company_id: string;
  is_taskable?: number | null;
  execution_date_from?: Date | null;
  execution_date_to?: Date | null;
}

export interface CRM_ORD_4 {
  company_id: string;
  execution_date_from?: Date | null;
  execution_date_to?: Date | null;
}


export interface CRM_ORD_5 extends date_input {
  company_id: string;
}

export interface CRM_ORD_6 extends date_input {
  company_id: string;
}

export interface CRM_ORD_7 extends date_input {
  company_id: string;
}

export interface CRM_ORD_8 extends date_input {
  company_id: string;
  paid_only?: boolean;
}

export interface CRM_ORD_9 {
  company_id: string;
  order_lines: OrderLineAsJobInput[];
}

export interface _CRM_ORD_9 extends Omit<CRM_ORD_9, 'company_id'> {}

export interface CRM_ORD_10 {
  company_id: string;
  order_id?: number;
  order_line_id?: number;
}

export interface CRM_ORD_11 {
  company_id: string;
  execution_date_from: Date;
  execution_date_to: Date;
  order_status_ids: number[];
}

export interface CRM_ORD_12 {
  company_id: string;
  order_id?: number;
  payment_id?: number;
  product_id?: number | null;
  order_line_name: string;
  unit_price_inc_vat: number;
  vat_rate_id: number;
  unit_id: number;
  quantity: number;
  price_rule_ids: number[];
  comment?: string | null;
  discount_percentage?: number | null;
  work_order_id?: number | null;
  payment_exclusive?: boolean;
  template?: boolean;
  update_upcoming_work_orders?: boolean;
  add_directly_to_order?: boolean;
}

export interface _CRM_ORD_12 extends Omit<CRM_ORD_12, 'company_id'> {}


export interface CRM_ORD_13 {
  company_id: string;
  order_id: number;
  discount_percentage: number;
  order_line_ids: number[];
}

export interface _CRM_ORD_13 extends Omit<CRM_ORD_13, 'company_id'> {}

export interface CRM_ORD_15 {
  company_id: string;
  order_line_id: number;
}

export interface CRM_ORD_16 extends pagination_input, sorting_input, search_input {
  execution_date_from: Date;
  execution_date_to: Date;
  order_status_ids: number[];
}

export interface CRM_ORD_17 {
  company_id: string;
  order_line_id: number;
  price_modifier_ids: number[];
  execution_at: Date;
  execution_to: Date;
  quantity: number;
}

export interface CRM_ORD_19 {
  company_id: string;
  order_id: number;
  note_text: string;
  internal: number;
}

export interface CRM_ORD_20 {
  company_id: string;
  order_id: number;
}

export interface CRM_ORD_21 {
  company_id: string;
  order_note_id: number;
}

export interface CRM_ORD_22 {
  company_id: string;
  order_note_id: number;
  note_text: string;
}

export interface CRM_ORD_23 {
  order_id: number;
  note_text: string;
}

export interface CRM_ORD_25 {
  order_note_id: number;
}

export interface CRM_ORD_26 {
  order_note_id: number;
  note_text: string;
}

export interface CRM_ORD_27 {
  company_id: string;
  order_id: number;
  sms: number;
  email: number;
}

export interface CRM_ORD_28 {
  order_id: number;
  order_token: string;
}

export interface CRM_ORD_29 extends pagination_input, sorting_input, search_input {
  order_id?: number | null;
}

export interface CRM_ORD_31 {
  company_id: string;
  order_line_id: number;
  order_line_name?: string;
  quantity?: number;
  comment?: string | null;
  unit_price_inc_vat?: number | null;
  discount_percentage?: number | null;
  added_price_rule_ids?: number[];
  removed_price_rule_ids?: number[];
  work_order_id?: number | null;
  index?: number;
  vat_rate_id?: number;
  track_time?: boolean;
  update_upcoming_work_orders?: boolean;
}

export interface _CRM_ORD_31 extends Omit<CRM_ORD_31, 'company_id'> {}

export interface CRM_ORD_33 {
  order_id: number;
}

export interface CRM_ORD_34 {
  order_id: number;
  order_token: string;
}

export interface CRM_ORD_35 {
  company_id: string;
  execution_date_from: Date;
  execution_date_to: Date;
}

export interface CRM_ORD_36 {
  company_id: string;
  order_line_id: number;
  cargo: [
    {
      cargo_type_id: number;
      quantity: number;
    }
    ];
}

export interface CRM_ORD_37 {
  order_id: number;
  rating: number;
  comment: string;
}

export interface CRM_ORD_38 {
  order_id: number;
  order_token: string;
}

export interface CRM_ORD_39 {
  company_id: string;
  order_id: number;
  send_confirmation?: boolean;
}

export interface CRM_ORD_40 {
  order_id: number;
}

export interface CRM_ORD_41 {
  order_line_id: number;
  quantity: number;
}

export interface CRM_ORD_42 {
  order_id: number;
  order_line: {
    product_id: number;
    quantity: number;
  }
}

export interface CRM_ORD_43 {
  order_id: number;
}

export interface CRM_ORD_45 {
  order_line_id: number;
  cargo: [
    {
      cargo_type_id: number;
      quantity: number;
    }
  ]
}

export interface CRM_ORD_46 {
  order_line_id: number;
}

export interface CRM_ORD_47 {
  order_id: number;
  order_line: {
    product_id: number;
    quantity: number;
  }
}

export interface CRM_ORD_48 {
  order_id: number;
  company_id: string;
}

export interface CRM_ORD_49 {
  order_id: number;
  company_id: string;
}

export interface _CRM_ORD_49 extends Omit<CRM_ORD_49, 'company_id'> {}

export interface CRM_ORD_53 {
  company_id: string;
  order_line_stage_id: number;
  address: UnitDetails;
}

export interface _CRM_ORD_53 extends Omit<CRM_ORD_53, 'company_id'> {}


export interface CRM_ORD_55 {
  company_id: string;
  order_id: number;
  order_status_id: number;
  notify_customer?: number | null
}

export interface _CRM_ORD_55 extends Omit<CRM_ORD_55, 'company_id'> {}

export interface CRM_ORD_56 {
  company_id: string;
  order_id: number;
  partner_id: number | null; // For now, this should be the same as paper_company_id (PCO-X)
  partner_contact_id: number | null; // For now, this should be the same as paper_employee_id (PEM-X)
  refresh_affiliate_prices: boolean;
}

export interface _CRM_ORD_56 extends Omit<CRM_ORD_56, 'company_id'> {}

// export interface CRM_ORD_57 {
//   company_id: string;
//   order_id: number;
//   invoice_recipient_type_id: number; // Either 0 (Customer) or 1 (Partner)
// }

export interface CRM_ORD_60 {
  company_id: string;
  order_id: number;
  product_name: string;
  price_inc_vat: number;
  vat_rate_id: number;
}

export interface _CRM_ORD_60 extends Omit<CRM_ORD_60, 'company_id'> {}

export interface CRM_ORD_61 {
  company_id: string,
  order_line_id: number,
  stage_id: number,
  address: UnitDetails
}

export interface _CRM_ORD_61 extends Omit<CRM_ORD_61, 'company_id'> {}

export interface CRM_ORD_64 {
  company_id: string,
  order_line_stage_id: number,
}


export interface CRM_ORD_66 {
  company_id: string;
  order_line_id: number;
  user_ids: string[];
}

export interface _CRM_ORD_66 extends Omit<CRM_ORD_66, 'company_id'> {}

export interface CRM_ORD_67 {
  company_id: string;
  order_line_id: number;
  resource_ids: number[];
}

export interface _CRM_ORD_67 extends Omit<CRM_ORD_67, 'company_id'> {}

export interface CRM_ORD_68 {
  company_id: string;
  order_id: number;
  archived: number;
}

export interface CRM_ORD_69 {
  company_id: string;
  order_line_id: number;
  total_price_inc_vat: number;
}

export interface CRM_ORD_71 {
  company_id: string;
  order_id: number;
}

export interface CRM_ORD_73 {
  company_id: string;
  order_id: number;
  order_line_id: number;
  discount_amount: number;
}

export interface _CRM_ORD_73 extends Omit<CRM_ORD_73, 'company_id'> {}

export interface CRM_ORD_74 {
  company_id: string;
  order_id: number;
  invoice_send_type_id: number;
  invoice_due_date_days: number;
  invoice_reference_text: string;
  invoice_email?: string | null;
}

export interface _CRM_ORD_74 extends Omit<CRM_ORD_74, 'company_id'> {}


export interface CRM_ORD_76 {
  company_id: string;
  start_time: Date;
  end_time: Date;
  start_date: Date;
  status?: number;
  schedule_repeat_type_id: number;
  weekdays: number[];
  every: number;
  date: number | null;
  nth_weekday: number | null;
  orders_in_advance: number;
  payment_recipient_id: number;
  service_recipient_id?: number | null;
  partner_id?: number;
  partner_contact_id?: number;
  payment_method_id?: number;
  enable_customer_notifications: number;
  order_schedule_order_lines: NestedOrderScheduleOrderLine[];
  invoice_reference_text?: string | null;
  fixed_payment: NestedFixedPayment | null;
}

export interface _CRM_ORD_76 extends Omit<CRM_ORD_76, 'company_id'> {}

export interface CRM_ORD_77 {
  company_id: string;
  order_schedule_id: number;
  cancel_future_orders: number;
}

export interface CRM_ORD_78 {
  company_id: string;
  order_schedule_id: number;
}

export interface CRM_ORD_79 {
  company_id: string;
  order_schedule_id: number;
  start_time?: Date;
  end_time?: Date;
  status?: number;
  schedule_repeat_type_id?: number;
  weekdays?: number[];
  every?: number;
  date?: number | null;
  nth_weekday?: number | null;
  orders_in_advance?: number;
  enable_customer_notifications?: number;
  partner_id?: number;
  partner_contact_id?: number;
  payment_method_id?: number;
  cancel_future_orders?: number;
  user_ids?: string[];
  resource_ids?: number[];
}

export interface _CRM_ORD_79 extends Omit<CRM_ORD_79, 'company_id'> {}

export interface CRM_ORD_80 extends pagination_input, sorting_input, search_input {
  company_id: string;
}

export interface _CRM_ORD_80 extends Omit<CRM_ORD_80, 'company_id'> {}

export interface CRM_ORD_81 {
  company_id: string;
  order_schedule_id: number;
  product_id?: number;
  quantity: number;
  total_price_inc_vat?: number | null;
  product_price_inc_vat?: number | null;
  total_price_override?: number | null;
  custom_vat_rate_id?: number | null;
  custom_product_name?: number | null;
  price_rule_ids: number[];
}

export interface _CRM_ORD_81 extends Omit<CRM_ORD_81, 'company_id'> {}

export interface CRM_ORD_82 {
  company_id: string;
  order_schedule_id: number;
  order_schedule_order_line_id: number;
  product_id?: number | null;
  quantity: number;
  total_price_inc_vat?: number | null;
  product_price_inc_vat?: number | null;
  total_price_override?: number | null;
  custom_vat_rate_id?: number | null;
  custom_product_name?: number | null;
  price_rule_ids?: number[];
  stages?: OrderLineStageInput[];
  user_ids?: string[];
  resource_ids?: number[];
}

export interface _CRM_ORD_82 extends Omit<CRM_ORD_82, 'company_id'> {}

export interface CRM_ORD_83 {
  company_id: string;
  order_schedule_id: number;
  order_schedule_order_line_id: number;
}

export interface _CRM_ORD_83 extends Omit<CRM_ORD_83, 'company_id'> {}

export interface CRM_ORD_85 {
  company_id: string;
  start_time: Date | null;
  end_time: Date | null;
  start_date?: Date | null;
  end_date?: Date | null;
  schedule_repeat_type_id: number;
  weekdays: number[];
  every: number;
  date: number | null;
  nth_weekday: number | null;
  instances_in_advance?: number;
}

export interface _CRM_ORD_85 extends Omit<CRM_ORD_85, 'company_id'> {}

export interface CRM_ORD_86 {
  company_id: string;
  order_id: number;
  payment_recipient_id: number;
  affiliate_contact_id?: number | null;
}

export interface _CRM_ORD_86 extends Omit<CRM_ORD_86, 'company_id'> {}

export interface CRM_ORD_87 {
  company_id: string;
  order_id: number;
  log_type_ids?: number[];
}

export interface _CRM_ORD_87 extends Omit<CRM_ORD_87, 'company_id'> {}

export interface CRM_ORD_90 {
  company_id: string;
  order_schedule_id: number;
  for_customer: number;
}

export interface CRM_ORD_91 {
  company_id: string;
  order_schedule_id: number;
  sms: number;
  email: number;
}

export interface CRM_ORD_93 {
  company_id: string;
  order_schedule_id: number;
}

export interface CRM_ORD_95 {
  company_id: string;
  order_schedule_id: number;
  active: number;
  send_customer_confirmation: number;
}

export interface CRM_ORD_96 {
  company_id: string;
  order_schedule_id: number;
  payment_method_id: number;
}

export interface CRM_ORD_97 {
  company_id: string;
  order_id: number;
  enable_customer_notifications?: number;
  comment?: string | null;
  repeating?: boolean;
  order_title?: string | null;
  quote_expiry_days?: number | null;
  is_project?: boolean;
  convert_tracked_time_to_working_hours?: boolean;
  activity_id?: number;
  project_id?: number | null;
  department_id?: number | null;
  require_crew_work_order_confirmation?: boolean;
}

export interface _CRM_ORD_97 extends Omit<CRM_ORD_97, 'company_id'> {}

export interface CRM_ORD_98 {
  company_id: string;
  order_id: number;
  attachment: File;
  work_order_id?: number;
}

export interface _CRM_ORD_98 extends Omit<CRM_ORD_98, 'company_id'> {}

export interface CRM_ORD_99 {
  company_id: string;
  attachment_id: number;
}

export interface CRM_ORD_100 {
  company_id: string;
  order_id: number;
  attachment_id: number;
}

export interface CRM_ORD_101 {
  company_id: string;
  work_order_id: number;
  affiliate_ids: number[];
  accept: boolean;
}

export interface _CRM_ORD_101 extends Omit<CRM_ORD_101, 'company_id'> {}

export interface CRM_ORD_102 {
  company_id: string;
  order_id: number;
  affiliate_id: number;
}

export interface CRM_ORD_103 {
  company_id: string;
  order_id: number;
  payment_recipient_id?: number;
  service_recipient_id?: number;
  affiliate_contact_id?: number;
  refresh_affiliate_prices?: boolean;
}

export interface _CRM_ORD_103 extends Omit<CRM_ORD_103, 'company_id'> {}

export interface CRM_ORD_104 {
  company_id: string;
  order_id: number;
  affiliate_id: number;
}

export interface CRM_ORD_107 {
  company_id: string;
  order_id: number;
}

export interface CRM_ORD_110 {
  company_id: string;
  order_id: number;
  attachment_id?: number;
  attach_to_invoice?: number;
  visible_to_customer?: boolean;
  visible_to_crew?: boolean;
}

export interface _CRM_ORD_110 extends Omit<CRM_ORD_110, 'company_id'> {}

export interface CRM_ORD_113 {
  company_id: string;
  work_order_id: number;
  user_id: string;
  update_children_assignments?: boolean;
}

export interface _CRM_ORD_113 extends Omit<CRM_ORD_113, 'company_id'> {}

export interface CRM_ORD_114 {
  company_id: string;
  work_order_id: number;
  user_id: string;
  update_children_assignments?: boolean;
}

export interface _CRM_ORD_114 extends Omit<CRM_ORD_114, 'company_id'> {}

export interface CRM_ORD_115 {
  company_id: string;
  work_order_id: number;
  resource_id: string;
}

export interface _CRM_ORD_115 extends Omit<CRM_ORD_115, 'company_id'> {}

export interface CRM_ORD_116 {
  company_id: string;
  work_order_id: number;
  resource_id: number;
  update_children_assignments?: boolean;
}

export interface _CRM_ORD_116 extends Omit<CRM_ORD_116, 'company_id'> {}

export interface CRM_ORD_117 {
  company_id: string;
  order_id?: number | null;
  work_order_title?: string;
  work_order_description?: string;
  execution_at: Date | null;
  execution_to: Date | null;
  arrival_from?: string | null;
  arrival_to?: string | null;
  addresses?: UnitDetails[];
  user_ids?: string[];
  resource_ids?: number[];
  work_order_template_id?: number | null;
  skip_order_line_creation?: boolean;
  create_order?: boolean;
  custom_color_hex?: string | null;
}

export interface _CRM_ORD_117 extends Omit<CRM_ORD_117, 'company_id'> {}

export interface CRM_ORD_118 {
  company_id: string;
  work_order_id: number;
  execution_at?: Date;
  execution_to?: Date;
  arrival_from?: string | null;
  arrival_to?: string | null;
  work_order_title?: string;
  work_order_description?: string;
  schedule?: ScheduleInput;
  update_children_executions?: boolean;
  update_children_text?: boolean;
  addresses?: UnitDetails[];
  deleted_address_ids?: number[];
  additional_data?: { addresses: string[] };
  work_order_status_id?: number;
  convert_tracked_time_to_working_hours?: boolean;
  activity_id?: number;
  update_activity_and_time_tracking?: boolean;
  update_children_addresses?: boolean;
  fixed_transport_duration_minutes?: number | null;
  custom_color_hex?: string | null;
  default_pause_duration_minutes?: number | null;
  default_pause_start_time?: string | null;
  hide_in_order_lines_list?: boolean;
}

export interface _CRM_ORD_118 extends Omit<CRM_ORD_118, 'company_id'> {}

export interface CRM_ORD_119 {
  company_id: string;
  work_order_id: number;
  delete_future_children?: boolean;
  delete_payment?: boolean;
}

export interface _CRM_ORD_119 extends Omit<CRM_ORD_119, 'company_id'> {}

export interface CRM_ORD_120 {
  company_id: string;
  order_title?: string;
  repeating?: boolean;
}

export interface _CRM_ORD_120 extends Omit<CRM_ORD_120, 'company_id'> {}

export interface CRM_ORD_121 {
  company_id: string;
  order_id: number;
}

export interface ScheduleInput {
  date: number | null
  every: number;
  schedule_repeat_type_id: number;
  schedule_repeat_type_name?: string;
  weekdays: number[];
  nth_weekday: number | null;
  instances_in_advance?: number;
  active?: boolean;
  start_date?: Date | null;
  end_date?: Date | null;
  executionAt?: Date;
  initDescription?: string | null;
  sourceName?: string | null;
}

export type PatchScheduleInput = Partial<ScheduleInput>;

export interface CRM_ORD_122 extends CRM_ORD_117, ScheduleInput {
  company_id: string;
  active: boolean;
}

export interface _CRM_ORD_122 extends Omit<CRM_ORD_122, 'company_id'> {}

export interface CRM_ORD_123 {
  company_id: string;
  work_order_schedule_id: number;
  start_date?: Date | null;
  schedule_repeat_type_id?: number;
  weekdays?: number[];
  every?: number;
  date?: number | null;
  nth_weekday?: number | null;
  instances_in_advance?: number;
  active?: boolean;
  update_work_orders?: number;
}

export interface _CRM_ORD_123 extends Omit<CRM_ORD_123, 'company_id'> {}

export interface CRM_ORD_124 {
  company_id: string;
  work_order_schedule_id: number;
}

export interface CRM_ORD_125 {
  company_id: string;
  work_order_schedule_id: number;
}

export interface CRM_ORD_126 {
  company_id: string;
  work_order_id: number;
  task_template_id: number;
}

export interface _CRM_ORD_126 extends Omit<CRM_ORD_126, 'company_id'> {}

export interface CRM_ORD_127 {
  company_id: string;
  work_order_id: number;
  task_group_id: number;
  task_name: string;
  index?: number;
}

export interface _CRM_ORD_127 extends Omit<CRM_ORD_127, 'company_id'> {}

export interface CRM_ORD_128 {
  company_id: string;
  work_order_id: number;
  task_id: number;
  task_name?: string;
  task_status?: number;
  comment?: string;
  index?: number;
}

export interface _CRM_ORD_128 extends Omit<CRM_ORD_128, 'company_id'> {}

export interface CRM_ORD_129 {
  company_id: string;
  work_order_id: number;
  task_id: number;
}

export interface _CRM_ORD_129 extends Omit<CRM_ORD_129, 'company_id'> {}

export interface CRM_ORD_130 {
  company_id: string;
  work_order_id: number;
  task_group_name: string;
  icon_id?: number;
  index?: number;
  tasks?: {
    task_name: string;
    index?: number;
  }[];
}

export interface _CRM_ORD_130 extends Omit<CRM_ORD_130, 'company_id'> {}

export interface CRM_ORD_131 {
  company_id: string;
  work_order_id: number;
  task_group_id: number;
  task_group_name?: string;
  icon_id?: number;
  index?: number;
  tasks?: {
    task_id: number | null;
    task_name?: string;
    index?: number | null;
    deleted?: boolean;
  }[];
}

export interface _CRM_ORD_131 extends Omit<CRM_ORD_131, 'company_id'> {}

export interface CRM_ORD_132 {
  company_id: string;
  work_order_id: number;
  task_group_id: number;
}

export interface _CRM_ORD_132 extends Omit<CRM_ORD_132, 'company_id'> {}

export interface CRM_ORD_133 {
  company_id: string;
  template_id: number;
  order_id: number;
}

export interface _CRM_ORD_133 extends Omit<CRM_ORD_133, 'company_id'> {}

export interface CRM_ORD_134 {
  company_id: string;
  order_id: number;
  question_text: string;
  radio_selection: number;
  required: number;
  choices?: {
    choice_name: string;
    choice_ghost_text: string | null;
    index?: number;
  }[];
}

export interface _CRM_ORD_134 extends Omit<CRM_ORD_134, 'company_id'> {}

export interface CRM_ORD_135 {
  company_id: string;
  order_id: number;
  order_question_id: number;
  // question_id?: number;
  question_text?: string;
  radio_selection?: number;
  required?: number;
  index?: number;
  choices?: {choice_id: number | null, choice_name: string, choice_ghost_text: string | null, deleted: boolean}[];
}

export interface _CRM_ORD_135 extends Omit<CRM_ORD_135, 'company_id'> {}

export interface CRM_ORD_136 {
  company_id: string;
  order_id: number;
  order_question_id: number;
}

export interface _CRM_ORD_136 extends Omit<CRM_ORD_136, 'company_id'> {}

export interface CRM_ORD_137 {
  company_id: string;
  order_question_id: number;
  order_choice_id: number;
  choice_name: string;
  choice_ghost_text?: string | null;
  index?: number;
}

export interface _CRM_ORD_137 extends Omit<CRM_ORD_137, 'company_id'> {}

export interface CRM_ORD_138 {
  company_id: string;
  order_question_id: number;
  order_choice_id: number;
  choice_name?: string;
  choice_ghost_text?: string | null;
  index?: number;
}

export interface _CRM_ORD_138 extends Omit<CRM_ORD_138, 'company_id'> {}

export interface CRM_ORD_139 {
  company_id: string;
  order_question_id: number;
  order_choice_id: number;
}

export interface _CRM_ORD_138 extends Omit<CRM_ORD_139, 'company_id'> {}

export interface CRM_ORD_142 {
  company_id: string;
  work_order_id: number;
  attachment: File;
}

export interface CRM_ORD_143 {
  company_id: string;
  work_order_id: number;
}

export interface CRM_ORD_144 {
  company_id: string;
  work_order_ids: number[];
  address: UnitDetails;
}

export interface _CRM_ORD_144 extends Omit<CRM_ORD_144, 'company_id'> {}

export interface NestedOrderScheduleOrderLine {
  product_id?: number | null;
  quantity: number;
  is_taskable: number;
  total_price_inc_vat?: number | null;
  product_price_inc_vat?: number | null;
  total_price_override?: number | null;
  custom_vat_rate_id?: number | null;
  custom_product_name?: number | null;
  price_rule_ids: number[];
  stages: OrderLineStageInput[];
  user_ids: string[];
  resource_ids: number[];
}

export interface CRM_ORD_150 {
  company_id: string;
  work_order_id: number;
}

export interface CRM_ORD_151 {
  company_id: string;
  order_choice_id: number;
  value: number;
  input?: string | null;
}

export interface _CRM_ORD_151 extends Omit<CRM_ORD_151, 'company_id'> {}

export interface CRM_ORD_153 extends pagination_input, sorting_input {
  company_id: string;
  work_order_id: number;
  work_order_status_ids: number[];
  as_contractor?: boolean;
}

export interface _CRM_ORD_153 extends Omit<CRM_ORD_153, 'company_id'> {}

export interface CRM_ORD_154 {
  company_id: string;
  work_order_id: number;
  execution_at: Date;
  copy_users: boolean;
  copy_resources: boolean;
  copy_tasks: boolean;
  copy_order_lines: boolean;
}

export interface _CRM_ORD_154 extends Omit<CRM_ORD_154, 'company_id'> {}

export interface CRM_ORD_162 {
  company_id: string;
  order_id?: number;
  work_order_id?: number;
  payment_id?: number;
}

export interface _CRM_ORD_162 extends Omit<CRM_ORD_162, 'company_id'> {}

export interface CRM_ORD_163 {
  company_id: string;
  order_id: number;
  entry_id: number;
}

export interface _CRM_ORD_163 extends Omit<CRM_ORD_163, 'company_id'> {}

export interface CRM_ORD_164 {
  company_id: string;
  order_id: number;
  entry_id: number;
}

export interface _CRM_ORD_164 extends Omit<CRM_ORD_164, 'company_id'> {}

export interface CRM_ORD_165 extends pagination_input {
  company_id: string;
  order_number?: string;
  product_name?: string;
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
  address?: string;
  comment?: string;
}

export interface _CRM_ORD_165 extends Omit<CRM_ORD_165, 'company_id'> {}

export interface CRM_ORD_167 {
  company_id: string;
  order_id: number;
}

export interface CRM_ORD_168 extends pagination_input, sorting_input {
  company_id: string;
  accepted?: boolean;
  unanswered_only?: boolean;
  include_schedules?: boolean;
}

export interface _CRM_ORD_168 extends Omit<CRM_ORD_168, 'company_id'> {}

export interface CRM_ORD_169 {
  company_id: string;
  work_order_id: number;
  accept?: true;
  decline?: true;
  declined_reason?: string;
}

export interface _CRM_ORD_169 extends Omit<CRM_ORD_169, 'company_id'> {}

export interface CRM_ORD_170 extends search_input, pagination_input, sorting_input {
  company_id: string;
  work_order_status_ids?: number[];
  execution_date_from?: Date | null;
  execution_date_to?: Date | null;
  user_ids?: string[];
  resource_ids?: number[];
  received_work_orders_only?: boolean;
  sent_work_orders_only?: boolean;
  unplanned_only?: boolean;
  work_order_id?: number | null;
  order_id?: number | null;
  template_filter?: boolean;
  parent_work_order_ids?: number[];
  allow_unplanned?: boolean;
}

export interface _CRM_ORD_170 extends Omit<CRM_ORD_170, 'company_id'> {}

export interface CRM_ORD_171 {
  company_id: string;
  work_order_id: number;
}

export interface CRM_ORD_172 {
  company_id: string;
  work_order_id: number;
}

export interface CRM_ORD_177 {
  company_id: string;
  work_order_id: number;
  address_id: number;
  index: number;
}

export interface _CRM_ORD_177 extends Omit<CRM_ORD_177, 'company_id'> {}

export interface CRM_ORD_178 {
  company_id: string;
  work_order_id: number;
}

export interface CRM_ORD_179 {
  company_id: string;
  customer: {
    name?: string | null;
    email?: string | null;
    phone?: string | null;
  }
  source_tag?: string; // Free-text tag to recognize the source of the order
  order_title?: string | null;
  repeating?: boolean; // Whether this order will include repeating elements. Can be altered per order later.
  order_note?: string | null; // Note from the customer
  default_duration_hours?: number | null; // Default duration (in hours) for any work orders created from this payload
  work_order?: {
    execution_at?: Date | null;
    arrival_from?: string | null; // HH:mm
    arrival_to?: string | null; // HH:mm
    work_order_title?: string | null;
    work_order_description?: string | null;
    addresses?: UnitDetails[];
  }
}

export interface CRM_EMB_0 {
  company_id: string;
}

export interface CRM_EMB_1 {
  company_id: string;
}

export interface CRM_EMB_2 {
  embed_id: number;
  company_id: string;
  embed_name?: string;
  primary_color?: string;
  button_background_color?: string;
  button_border_color?: string;
  button_hover_background_color?: string;
  button_hover_border_color?: string;
  button_border_radius?: number;
  button_text_color?: string;
  button_hover_text_color?: string;
  button_text?: string;
  show_prices_for_customers?: number;
  require_otp?: number;
  show_upsell?: number;
  google_tracking_id?: string;
  google_conversion_send_to?: string;
  show_prices_ex_vat?: number;
  seller_user_id?: string | null;
}

export interface _CRM_EMB_2 extends Omit<CRM_EMB_2, 'company_id'> {}

export interface CRM_EMB_3 {
  company_id: string;
  embed_id: number;
}

export  interface _CRM_EMB_3 extends Omit<CRM_EMB_3, 'company_id'> {}

export interface CRM_EMB_4 {
  company_id: string;
  embed_id: number;
}

export interface _CRM_EMB_4 extends Omit<CRM_EMB_4, 'company_id'> {}

export interface CRM_EMB_5 {
  company_id: string;
  embed_id: number;
  product_id: number;
}

export interface _CRM_EMB_5 extends Omit<CRM_EMB_5, 'company_id'> {}

export interface CRM_EMB_6 {
  company_id: string;
  embed_id: number;
  embed_product_id: number;
  allow_recurring?: number;
  index?: number;
}

export interface _CRM_EMB_6 extends Omit<CRM_EMB_6, 'company_id'> {}

export interface CRM_EMB_7 {
  company_id: string;
  embed_id: number;
  embed_product_id: number;
}

export interface _CRM_EMB_7 extends Omit<CRM_EMB_7, 'company_id'> {}

export interface NestedEmbedSpecificationChoiceInput {
  choice_name: string;
  choice_ghost_text: string | null;
  index: number;
}

export interface CRM_EMB_8 {
  company_id: string;
  embed_product_id: number;
  specification_text: string;
  radio_selection: number;
  required: number;
  index: number;
  choices: NestedEmbedSpecificationChoiceInput[]
}

export interface _CRM_EMB_8 extends Omit<CRM_EMB_8, 'company_id'> {}

export interface CRM_EMB_9 {
  company_id: string;
  embed_product_id: number;
  specification_id: number;
  specification_text?: string;
  required?: number;
  radio_selection?: number;
  index?: number;
  choices?: NestedEmbedSpecificationChoiceInput[]
}

export interface _CRM_EMB_9 extends Omit<CRM_EMB_9, 'company_id'> {}

export interface CRM_EMB_10 {
  company_id: string;
  specification_id: number;
}

export interface _CRM_EMB_10 extends Omit<CRM_EMB_10, 'company_id'> {}

export interface CRM_EMB_11 {
  company_id: string;
  specification_id: number;
  choice_name: string;
  choice_ghost_text: string | null;
  index: number;
}

export interface _CRM_EMB_11 extends Omit<CRM_EMB_11, 'company_id'> {}

export interface CRM_EMB_12 {
  company_id: string;
  specification_id: number;
  choice_id: number;
  choice_name?: string;
  choice_ghost_text?: string | null;
  index?: number;
}

export interface _CRM_EMB_12 extends Omit<CRM_EMB_12, 'company_id'> {}

export interface CRM_EMB_13 {
  company_id: string;
  specification_id: number;
  choice_id: number;
}

export interface _CRM_EMB_13 extends Omit<CRM_EMB_13, 'company_id'> {}


export interface CRM_EMB_18 {
  company_id: string;
  embed_id: number;
}

export interface CompanyInviteeInput {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
}

export interface CRM_COY_0 {
  company_name: string;
  organisation_number: string;
  address: UnitDetails;
  phone: string;
  email: string;
  company_type_id?: number;
  invitee?: CompanyInviteeInput | null;
}

export interface CRM_COY_1 {
  company_id: string;
}

export interface CRM_COY_2 extends full_select {
  company_type_id?: number | null;
}

export interface CRM_COY_3 extends full_select {
  company_id: string;
  company_name: string;
  address: UnitDetails | null;
  phone: string;
  email: string;
  company_color?: string;
}

export interface _CRM_COY_3 extends Omit<CRM_COY_3, 'company_id'> {}

export interface CRM_COY_4 {
  company_id: string;
  start_time?: Date;
  end_time?: Date;
  open_from?: string;
  open_to?: string;
  days?: {
    '0': 1 | 0;
    '1': 1 | 0;
    '2': 1 | 0;
    '3': 1 | 0;
    '4': 1 | 0;
    '5': 1 | 0;
    '6': 1 | 0;
  }
}

export interface _CRM_COY_4 extends Omit<CRM_COY_4, 'company_id'> {}


export interface CRM_COY_5 {
  company_id: string;
}

export interface CRM_COY_6 {
  company_id: string;
  threshold_1: number;
  threshold_2: number;
  // open_from: string;
  // open_to: string;
  // days: {
  //   '0': 1 | 0;
  //   '1': 1 | 0;
  //   '2': 1 | 0;
  //   '3': 1 | 0;
  //   '4': 1 | 0;
  //   '5': 1 | 0;
  //   '6': 1 | 0;
  // }
}

export interface _CRM_COY_6 extends Omit<CRM_COY_6, 'company_id'> {}

export interface CRM_COY_7 {
  company_id: string;
}

export interface CRM_COY_8 {
  company_id: string;
  image: File;
}

export interface CRM_COY_9 {
  company_id: string;
  toc: string;
}

export interface CRM_COY_10 {
  company_id: string;
}

export interface notification_type_setting {
  notification_type_id: number;
  time_delta_hours: number;
  enabled: number;
}

export interface CRM_COY_11 {
  company_id: string;
  settings: notification_type_setting[]
}

export interface _CRM_COY_11 extends Omit<CRM_COY_11, 'company_id'> {}

export interface CRM_COY_12 {
  company_id: string;
}

export interface CRM_COY_13 {
  company_id: string;
}

export interface CRM_COY_14 {
  company_id: string;
}

export interface CRM_COY_15 {
  company_id: string;
  payment_method_id: number;
  credentials: {
    [key: string]: string | number;
  }
}

export interface CRM_COY_16 {
  company_id: string;
}

export interface CRM_COY_17 {
  company_id: string;
  payment_method_id: number;
  credentials: {
    [key: string]: string | number;
  }
}

export interface _CRM_COY_17 extends Omit<CRM_COY_17, 'company_id'> {}

export interface CRM_COY_19 {
  company_id: string;
}

export interface CRM_COY_20 {
  company_id: string;
}

export interface CRM_COY_21 {
  company_id: string;
  primary_color_hex: string;
  secondary_color_hex: string;
  content: {[key: string]: string | number | boolean | null};
}

export interface CRM_COY_22 {
  company_id: string;
  order_line_quantity_calculation_resolution?: number;
  company_order_number_prefix?: string;
  company_order_number_starting_number?: number;
  include_vat_in_price_business?: number;
  include_vat_in_price_private?: number;
  transfer_external_orders_to_accounting?: number;
  transfer_free_orders_to_accounting?: number;
  archive_order_when_finished?: number;
  calendar_start_hour?: number;
  calendar_end_hour?: number;
  auto_synchronise_accounting?: number;
  auto_send_invoice?: number;
  mark_external_orders_as_paid_automatically?: number;
  sms_name?: string;
  standard_property_type_id_for_unspecified_properties?: number;
  include_transport_in_duration_calculation?: number;
  geo_lock_range?: number | null;
  default_business_customers_due_date?: number;
  default_private_customers_due_date?: number;
  manual_order_confirmation_email?: number;
  split_interday_orders_in_multiple_events?: number;
  set_invoice_date_to_sent_to_payment?: number;
  auto_sync_on_send_to_payment?: number;
  show_employee_initials_only?: number;
  color_hex_unconfirmed?: string;
  color_hex_job_not_started?: string;
  color_hex_job_started?: string;
  color_hex_job_finished?: string;
  color_hex_no_order?: string;
  event_title_tags?: string[],
  customer_can_only_accept?: number;
  operate_ex_vat?: boolean;
  default_activity_id?: number | null;
  default_transport_activity_id?: number | null;
  require_manual_accounting_push?: boolean;
  auto_finish_work_orders?: boolean;
  auto_finish_all_work_orders?: boolean;
  planned_registered_ratio_warning_percentage?: number | null;
  quote_expiry_days?: number | null;
  default_fixed_salary_salary_type_id?: number | null;
  convert_tracked_time_to_working_hours?: boolean;
  default_department_id?: number | null;
  customer_cancel_work_order?: number;
  default_vacation_activity_id?: number | null;
  default_repeating_orders?: boolean;
  require_crew_work_order_confirmation?: boolean;
}

export interface _CRM_COY_22 extends Omit<CRM_COY_22, 'company_id'> {}

export interface CRM_COY_23 {
  company_id: string;
}

export interface CRM_COY_25 {
  company_id: string;
}

export interface CRM_COY_26 {
  company_id: string;
  payment_method_id: number;
  active?: number;
  clear?: number;
  private_customer_available?: number;
  business_customer_available?: number;
}

export interface _CRM_COY_26 extends Omit<CRM_COY_26, 'company_id'> {}

export interface CRM_COY_27 {
  company_id: string;
}

export interface CRM_COY_31 {
  company_id: string;
}

export interface CRM_COY_32 {
  company_id: string;
  property_type_id: number;
  livable_area?: number;
  floor?: number | null;
  number_of_floors?: number | null;
  number_of_rooms?: number;
  number_of_bathrooms?: number;
  shed_area?: number;
  enabled?: number;
}

export interface _CRM_COY_32 extends Omit<CRM_COY_32, 'company_id'> {}

export interface CRM_COY_33 {
  company_id: string;
}

export interface CRM_COY_34 extends pagination_input {
  company_id: string;
}


export interface CRM_COY_35 {
  company_id: string;
}

export interface _CRM_COY_35 {
  // Any additional parameters beyond company_id
}

export interface CRM_COY_35 {
  company_id: string;
  // Include all parameters from _CRM_COY_35
}

export interface CRM_COY_36 {
  company_id: string;
  table_name: string;
  table_config: any;
}

export interface _CRM_COY_36 {
  table_name: string;
  table_config: any;
}

export interface CRM_COY_37 {
  company_id: string;
  entry_id: number;
  table_config: any;
}

export interface _CRM_COY_37 {
  entry_id: number;
  table_config: any;
}

export interface CRM_COY_37 {
  company_id: string;
  entry_id: number;
  table_config: any;
}

export interface CRM_COY_38 {
  company_id: string;
  entry_id: number;
}



export interface CRM_PRD_0 {
  company_id: string;
  product_name: string;
  description: string | null;
  price_inc_vat: number;
  product_type_id: number;
  unit_id: number;
  vat_rate_id: number;
  icon_id: number;
  accounting_id: string | number | null;
  accounting_name: string | null;
}

export interface _CRM_PRD_0 extends Omit<CRM_PRD_0, 'company_id'> {}

export interface CRM_PRD_1 extends full_select {
  company_id: string;
  product_id?: number;
  product_type_id?: number;
  affiliate_id?: number | null;
}

export interface _CRM_PRD_1 extends Omit<CRM_PRD_1, 'company_id'> {}

export interface CRM_PRD_2 {
  company_id: string;
  product_id: number;
  product_name: string;
  description: string | null;
  price_inc_vat: number;
  vat_rate_id: number;
  icon_id: number;
  accounting_id: string | number | null;
  accounting_name: string | null;
  require_ack_on_important_information?: number;
  work_order_data?: ProductWorkOrderData | null;
}

export interface CRM_PRD_65 {
  company_id: string;
  product_id: number;
  product_name?: string;
  description?: string ;
  price_inc_vat?: number;
  vat_rate_id?: number;
  icon_id?: number;
  accounting_id?: string | number | null;
  accounting_name?: string | null;
  enable_duration_calculation?: number;
  individual_time_tracking?: number;
  work_order_data?: ProductWorkOrderData | null;
  unit_id?: number | null;
}

export interface CRM_PRD_66 {
  company_id: string;
  affiliate_id: number;
  product_id: number;
}

export interface CRM_PRD_67 {
  company_id: string;
  price_rule_id: number;
  affiliate_id: number;
  value: number;
}

export interface _CRM_PRD_67 extends Omit<CRM_PRD_67, 'company_id'>{}

export interface CRM_PRD_68 {
  company_id: string;
  affiliate_price_rule_id: number;
}

export interface _CRM_PRD_68 extends Omit<CRM_PRD_68, 'company_id'> {}

export interface _CRM_PRD_65 extends Omit<CRM_PRD_65, 'company_id'> {}

export interface _CRM_PRD_2 extends Omit<CRM_PRD_2, 'company_id'> {}

export interface CRM_PRD_3 {
  company_id: string;
  product_id: number;
}

export interface QuantityTriggerInput {
  range_from: number | null;
  range_to: number | null;
}

export interface TimeTriggerInput {
  weekdays: number[];
  start_time: string | null;
  end_time: string | null;
}

export interface CompleteTriggerInput extends TimeTriggerInput, QuantityTriggerInput {}

export interface CRM_PRD_11 {
  company_id: string;
  price_rule_name: string | null;
  price_rule_type_id: number;
  product_id: number;
  value: number | null;
  trigger: CompleteTriggerInput;
  embed: number;
}

export interface _CRM_PRD_11 extends Omit<CRM_PRD_11, 'company_id'> {}

export interface CRM_PRD_12 {
  company_id: string;
  price_rule_group_id: number;
}

export interface CRM_PRD_13 {
  company_id: string;
  price_rule_id: number;
  price_rule_name: string | null;
  value: number;
  trigger: CompleteTriggerInput;
  embed: number;
}

export interface _CRM_PRD_13 extends Omit<CRM_PRD_13, 'company_id'> {}

export interface CRM_PRD_14 {
  company_id: string;
  price_rule_id: number;
}

export interface _CRM_PRD_14 extends Omit<CRM_PRD_14, 'company_id'> {}

export interface CRM_PRD_15 {
  company_id: string;
  product_id: number;
  price_rule_group_name: string;
  trigger_type_id: number;
}

export interface CRM_PRD_16 {
  company_id: string;
  product_id: number;
}

export interface CRM_PRD_17 {
  company_id: string;
  price_rule_group_id: number;
  price_rule_group_name: string;
  trigger_type_id: number;
}

export interface CRM_PRD_18 {
  company_id: string;
  price_rule_group_id: number;
}

export interface CRM_PRD_29 {
  company_id: string;
  product_id: number;
  upsell_product_id: number;
  index: number;
}

export interface _CRM_PRD_29 extends Omit<CRM_PRD_29, 'company_id'> {}

export interface CRM_PRD_30 extends search_input, sorting_input{
  company_id: string;
  product_id: number;
}

export interface CRM_PRD_32 {
  company_id: string;
  product_id: number;
  upsell_product_id: number;
}

export interface _CRM_PRD_32 extends Omit<CRM_PRD_32, 'company_id'> {}

export interface CRM_PRD_34 {
  order_id: number;
}

export interface CRM_PRD_35 {
  company_id: string;
  product_id: number;
}

export interface CRM_PRD_36 {
  company_id: string;
  product_id: number;
}

export interface _CRM_PRD_36 extends Omit<CRM_PRD_36, 'company_id'> {}

export interface CRM_PRD_37 {
  company_id: string;
  stage_id: number;
  stage_name: string;
  billable: number;
  index: number;
  new_address: number;
  duplicatable: number;
  include_transport: number;
  billable_transport: number;
  use_in_calculation: number;
}

export interface _CRM_PRD_37 extends Omit<CRM_PRD_37, 'company_id'> {}

export interface CRM_PRD_38 {
  company_id: string;
  product_id: number;
  stage_id: number;
}

export interface _CRM_PRD_38 extends Omit<CRM_PRD_38, 'company_id'> {}


export interface CRM_PRD_45 {
  company_id: string;
  product_id: number;
  title: string;
  description: string;
  required: number;
}

export interface _CRM_PRD_45 extends Omit<CRM_PRD_45, 'company_id'> {}

export interface CRM_PRD_46 {
  company_id: string;
  entry_id: number;
  title: string;
  description: string;
  required: number;
}

export interface _CRM_PRD_46 extends Omit<CRM_PRD_46, 'company_id'> {}

export interface CRM_PRD_47 {
  company_id: string;
  entry_id: number;
}

export interface _CRM_PRD_47 extends Omit<CRM_PRD_47, 'company_id'> {}

export interface CRM_PRD_48 {
  company_id: string;
  product_id: number;
}

export interface CRM_PRD_49 {
  company_id: string;
}


export interface CRM_PRD_50 {
  company_id: string;
  product_id: number;
  specification_text: string;
  specification_description: string;
  radio_selection: number;
  index: number;
  required: number;
  choices: {
    choice_name: string;
    choice_ghost_text: string | null;
    index: number;
  }[];
}

export interface _CRM_PRD_50 extends Omit<CRM_PRD_50, 'company_id'> {}

export interface CRM_PRD_51 {
  company_id: string;
  specification_id: number;
  specification_text: string;
  radio_selection: number;
  index: number;
  required: number;
  choices?: {
    choice_id: number | null;
    choice_name: string;
    choice_ghost_text: string | null;
    index: number;
  }[];
}

export interface _CRM_PRD_51 extends Omit<CRM_PRD_51, 'company_id'> {}

export interface CRM_PRD_52 {
  company_id: string;
  specification_id: number;
}

export interface CRM_PRD_53 {
  company_id: string;
  product_id: number;
}

export interface CRM_PRD_54 {
  company_id: string;
  specification_id: number;
  choice_name: string;
  choice_ghost_text: string | null;
  index: number;
}

export interface _CRM_PRD_54 extends Omit<CRM_PRD_54, 'company_id'> {}

export interface CRM_PRD_55 {
  company_id: string;
  choice_id: number;
  choice_name: string;
  choice_ghost_text: string | null;
  index: number;
}

export interface _CRM_PRD_55 extends Omit<CRM_PRD_55, 'company_id'> {}

export interface CRM_PRD_56 {
  company_id: string;
  choice_id: number;
}

export interface CRM_PRD_57 extends full_select {
  company_id: string;
  product_id?: number;
  product_type_id?: number;
  search_string?: string | null;
  search_string_columns?: string[];
  affiliate_id?: number | null;
}

export interface _CRM_PRD_57 extends Omit<CRM_PRD_57, 'company_id'> {}

export interface CRM_PRD_59 {
  company_id: string;
}

export interface CRM_PRD_60 extends search_input, sorting_input {
  company_id: string;
  product_id: number;
}

export interface CRM_PRD_61 {
  company_id: string;
  product_id: number;
  sku: string;
  weight: number;
  stock_quantity: number;
  out_of_stock_availability: number;
}

export interface _CRM_PRD_61 extends Omit<CRM_PRD_61, 'company_id'> {}

export interface CRM_PRD_62 {
  company_id: string;
  product_id: number;
}

export interface CRM_PRD_63 {
  company_id: string;
}

export interface CRM_PRD_64 {
  company_id: string;
  product_id: number;
  price_rule_ids: number[];
  quantity: number | null;
  start_time: Date | null;
  end_time: Date | null;
}

export interface CRM_PRD_69 {
  company_id: string;
  affiliate_id: number;
  product_id: number;
  price_inc_vat: number;
}

export interface _CRM_PRD_69 extends Omit<CRM_PRD_69, 'company_id'> {}

export interface CRM_PRD_70 {
  company_id: string;
  affiliate_id?: number | null;
}

export interface CRM_PRD_71 {
  company_id: string;
  product_package_name: string;
  description: string;
  icon_id: number;
}

export interface CRM_PRD_72 {
  company_id: string;
  product_package_id: number;
}

export interface CRM_PRD_73 {
  company_id: string;
  product_package_id: number;
  product_package_name: string;
  description: string;
  icon_id: number;
}

export interface CRM_PRD_74 {
  company_id: string;
  product_package_id: number;
  product_id: number;
  default_quantity: number;
  main_product: number; // 0 or 1
}

export interface CRM_PRD_75 {
  company_id: string;
  product_package_id: number;
  product_id: number;
}

export interface CRM_PRD_76 {
  company_id: string;
  product_package_id: number;
  product_id: number;
  default_quantity: number;
  main_product: number; // 0 or 1
}

export interface CRM_PRD_77 {
  company_id: string;
  product_id: number;
  quantity: number | null;
  price_rule_ids: number[];
  calculate_duration: number;
  preview_price_rule_ids: number[];
  start_time: Date | null;
  end_time: Date | null;
  use_stages: number;
  stages: {stage_id: number, address: {[key: string]: any}}[]
  order_lines?: {product_id: number | null, price_inc_vat: number; quantity: number, custom_product_name: string | null, vat_rate_id: number | null}[]
  order_response?: number;
  affiliate_id?: number | null;
  partner_contact_id?: number | null;
  override_quantity?: number | null;
  crew_size?: number | null;
  manual_hours?: number | null;
}

export interface _CRM_PRD_77 extends Omit<CRM_PRD_77, 'company_id'> {}

export interface CRM_PRD_78 {
  company_id: string;
  search_term?: string;
}

export interface CRM_PRD_82 {
  company_id: string;
  product_id: number;
}

export interface CRM_PRD_83 {
  company_id: string;
  product_id: number;
  calculation_type_id?: number | null;
  value_source_id?: number | null;
  factor?: number | null;
  default_value?: number | null;
  prefixed_calculation_type_id?: number | null;
  index?: number | null;
}

export interface _CRM_PRD_83 extends Omit<CRM_PRD_83, 'company_id'> {}

export interface CRM_PRD_84 {
  company_id: string;
  product_id: number;
  calculation_id: number;
  calculation_type_id?: number;
  value_source_id?: number | null;
  factor?: number | null;
  default_value?: number | null;
  prefixed_calculation_type_id?: number | null;
  index?: number | null;
}

export interface _CRM_PRD_84 extends Omit<CRM_PRD_84, 'company_id'> {}

export interface CRM_PRD_85 {
  company_id: string;
  product_id: number;
  calculation_id: number;
}

export interface _CRM_PRD_85 extends Omit<CRM_PRD_85, 'company_id'> {}

export interface CRM_PRD_86 {
  company_id: string;
  product_id: number;
  addresses: UnitDetails[];
  order_data: {
    crew_size: number | null;
  }
}

export interface _CRM_PRD_86 extends Omit<CRM_PRD_86, 'company_id'> {}

export interface CRM_PRD_87 {
  company_id: string;
  affiliate_id: number;
}

export interface CRM_PRD_88 {
  company_id: string;
  product_id: number;
  affiliate_id: number;
}

export interface CRM_PRD_90 {
  company_id: string;
}

export interface CRM_EMB_14 {
  company_id: string;
  embed_product_id: number;
  schedule_repeat_type_id: number;
  weekdays: number[] | null | undefined;
  every: number;
  date: number | null;
  nth_weekday: number | null;
  schedule_template_name: string;
}

export interface _CRM_EMB_14 extends Omit<CRM_EMB_14, 'company_id'> {}

export interface CRM_EMB_15 {
  company_id: string;
  schedule_template_id: number;
}

export interface CRM_EMB_16 {
  company_id: string;
  embed_product_id: number;
}

export interface CRM_EMB_17 {
  company_id: string;
  schedule_template_id: number | undefined;
  schedule_repeat_type_id: number | undefined;
  weekdays: number[] | null | undefined;
  every: number | undefined;
  date: number | null| undefined;
  nth_weekday: number | null | undefined;
  schedule_template_name: string | undefined;
}

export interface _CRM_EMB_17 extends Omit<CRM_EMB_17, 'company_id'> {}

export interface CRM_PCG_0 {
  company_id: string;
  product_category_name: string;
  icon_id?: number | null;
  accounting_id?: string | number | null;
}

export interface _CRM_PCG_0 extends Omit<CRM_PCG_0, 'company_id'> {}

export interface CRM_PCG_1 extends pagination_input, sorting_input, search_input {
  company_id: string;
}

export interface _CRM_PCG_1 extends Omit<CRM_PCG_1, 'company_id'> {}

export interface CRM_PCG_2 {
  company_id: string;
  product_category_id: number;
}

export interface CRM_PCG_3 {
  company_id: string;
  product_category_id: number;
  product_category_name?: string;
  icon_id?: number | null;
  accounting_id?: string | number | null;
}

export interface _CRM_PCG_3 extends Omit<CRM_PCG_3, 'company_id'> {}

export interface CRM_STG_2 {
  company_id: string;
  order_line_stage_id: number;
  latitude: number;
  longitude: number;
  transport_started_at: Date;
  transport_finished_at: Date;
  started_at: Date;
  finished_at: Date;
  tasks: {order_line_task_id: number, status: number}[];
}

export interface CRM_STG_4 {
  company_id: string;
  order_line_stage_id: number;
}

export interface CRM_STG_5 {
  company_id: string;
  order_line_stage_id: number;
  incident_type_id: number;
  description: string;
  internal: number;
}

export interface CRM_STG_6 {
  company_id: string;
  order_id: number;
}

export interface CRM_STG_7 {
  company_id: string;
  order_line_task_id: number;
  status: number;
}

export interface CRM_STG_10 {
  company_id: string;
  incident_id: number;
  image: File;
}

export interface CRM_STG_11 {
  company_id: string;
  incident_id: number;
}

export interface CRM_STG_12 {
  company_id: string;
  order_line_stage_choice_id: number;
  value: number;
  input: string
}

export interface CRM_STG_15 {
  order_line_stage_choice_id: number;
  value: number;
  input: string
}

export interface CRM_STG_16 {
  company_id: string;
  order_line_stage_id: number;
  pause: number;
}

export interface CRM_PAY_0 {
  order_id: number;
  payment_method_id: number;
}

export interface CRM_PAY_2 {
  company_id: string;
  payment_id: number;
  refund_reason: string;
  refund_notification: number;
  order_lines: {order_line_id: number, quantity: number}[];
  full_refund: number;
}

export interface _CRM_PAY_2 extends Omit<CRM_PAY_2, 'company_id'> {}

export interface CRM_PAY_6 {
  company_id: string;
}

export interface CRM_PAY_7 {
  company_id: string;
  payment_id: number;
}

export interface _CRM_PAY_7 extends Omit<CRM_PAY_7, 'company_id'> {}

export interface CRM_PAY_8 {
  order_id: number;
}

export interface CRM_PAY_13 {
  company_id: string;
  order_id: number;
  payment_method_id: number;
}

export interface CRM_PAY_15 {
  company_id: string;
  payment_id: number;
  send_type_id: number;
}

export interface _CRM_PAY_15 extends Omit<CRM_PAY_15, 'company_id'> {}

export interface CRM_PAY_16 {
  company_id: string;
}

export interface CRM_PAY_17 {
  payment_method_id?: number;
  payment_id?: number;
  company_id?: string;
  as_admin: true,
}

export interface _CRM_PAY_17 extends Omit<CRM_PAY_17, 'company_id'> {}

export interface CRM_PAY_23 {
  company_id: string;
  payment_id: number;
}

export interface CRM_PAY_24 {
  company_id: string;
  payment_id: number;
}

export interface CRM_PAY_25 {
  company_id: string;
  payment_id: number;
}

export interface CRM_PAY_26 {
  order_id: number | null;
  company_id: string;
  order_line_ids: number[];
  payment_method_id?: number;
  invoice_send_type_id?: number;
  invoice_due_date_days?: number;
  invoice_email?: string;
  invoice_reference_text?: string | null;
  payment_name?: string | null;
  comment?: string | null;
  send_to_payment?: boolean;
  work_order_ids?: number[];
  template?: boolean;
  schedule?: ScheduleInput | null;
  update_upcoming_work_orders?: boolean;
  payment_reminders_disabled?: boolean;
  is_parent_consolidated_invoice?: boolean;
  payment_recipient_id?: number;
  consolidated_invoice_payment_id?: number;
  disable_customer_portal?: boolean;
  auto_send?: boolean;
  schedule_option_id?: number | null;
}

export interface _CRM_PAY_26 extends Omit<CRM_PAY_26, 'company_id'> {}

export interface CRM_PAY_27 {
  company_id: string;
  payment_id: number;
  payment_method_id?: number;
  invoice_send_type_id?: number;
  invoice_due_date_days?: number;
  invoice_email?: string;
  invoice_reference_text?: string;
  payment_name?: string;
  comment?: string;
  added_order_line_ids?: number[];
  removed_order_line_ids?: number[];
  payment_reminders_disabled?: boolean;
  custom_payment_reminder_1_hours_delta?: number | null;
  custom_payment_reminder_2_hours_delta?: number| null;
  work_order_ids?: number[];
  schedule?: PatchScheduleInput | null;
  consolidated_invoice_payment_id?: number;
  project_id?: number | null;
  department_id?: number | null;
  disable_customer_portal?: boolean;
  schedule_option_id?: number | null;
  auto_send?: boolean;
  send_to_payment?: boolean;
}

export interface _CRM_PAY_27 extends Omit<CRM_PAY_27, 'company_id'> {}

export interface CRM_PAY_28 {
  company_id: string;
  payment_id: number;
}

export interface CRM_PAY_29 {
  company_id: string;
  payment_id: number;
}

export interface CRM_PAY_30 {
  company_id: string;
  order_id: number;
  template?: boolean;
  refund?: boolean;
}

export interface CRM_PAY_31 {
  company_id: string;
  payment_id: number;
}

export interface CRM_PAY_32 {
  company_id: string;
  payment_id: number;
}

export interface CRM_PAY_33 {
  company_id: string;
  payment_id: number;
}

export interface CRM_PAY_34 {
  company_id: string;
  payment_id: number;
  phone: string | null;
}

export interface _CRM_PAY_34 extends Omit<CRM_PAY_34, 'company_id'> {}

export interface CRM_PAY_36 extends ScheduleInput {
  company_id: string;
}

export interface _CRM_PAY_36 extends Omit<CRM_PAY_36, 'company_id'> {}

export interface CRM_PAY_37 {
  company_id: string;
  payment_schedule_id: number;
}

export interface CRM_PAY_38 {
  work_order_id: number;
  company_id: string;
  payment_method_id?: number;
  invoice_send_type_id?: number;
  invoice_due_date_days?: number;
  invoice_email?: string;
  invoice_reference_text?: string;
  payment_name?: string;
  comment?: string;
  send_to_payment?: boolean;
}

export interface _CRM_PAY_38 extends Omit<CRM_PAY_38, 'company_id'> {}

export interface CRM_PAY_41 extends pagination_input, sorting_input {
  company_id: string;
  customer_name?: string;
  customer_id?: number;
  payment_status_ids?: number[];
  product_ids?: number[];
  payment_method_ids?: number[];
  payment_sent_at_from?: Date;
  payment_sent_at_to?: Date;
  created_at_from?: Date;
  created_at_to?: Date;
  captured_at_from?: Date;
  captured_at_to?: Date;
  refunds_only?: boolean;
  include_consolidated_invoice_parents?: boolean;
  parent_payment_id?: number;
}

export interface _CRM_PAY_41 extends Omit<CRM_PAY_41, 'company_id'> {}

export interface CRM_PAY_42 {
  company_id: string;
  payment_ids: number[];
  payment_recipient_id: number;
}

export interface _CRM_PAY_42 extends Omit<CRM_PAY_42, 'company_id'> {}

export interface CRM_PAY_43 {
  company_id: string;
  payment_id: number;
  email?: string;
}

export interface _CRM_PAY_43 extends Omit<CRM_PAY_43, 'company_id'> {}

export interface CRM_PAY_44 extends CRM_PAY_41 {}

export interface _CRM_PAY_44 extends Omit<CRM_PAY_44, 'company_id'> {}

export interface CRM_PAY_46 {
  company_id: string;
  payment_id: number;
}

export interface CRM_CUS_0 extends search_input {
  company_id: string;
}

export interface CRM_CUS_1 {
  first_name: string;
  last_name: string;
  phone: string | null;
  email: string | null;
  company_id: string;
  accounting_id?: number | string | null;
  sub_ledger_account_id?: number | null;
}

export interface _CRM_CUS_1 extends Omit<CRM_CUS_1, 'company_id'> {}

export interface CRM_CUS_4 {
  company_id: string;
  customer_name: string;
  organisation_number: string;
  phone: string;
  email: string;
  invoice_email: string;
  address: UnitDetails;
  accounting_id: string;
  invoice_send_type_id?: number;
  invoice_due_date_days?: number;
  invitee?: {
    email: string;
    first_name: string;
    last_name: string;
    phone: string;
  }
}

export interface _CRM_CUS_4 extends Omit<CRM_CUS_4, 'company_id'> {}

export interface CRM_CUS_5 {
  company_id: string;
  customer_id: number;
  customer_name?: string;
  organisation_number?: string;
  phone?: string;
  email?: string;
  address?: UnitDetails;
  invoice_address?: UnitDetails | null;
  postal_code?: string;
  city?: string;
  active?: number;
  accounting_id?: string;
  invoice_email?: string;
  invoice_send_type_id?: number;
  invoice_due_date_days?: number;
}

export interface _CRM_CUS_5 extends Omit<CRM_CUS_5, 'company_id'> {}

export interface CRM_CUS_12 {
  company_id: string;
  affiliate_id: number;
  first_name?: string;
  last_name?: string;
  phone?: string;
  email?: string;
  active?: number;
  accounting_id?: string | null;
  address?: UnitDetails | null;
  invoice_address?: UnitDetails | null;
  invoice_due_date_days?: number;
  sub_ledger_account_id?: number | null;
}

export interface _CRM_CUS_12 extends Omit<CRM_CUS_12, 'company_id'> {}

export interface CRM_CUS_6 extends pagination_input, sorting_input, search_input {
  company_id: string;
  active?: number | null;
}

export interface _CRM_CUS_6 extends Omit<CRM_CUS_6, 'company_id'> {}

export interface CRM_CUS_7 {
  company_id: string;
  customer_id: number;
}

export interface CRM_CUS_8 {
  company_id: string;
  customer_id: number;
}

export interface _CRM_CUS_10 extends Omit<CRM_CUS_10, 'company_id'> {}

export interface CRM_CUS_10 extends pagination_input, sorting_input, search_input {
  company_id: string;
  active?: number | null;
  customer_id?: number | null;
}

export interface CRM_CUS_14 {
  company_id: string;
  affiliate_id: number;
}

export interface CRM_CUS_15 extends CRM_CUS_1 {}

export interface _CRM_CUS_15 extends Omit<CRM_CUS_15, 'company_id'> {}

export interface CRM_CUS_16 {
  company_id: string;
}

// export interface CRM_CUS_17 {
//   company_id: string;
//   private_customer_id?: number;
//   business_customer_id?: number;
//   accounting_id: string | null;
// }

export interface CRM_CUS_18 {
  company_id: string;
  customer_id: number;
}

export interface CRM_CUS_19 {
  company_id: string;
  user_id: string;

}

export interface CRM_REP_0 {
  company_id: string;
  date_from: string | undefined;
  date_to: string | undefined;
}

export interface CRM_REP_1 {
  company_id: string;
  date_from: string;
  date_to: string;
}

export interface CRM_ADR_1 {
  address: string;
}

export interface CRM_ADR_3 {
  company_id: string;
  internal_id: string;
}

export interface _CRM_ADD_3 extends Omit<CRM_ADR_3, 'company_id'> {}

export interface CRM_ADR_4 {
  company_id: string;
  internal_id: string;
  section_id: string;
}

export interface CRM_ADR_5 {
  street: string;
  street_number: number;
  postal_code: string;
  street_letter?: string | null
}

export interface CRM_ADR_6 {
  address: string,
  skip_google: boolean
}

export interface CRM_ADR_7 {
  unit_id: string;
}

export interface CRM_ADR_8 extends UnitDetails {
  company_id: string;
}

export interface CRM_ADR_9 {
  address_id: number;
}

export interface CRM_ADR_10 {
  company_id: string;
  address: AddressInput
  access_object_type: string;
  object_id: number | string;
}

export interface _CRM_ADR_10 extends Omit<CRM_ADR_10, 'company_id'> {}


export interface CRM_ADR_12 {
  company_id: string;
  affiliate_id: number;
}

export interface CRM_PRT_0 {
  company_id: string;
  partner_company_id: string;
  invoice_send_type_id?: number;
  invoice_due_date_days?: number;
  preferred_payment_method_id?: number;
  hide_payment_data?: number;
}

export interface _CRM_PRT_0 extends Omit<CRM_PRT_0, 'company_id'> {}

export interface CRM_PRT_1 {
  company_id: string;
  company_name: string;
  organisation_number: string;
  address: string;
  postal_code: string;
  city: string;
  phone: string;
  email: string;
}

export interface _CRM_PRT_1 extends Omit<CRM_PRT_1, 'company_id'> {}

export interface CRM_PRT_2 extends full_select {
  company_id: string;
  partner_invitation_status?: number;
}

export interface _CRM_PRT_2 extends Omit<CRM_PRT_2, 'company_id'> {}

export interface CRM_PRT_3  extends full_select {
  company_id: string;
  paper_company_id: string;
}

export interface _CRM_PRT_3 extends Omit<CRM_PRT_3, 'company_id'> {}

export interface PartnerContactAttributeInput {
  attribute_id: number;
  attribute_value: number | null;
  attribute_text: string | null;
}

export interface TempPartnerEmployeeAttributeInput extends PartnerContactAttributeInput {
  attribute_name: string;
  attribute_selection_type_id: number;
}

export interface CRM_PRT_4 {
  company_id: string;
  paper_company_id: string;
  first_name: string;
  last_name: string;
  phone: string;
  email: string;
  role_description: string;
  attributes: PartnerContactAttributeInput[];
}

export interface _CRM_PRT_4 extends Omit<CRM_PRT_4, 'company_id'> {}

export interface CRM_PRT_5 {
  company_id: string;
  paper_company_id: string;
  paper_employee_id: string;
}

export interface _CRM_PRT_5 extends Omit<CRM_PRT_5, 'company_id'> {}

export interface CRM_PRT_6 {
  company_id: string;
  paper_company_id: string;
  paper_employee_id: string;
  first_name: string;
  last_name: string;
  phone: string;
  email: string;
  role_description: string;
  attributes: PartnerContactAttributeInput[];
}

export interface _CRM_PRT_6 extends Omit<CRM_PRT_6, 'company_id'> {}

export interface CRM_PRT_7 {
  company_id: string;
  paper_company_id: string;
  company_name: string;
  organisation_number: string;
  address: string;
  postal_code: string;
  city: string;
  phone: string;
  email: string;
  hide_payment_data: number
}

export interface _CRM_PRT_7 extends Omit<CRM_PRT_7, 'company_id'> {}

export interface CRM_PRT_8 extends full_select {
  company_id: string;
}

export interface CRM_PRT_9 {
  company_id: string;
  partner_id: number
}

export interface CRM_PRT_10 {
  company_id: string;
}

export interface CRM_PRT_11 {
  company_id: string;
  attribute_name: string;
  attribute_selection_type_id: number;
}

export interface _CRM_PRT_11 extends Omit<CRM_PRT_11, 'company_id'> {}

export interface CRM_PRT_12 {
  company_id: string;
  attribute_id: number;
  attribute_name: string;
}

export interface _CRM_PRT_12 extends Omit<CRM_PRT_12, 'company_id'> {}

export interface CRM_PRT_13 {
  company_id: string;
  attribute_id: number;
}

export interface CRM_PRT_15 {
  company_id: string;
  search_term?: string | null;
}

export interface CRM_PRT_16 {
  company_id: string;
  partner_id: number;
}

export interface CRM_PRT_17 {
  company_id: string;
  address: UnitDetails;
  company_name: string;
  email: string;
  organisation_number: string;
  phone: string;
  invoice_send_type_id?: number;
  invoice_due_date_days?: number;
  preferred_payment_method_id?: number;
  hide_payment_data?: number;
  invoice_email?: string;
}

export interface _CRM_PRT_17 extends Omit<CRM_PRT_17, 'company_id'> {}

export interface CRM_PRT_18 {
  company_id: string;
  partner_id: number;
  invoice_send_type_id?: number;
  invoice_due_date_days?: number;
  preferred_payment_method_id?: number;
  hide_payment_data?: number;
  phone?: string;
  email?: string;
  invoice_email?: string;
}

export interface _CRM_PRT_18 extends Omit<CRM_PRT_18, 'company_id'> {}

export interface CRM_PRT_19 {
  company_id: string;
  partner_contact_id: number;
  attributes: PartnerContactAttributeInput[];
}

export interface CRM_PRT_20 {
  company_id: string;
  partner_id: number;
  company_name?: string;
  phone?: string;
  email?: string;
  address?: UnitDetails;
}

export interface _CRM_PRT_20 extends Omit<CRM_PRT_20, 'company_id'> {}

export interface CRM_PRT_21 {
  company_id: string;
  partner_id: number;
  first_name?: string;
  last_name?: string;
  phone?: string;
  email?: string;
  user_id?: string;
  role_description?: string;
  attributes: PartnerContactAttributeInput[];
}

export interface _CRM_PRT_21 extends Omit<CRM_PRT_21, 'company_id'> {}

export interface CRM_PRT_22 {
  company_id: string;
  partner_contact_id: number;
}

export interface CRM_PRT_23 {
  company_id: string;
  partner_contact_id: number;
  role_description?: string;
  attributes?: PartnerContactAttributeInput[];
}

export interface _CRM_PRT_23 extends Omit<CRM_PRT_23, 'company_id'> {}

export interface CRM_INT_7 {
  company_id: string;
  employee_token: string;
  test_connection_only?: number;
}

export interface _CRM_INT_7 extends Omit<CRM_INT_7, 'company_id'> {}

export interface CRM_INT_8 {
  company_id: string;
}

export interface CRM_INT_9 {
  company_id: string;
  accounts: {entry_id: number; accounting_account_id: string;}[]
}

export interface _CRM_INT_9 extends Omit<CRM_INT_9, 'company_id'> {}

export interface CRM_INT_10 {
  company_id: string;
  search_term: string;
}

export interface _CRM_INT_10 extends Omit<CRM_INT_10, 'company_id'> {}

export interface CRM_INT_11 {
  company_id: string;
  start_date: Date;
}

export interface CRM_INT_12 {
  company_id: string;
}

export interface CRM_INT_13 {
  company_id: string;
  payment_methods: {payment_method_id: number; accounting_payment_method_id: string; accounting_payment_method_name: string}[]
}

export interface CRM_INT_14 {
  company_id: string;
}

export interface _CRM_INT_13 extends Omit<CRM_INT_13, 'company_id'> {}

export interface _CRM_INT_11 extends Omit<CRM_INT_11, 'company_id'> {}

export interface CRM_INT_15 {
  company_id: string;
  integration_application_type_id?: number;
}

export interface CRM_INT_16 {
  company_id: string;
  sales_accounts_only?: number;
  receivable_accounts_only?: number;
}

export interface CRM_INT_17 {
  company_id: string;
  client_key: string;
}

export interface CRM_INT_18 {
  company_id: string;
  accounts: {account_id: number; accounting_account_id: string;}[]
  payment_methods: {payment_method_id: number; accounting_payment_method_id: string; accounting_payment_method_name: string}[]
}

export interface _CRM_INT_18 extends Omit<CRM_INT_18, 'company_id'> {}

export interface CRM_INT_19 {
  company_id: string;
  code: string;
  redirect_uri: string;
}


export interface _CRM_INT_19 extends Omit<CRM_INT_19, 'company_id'> {}

export interface CRM_INT_20 {
  company_id: string;
  fiken_account_filter?: string | null;
}

export interface CRM_INT_21 {
  company_id: string;
  payment_methods: {payment_method_id: number; accounting_payment_method_id: string;}[]
}

export interface _CRM_INT_21 extends Omit<CRM_INT_21, 'company_id'> {}

export interface CRM_INT_22 {
  company_id: string;
  clearhaus_id: string;
}

export interface CRM_INT_29 {
  company_id: string;
  search_term?: string;
}

export interface CRM_INT_30 {
  company_id: string;
  search_term?: string;
}

export interface CRM_INT_31 {
  company_id: string;
  search_term?: string | null;
}

export interface CRM_INT_32 {
  company_id: string;
  search_term?: string | null;
}

export interface CRM_INT_33 {
  company_id: string;
}


export interface EventChecklistItemInput {
  item_id: number | null;
  item_name: string;
  item_status: number;
}

export interface CRM_EVT_0 {
  company_id: string;
  event_type_id: number;
  execution_at: Date;
  execution_to: Date;
  event_name?: string | null;
  event_description?: string | null;
  order_id?: number | null;
  address?: UnitDetails | null;
  checklist_items?: EventChecklistItemInput[] | null;
  user_ids?: string[];
  resource_ids?: number[];
  all_day?: number;
  approved: 1;
}

export interface _CRM_EVT_0 extends Omit<CRM_EVT_0, 'company_id'> {}

export interface CRM_EVT_1 extends date_input, pagination_input {
  company_id: string;
  order_status_ids?: number[];
  payment_status_ids?: number[];
  contactor_id?: number | null;
  user_ids?: string[];
  work_order_ids?: number[] | null;
  event_type_ids?: number[];
  resource_ids?: number[];
  order_ids?: number[] | null;
}

export interface _CRM_EVT_1 extends Omit<CRM_EVT_1, 'company_id'> {}

export interface CRM_EVT_2 {
  company_id: string;
  event_id: number;
  event_type_id?: number;
  execution_at?: Date;
  execution_to?: Date;
  event_name?: string | null;
  event_description?: string | null;
  address?: UnitDetails | null | undefined;
  approved?: 1;
  user_ids?: string[];
  checklist_items?: EventChecklistItemInput[] | null;
  resource_ids?: number[];
  all_day?: number;
}

export interface _CRM_EVT_2 extends Omit<CRM_EVT_2, 'company_id'> {}

export interface CRM_EVT_3 {
  company_id: string;
  event_id: number;
}

export interface CRM_EVT_5 {
  company_id: string;
  event_id: number;
  note_text: string;
}

export interface _CRM_EVT_5 extends Omit<CRM_EVT_5, 'company_id'> {}

export interface CRM_EVT_6 {
  company_id: string;
  event_id: number;
  note_id: number;
  note_text: string;
}

export interface _CRM_EVT_6 extends Omit<CRM_EVT_6, 'company_id'> {}

export interface CRM_EVT_7 {
  company_id: string;
  event_id: number;
  note_id: number;
}

export interface _CRM_EVT_7 extends Omit<CRM_EVT_7, 'company_id'> {}

export interface CRM_EVT_8 {
  company_id: string;
  event_id: number;
  user_id: string;
}

export interface _CRM_EVT_8 extends Omit<CRM_EVT_8, 'company_id'> {}

export interface CRM_EVT_9 {
  company_id: string;
  event_id: number;
  user_id: string;
}

export interface _CRM_EVT_9 extends Omit<CRM_EVT_9, 'company_id'> {}

export interface CRM_EVT_12 {
  company_id: string;
  datetime_from: Date;
  datetime_to: Date;
  user_ids: string[];
  resource_ids: number[];
  excluded_event_ids: number[];
}

export interface _CRM_EVT_12 extends Omit<CRM_EVT_12, 'company_id'> {}

export interface CRM_EVT_17 {
  company_id: string;
  order_number?: string;
  product_name?: string;
  customer_name?: string;
  customer_phone?: string;
  address?: string;
  comment?: string;
}

export interface _CRM_EVT_17 extends Omit<CRM_EVT_17, 'company_id'> {}

export interface CRM_EVT_20 {
  company_id: string;
  order_id: number;
}


export interface CRM_TTR_0 {
  company_id: string;
  user_id: string;
  started_at: Date;
  stopped_at: Date;
  description: string;
  work_order_id?: number | null;
  activity_id: number;
  comment: string | null;
  use_in_salary: boolean;
  project_id: number | null;
  department_id: number | null;
}

export interface _CRM_TTR_0 extends Omit<CRM_TTR_0, 'company_id'> {}

export interface CRM_TTR_1 extends date_input {
  company_id: string;
  exclude_non_salary?: boolean
  exclude_auto_registered?: boolean;
  user_id?: string;
}

export interface CRM_TTR_2 {
  company_id: string;
  entry_id: number;
  started_at?: Date;
  stopped_at?: Date;
  description?: string;
  activity_id?: number;
  work_order_id?: number | null;
  use_in_salary?: boolean;
  comment?: string | null;
  project_id?: number | null;
  department_id?: number | null;
}

export interface _CRM_TTR_2 extends Omit<CRM_TTR_2, 'company_id'> {}

export interface CRM_TTR_3 {
  company_id: string;
  entry_id: number;
}

export interface CRM_TTR_4 {
  company_id: string;
  started_at: Date;
  stopped_at: Date;
  description: string;
  latitude?: number;
  longitude?: number;
}

export interface CRM_TTR_5 {
  company_id: string;
  entry_id: number;
}

export interface CRM_TTR_6 {
  company_id: string;
  entry_id: number;
  started_at?: Date;
  stopped_at?: Date;
  description?: string;
}

export interface CRM_TTR_7 extends date_input {
  company_id: string;
}

export interface CRM_TTR_8 extends date_input {
  company_id: string;
}

export interface CRM_TTR_8 {
  company_id: string;
  entry_id: number;
}

export interface CRM_TTR_9 extends date_input {
  company_id: string;
}

export interface CRM_TTR_10 {
  company_id: string;
  entry_ids: number[];
}

export interface _CRM_TTR_10 extends Omit<CRM_TTR_10, 'company_id'> {}

export interface CRM_TTR_11 {
  company_id: string;
}

export interface CRM_TTR_12 {
  company_id: string;
}

export interface CRM_TTR_13 {
  company_id: string;
  salary_rule_type_id: number;
  salary_type_id: number;
  weekdays?: number[];
  start_time?: string | null;
  end_time?: string | null;
  num_hours?: number;
  active_from?: Date | null;
  active_to?: Date | null;
  description?: string | null;
}

export interface _CRM_TTR_13 extends Omit<CRM_TTR_13, 'company_id'> {}

export interface CRM_TTR_14 {
  company_id: string;
  salary_rule_id: number;
  salary_rule_type_id?: number;
  salary_type_id?: number;
  weekdays?: number[];
  start_time?: string | null;
  end_time?: string | null;
  num_hours?: number;
  active_from?: Date | null;
  active_to?: Date | null;
  description?: string | null;
}

export interface _CRM_TTR_14 extends Omit<CRM_TTR_14, 'company_id'> {}

export interface CRM_TTR_15 {
  company_id: string;
  salary_rule_id: number;
}

export interface _CRM_TTR_15 extends Omit<CRM_TTR_15, 'company_id'> {}

export interface CRM_TTR_16 {
  company_id: string;
}

export interface CRM_TTR_17 {
  company_id: string;
  salary_type_name: string;
  value: number | null;
  accounting_id?: string | null;
  accounting_name?: string | null;
  index?: number;
  calculation_type_id: number;
}

export interface _CRM_TTR_17 extends Omit<CRM_TTR_17, 'company_id'> {}

export interface CRM_TTR_18 {
  company_id: string;
  salary_type_id: number;
  salary_type_name?: string;
  value?: number | null;
  accounting_id?: string | null;
  accounting_name?: string | null;
  index?: number;
  calculation_type_id?: number;
}

export interface _CRM_TTR_18 extends Omit<CRM_TTR_18, 'company_id'> {}

export interface CRM_TTR_19 {
  company_id: string;
  salary_type_id: number;
}

export interface _CRM_TTR_19 extends Omit<CRM_TTR_19, 'company_id'> {}

export interface CRM_TTR_20 {
  company_id: string;
}

export interface CRM_TTR_21 {
  company_id: string;
}

export interface CRM_TTR_22 {
  company_id: string;
  month: number;
  year: number;
  user_id: string;
}


export interface _CRM_TTR_22 extends Omit<CRM_TTR_22, 'company_id'> {}


export interface CRM_TTR_23 {
  company_id: string;
  month: number;
  year: number;
  user_id: string;
  status_id?: number;
}

export interface _CRM_TTR_23 extends Omit<CRM_TTR_23, 'company_id'> {}

export interface CRM_TTR_24 {
  company_id: string;
  month: number;
  year: number;
  user_id: string;
}

export interface _CRM_TTR_24 extends Omit<CRM_TTR_24, 'company_id'> {}

export interface CRM_TTR_25 {
  company_id: string;
  month: number;
  year: number;
  day: number;
  user_id: string;
  activity_id?: number | null;
  salary_type_id?: number | null;
  quantity: number;
  comment: string;
  project_id?: number | null;
  department_id?: number | null;
}

export interface _CRM_TTR_25 extends Omit<CRM_TTR_25, 'company_id'> {}

export interface CRM_TTR_26 {
  company_id: string;
  entry_id: number;
  activity_id?: number | null;
  salary_type_id?: number | null;
  quantity?: number;
  comment?: string;
  project_id?: number | null;
  department_id?: number | null;
}

export interface _CRM_TTR_26 extends Omit<CRM_TTR_26, 'company_id'> {}

export interface CRM_TTR_27 {
  company_id: string;
  entry_id: number;
}

export interface CRM_TTR_28 {
  company_id: string;
  year: number;
  month: number;
}

export interface _CRM_TTR_28 extends Omit<CRM_TTR_28, 'company_id'> {}

export interface CRM_TTR_29 {
  company_id: string;
  entry_id: number;
}

export interface CRM_TTR_30 {
  company_id: string;
}

export interface CRM_TTR_31 {
  company_id: string;
  activity_name: string;
  activity_type_id: number;
  default_salary_type_id: number | null;
  accounting_id?: string | null;
  accounting_name?: string | null;
  index?: number;
  color_hex?: string | null;
}

export interface _CRM_TTR_31 extends Omit<CRM_TTR_31, 'company_id'> {}

export interface CRM_TTR_32 {
  company_id: string;
  activity_id: number;
  activity_name?: string;
  activity_type_id?: number;
  default_salary_type_id?: number | null;
  accounting_id?: string | null;
  accounting_name?: string | null;
  index?: number;
  color_hex?: string | null;
}

export interface _CRM_TTR_32 extends Omit<CRM_TTR_32, 'company_id'> {}

export interface CRM_TTR_33 {
  company_id: string;
  activity_id: number;
}

export interface CRM_TTR_34 {
  company_id: string;
  salary_approval_id: number;
}

export interface CRM_TTR_35 {
  company_id: string;
  year: number;
  month: number;
  include_monthly_salaries: boolean;
}

export interface _CRM_TTR_35 extends Omit<CRM_TTR_35, 'company_id'> {}

export interface CRM_TTR_36 {
  company_id: string;
  salary_approval_id: number;
}

export interface CRM_TTR_37 {
  company_id: string;
  salary_approval_id: number;
}

export interface CRM_TTR_38 {
  company_id: string;
}

export interface CRM_TTR_39 {
  company_id: string;
  salary_approval_id: number;
}

export interface CRM_TTR_40 {
  company_id: string;
  salary_approval_id: number;
}

export interface CRM_TTR_41 {
  company_id: string;
  year: number;
  month: number;
}

export interface _CRM_TTR_41 extends Omit<CRM_TTR_41, 'company_id'> {}

export interface CRM_TTR_42 {
  company_id: string;
  entry_id: number;
}

export interface CRM_TTR_43 {
  company_id: string;
  parent_entry_id: number;
}

export interface CRM_TTR_44 {
  company_id: string;
  entry_id: number;
}

export interface CRM_TTR_45 {
  company_id: string;
}

export interface CRM_TTR_47 {
  company_id: string;
  user_id: string;
  active_from: Date;
  active_to: Date;
  activity_id: number;
  description?: string | null;
  approve?: boolean;
}

export interface _CRM_TTR_47 extends Omit<CRM_TTR_47, 'company_id'> {}

export interface CRM_TTR_48 {
  company_id: string;
  user_id: string;
}

export interface CRM_TTR_49 {
  company_id: string;
  entry_id: number;
  active_from?: Date;
  active_to?: Date;
  activity_id?: number | null;
  description?: string;
  approve?: boolean;
  decline?: boolean;
}

export interface _CRM_TTR_49 extends Omit<CRM_TTR_49, 'company_id'> {}

export interface CRM_TTR_50 {
  company_id: string;
  entry_id: number;
}

export interface CRM_TTR_51 {
  company_id: string;
  year: number;
}

export interface CRM_TTR_52 extends date_input {
  company_id: string;
}

export interface _CRM_TTR_52 extends Omit<CRM_TTR_52, 'company_id'> {}

export interface CRM_RSC_0 {
  company_id: string;
  resource_name: string;
  resource_description: string;
  resource_type_id: number;
  resource_data?: {[key: string]: string | number | null} | null;
}

export interface _CRM_RSC_0 extends Omit<CRM_RSC_0, 'company_id'> {}

export interface CRM_RSC_1 {
  company_id: string;
  resource_id: number;
  resource_name?: string;
  resource_description?: string | null;
  resource_data?: {[key: string]: string | number | null | Date} | null;
}

export interface _CRM_RSC_1 extends Omit<CRM_RSC_1, 'company_id'> {}

export interface CRM_RSC_2 extends search_input, pagination_input, sorting_input {
  company_id: string;
  resource_type_ids?: number[] | null;
}

export interface _CRM_RSC_2 extends Omit<CRM_RSC_2, 'company_id'> {}

export interface CRM_RSC_3 {
  company_id: string;
  resource_id: number;
}

export interface CRM_RSC_4 extends search_input {}

export interface CRM_RSC_5 {
  company_id: string;
  resource_id: number;
  image: File;
}

export interface _CRM_RSC_5 extends Omit<CRM_RSC_5, 'company_id'> {}

export interface CRM_RSC_6 {
  company_id: string;
  resource_id: number;
}


export interface _CRM_RSC_7 extends Omit<CRM_RSC_7, 'company_id'> {}

export interface CRM_RSC_7 {
  company_id: string,
  date_from: Date,
  date_to: Date
}

export interface _CRM_RSC_6 extends Omit<CRM_RSC_6, 'company_id'> {}

export interface CRM_TMP_0 {
  company_id: string;
  question_text: string;
  radio_selection: number;
  required: number;
  template_id: number;
  choices?: {choice_name: string, choice_ghost_text: string | null}[];
}

export interface _CRM_TMP_0 extends Omit<CRM_TMP_0, 'company_id'> {}

export interface CRM_TMP_1 {
  company_id: string;
}

export interface CRM_TMP_2 {
  company_id: string;
  question_id: number;
  question_text?: string;
  radio_selection?: number;
  required?: number;
  choices?: {choice_id: number | null, choice_name: string, choice_ghost_text: string | null, deleted: boolean}[];
}

export interface _CRM_TMP_2 extends Omit<CRM_TMP_2, 'company_id'> {}

export interface CRM_TMP_3 {
  company_id: string;
  question_id: number;
}

export interface CRM_TMP_4 {
  company_id: string;
  template_name: string;
  questions?: {question_id: number}[];
}

export interface _CRM_TMP_4 extends Omit<CRM_TMP_4, 'company_id'> {}

export interface CRM_TMP_5 {
  company_id: string;
}

export interface CRM_TMP_6 {
  company_id: string;
  template_id: number;
  template_name?: string;
  favourite?: boolean;
}

export interface _CRM_TMP_6 extends Omit<CRM_TMP_6, 'company_id'> {}

export interface CRM_TMP_7 {
  company_id: string;
  template_id: number;
}

export interface CRM_TMP_8 {
  company_id: string;
  question_id: number;
  choice_name: string;
  choice_ghost_text: string | null;
}

export interface _CRM_TMP_8 extends Omit<CRM_TMP_8, 'company_id'> {}

export interface CRM_TMP_9 {
  company_id: string;
  question_id: number;
  choice_id: number;
  choice_name?: string;
  choice_ghost_text?: string | null;
  index?: number;
}

export interface _CRM_TMP_9 extends Omit<CRM_TMP_9, 'company_id'> {}

export interface CRM_TMP_10 {
  company_id: string;
  question_id: number;
  choice_id: number;
}

export interface _CRM_TMP_10 extends Omit<CRM_TMP_10, 'company_id'> {}

export interface CRM_TMP_11 {
  company_id: string;
  template_id: number;
  product_id: number;
}

export interface _CRM_TMP_11 extends Omit<CRM_TMP_11, 'company_id'> {}

export interface CRM_TMP_12 {
  company_id: string;
  template_id: number;
  product_id: number;
}

export interface _CRM_TMP_12 extends Omit<CRM_TMP_12, 'company_id'> {}

export interface CRM_TMP_13 {
  company_id: string;
  task_group_id: number;
  task_name: string;
  index: number;
}

export interface _CRM_TMP_13 extends Omit<CRM_TMP_13, 'company_id'> {}

export interface CRM_TMP_14 {
  company_id: string;
  task_id: number;
  task_name: string;
  index: number;
}

export interface _CRM_TMP_14 extends Omit<CRM_TMP_14, 'company_id'> {}

export interface CRM_TMP_15 {
  company_id: string;
  task_id: number;
}

export interface _CRM_TMP_15 extends Omit<CRM_TMP_15, 'company_id'> {}


export interface CRM_TMP_16 {
  company_id: string;
  task_group_name: string;
  index: number;
  task_template_id: number;
  tasks: {
    task_name: string;
    index: number;
  }[];
}

export interface _CRM_TMP_16 extends Omit<CRM_TMP_16, 'company_id'> {}

export interface CRM_TMP_17 {
  company_id: string;
  task_group_id: number;
  task_group_name: string;
  icon_id: number;
  index: number;
  tasks: {
    task_id: number | null;
    task_name: string;
    index: number;
  }[];
}
export interface _CRM_TMP_17 extends Omit<CRM_TMP_17, 'company_id'> {}

export interface CRM_TMP_18 {
  company_id: string;
  task_group_id: number;
}

export interface _CRM_TMP_18 extends Omit<CRM_TMP_18, 'company_id'> {}


export interface CRM_TMP_19 {
  company_id: string;
  task_template_name: string;
}

export interface _CRM_TMP_19 extends Omit<CRM_TMP_19, 'company_id'> {}

export interface CRM_TMP_20 {
  company_id: string;
  task_template_id: number;
  task_template_name?: string;
  favourite?: boolean;
}

export interface _CRM_TMP_20 extends Omit<CRM_TMP_20, 'company_id'> {}

export interface CRM_TMP_21 {
  company_id: string;
  task_template_id: number;
}

export interface CRM_TMP_22 {
  company_id: string;
  task_template_id: number;
  product_id: number;
}

export interface _CRM_TMP_22 extends Omit<CRM_TMP_22, 'company_id'> {}

export interface CRM_TMP_23 {
  company_id: string;
  task_template_id: number;
  product_id: number;
}

export interface _CRM_TMP_23 extends Omit<CRM_TMP_23, 'company_id'> {}

export interface CRM_TMP_24 {
  company_id: string;
}

export interface _CRM_TMP_24 extends Omit<CRM_TMP_24, 'company_id'> {}

export interface CRM_TMP_25 {
  company_id: string;
  template_name: string;
  work_order_title?: string | null;
  work_order_description?: string | null;
  address_names?: string[];
  task_template_ids?: number[];
  customer_question_template_ids?: number[];
  color?: string | null;
}

export interface _CRM_TMP_25 extends Omit<CRM_TMP_25, 'company_id'> {}

export interface CRM_TMP_26 {
  company_id: string;
}

export interface CRM_TMP_27 {
  company_id: string;
  template_id: number;
  template_name?: string;
  favourite?: boolean;
  work_order_title?: string;
  work_order_description?: string;
  address_names?: string[];
  task_template_ids?: number[];
  customer_question_template_ids?: number[];
  color?: string | null;
}

export interface _CRM_TMP_27 extends Omit<CRM_TMP_27, 'company_id'> {}

export interface CRM_TMP_28 {
  company_id: string;
  template_id: number;
}

export interface CRM_TMP_29 {
  company_id: string;
  product_id: number;
  template_id: number;
}

export interface _CRM_TMP_29 extends Omit<CRM_TMP_29, 'company_id'> {}

export interface CRM_TMP_30 {
  company_id: string;
  template_id: number;
  product_id: number;
}


export interface _CRM_TMP_30 extends Omit<CRM_TMP_30, 'company_id'> {}

export interface CRM_TMP_31 extends Omit<CRM_ORD_12, 'order_id' | 'payment_id' | 'payment_exclusive' | 'template' | 'work_order_id'> {
  template_id: number;
}

export interface _CRM_TMP_31 extends Omit<CRM_TMP_31, 'company_id'> {}

export interface CRM_TMP_34 {
  company_id: string;
  title: string;
  description: string;
  required: number;
}

export interface _CRM_TMP_34 extends Omit<CRM_TMP_34, 'company_id'> {}

export interface CRM_TMP_35 {
  company_id: string;
}

export interface _CRM_TMP_35 extends Omit<CRM_TMP_35, 'company_id'> {}

export interface CRM_TMP_36 {
  company_id: string;
  entry_id: number;
  title?: string;
  description?: string;
  required?: number;
}

export interface _CRM_TMP_36 extends Omit<CRM_TMP_36, 'company_id'> {}

export interface CRM_TMP_37 {
  company_id: string;
  entry_id: number;
}

export interface CRM_TMP_38 {
  company_id: string;
  entry_id: number;
  product_id: number;
}

export interface _CRM_TMP_38 extends Omit<CRM_TMP_38, 'company_id'> {}

export interface CRM_TMP_39 {
  company_id: string;
  entry_id: number;
  product_id: number;
}

export interface _CRM_TMP_39 extends Omit<CRM_TMP_39, 'company_id'> {}

export interface CRM_APP_0 {
  company_id: string;
  application_id: number;
}

export interface CRM_APP_1 {
  company_id: string;
  application_type_id?: number;
}

export interface CRM_APP_2 {
  company_id: string;
  application_id: number;
}

export interface CRM_PRJ_0 {
  company_id: string;
}

export interface CRM_PRJ_1 {
  company_id: string;
  project_name: string;
  accounting_id?: number;
  match_accounting?: boolean;
}

export interface _CRM_PRJ_1 extends Omit<CRM_PRJ_1, 'company_id'> {}

export interface CRM_PRJ_2 {
  company_id: string;
  project_id: number;
  project_name?: string;
  accounting_id?: number;
}

export interface _CRM_PRJ_2 extends Omit<CRM_PRJ_2, 'company_id'> {}

export interface CRM_PRJ_3 {
  company_id: string;
  project_id: number;
}

export interface CRM_DEP_0 {
  company_id: string;
}

export interface CRM_DEP_1 {
  company_id: string;
  department_name: string;
  accounting_id?: number;
  match_accounting?: boolean;
}

export interface _CRM_DEP_1 extends Omit<CRM_DEP_1, 'company_id'> {}

export interface CRM_DEP_2 {
  company_id: string;
  department_id: number;
  department_name?: string;
  accounting_id?: number;
}

export interface _CRM_DEP_2 extends Omit<CRM_DEP_2, 'company_id'> {}

export interface CRM_DEP_3 {
  company_id: string;
  department_id: number;
}

export interface CRM_SER_8 {
  datetime: Date;
}

export interface CRM_SER_12 {
  registration_number: string;
}

export interface CRM_SER_13 {
  status_code?: 200 | 400 | 401 | 403 | 404 | 405 | 500 | 503;
  asset_id?: string;
}

export interface CRM_SER_15 {
  organisation_number: string;
}

export interface CRM_SER_32 {
  top_bar_title: string;
  top_bar_message: string;
  top_bar_color: string;
  top_bar_enabled: boolean;
}

export interface CRM_NOT_0 extends pagination_input, sorting_input, search_input {
  company_id?: string;
  unread_only?: boolean;
}

export interface _CRM_NOT_0 extends Omit<CRM_NOT_0, 'company_id'> {}

export interface CRM_NOT_1 {
  company_id: string;
  entry_ids?: number[];
  mark_all_read?: boolean;
}

export interface _CRM_NOT_1 extends Omit<CRM_NOT_1, 'company_id'> {}


export interface GEO_ADR_0 {
  sok: string;
  fuzzy?: boolean;
  sokemodus?: "AND" | "OR";
  adressenavn?: string;
  adressetekst?: string;
  adressetilleggsnavn?: string;
  adressekode?: string;
  nummer?: number;
  bokstav?: string | null;
  kommunenummer?: string;
  kommunenavn?: string;
  gardsnummer?: number;
  bruksnummer?: number;
  festenummer?: number;
  undernummer?: number;
  bruksenhetnummer?: string;
  objtype?: "Vegadresse" | "Matrikkeladresse";
  poststed?: string;
  postnummer?: string;
  filter?: string;
  utkoordsys?: number;
  treffPerSide?: number;
  side?: number;
  asciiKompatibel?: boolean;
}


// REPORTINATOR

export interface REP_TMP_0 {
  company_id: string;
  template_name: string;
  template_description: string;
  template_type_id?: number;
}

export interface _REP_TMP_0 extends Omit<REP_TMP_0, 'company_id'> {}

export interface REP_TMP_1 extends full_select {
  company_id: string;
}

export interface _REP_TMP_1 extends Omit<REP_TMP_1, 'company_id'> {}

export interface REP_TMP_2 {
  company_id: string;
  template_id: number;
  template_name?: string;
  template_description?: string;
  area_description?: string;
  cost_estimate_description?: string;
  tg_general_description?: string;
  tgiu_description?: string;
  tg0_description?: string;
  tg1_description?: string;
  tg2_description?: string;
  tg3_description?: string;
  upgrades_description?: string;
  report_description?: string;
}

export interface _REP_TMP_2 extends Omit<REP_TMP_2, 'company_id'> {}

export interface REP_TMP_3 {
  company_id: string;
  template_id: number;
}

export interface REP_TMP_4 {
  company_id: string;
  template_id: number;
  element_id: number;
  parent_relation_id: number | null;
  index?: number;
}

export interface _REP_TMP_4 extends Omit<REP_TMP_4, 'company_id'> {}

export interface REP_TMP_5 {
  company_id: string;
  template_id: number;
  relation_id: number;
}

export interface _REP_TMP_5 extends Omit<REP_TMP_5, 'company_id'> {}

export interface REP_TMP_6 {
  company_id: string;
  template_id: number;
}

export interface REP_TMP_7 {
  company_id: string;
  template_id: number;
  location_ids: number[];
}

export interface _REP_TMP_7 extends Omit<REP_TMP_7, 'company_id'> {}

export interface REP_TMP_8 {
  company_id: string;
  template_id: number;
}

export interface REP_TMP_9 {
  company_id: string;
  relation_id: number;
  parent_relation_id?: number | null;
  index?: number;
  allow_page_break?: boolean;
  collapsed?: boolean;
}

export interface _REP_TMP_9 extends Omit<REP_TMP_9, 'company_id'> {}

export interface REP_TMP_10 {
  company_id: string;
  template_id: number;
  title: string;
  body: string;
  index?: number | null;
}

export interface _REP_TMP_10 extends Omit<REP_TMP_10, 'company_id'> {}

export interface REP_TMP_11 {
  company_id: string;
  template_id: number;
}

export interface REP_TMP_12 {
  company_id: string;
  entry_id: number;
}

export interface REP_TMP_13 {
  company_id: string;
  entry_id: number;
  title?: string;
  body?: string;
  index?: number;
}

export interface _REP_TMP_13 extends Omit<REP_TMP_13, 'company_id'> {}

export interface REP_ELM_0 {
  company_id: string;
  element_name: string;
  element_type_id: number;
  assessment_type_id: number;
  show_in_navigation: boolean;
  attribute_ids: number[];
}

export interface _REP_ELM_0 extends Omit<REP_ELM_0, 'company_id'> {}

export interface REP_ELM_1 extends full_select {
  company_id: string;
  element_type_ids?: number[];
}

export interface _REP_ELM_1 extends Omit<REP_ELM_1, 'company_id'> {}

export interface REP_ELM_2 {
  company_id: string;
  element_id: number;
  element_name?: string;
  assessment_type_id?: number;
  show_in_navigation?: boolean;
  location_ids?: number[];
}

export interface _REP_ELM_2 extends Omit<REP_ELM_2, 'company_id'> {}

export interface REP_ELM_3 {
  company_id: string;
  element_id: number;
}

export interface REP_ELM_4 {
  company_id: string;
  element_id: number;
}

export interface REP_ELM_5 {
  company_id: string;
  element_id: number;
  attribute_id: number;
  default_value?: number | string | null
}

export interface _REP_ELM_5 extends Omit<REP_ELM_5, 'company_id'> {}

export interface REP_ELM_6 {
  company_id: string;
  element_id: number;
  relation_id: number;
}

export interface _REP_ELM_6 extends Omit<REP_ELM_6, 'company_id'> {}

export interface REP_ELM_7 {
  company_id: string;
  element_id: number;
  attribute_id: number;
  phrase_title: string;
  phrase_text: string;
  rules: NestedPhraseRule[];
}

export interface _REP_ELM_7 extends Omit<REP_ELM_7, 'company_id'> {}

export interface REP_ELM_8 {
  company_id: string;
  phrase_id: number;
  phrase_title?: string;
  phrase_text?: string;
  rules?: NestedPhraseRule[];
  deleted_rule_ids?: number[];
  index?: number;
}

export interface _REP_ELM_8 extends Omit<REP_ELM_8, 'company_id'> {}

export interface REP_ELM_9 {
  company_id: string;
  phrase_id: number;
}

export interface REP_ELM_10 {
  company_id: string;
  phrase_id: number;
  value: number;
  attribute_id: number | null;
  secondary_value?: number | null;
  use_assessment_value: boolean;
  index?: number;
  phrase_filter_type_id: number;
}

export interface NestedPhraseRule extends Omit<REP_ELM_10, 'company_id' | 'phrase_id'> {}

export interface _REP_ELM_10 extends Omit<REP_ELM_10, 'company_id'> {}

export interface REP_ELM_11 {
  company_id: string;
  phrase_id: number;
  rule_id: number;
  value?: number;
  attribute_id?: number | null;
  secondary_value?: number | null;
  use_assessment_value?: boolean;
  index?: number;
  phrase_filter_type_id?: number;
}

export interface _REP_ELM_11 extends Omit<REP_ELM_11, 'company_id'> {}

export interface REP_ELM_12 {
  company_id: string;
  phrase_id: number;
  rule_id: number;
}

export interface _REP_ELM_12 extends Omit<REP_ELM_12, 'company_id'> {}

export interface REP_OBJ_0 {
  element_id?: number;
}

export interface REP_OBJ_2 {
  user_creatable_only?: boolean;
}

export interface REP_ELM_17 {
  company_id: string;
  element_id: number;
  child_element_id: number;
  index?: number;
}

export interface _REP_ELM_17 extends Omit<REP_ELM_17, 'company_id'> {}

export interface REP_ELM_18 {
  company_id: string;
  element_id: number;
}

export interface REP_ELM_19 {
  company_id: string;
  element_id: number;
  relation_id: number;
  index?: number;
}

export interface _REP_ELM_19 extends Omit<REP_ELM_19, 'company_id'> {}

export interface REP_ELM_20 {
  company_id: string;
  element_id: number;
  relation_id: number;
}

export interface _REP_ELM_20 extends Omit<REP_ELM_20, 'company_id'> {}

export interface REP_ELM_21 {
  company_id: string;
  element_id: number;
  relation_id: number;
  default_value?: number | string | null;
}

export interface _REP_ELM_21 extends Omit<REP_ELM_21, 'company_id'> {}

export interface REP_ELM_22 {
  company_id: string;
  element_id: number;
  relation_id: number;
}

export interface _REP_ELM_22 extends Omit<REP_ELM_22, 'company_id'> {}

export interface REP_REP_0 {
  company_id: string;
  template_id: number;
  revision_name?: string;
  order_id?: number;
}

export interface _REP_REP_0 extends Omit<REP_REP_0, 'company_id'> {}

export interface REP_REP_1 extends full_select {
  company_id: string;
}

export interface _REP_REP_1 extends Omit<REP_REP_1, 'company_id'> {}

export interface REP_REP_2 {
  company_id: string;
  report_id: number;
  current_revision_id?: number;
  delivery_at?: Date;
  comment?: string;
  present_user_ids?: string[];
  conclusion?: string;
  building_reference_level?: string;
}

export interface _REP_REP_2 extends Omit<REP_REP_2, 'company_id'> {}

export interface REP_REP_3 {
  company_id: string;
  report_id: number;
}

export interface REP_REP_4 {
  company_id: string;
  report_id: number;
}

export interface REP_REP_5 {
  company_id: string;
  report_id: number;
  revision_name?: string;
}

export interface _REP_REP_5 extends Omit<REP_REP_5, 'company_id'> {}

export interface REP_REP_6 {
  company_id: string;
  report_id: number;
}

export interface REP_REP_7 {
  company_id: string;
  revision_id: number,
  revision_name?: string;
  location_ids?: number[];
}

export interface _REP_REP_7 extends Omit<REP_REP_7, 'company_id'> {}

export interface REP_REP_8 {
  company_id: string;
  revision_id: number;
}

export interface REP_REP_9 {
  company_id: string;
  revision_id: number;
}

export interface REP_REP_10 {
  company_id: string;
  revision_id: number;
  element_id: number;
  parent_report_element_id: number | null;
  index?: number;
}

export interface _REP_REP_10 extends Omit<REP_REP_10, 'company_id'> {}

export interface REP_REP_11 {
  company_id: string;
  revision_id: number;
}

export interface REP_REP_12 {
  company_id: string;
  report_element_id: number;
  report_element_name?: string;
  parent_report_element_id?: number | null;
  index?: number;
  collapsed?: boolean;
  assessment_value?: number;
  location_ids?: number[];
  status_id?: number;
}

export interface _REP_REP_12 extends Omit<REP_REP_12, 'company_id'> {}

export interface REP_REP_13 {
  company_id: string;
  report_element_id: number;
}

export interface REP_REP_14 {
  company_id: string;
  report_element_id: number;
  attribute_id: number;
}

export interface _REP_REP_14 extends Omit<REP_REP_14, 'company_id'> {}

export interface REP_REP_16 {
  company_id: string;
  report_element_id: number;
  entry_id: number;
  value: number | string | null;
}

export interface _REP_REP_16 extends Omit<REP_REP_16, 'company_id'> {}

export interface REP_REP_17 {
  company_id: string;
  report_element_id: number;
  entry_id: number;
}

export interface _REP_REP_17 extends Omit<REP_REP_17, 'company_id'> {}

export interface REP_REP_18 {
  company_id: string;
  order_id: number;
}

export interface REP_REP_19 {
  company_id: string;
  report_element_id: number;
  report_element_floor_setup_entry_id: number;
  measurements: {[key: string]: number | null}
}

export interface _REP_REP_19 extends Omit<REP_REP_19, 'company_id'> {}

export interface REP_REP_20 {
  company_id: string;
  report_element_id: number;
  floor_id?: number;
  floor_name?: string | null;
  rooms?: NestedRoomInput[]
  floor_ids?: number[]
}

export interface _REP_REP_20 extends Omit<REP_REP_20, 'company_id'> {}

export interface REP_REP_21 {
  company_id: string;
  report_element_id: number;
  report_element_floor_setup_entry_id: number;
}

export interface _REP_REP_21 extends Omit<REP_REP_21, 'company_id'> {}

export interface REP_REP_22 {
  company_id: string;
  report_element_id: number;
  report_element_floor_setup_entry_id: number;
  floor_name?: string;
}

export interface _REP_REP_22 extends Omit<REP_REP_22, 'company_id'> {}

export interface REP_REP_23 {
  company_id: string;
  report_element_id: number;
  room_id?: number;
  report_element_floor_setup_entry_id: number;
  room_name?: string | null;
  measurement_key: string;
  room_ids?: number[]
}

export interface _REP_REP_23 extends Omit<REP_REP_23, 'company_id'> {}

export interface NestedRoomInput extends Omit<_REP_REP_23, 'report_element_id'> {}

export interface REP_REP_24 {
  company_id: string;
  report_element_id: number;
  report_element_room_setup_entry_id: number;
}

export interface _REP_REP_24 extends Omit<REP_REP_24, 'company_id'> {}

export interface REP_REP_25 {
  company_id: string;
  report_element_id: number;
  report_element_room_setup_entry_id: number;
  room_name?: string;
}

export interface _REP_REP_25 extends Omit<REP_REP_25, 'company_id'> {}

export interface REP_REP_26 {
  company_id: string;
  report_element_id: number;
  report_element_floor_setup_entry_id: number;
}

export interface _REP_REP_26 extends Omit<REP_REP_26, 'company_id'> {}

export interface REP_REP_27 {
  company_id: string;
  report_element_id: number;
  floor_relation_id: number;
}

export interface _REP_REP_27 extends Omit<REP_REP_27, 'company_id'> {}

export interface REP_REP_28 {
  company_id: string;
  report_element_id: number;
  report_element_room_setup_entry_id: number;
}

export interface _REP_REP_28 extends Omit<REP_REP_28, 'company_id'> {}

export interface REP_REP_29 {
  company_id: string;
  report_element_id: number;
  room_relation_id: number;
}

export interface _REP_REP_29 extends Omit<REP_REP_29, 'company_id'> {}

export interface REP_REP_30 {
  company_id: string;
  report_element_id: number;
}

export interface REP_REP_31 {
  company_id: string;
  report_element_id: number;
}

export interface REP_REP_32 {
  revision_public_key: string;
}

export interface REP_REP_33 {
  company_id: string;
  report_id: number;
  user_ids: string[];
}

export interface REP_REP_34 {
  company_id: string;
  report_id: number;
  image: File;
}

export interface REP_REP_35 {
  company_id: string;
  report_element_id: number;
  image: File;
}

export interface REP_REP_36 {
  company_id: string;
  report_element_id: number;
  entry_id: number;
  include_in_pdf?: boolean;
  include_in_report?: boolean;
}

export interface _REP_REP_36 extends Omit<REP_REP_36, 'company_id'> {}

export interface REP_REP_37 {
  company_id: string;
  report_element_id: number;
  entry_id: number;
}

export interface _REP_REP_37 extends Omit<REP_REP_37, 'company_id'> {}

export interface REP_REP_38 {
  company_id: string;
  revision_id: number;
}

export interface REP_REP_39 {
  report_id: number;
  company_id: string;
  year: number;
  description: string;
  done_by: string;
  documentation_type_id: number;
  documentation_description?: string;
}

export interface _REP_REP_39 extends Omit<REP_REP_39, 'company_id'> {}

export interface REP_REP_40 {
  company_id: string;
  report_id: number;
}

export interface REP_REP_41 {
  company_id: string;
  report_id: number;
  upgrade_id: number;
  year?: number;
  description?: string;
  done_by?: string;
  documentation_type_id?: number;
  documentation_description?: string;
}

export interface _REP_REP_41 extends Omit<REP_REP_41, 'company_id'> {}

export interface REP_REP_42 {
  company_id: string;
  report_id: number;
  upgrade_id: number;
}

export interface _REP_REP_42 extends Omit<REP_REP_42, 'company_id'> {}

export interface REP_REP_43 {
  company_id: string;
  report_id: number;
  user_id: string | null;
  name: string;
  type_id: number;
  type_name: string;
}

export interface _REP_REP_43 extends Omit<REP_REP_43, 'company_id'> {}

export interface REP_REP_44 {
  company_id: string;
  report_id: number;
  relation_id: number;
  name: string;
  type_id: number;
}

export interface _REP_REP_44 extends Omit<REP_REP_44, 'company_id'> {}

export interface REP_REP_45 {
  company_id: string;
  report_id: number;
  relation_id: number;
}

export interface _REP_REP_45 extends Omit<REP_REP_45, 'company_id'> {}


export interface REP_REP_46 {
  company_id: string;
  report_element_id: number;
}

export interface REP_REP_47 {
  company_id: string;
  report_id: number;
}

export interface REP_REP_48 {
  company_id: string;
  report_id: number;
  registered_encumbrances?: string;
  plot_description?: string;
  access_road?: string;
  regulation?: string;
  location?: string;
  water_connection_description?: string;
  sewage_connection_description?: string;
  building_description?: string;
}

export interface _REP_REP_48 extends Omit<REP_REP_48, 'company_id'> {}

export interface REP_REP_49 {
  company_id: string;
  report_id: number;
  source_name?: string;
  source_date?: Date;
  source_provider?: string;
  status?: string;
  comment?: string;
}

export interface _REP_REP_49 extends Omit<REP_REP_49, 'company_id'> {}

export interface REP_REP_50 {
  company_id: string;
  report_id: number;
}

export interface REP_REP_51 {
  company_id: string;
  report_id: number;
  source_id: number;
  source_name?: string;
  source_date?: Date;
  source_provider?: string;
  status?: string;
  comment?: string;
}

export interface _REP_REP_51 extends Omit<REP_REP_51, 'company_id'> {}

export interface REP_REP_52 {
  company_id: string;
  report_id: number;
  source_id: number;
}

export interface _REP_REP_52 extends Omit<REP_REP_52, 'company_id'> {}

export interface REP_REP_53 {
  company_id: string;
  report_id: number;
  source_id: number;
  file: File;
}

export interface _REP_REP_53 extends Omit<REP_REP_53, 'company_id'> {}

export interface REP_REP_54 {
  company_id: string;
  report_id: number;
  source_id: number;
}

export interface _REP_REP_54 extends Omit<REP_REP_54, 'company_id'> {}
