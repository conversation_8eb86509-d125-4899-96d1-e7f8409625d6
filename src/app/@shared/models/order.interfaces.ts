import {UnitDetails, CompleteTriggerInput} from "./input.interfaces";
import {ResourceResponse} from "./resources.interfaces";
import {InternalUserResponse} from "./user.interfaces";
import {AffiliateContactResponse, AffiliateResponse} from "./affiliate.interfaces";
import {OrderPaymentResponse} from "./payment.interfaces";
import {TimeTrackingResponse} from "./timetracking.interfaces";
import {ProjectResponse} from "./projects.interfaces";
import {DepartmentResponse} from "./departments.interfaces";

export interface OrderResponse {
  order_title: string;
  order_id: number;
  order_number: string;
  company_id: string;
  company_name: string;
  company_logo_url: string;
  total_amount_inc_vat: number;
  top_schedule_total_amount_inc_vat: number;
  hide_payment_data: number;
  acceptance_data: OrderAcceptanceDataResponse;
  seller: OrderSellerResponse;
  payment_recipient: PaymentRecipientResponse | null;
  service_recipient: ServiceRecipientResponse | null;
  single_affiliate: number;
  affiliate_contact: AffiliateContactResponse | null;
  order_status_id: number;
  order_status_name: string;
  customer_order_status_name: string;
  customer_questions: OrderCustomerQuestionResponse[];
  order_notes: OrderNoteResponse[];
  feedback_rating: number;
  feedback_comment: string;
  quote_sent_at: Date | null;
  quote_expiry_days: number | null;
  locked: number;
  payment_status_id: number;
  payment_status_name: string;
  confirmation_status_id: number;
  confirmation_status_name: string;
  confirmation_status_history: ConfirmationStatusHistoryResponse[];
  partner: OrderPartnerResponse | null;
  partner_contact: OrderPartnerContactResponse | null;
  show_prices_inc_vat: number;
  refunded: number;
  refunded_amount: number;
  order_source_id: number;
  order_source_name: string;
  source_tag: string;
  hide_addresses_in_customer_portal: number;
  attachments: OrderAttachmentResponse[];
  comment: string | null;
  archived_at: Date | null;
  updated_by: string;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
  execution_at: Date | null;
  execution_to: Date | null;
  display_address: string;
  repeating: boolean;
  is_project: boolean;
  convert_tracked_time_to_working_hours: boolean;
  activity_id: number | null;
  project: ProjectResponse | null;
  department: DepartmentResponse | null;
  require_crew_work_order_confirmation: boolean;

  contractorView: boolean;
  orderSourceTag: string;
}

export interface WorkOrderResponse {
  work_order_id: number;
  work_order_number: string;
  order_id: number;
  work_order_title: string;
  work_order_description: string;
  company_id: string;
  company_name: string;
  execution_at: Date;
  execution_to: Date;
  arrival_from: string | null;
  arrival_to: string | null;
  addresses: UnitDetails[];
  users: WorkOrderUserRelationResponse[];
  resources: ResourceResponse[];
  work_order_status_id: number;
  work_order_status_name: string;
  total_checked_in_seconds: number;
  user_time_tracking_response: UserTimeTrackingResponse[];
  incidents: IncidentResponse[];
  task_groups: WorkOrderTaskGroupResponse[];
  schedule: WorkOrderScheduleResponse | null;
  schedule_template: boolean;
  next_execution_at: Date | null;
  confirmation_notification_status_id: number;
  confirmation_notification_status_name: string;
  customer_confirmation_threshold: number;
  customer_confirmation_reminder_threshold: number;
  customer_confirmation_sent_at: Date | null;
  subcontractors: OrderSubContractorResponse[];
  payment: OrderPaymentResponse | null;
  additional_data: { addresses?: string[] };
  attachments: OrderAttachmentResponse[];
  order_lines: OrderLineResponse[];
  parent_work_order_id: number | null;
  is_project: boolean;
  payment_recipient_name: string | null;
  contractor_relation: OrderSubContractorResponse | null;
  activity_id: number | null;
  convert_tracked_time_to_working_hours: boolean;
  fixed_transport_duration_minutes: number | null;
  custom_color_hex: string | null;
  default_pause_duration_minutes: number | null;
  default_pause_start_time: string | null;
  hide_in_order_lines_list: boolean;
  require_crew_work_order_confirmation: boolean;
}

export interface WorkOrderCompactResponse {
  work_order_id: number;
  work_order_number: string;
  work_order_title: string;
  execution_at: Date | null;
  execution_to: Date | null;
  company_name: string;
  work_order_status_id: number;
  work_order_status_name: string;
  payment_recipient_name: string;
  display_addresses: string[];
  parent_work_order_id: number | null;
  subcontractors: OrderSubContractorResponse[];
  contractor_relation: OrderSubContractorResponse | null;
  users: WorkOrderUserRelationResponse[];
  custom_color_hex: string | null;
  require_crew_work_order_confirmation: boolean;
}

export interface QuantityProposalOrderLineResponse {
  order_line_id: number;
  order_line_name: string;
  quantity: number;
  comment: string;
  price_rule_ids: number[];
  product_id: number;
  precalculated_unit_price_inc_vat: number;
  unit_abbreviation: string;
}

export interface WorkOrderScheduleResponse {
  work_order_schedule_id: number;
  work_order_id: number;
  start_date: Date;
  end_date: Date;
  schedule_repeat_type_id: number;
  schedule_repeat_type_name: string;
  weekdays: number[];
  every: number;
  date: number | null;
  nth_weekday: number;
  instances_in_advance: number;
  active: boolean;
  deactivated_at: Date | null;
  schedule_description: string;
  num_work_orders: number;
  num_unstarted_work_orders: number;
}

export interface WorkOrderAddressResponse extends UnitDetails {
  empty: boolean;
}

export interface OrderResponseCompact {
  order_id: number;
  order_number: string;
  company_id: string;
  display_address: string;
  order_status_id: number;
  order_status_name: string;
  payment_status_id: number;
  payment_status_name: string;
  comment: string | null;
  payment_recipient: PaymentRecipientResponse;
  service_recipient: ServiceRecipientResponse;
  main_product_name: string;
  order_source_name: string;
  has_subcontractors: number;
  execution_at: Date | null;
  total_amount_inc_vat: number;
  total_amount_ex_vat: number;
  top_schedule_total_amount_inc_vat: number;
  top_schedule_total_amount_ex_vat: number;
  feedback_comment: string;
  feedback_rating: number;
  order_title: string;
  contains_work_order_schedules: boolean;
  contains_active_work_order_schedules: boolean;
  contains_payment_schedules: boolean;
  contains_active_payment_schedules: boolean;
  repeating: boolean;
}

export interface OrderSubContractorResponse {
  entry_id: number;
  affiliate_id: number;
  affiliate_name: string;
  accepted_at: Date | null;
  accepted_by_name: string | null;
  declined_at: Date | null;
  declined_by_name: string | null;
  declined_reason: string | null;
  created_at: Date;
  created_by_name: string;
  popUpDescription: string;
}

export interface OrderAttachmentResponse {
  attachment_id: number;
  work_order_id: number | null;
  file_id: string;
  file_name: string;
  extension: string;
  file_url: string;
  uploaded_by: string;
  attach_to_invoice: number;
  invoice_attachment_id: number | null;
  visible_to_customer: boolean;
  visible_to_crew: boolean;
  created_at: Date;
  updated_at: Date | null;
  updated_by: string;
  deleted_at: Date | null;
}

export interface PaymentRecipientResponse {
  affiliate_id: number;
  user_id: string | null;
  name: string | null;
  email: string | null;
  phone: string | null;
  organisation_number: string | null;
  is_private: number;
}

export interface ServiceRecipientResponse {
  affiliate_id: number;
  user_id: string | null;
  name: string | null;
  email: string | null;
  phone: string | null;
  organisation_number: string | null;
  is_private: number;
}

export interface OrderPartnerResponse {
  affiliate_id: number;
  company_id: string;
  company_name: string;
  email: string;
  phone: string;
  organisation_number: string;
}

export interface OrderPartnerContactResponse {
  affiliate_contact_response: string;
  affiliate_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string;
  phone: string;
  company_name: string;
  company_id: string;
}

export interface CustomerAggregatedDataResponse {
  total_orders : number;
  revenue_inc_vat: number;
  revenue_ex_vat: number;
}

export interface OrderSellerResponse {
  user_id: string;
  first_name: string;
  last_name: string;
  phone: string;
  email: string;
  profile_image_url: string;
}

export interface OrderAcceptanceDataResponse {
  customer_accepted: number;
  user_id: string;
  first_name: string;
  last_name: string;
  accepted_at: Date;
}

export interface OrderNoteResponse {
  order_note_id: number;
  order_id: number;
  note_text: string;
  internal: number;
  updated_by: string;
  updated_by_name: string;
  updated_by_profile_image_url: string;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
}
export interface ConfirmationStatusHistoryResponse {
  entry_id: number;
  confirmation_status_id: number;
  confirmation_status_name: string;
  set_by: string;
  set_at: Date;
}
export interface OrderLineResponse {
  order_line_id: number;
  order_line_name: string;
  payment_id: number;
  work_order_id: number | null;
  work_order_number: string | null;
  work_order_title: string | null;
  work_order_execution_at: Date | null;
  work_order_status_id: number | null;
  work_order_status_name: string | null;
  company_id: string;
  order_id: number;
  product_id: number | null;
  product_name: string;
  product_description: string;
  vat_rate_id: number;
  vat_rate: number;
  unit_id: number;
  unit_name: string;
  unit_abbreviation: string;
  quantity: number;
  remaining_quantity: number;
  added_by_customer: number;
  precalculated_unit_price_inc_vat: number;
  precalculated_unit_price_ex_vat: number;
  unit_price_inc_vat: number;
  unit_price_ex_vat: number;
  calculated_total_price_inc_vat: number;
  calculated_total_price_ex_vat: number;
  gross_total_price_inc_vat: number;
  gross_total_price_ex_vat: number;
  gross_total_price_vat_amount: number;
  price_rules: PriceRuleResponse[];
  refunded: number;
  refunded_amount: number;
  discount_amount: number;
  discount_percentage: number;
  has_set_total_price: boolean;
  locked: boolean;
  product_price_rules: PriceRuleResponse[];
  comment: string;
  payment_status_id: number;
  payment_status_name: string;
  index: number;
  track_time: boolean;
  rounding_line: boolean;
  hide_in_order_lines_list: boolean;
  updated_by: string;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
}

export interface OrderLineTaskGroupResponse {
  task_group_id: number;
  task_group_name: string;
  task_group_icon_id: number;
  index: number;
  tasks: WorkOrderTaskResponse[];
}

export interface OrderLineStagePauseResponse {
  entry_id: number,
  paused_at: Date,
  unpaused_at: Date | null,
  paused_by: string,
  unpaused_by: string | null,
  total_pause_duration_in_seconds: number,
}

export interface OrderLineStageResponse {
  order_line_stage_id: number;
  stage_id: number;
  stage_name: string;
  started_at: Date | null;
  finished_at: Date | null;
  transport_started_at: Date | null;
  transport_finished_at: Date | null;
  address: UnitDetails | null;
  latitude: number;
  longitude: number;
  index: number;
  status_id: number;
  status_name: string;
  task_groups: OrderLineTaskGroupResponse[];
  billable: number;
  notify_customer: number;
  sets_quantity: number;
  order_status_id: number;
  deletable: number;
  include_transport: number;
  billable_transport: number;
  new_address: number;
  pauses: OrderLineStagePauseResponse[];
  total_pause_duration_in_seconds: number;
  total_checked_in_seconds: number;
  is_paused: number;
  incidents: IncidentResponse[];
  time_trackings: [OrderLineStageUserTimeTrackingResponse]
  next_stage_id: number;
  updated_by: string;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
  timeSpent?: string;
}

export interface UserTimeTrackingResponse {
  user: InternalUserResponse;
  duration_in_seconds: number;
  checked_in: boolean;
  time_trackings: TimeTrackingResponse[];
}

export interface OrderLineStageUserTimeTrackingResponse {
  user: InternalUserResponse;
  duration_in_seconds: number;
  checked_in: number;
  time_trackings: TimeTrackingResponse[];
}

export interface WorkOrderTaskResponse {
  work_order_task_id: number;
  task_id: number;
  task_name: string;
  task_status: number;
  comment: string;
  index: number;
  deleted?: boolean;
  updated_by_name: string | null;
}

export interface WorkOrderTaskGroupResponse {
  task_group_id: number;
  task_group_name: string;
  icon_id: number;
  index: number;
  tasks: WorkOrderTaskResponse[];
  deleted?: boolean;
  isCollapsed?: boolean; // Optional collapse property
}

export interface OrderCustomerQuestionResponse {
  order_question_id: number;
  question_id: number;
  question_text: string;
  radio_selection: number;
  required: number;
  answered: number;
  choices: OrderCustomerQuestionChoiceResponse[];
}

export interface OrderCustomerQuestionChoiceResponse {
  order_choice_id: number;
  choice_id: number;
  question_id: number;
  choice_name: string;
  choice_ghost_text: string;
  value: number;
  input: string;
  index: number;
  updated_at: Date | null;
  updated_by_name: string | null;
}


export interface ExtendedPriceRuleResponse extends PriceRuleResponse {
  days?: { name: string, value: number, abbreviation: string, active: boolean }[];
  isEditing?: boolean;
}


// export interface PriceRuleResponse {
//   price_rule_id: number;
//   price_rule_name: string;
//   value: number;
//   calculation_type_id: number;
//   calculation_type_name: string;
//   trigger: CompleteTriggerInput;
//   trigger_type_id: number;
//   trigger_type_name: string;
//   price_rule_group_id: number;
//   order_line_name: string;
//   total_price_adjustment: number;
//   manual: boolean;
//   percentage_adjustment: boolean;
//   manual_trigger: number;
//   fixed_price: number;
//   embed: number;
//   updated_by: string;
//   created_at: Date;
//   updated_at: Date | null;
//   deleted_at: Date | null;
// }

export interface PriceRuleResponse {
  price_rule_id: number;
  price_rule_name: string;
  price_rule_type_id: number;
  value: number;
  percentage_adjustment: boolean;
  total_price_adjustment: boolean;
  manual: boolean;
  embed: number;
  trigger: CompleteTriggerInput;
  updated_by: string;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
}

export interface TimeTriggerResponse {
  start_time: Date,
  end_time: Date,
  weekdays: number[],
}

export interface QuantityTriggerResponse {
  range_from: number,
  range_to: number,
}

export interface CargoResponse {
  cargo_type_id: number;
  cargo_type_name: string;
  cargo_type_description: string;
  cargo_type_long_description: string;
  icon_id: number;
  index: number;
  quantity: number;
}

export interface CrewResponse {
  user_id: string;
  first_name: string;
  last_name: string;
  phone: string;
  email: string;
  profile_image_url: string;
  role_name: string;
  role_id: number;
}

export interface AddressResponse {
  address_id: number;
  street: string;
  city: string;
  postal_code: string;
  section_id: string;
  bathroom_quantity: number;
  bedroom_quantity: number;
  has_elevator: number;
  has_garage: number;
  lat: number;
  lng: number;
  floor_number: number;
  floors_in_building: number;
  property_size: number;
  property_type_id: number;
  property_type_name: string;
  room_quantity: number;
  updated_by: string;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
}

export interface IncidentResponse {
  incident_id: number;
  order_line_stage_id: number;
  incident_type_id: number;
  incident_type_name: string;
  internal: number;
  stage_name: string;
  description: string;
  reported_by: string;
  reported_by_first_name: string;
  reported_by_last_name: string;
  images: string[];
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
}

export interface DashboardOrderLinesByDateResponse {
  // Ex: "2023-01-01": { "order_lines_execution_at": 10 , "percentage_difference": 57, "threshold_level": 2}
  [date: string]: {
    'order_lines_execution_at': number,
    'percentage_difference': number,
    'threshold_level': number,
  }
}

export interface DashboardOrdersByDateResponse {
  // Ex: "2023-01-01": { "orders_created_at": 10 , "percentage_difference": 57}
  [date: string]: {
    'orders_created_at': number,
    'percentage_difference': number,
  };
}

export interface DashboardOrdersByMonthResponse {
  // Ex: "2023-01-01": { "orders_created_at": 10 , "percentage_difference": 57}
  [date: string]: {
    'orders_created_at': number,
    'percentage_difference': number,
  };
}

export interface DashboardRevenueByDateResponse {
  // Ex: "2023-01-01": { "orders_created_at": 10 , "percentage_difference": 57}
  [date: string]: {
    'revenue_inc_vat': number,
    'revenue_ex_vat': number,
    'percentage_difference': number,
  };
}

export interface DashboardRevenueByMonthResponse {
  // Ex: "2023-01-01": { "orders_created_at": 10 , "percentage_difference": 57}
  [date: string]: {
    'revenue_inc_vat': number,
    'revenue_ex_vat': number,
    'percentage_difference': number,
  };
}

export interface OrderStatusResponse {
  order_status_id: number;
  order_status_name: string;
}

export interface ConfirmationStatusResponse {
  confirmation_status_id: number;
  confirmation_status_name: string;
}

export interface RefundOrderLineResponse {
  refund_order_line_id: number;
  order_line_id: number;
  quantity: number;
  product_name: string;
  product_price_inc_vat: number;
  refund_amount: number;
  refund_vat_amount: number;
  fixed_price: number;
  unit_abbreviation: string;
  total_price_inc_vat: number;
  is_taskable: number;
  discount_order_line: number;
  discounted_order_line_id: number;
  price_rules: PriceRuleResponse[];
  triggered_price_rules: PriceRuleResponse[];
  total_quantity: number;
  total_price_override: number;
}

export interface RefundOrderResponse {
  refund_order_id: number;
  order_id: number;
  refund_amount: number;
  refund_vat_amount: number;
  refund_order_lines: RefundOrderLineResponse[];
  refund_reason: string;
  provider_data: {}
}

export interface InvoiceRecipientTypeResponse {
  invoice_recipient_type_id: number;
  invoice_recipient_type_name: string;
}

export interface EmbedOrderResponse {
  order_id: number;
  order_number: string;
  company_id: string;
  company_name: string;
  total_amount_ex_vat: number;
  total_amount_inc_vat: number;
  total_vat_amount: number;
  discount_amount: number;
  discount_percentage: number;
  discount_reason: string;
  calculated_discount_amount: number;
  sales_price: number;
  sales_price_ex_vat: number;
  sales_price_vat_amount: number;
  execution_at: Date;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
  order_lines: EmbedOrderLineResponse[];
}

export interface EmbedOrderLineResponse {
  order_line_id: number;
  order_id: number;
  execution_at: Date;
  product_id: number;
  product_name: string;
  product_description: string;
  vat_rate: number;
  vat_rate_id: number;
  unit_id: number;
  unit_name: string;
  unit_abbreviation: string;
  quantity: number;
  total_quantity: number;
  product_price_inc_vat: number;
  total_price_inc_vat: number;
  fixed_price: number;
  specifications: OrderCustomerQuestionResponse[];
  price_rules: PriceRuleResponse[];
  triggered_price_rules: PriceRuleResponse[];
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
}

export interface EditBasePriceResponse {
  price_inc_vat: number;
}

export interface OrderLineCompactResponse {
  order_line_id: number;
  product_id: number;
  product_name: string;
}

export interface OrderScheduleResponse {
  order_schedule_id: number;
  order_schedule_number: string;
  schedule_description: string;
  start_time: Date;
  end_time: Date;
  start_date: Date;
  status: number;
  active: number;
  schedule_repeat_type_id: number;
  schedule_repeat_type_name: string;
  weekdays: number[];
  every: number;
  date: number | null;
  nth_weekday: number;
  orders_in_advance: number;
  payment_recipient: PaymentRecipientResponse;
  service_recipient: ServiceRecipientResponse | null;
  partner: OrderPartnerResponse;
  partner_contact: OrderPartnerContactResponse;
  main_product_name: string;
  status_name: string;
  payment_status_id: number;
  payment_status_name: string;
  next_execution_date: Date;
  created_at: Date;
  company_accepted_at: Date | null;
  company_accepted_by: InternalUserResponse | null;
  payment_method_id: number;
  payment_method_name: string;
  sales_price: number;
  sales_price_ex_vat: number;
  sales_price_vat_amount: number;
  enable_customer_notifications: number;
  payment_subscription_authorised_at: Date | null;
  creation_failed: number;
  order_source_id: number;
  order_source_name: string;
  specifications: OrderCustomerQuestionResponse[];
  order_lines: OrderScheduleOrderLineResponse[];
  send_customer_confirmation: number;
  single_affiliate: number;
  has_fixed_payment: number;
  fixed_payment: FixedPaymentResponse | null;
}

export interface FixedPaymentResponse {
  fixed_payment_id: number;
  fixed_payment_number: string;
  total_price_inc_vat: number;
  total_price_ex_vat: number;
  active: number;
  date: number;
  every: number;
  vat_rate_id: number;
  vat_rate_display_name: string;
  payment_method_id: number;
  payment_method_name: string;
  invoice_reference_text: string;
  service_description: string;
  instances: FixedPaymentInstanceResponse[];
}

export interface FixedPaymentInstanceResponse {
  instance_id: number;
  created_at: Date;
  invoice_sent_at: Date | null;
  captured_at: Date | null;
}

export interface OrderScheduleOrderLineResponse {
  order_schedule_order_line_id: number;
  product_id: number;
  product_name: string;
  quantity: number;
  is_taskable: number;
  unit_id: number;
  unit_name: string;
  unit_abbreviation: string;
  total_price_inc_vat: number;
  product_price_inc_vat: number;
  total_price_override: number;
  vat_rate_id: number;
  vat_rate: number;
  price_rules: PriceRuleResponse[];
  triggered_price_rules: PriceRuleResponse[];
  stages: OrderScheduleStageResponse[];
  crew: InternalUserResponse[];
  resources: ResourceResponse[];
}

export interface OrderScheduleStageResponse {
  stage_id: number;
  stage_name: string;
  address: UnitDetails | null;
  sets_quantity: number;
  duplicated: number;
  index: number;
}

export interface ScheduleRepeatTypeResponse {
  schedule_repeat_type_id: number;
  schedule_repeat_type_name: string;
}

export interface ScheduleExecutionDatePreviewResponse {
  schedule_description: string;
  execution_dates: Date[];
}

export interface OrderLogResponse {
  timestamp: Date;
  log_type_id: number;
  log_type_name: string;
  title: string;
  data: {value: string | number, label: string, data_id: string, hidden?: boolean}[];
  user: InternalUserResponse;
}

export interface OrderLogTypeResponse {
  order_log_type_id: number;
  order_log_type_name: string;
}

export interface OrderLineRow extends OrderLineResponse {
  checked: boolean;
}

export interface WorkOrderStatusResponse {
  work_order_status_id: number;
  work_order_status_name: string;
}

export interface DetailsViewSettings {
  modalView?: boolean;
  createView?: boolean;
  repeatingView?: boolean;
  repeatingPlaceholderView?: boolean
  listView?: boolean;
  orderDetailsOrderLinesView?: boolean;
  collapsedOrderLines?: boolean;
  workOrderView?: boolean;
  workOrderTemplateView?: boolean;
  createOrderView?: boolean;
  fromCalendar?: boolean;
  createPaymentView?: boolean;
  contractorView?: boolean;
  contractorNotAcceptedView?: boolean;
  workOrderStandaloneView?: boolean;
  paymentStandaloneView?: boolean;
  paymentView?: boolean;
  consolidatedInvoiceView?: boolean;
  compactView?: boolean;
}

export interface WorkOrderUserRelationResponse extends InternalUserResponse {
  absence_description: string | null;
  accepted_at: Date | null;
  declined_at: Date | null;
}
