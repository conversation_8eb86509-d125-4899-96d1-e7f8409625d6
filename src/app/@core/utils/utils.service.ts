import {Injectable} from '@angular/core';
import {AbstractControl, AsyncValidatorFn, FormControl, FormGroup, ValidationErrors} from '@angular/forms';
import {Observable, of} from 'rxjs';
import {add, differenceInMilliseconds, format, parseISO} from 'date-fns';
import {TranslateService} from "@ngx-translate/core";
import {AddressCompactResponse, AddressSearchResultItemResponse} from "../../@shared/models/address.interfaces";
import {UnitDetails} from "../../@shared/models/input.interfaces";
import {BrregCompanyResponse} from "../../@shared/services/companyData.service";
import {AddressService} from "../../@shared/services/address.service";
import {switchMap} from "rxjs/operators";
import {environment} from "../../../environments/environment";
import * as AWS from "aws-sdk";
import {DatePipe, formatDate, registerLocaleData, TitleCasePipe} from "@angular/common";
import {OrderResponse, OrderResponseCompact, WorkOrderCompactResponse, WorkOrderResponse} from "../../@shared/models/order.interfaces";
import localeNb from '@angular/common/locales/nb';
import {OrderPaymentResponse, OrderPaymentResponseCompact} from "../../@shared/models/payment.interfaces";
import {utcToZonedTime} from 'date-fns-tz'
import {DateTime} from "luxon";

export function escapeHtml(unsafe: string) {
  const div = document.createElement('div');
  div.textContent = unsafe;
  return div.innerHTML;
}

export function createEmptyAddress(): UnitDetails {
  return {
      address_id: null,
      index: null,
      external_id: null,
      created_at: null,
      city: null,
      lat: null,
      lng: null,
      municipality_id: null,
      municipality_name: null,
      street_id: null,
      street: null,
      number: null,
      letter: null,
      postal_code: null,
      area_id: null,
      area: null,

      cadastre: null,

      business_manager: null,
      homeowners_name: null,
      ownership_fraction: null,
      share_number: null,
      stock_number: null,
      organisation_number: null,
      organisation_name: null,
      apartment_number: null,
      section_number: null,
      leasehold: null,
      leasehold_number: null,
      leasehold_start_year: null,
      leasehold_regulation_year: null,
      leasehold_expiry_year: null,

      section_id: null,
      floor: null,
      radon_level: null,
      energy_score: null,
      heating_score: null,
      primary_area: null,
      livable_area: null,
      plot_area: null,
      number_of_floors: null,
      number_of_rooms: null,
      number_of_bathrooms: null,
      number_of_bedrooms: null,
      number_of_units_on_address: null,
      has_elevator: null,
      has_parking: null,
      has_garage: null,
      build_year: null,
      shed_area: null,
      address_name: null,
    }
}

export function upperCaseFirstLetter(value: string | null | undefined): string | null {
  if (!value) return null;
  return value[0].toUpperCase() + value.substring(1).toLowerCase();
}

function convertDateUTCToLocal(date: Date): Date {
  const utcTimestamp = Date.UTC(
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
    date.getHours(),
    date.getMinutes(),
    date.getSeconds(),
    date.getMilliseconds()
  )
  const utcDate = new Date(utcTimestamp)
  return utcToZonedTime(utcDate, 'Europe/Oslo')
}

function convertDateLocalToUTC(date: Date): Date {
  const local = DateTime.fromJSDate(date);
  const osloDateTime = DateTime.fromObject(
    {
      year: local.year,
      month: local.month,
      day: local.day,
      hour: local.hour,
      minute: local.minute,
      second: local.second,
      millisecond: local.millisecond,
    },
    { zone: 'Europe/Oslo' }
  );
  return osloDateTime.toUTC().toJSDate()
}

export function localToUTCDelta() {
  return 0
}

export function convertResponseDatetime(obj: any) {
  for (const prop in obj) {
    if (obj.hasOwnProperty(prop)) {

      // If prop is object, iterate recursively
      if (typeof obj[prop] === 'object' && obj[prop] !== null) {
        convertResponseDatetime(obj[prop]);
      }

      // If prop is string and ISO date, convert to local datetime
      else if (typeof obj[prop] === 'string' && isISODateString(obj[prop])) {
        const isoDateString = obj[prop].replace(' ', 'T');
        const date = new Date(isoDateString);
        // Make sure that the date is not invalid
        if (!isNaN(date.getTime())) {
          obj[prop] = convertDateUTCToLocal(date);
        }
      }
    }
  }
}

export function convertPayloadDatetime(data: any) {
  for (const prop in data) {
    if (data.hasOwnProperty(prop)) {
      if (typeof data[prop] === 'object' && data[prop] !== null) {
        convertPayloadDatetime(data[prop]);
      }
      else if (typeof data[prop] === 'string' && isISODateString(data[prop])) {
        let date = new Date(data[prop]);
        date = convertDateLocalToUTC(date)
        try {
          data[prop] = date.toISOString();
        } catch (e) {
          console.error('Invalid time value in response at convertPayloadDatetime (string)', data[prop]);
          throw e;
        }
      }
      if (data[prop] instanceof Date) {
        data[prop] = convertDateLocalToUTC(data[prop])
        try {
          data[prop] = data[prop].toISOString();
        } catch (e) {
          console.error('Invalid time value in response at convertPayloadDatetime (Date)', data[prop]);
          throw e;
        }
      }
    }
  }
  return data;
}

export function getImageFileFromS3(fileName: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const params = {
      Bucket: environment.s3BucketName,
      Key: fileName
    };

    AWS.config.update({
      region: environment.s3BucketRegion,
      accessKeyId: environment.s3BucketAccessKeyId,
      secretAccessKey: environment.s3BucketSecretAccessKey
    });

    let s3 = new AWS.S3();
    s3.getObject(params, function (err: any, data: any) {
      if (err) {
        console.error(err);
        reject(err);
      } else {
        const userImage = 'data:image/png;base64,' + data.Body.toString('base64');
        resolve(userImage);
      }
    });
  });
}

function isISODateString(value: string): boolean {
  const isoDatePattern = /^\d{4}-\d{2}-\d{2}/;
  return isoDatePattern.test(value);
}

////////////// Date and time formatting //////////////

// 31-05-2024
export function formatDateDMY(date: Date | null, divider: '-' | '.' = '-'){
  date = verifyDate(date);
  if (!(date instanceof Date)) {
    return ''
  }

  if (divider == '.') {
    return format(date, 'dd.MM.yyyy')
  } else {
    return format(date, 'dd-MM-yyyy')
  }
}

// 2024-05-31
export function formatDateYMD(date: Date | null){
  date = verifyDate(date);
  if (!(date instanceof Date)) {
    return ''
  }
  return format(date, 'yyyy-MM-dd')
}

// 31-05-2024 12:00
export function formatDateDMYHHMM(date: Date | null){
  date = verifyDate(date);
  if (!(date instanceof Date)) {
    return ''
  }
  return format(date, 'dd-MM-yyyy - HH:mm')
}

// 12:00
export function formatTimeHM(date: Date | null){
  date = verifyDate(date);
  if (!(date instanceof Date)) {
    return ''
  }
  registerLocaleData(localeNb, 'nb');
  return format(date, 'HH:mm')
}

// 12:00:00
export function formatTimeHMSS(date: Date | null){
  date = verifyDate(date);
  if (!(date instanceof Date)) {
    return ''
  }
  return format(date, 'HH:mm:ss')
}

// Fredag 31.05.24 kl 12:00
export function displayDate(date: Date | null, includeWeekDay: boolean = true, includeTime: boolean = true): string {
  date = verifyDate(date);
  if (!(date instanceof Date)) {
    return ''
  }

  registerLocaleData(localeNb, 'nb');
  const titleCasePipe = new TitleCasePipe();
  const datePipe = new DatePipe(localStorage.getItem('language') === 'no' ? 'nb-NO' : 'en-US');
  const hour = localStorage.getItem('language') === 'no' ? 'kl' : 'at';

  const day = titleCasePipe.transform(datePipe.transform(date, 'EEE'));
  let formattedValue: string | null;
  if (includeTime) {
    formattedValue = datePipe.transform(date, `dd.MM.yy \'${hour}\' HH:mm`);
  } else {
    formattedValue = datePipe.transform(date, `dd.MM.yy`);
  }

  if (includeWeekDay) {
    return `${day} ${formattedValue}`;
  } else {
    return `${formattedValue}`;
  }
}

// Fredag 12. April 2024
export function formatFullDayAndDate(date: Date | null, includeYear: boolean = true, includeDay: boolean = true): string {
  date = verifyDate(date);
  if (!(date instanceof Date)) {
    return ''
  }

  registerLocaleData(localeNb, 'nb');
  const titleCasePipe = new TitleCasePipe();
  const languageSetting = localStorage.getItem('language') === 'no' ? 'nb-NO' : 'en-US';
  const datePipe = new DatePipe(languageSetting);

  const day = titleCasePipe.transform(datePipe.transform(date, 'EEEE'));  // Full name of the day
  const formattedDate = datePipe.transform(date, (includeYear ? 'd. MMMM yyyy' : 'd. MMMM'));  // Day of the month, full month name, and year

  if (!includeDay) {
    return `${formattedDate}`;
  }

  return `${day} ${formattedDate}`;
}

export function verifyDate(date: Date | any): Date | null {
  if (date === null || date === undefined) {
    return null;
  }
  if (!(date instanceof Date)) {
    try {
      date = new Date(date);
      return date;
    } catch (e) {
      console.error('Invalid date format:', date);
      return null;
    }
  }
  return date;
}



export function currencyFormat(number: number | null | undefined, showDecimals: boolean = true): string {
    if (number === null || number === undefined) {
      number = 0;
    }
    const decimals = showDecimals ? 2 : 0;
    const formattedNumber = number.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ' ').replace('.', ',');
    return (showDecimals ? '' : '') + formattedNumber + (showDecimals ? '' : ',-');
  }

export function getFormControl(formGroup: FormGroup, controlName: string): FormControl {
  return formGroup.get(controlName) as FormControl;
}

export function capitaliseFirstLetter(string: string | null | undefined) {
  if (typeof string !== 'string') {
    return string;
  }
  return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
}

export function capitalizeFirstLetterOfAllWords(string: string | null | undefined) {
  if (typeof string !== 'string') {
    return string;
  }
  return string.toLowerCase().replace(/\b\w/g, (char) => char.toUpperCase());
}

export function getPaymentStatusColor(payment: OrderPaymentResponse) {
    // If payment is invoice, but status "Sent" (0) instead of "Invoice sent" (6), return danger as the invoice should have been sent but has not
    if (payment.payment_status_id === 0 && payment.payment_method_id === 10) {
      return 'text-danger'
    }

    const paymentStatusColorMap: {[key: string]: string} = {
      '-1': 'text-muted',
      '0': '',
      '1': '',
      '2': 'text-warning',
      '3': 'text-success',
      '4': 'text-danger',
      '5': 'text-danger',
      '6': 'text-warning',
      '7': '',
      '10': 'text-warning',
      '11': 'text-warning'
    }
    return paymentStatusColorMap[payment.payment_status_id.toString()];
  }

export function convertSearchItemToUnitDetails(item: AddressSearchResultItemResponse | null): UnitDetails | null {
  if (item === null) {
    return null
  }

  let addressInput: UnitDetails = createEmptyAddress();

  addressInput.street = item.street;
  addressInput.number = item.number;
  addressInput.letter = item.letter;
  addressInput.postal_code = item.postal_code;
  addressInput.city = item.city;
  addressInput.display = item.display;
  addressInput.external_id = item.unit_id;

  return addressInput
}

export function workOrderBadgeStatus(workOrder: WorkOrderResponse | WorkOrderCompactResponse): string {
  let statsClass;
  let status_id = workOrder.work_order_status_id
  let status_name = workOrder.work_order_status_name
  switch (status_id) {
    // Not started
    case 0:
      statsClass = "badge bg-secondary";
      break;

    // Started
    case 1:
      statsClass = "badge bg-warning";
      break;

    // Finished
    case 2:
      statsClass = "badge bg-success";
      break;

    // Cancelled
    case 8:
      statsClass = "badge bg-danger";
      break;

    default:
      statsClass = "";
  }
  return `<span class="${statsClass} badge-min-width">${status_name}</span>`
}

export function orderBadgeStatus(order: OrderResponse | OrderResponseCompact): string {
  let statsClass;
  let status_id = order.order_status_id
  let status_name = order.order_status_name

  if (order.repeating && [3, 4, 5].includes(status_id)) {
    return `<span class="badge bg-success badge-min-width">${status_name}</span>`;
  }


  switch (status_id) {
    // Draft
    case -1:
      statsClass = "badge bg-secondary";
      break;

    // Lead
    case 0:
      statsClass = "badge custom-badge-order-color";
      break;

    // Accepted
    case 1:
      statsClass = "badge bg-warning";
      break;

    // Ready
    case 2:
      statsClass = "badge bg-success";
      break;

    // Assigned
    case 3:
      statsClass = "badge bg-primary";
      break;

    // Ongoing
    case 4:
      statsClass = "badge bg-warning text-dark";
      break;

    // Completed
    case 5:
      statsClass = "badge bg-info";
      break;

    // Payment ready
    case 6:
      statsClass = "badge bg-danger";
      break;

    // Closed
    case 7:
      statsClass = "badge bg-dark";
      break;

    // Cancelled
    case 8:
      statsClass = "badge bg-dark";
      break;

    // Invoice sent
    case 9:
      statsClass = "badge bg-warning text-dark";
      break;

    // Queued in consolidated invoice
    case 10:
      statsClass = "badge bg-warning text-dark";
      break;
    default:
      statsClass = "bg-light text-dark";
  }
  return `<span class="${statsClass} badge-min-width">${status_name}</span>`
}

export function favouriteStar(enabled: boolean, readOnly: boolean = false): string {
  let enabledClass = enabled ? 'fa-solid enabled' : 'fa-regular';
  let readOnlyClass = readOnly ? 'read-only' : '';
  return `<i class="${enabledClass} fa-star favourite-star ${readOnlyClass}"></i>`;
}

export function paymentStatusBadge(order: OrderResponse | OrderResponseCompact | OrderPaymentResponse | OrderPaymentResponseCompact, returnClass = false): string {
  // Define a map from payment_status_id to CSS class
  const classMap: { [key: string]: string } = {
    '-2': 'badge-payment-no-payment', // No payment
    '-1': 'badge-payment-open', // Open
    '0': 'badge-payment-open', // Open
    '1': 'badge-payment-initiated', // Initiated
    '2': 'badge-payment-reserved', // Reserved
    '3': 'badge-payment-captured', // Captured
    '4': 'badge-payment-user-declined', // User declined
    '5': 'badge-payment-provider-declined', // Provider declined
    '6': 'badge-payment-invoice-sent', // "Invoice sent
    '7': 'badge-payment-queued', // Queued in consolidated invoice
    '8': 'badge-payment-fixed', // Fixed payment
    '9': 'badge-payment-partially-paid', // Partially paid
    '10': 'badge-payment-partially-refunded', // Partially refunded
    '11': 'badge-payment-refunded', // Refunded
    '12': 'badge-payment-credited', // Closed
  };

  // Determine the appropriate class based on payment_status_id
  const badgeClass = classMap[order.payment_status_id.toString()]; // Use a default class if no specific mapping exists

  if (returnClass) {
    return badgeClass
  }

  return `<span class="badge ${badgeClass} badge-min-width">${order.payment_status_name}</span>`;
}

export function asyncPhoneNumberValidator(): AsyncValidatorFn {
  return (control: AbstractControl): Promise<ValidationErrors | null> | Observable<ValidationErrors | null> => {
    const phoneNumber = control.value;
    const isValid = /^\d{8}$/.test(phoneNumber);

    return new Observable((observer) => {
      if (isValid) {
        observer.next(null); // Phone number is valid, emit null
      } else {
        observer.next({ invalidPhoneNumber: true }); // Phone number is invalid, emit validation error object
      }
      observer.complete();
    });
  };
}


export function displayPhone(phoneNumber: string | null | undefined): string {
  // If phone number is null, return an empty string
  if (!phoneNumber) {
    return '';
  }

  const countryCode = '+47';

  if (phoneNumber.startsWith(countryCode)) {
      const remainingNumber = phoneNumber.slice(countryCode.length);

      // Format the remaining number into pairs
      const formattedNumber = remainingNumber.replace(/(\d{2})(?=\d)/g, '$1 ');

      // Combine the country code with the formatted number
      return `${countryCode} ${formattedNumber}`.trim();
  }

  // If the number doesn't start with +47, return it as is
  return phoneNumber;

}

@Injectable({
  providedIn: 'root',
})
export class UtilsService {

  constructor(private translate: TranslateService, private addressService: AddressService) {}

  formatDateWdDYM(date: Date | null, useSlashSeparator: boolean = true, includeWeekday: boolean = true): string {
    if (date === null) {
      return '';
    }

    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Month is 0-based
    const year = date.getFullYear().toString().slice(-2);
    const weekday = this.translate.instant('WEEKDAYS.' + date.getDay());
    const separator = useSlashSeparator ? '/' : '.';

    return includeWeekday ? `${weekday}, ${day}${separator}${month}${separator}${year}` : `${day}${separator}${month}${separator}${year}`;
  }

  formatDurationFromHours(inputHours: number | null){
    if (inputHours === null) {
      return '';
    }
    let hours = Math.floor(inputHours);
    let minutes = Math.round((inputHours - hours) * 60);
    const minutesText = 'min';
    if (hours === 0) {
      return minutes + ' min'
    }
    if (minutes === 60) {
      hours += 1;
      minutes = 0;
    }
    return minutes > 0 ? `${hours}${this.translate.instant('HOUR-ABBREVIATION')} ${minutes} ${minutesText}` : `${hours}${this.translate.instant('HOUR-ABBREVIATION')}`;
  }

  formatDurationFromSeconds(seconds: number | null): string {
    if (seconds === null) {
      return '';
    }
    return this.formatDurationFromHours(seconds / 3600);
  }

  getAddressFromBrregResponse(company: BrregCompanyResponse): Observable<UnitDetails> {
    // Determine the address to use
    const addressToUse = company.businessAddress.address.length > 0 ? company.businessAddress : company.mailingAddress;
    let searchAddress = `${addressToUse.address} ${addressToUse.postalCode} ${addressToUse.city}`;

    return this.addressService.searchAddress(searchAddress, true).pipe(
      switchMap((res) => {
        if (res.length > 0) {
          let unitId = res[0].unit_id;
          return this.addressService.getAddressDetails(unitId).pipe(
            switchMap((res) => {
              let sectionKeys = Object.keys(res.sections);
              let addressResult: UnitDetails = sectionKeys.length > 0 ? res.sections[sectionKeys[0]] : res.unit!;
              return of(addressResult);
            })
          );
        } else {
          // If address is not found, create new UnitDetails object
          let addressResult: UnitDetails = createEmptyAddress();
          const pattern = /^(.*?)(\d+)([A-Za-z]*)$/;
          const match = addressToUse.address.trim().match(pattern);

          if (match) {
            addressResult.street = match[1].trim();
            addressResult.number = match[2];
            addressResult.letter = match[3];
          } else {
            addressResult.street = addressToUse.address;
          }

          addressResult.postal_code = addressToUse.postalCode;
          addressResult.city = addressToUse.city;
          addressResult.display = `${addressResult.street} ${addressResult.number ? addressResult.number : ''}${addressResult.letter ? addressResult.letter : ''}, ${addressResult.postal_code} ${addressResult.city}`;

          return of(addressResult);
        }
      })
    );
  }

  getRelativeTime(date: Date): string {
    const currentDate = new Date();
    const inputDate = new Date(date);

    const elapsed = currentDate.getTime() - inputDate.getTime();
    const seconds = Math.floor(elapsed / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return days.toString() + ' ' + this.translate.instant('orders.orderDetails.orderNotes.daysAgo');
    } else if (hours > 0) {
      return hours.toString() + ' ' + this.translate.instant('orders.orderDetails.orderNotes.hoursAgo');
    } else if (minutes > 0) {
      return minutes.toString() + ' ' + this.translate.instant('orders.orderDetails.orderNotes.minutesAgo');
    } else {
      return this.translate.instant('orders.orderDetails.orderNotes.justNow');
    }
  }

}

export function convertCompactAddressToUnitDetails(compactAddress: AddressCompactResponse | null): UnitDetails | null {
  if (compactAddress === null) {
    return null
  }

  let addressInput: UnitDetails = createEmptyAddress();

  addressInput.address_id = compactAddress.address_id;
  addressInput.city = compactAddress.city;
  addressInput.lat = compactAddress.lat;
  addressInput.lng = compactAddress.lng;
  addressInput.municipality_name = compactAddress.municipality_name;
  addressInput.street = compactAddress.street;
  addressInput.number = compactAddress.number;
  addressInput.letter = compactAddress.letter;
  addressInput.postal_code = compactAddress.postal_code;
  addressInput.display = compactAddress.display;

  return addressInput
}


  //  Compresses and resizes an image to JPEG format.
export function resizeAndConvertToJpeg(inputFile: File, quality: number): Promise<Blob> {

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    const maxWidth = 1920
    const maxHeight = 1080

    reader.onload = function (event) {
      const img = new Image();

      img.onload = function () {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d')!;

        let width = img.width;
        let height = img.height;

        // Calculate new dimensions while preserving aspect ratio
        if (width > maxWidth) {
          height *= maxWidth / width;
          width = maxWidth;
        }

        if (height > maxHeight) {
          width *= maxHeight / height;
          height = maxHeight;
        }

        canvas.width = width;
        canvas.height = height;

        ctx.drawImage(img, 0, 0, width, height);

        // Convert the canvas to a data URL with JPEG format
        canvas.toBlob(
          (blob) => {
            resolve(new File([blob!], inputFile.name, { type: 'image/jpeg' }));
          },
          'image/jpeg',
          quality
        );
      };

      img.src = event.target!.result as string;
    };

    reader.onerror = function (event) {
      reject(new Error('Error reading file.'));
    };

    reader.readAsDataURL(inputFile);
  });

}
