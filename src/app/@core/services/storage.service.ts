import {HostListener, Injectable} from '@angular/core';
import {UserEntityRelationWithoutUserDataResponse, UserOptionsResponse} from "../../@shared/models/user.interfaces";
import {UserService} from "./user.service";
import {_CRM_ORD_168, CRM_ORD_168, USM_ENT_3, USM_USR_22} from "../../@shared/models/input.interfaces";
import {EndpointService} from "../../@shared/services/endpoints.service";
import {BehaviorSubject, forkJoin, lastValueFrom, Observable, of} from "rxjs";
import {ToastService} from "./toast.service";
import {OrderResponse} from "../../@shared/models/order.interfaces";
import {CompanyApplicationResponse} from "../../@shared/models/applications.interfaces";
import {ApplicationService} from "../../@shared/services/application.service";
import {TopBarSetupComponent} from "../../pages/superadmin/top-bar-setup/top-bar-setup.component";
import {TopBarSetupResponse} from "../../@shared/models/global.interfaces";
import {filter, map} from "rxjs/operators";
import {WorkOrderTemplateResponse} from "../../@shared/models/templates.interfaces";
import {CompanyGeneralSettingsResponse} from "../../@shared/models/company.interfaces";

const USER_KEY = 'auth-user';
const COMPANY_KEY = 'selected-company';
const ORDER = "order"

@Injectable({
  providedIn: 'root'
})
export class StorageService {
  session = window.sessionStorage;
  local = window.localStorage;
  html: Element;
  constructor(private userService: UserService, private endpointService: EndpointService, private toastService: ToastService) {
    this.html = document.getElementsByTagName('html')[0]
  }

  private leftSideMenuCollapsedSubject = new BehaviorSubject<boolean>(false);
  leftSideMenuCollapsed$ = this.leftSideMenuCollapsedSubject.asObservable();

  private mobileViewSubject = new BehaviorSubject<boolean>(false);
  mobileView$ = this.mobileViewSubject.asObservable();

  // Application observables
  private applicationsLoadedSubject = new BehaviorSubject<boolean>(false);
  applicationsLoaded$ = this.applicationsLoadedSubject.asObservable();

  private accountingEnabledSubject = new BehaviorSubject<boolean>(false);
  accountingEnabled$ = this.accountingEnabledSubject.asObservable();

  private salaryAccountingEnabledSubject = new BehaviorSubject<boolean>(false);
  salaryAccountingEnabled$ = this.salaryAccountingEnabledSubject.asObservable();

  private tripletexEnabledSubject = new BehaviorSubject<boolean>(false);
  tripletexEnabled$ = this.tripletexEnabledSubject.asObservable();

  private powerOfficeEnabledSubject = new BehaviorSubject<boolean>(false);
  powerOfficeEnabled$ = this.powerOfficeEnabledSubject.asObservable();

  private fikenEnabledSubject = new BehaviorSubject<boolean>(false);
  fikenEnabled$ = this.fikenEnabledSubject.asObservable();

  private projectsEnabledSubject = new BehaviorSubject<boolean>(false);
  projectsEnabled$ = this.projectsEnabledSubject.asObservable();

  private departmentsEnabledSubject = new BehaviorSubject<boolean>(false);
  departmentsEnabled$ = this.departmentsEnabledSubject.asObservable();

  private resourcesEnabledSubject = new BehaviorSubject<boolean>(false);
  resourcesEnabled$ = this.resourcesEnabledSubject.asObservable();

  private partnersEnabledSubject = new BehaviorSubject<boolean>(false);
  partnersEnabled$ = this.partnersEnabledSubject.asObservable();

  private timeTrackingEnabledSubject = new BehaviorSubject<boolean>(false);
  timeTrackingEnabled$ = this.timeTrackingEnabledSubject.asObservable();

  private reportinatorEnabledSubject = new BehaviorSubject<boolean>(false);
  reportinatorEnabled$ = this.reportinatorEnabledSubject.asObservable();

  // Calendar observables
  private calendarOptionsSubject = new BehaviorSubject<any>({});
  calendarOptions$ = this.calendarOptionsSubject.asObservable();

  private calendarSelectedDateSubject = new BehaviorSubject<Date>(new Date());
  calendarSelectedDate$ = this.calendarSelectedDateSubject.asObservable();

  leftSideMenuWidthSubject = new BehaviorSubject<number>(0);
  leftSideMenuWidth$ = this.leftSideMenuWidthSubject.asObservable();

  topBarSetupSubject = new BehaviorSubject<any>(null);
  topBarSetup$: Observable<TopBarSetupResponse> = this.topBarSetupSubject.asObservable().pipe(filter((value) => value !== undefined))

  // Company general settings
  private showEmployeeInitialsSubject = new BehaviorSubject<boolean>(false);
  showEmployeeInitials$ = this.showEmployeeInitialsSubject.asObservable();

  private customerCanOnlyAcceptSubject = new BehaviorSubject<boolean>(false);
  customerCanOnlyAccept$ = this.customerCanOnlyAcceptSubject.asObservable();

  private companyPaymentRemindersActive = new BehaviorSubject<boolean>(true);
  companyPaymentRemindersActive$ = this.companyPaymentRemindersActive.asObservable();

  private operateExVatSubject = new BehaviorSubject<boolean>(false);
  operateExVat$ = this.operateExVatSubject.asObservable();

  private defaultRepeatingOrdersSubject = new BehaviorSubject<boolean>(false);
  defaultRepeatingOrders$ = this.defaultRepeatingOrdersSubject.asObservable();

  private requireCrewWorkOrderConfirmationSubject = new BehaviorSubject<boolean>(false);
  requireCrewWorkOrderConfirmation$ = this.requireCrewWorkOrderConfirmationSubject.asObservable();


  // Generic subjects
  private workOrderTemplatesSubject = new BehaviorSubject<WorkOrderTemplateResponse[]>([]);
  workOrderTemplates$ = this.workOrderTemplatesSubject.asObservable();

  private contractorWorkOrdersSubject = new BehaviorSubject<number>(0);
  contractorWorkOrders$ = this.contractorWorkOrdersSubject.asObservable();

  private _isMenuOpen = new BehaviorSubject<boolean>(false);
  isMenuOpen$ = this._isMenuOpen.asObservable();


  toggleNotifications(): void {
    this._isMenuOpen.next(!this._isMenuOpen.value);
  }

  closeNotifications(): void {
    this._isMenuOpen.next(false);
  }

  public setLeftSideMenuCollapsed(value: boolean) {
    let collapseType = 'condensed'
    this.session.setItem('leftSideMenuCollapsed', value.toString());
    this.leftSideMenuCollapsedSubject.next(value);
    // this.html.setAttribute('data-sidenav-size', value ? collapseType : 'default');
  }

  public setMobileView(value: boolean) {
    this.mobileViewSubject.next(value);
  }

  clean(): void {
    this.session.clear();
    this.local.clear();
  }

  public isLoggedIn(): boolean {
    const user = this.local.getItem(USER_KEY);
    return !!user;
  }

  public ownedByCompany(companyId: string): boolean {
    return this.getSelectedCompanyId() === companyId;
  }

  public saveSelectedCompany(company: UserEntityRelationWithoutUserDataResponse, pushSelection: boolean): Observable<any> {
    this.applicationsLoadedSubject.next(false);
    this.local.removeItem(COMPANY_KEY);
    this.local.setItem(COMPANY_KEY, JSON.stringify(company));
    this.registerApplicationsAndGeneralSettings();
    this.registerWorkOrderTemplates();
    this.registerContractorWorkOrders();
    this.registerCompanyPaymentRemindersActive();

    if (pushSelection) {
      let payload: USM_USR_22 = {
        last_selected_company_id: company.entity_id
      }
      return this.endpointService.usm_usr_22(payload).pipe();
    } else {
      return of(null)
    }
  }

  public registerApplicationsAndGeneralSettings(): void {
    // Fetch enabled applications for the company
    try {
      if (!this.getSelectedCompanyId(true)) {
        return;
      }
    } catch (e) {
      return;
    }
    this.applicationsLoadedSubject.next(false);

    forkJoin({
      appRes: this.endpointService.crm_app_1({company_id: this.getSelectedCompanyId()}),
      genRes: this.endpointService.crm_coy_23({company_id: this.getSelectedCompanyId()})}
    ).subscribe(({appRes, genRes}) => {
      let enabledApplicationIds = appRes.data.filter(app => app.enabled === 1).map(app => app.application_id);
      let generalSettings = genRes.data;

      // Set general settings values
      this.showEmployeeInitialsSubject.next(generalSettings.show_employee_initials_only === 1);
      this.customerCanOnlyAcceptSubject.next(generalSettings.customer_can_only_accept === 1);
      this.operateExVatSubject.next(generalSettings.operate_ex_vat);
      this.defaultRepeatingOrdersSubject.next(generalSettings.default_repeating_orders);
      this.requireCrewWorkOrderConfirmationSubject.next(generalSettings.require_crew_work_order_confirmation);

      // Check if accounting applications are enabled
      this.accountingEnabledSubject.next((enabledApplicationIds.includes(0) || enabledApplicationIds.includes(1)) || enabledApplicationIds.includes(9));

      // Check if salary accounting applications are enabled
      this.salaryAccountingEnabledSubject.next((enabledApplicationIds.includes(0) || enabledApplicationIds.includes(1)));

      // Check if tripletex applications are enabled
      this.tripletexEnabledSubject.next(enabledApplicationIds.includes(0));

      // Check if poweroffice applications are enabled
      this.powerOfficeEnabledSubject.next(enabledApplicationIds.includes(1));

      // Check if fiken applications are enabled
      this.fikenEnabledSubject.next(enabledApplicationIds.includes(9));

      // Check if resources are enabled
      this.resourcesEnabledSubject.next(enabledApplicationIds.includes(4));

      // Check if subcontractors are enabled
      // this.subContractorsEnabledSubject.next(enabledApplicationIds.includes(5));

      // Check if partners are enabled
      this.partnersEnabledSubject.next(enabledApplicationIds.includes(6));

      // Check if time tracking is enabled
      this.timeTrackingEnabledSubject.next(enabledApplicationIds.includes(7));

      // Check if projects are enabled
      this.projectsEnabledSubject.next(enabledApplicationIds.includes(10));

      // Check if departments are enabled
      this.departmentsEnabledSubject.next(enabledApplicationIds.includes(10) && generalSettings.default_department_id === null && !enabledApplicationIds.includes(9));

      // Check if reportinator is enabled
      this.reportinatorEnabledSubject.next(enabledApplicationIds.includes(8));

      this.applicationsLoadedSubject.next(true);
    });
  }

  public getSelectedCompany(): any {
    const company = this.local.getItem(COMPANY_KEY);
    if (company) {
      return JSON.parse(company);
    }
    return {};
  }

  public registerContractorWorkOrders(): void {
    try {
      if (!this.getSelectedCompanyId(true)) {
        return;
      }
    } catch (e) {
      return;
    }

    let params: CRM_ORD_168 = {
      company_id: this.getSelectedCompanyId(),
      paginate: 1,
      unanswered_only: true,
      page: 1,
      limit: 1,
    }
    this.endpointService.crm_ord_168(params).subscribe((data) => {
      this.contractorWorkOrdersSubject.next(data.total_items);
    });
  }

  public registerWorkOrderTemplates(): void {
    try {
      if (!this.getSelectedCompanyId(true)) {
        return;
      }
    } catch (e) {
      return;
    }

    this.endpointService.crm_tmp_26({company_id: this.getSelectedCompanyId()}).subscribe((res) => {
      this.workOrderTemplatesSubject.next(res.data);
    });
  }

  public registerCompanyPaymentRemindersActive(): void {
    try {
      if (!this.getSelectedCompanyId(true)) {
        return;
      }
    } catch (e) {
      return;
    }

    this.endpointService.crm_coy_12({company_id: this.getSelectedCompanyId()}).subscribe((res) => {
      let active = false;
      let firstReminder = res.data.find((reminder) => reminder.notification_type_id === 7);
      let secondReminder = res.data.find((reminder) => reminder.notification_type_id === 8);
      if ((firstReminder && firstReminder.enabled == 1) || (secondReminder && secondReminder.enabled == 1)) {
        active = true
      }
      this.companyPaymentRemindersActive.next(active);
    });
  }

  public updateWorkOrderTemplates(templates: WorkOrderTemplateResponse[]): void {
    this.workOrderTemplatesSubject.next(templates);
  }

  public getSelectedCompanyId(silent= false): string {
    const company = this.local.getItem(COMPANY_KEY);
    if (company) {
      return JSON.parse(company).entity_id;
    } else {
      if (!silent) {
        // this.toastService.errorToast('missing_company_id')
      }
      throw new Error('No company selected - getSelectedCompanyId()');
    }
  }

  updateTopBarSetup() {
    this.endpointService.crm_ser_33().subscribe((res) => {
      this.topBarSetupSubject.next(res.data);
    });
  }


  // --------------------------------------- | CalendarOptions | ---------------------------------------
  // setCalendarOptions(options: UserOptions) {
  //   this.calendarOptionsSubject.next(options);
  //
  //   let payload: USM_USR_22 = {
  //     ...options.userOptions
  //   }
  //   this.endpointService.usm_usr_22(payload).subscribe((res) => {
  //
  //   })
  // }

  // setCalendarSelectedDate(date: Date) {
  //   this.calendarSelectedDateSubject.next(date);
  // }
  //
  // setLeftSideMenuWidth(width: number) {
  //   this.leftSideMenuWidthSubject.next(width);
  // }

}

export interface UserOptions {
  userOptions: UserOptionsResponse;
  selected_date: Date;
  show_unassigned: boolean;
  users: any[];
  order_statuses: number[];
}
