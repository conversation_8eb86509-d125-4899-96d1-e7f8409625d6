export const environment = {
  envName: 'sandbox',
  production: false,
  usermanagementApiUrl: 'https://sandbox-usermanagement.between.as/',
  crmApiUrl: 'https://sandbox-crmplayground-api.between.as/',
  reportinatorApiUrl: 'https://sandbox-reportinator.between.as/',
  customerUrl: 'https://sandbox-customer.between.as/',
  crewUrl: 'https://app.between.as/',
  coreUrl: 'https://sandbox-core.between.as/',
  defaultLanguage: 'en',
  supportedLanguages: ['en'],
  s3BucketAccessKeyId: "********************",
  s3BucketSecretAccessKey: "N/v0caJ0PRhY/C+jsJFi2bO3qgTLZEEVKz/lsYgy",
  s3BucketRegion: "eu-west-1",
  s3BucketName: "development-between",
  hotjarId: '3590348',
  standardPageSize: 25,
  intercomSecretKey: 'Z_BfDzIDEe7azdMVFB2s1XJyY8pUFF7RDdPKoy9l',
  firebaseConfig : {
    apiKey: "AIzaSyD0xL4jZj5TVlWzKJTFL7QZTPYHIO16npA",
    authDomain: "between-crew.firebaseapp.com",
    projectId: "between-crew",
    storageBucket: "between-crew.appspot.com",
    messagingSenderId: "32072158022",
    appId: "1:32072158022:web:a1e32d7ee4b4bb20eff347",
    measurementId: "G-TG5B8RZEMR",
    vapidKey:"BH80OBSJjxt3t9SHqzOMeVf_g4IBAT5Y1FkSEqjaIJ57N3wQvSqtjh2-fXGL2bokz7ygwjpEu0NkoLypM92AcpM"
  }
};



