/* You can add global styles to this file, and also import other style files */
@import "assets/css/custom.css";
@font-face {
  font-family: 'MyFont';
  src: url('assets/fonts/unicons.ttf');
}

/* Font Awesome Pro is now loaded via npm package */

/*.content-page {*/
/*  min-height: 0 !important;*/
/*}*/


:root {
  --primary-color: #448C74;
  --secondary-color: #EDF1F5;
  --warning-color: #F8DB83;
  --light-green: #E6F2EE;
  --body-text-color: #576071;
  --heading-text-color: #121519;
  --light-bg-color: #F6F9FC;
  --disabled-primary: #A3C5B8
}

.color-header, .fw-bold{
  color: #313A46!important;
}

.grey-text {
  color: var(--body-text-color);
}

.cursor-pointer{
  cursor: pointer;
}

.btn-primary {
  background-color: #448C74 !important;
  border-color: #448C74 !important;
}

.btn {
  box-shadow: none !important;
  --ct-btn-border-radius: 0.30rem !important;
}

.btn-primary:hover {
  background-color: #3d7e68 !important;
  border-color: #397561 !important;
  box-shadow: rgba(56, 115, 95, 0.5) 0px 2px 6px 0px !important;
}

.btn-outline-primary {
  color: #448C74 !important;
  border-color: #448C74 !important;
  background-color: transparent !important;
}

.btn-outline-warning {
  color: #F8DB83 !important;
  border-color: #F8DB83 !important;
  background-color: transparent !important;
}

.btn-outline-primary:hover{
  background-color: #448C74 !important;
  color: #fff !important;
  border-color: #448C74 !important;
  box-shadow: rgba(68, 140, 116, 0.5) 0px 2px 6px 0px !important;
}

.btn-outline-warning:hover{
  background-color: #F8DB83 !important;
  color: #fff !important;
  border-color: #F8DB83 !important;
  box-shadow: rgba(248, 219, 131, 0.5) 0px 2px 6px 0px !important;
}


.link-primary {
  color: #448C74 !important;
  text-decoration: none !important;
  border: 1px solid #448C74 !important;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: transparent !important;
  display: inline-block;
}

.link-primary:hover {
  background-color: #448C74 !important;
  color: #fff !important;
  box-shadow: rgba(68, 140, 116, 0.5) 0 2px 6px 0 !important;
  text-decoration: none !important;
}


.form-check-input:checked {
  background-color: #448C74 !important;  /* Green color */
  border-color: #448C74 !important;      /* Green border */
}

.form-check-input {
  border-radius: 0.25rem; /* Optional, for smoother look */
}

.between-color-bg {
  background-color: #448C74 !important;
}

.between-color {
  color: #448C74 !important;
}

.details-card {
  border: 1px solid #DEE2E6;
  border-radius: 10px;
}

.scrollable-modal-body {
  max-height: 70vh;
  overflow-y: scroll;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

.b-toast-progress {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  height: 3px;
  background-color: #448C74!important;
  animation-name: progress;
  animation-timing-function: linear;
}

.profile-container {
  width: 100%;
  max-width: 1140px;
  margin-top: 1rem;
  padding-left: 24px;
  transition: max-width 0.5s ease, padding-left 0.5s ease;
}


@media (max-width: 1439px) {
  .profile-container {
    max-width: 1140px;
    padding-right: 28px ;
  }
}

@media (max-width: 1269px) {
  .profile-container {
    max-width: 1140px;
    padding-left: 0;
    padding-right: 12px
  }
}
/*@media (max-width: 1439px) {*/
/*  .profile-container {*/
/*    max-width: 992px;*/
/*  }*/
/*}*/

/*@media (max-width: 1269px) {*/
/*  .profile-container {*/
/*    max-width: 710px;*/
/*    padding-left: 0;*/
/*  }*/
/*}*/

:root {
  --ct-bg-leftbar: #fff;
  --ct-menu-item: #6c757d;
  --ct-menu-item-hover: #448C74;
  --ct-menu-item-active: #448C74;
  --ct-bg-leftbar-gradient: linear-gradient(135deg, #6A9D8C 0%, #448C74 60%);
}


body[data-leftbar-theme=light] {
  --ct-bg-leftbar: #fff;
  --ct-menu-item: #6c757d;
  --ct-menu-item-hover: #448C74;
  --ct-menu-item-active: #448C74;
}

.leftside-menu {
  transition: margin-left 0.3s ease;
  width: 260px !important;
}

@media (max-width: 767.98px) {
  body {
     overflow-x: hidden;
   }
  .leftside-menu {
    box-shadow: var(--ct-box-shadow);
    display: flex;
    z-index: 10 !important;
    padding-top:70px;
    height: 100%;
    display: none
  }
  .navbar-custom{
    margin-left:0px!important;
  }
  .sidebar-enable .leftside-menu {
    display: block;
    transition: margin-left 0.3s ease;
  }
  .navbar-nav.navbar-right {
    float: right;
  }

  .content-page {
    margin-left: 0 !important;
    padding: 0 !important;
  }
}

.full-width-content-page {
  margin-left: 0 !important;
  padding: 0 !important;
}

.offscreen-quote-pdf {
  position: absolute;
  left: -9999px;
}

body[data-leftbar-theme=light] .leftside-menu {
  background: var(--ct-bg-leftbar);
}
body[data-leftbar-theme=light] .leftside-menu .logo {
  background: var(--ct-bg-leftbar) !important;
}
body[data-leftbar-theme=light] .side-nav .side-nav-link {
  color: var(--ct-menu-item);
}
body[data-leftbar-theme=light] .side-nav .side-nav-link:hover, body[data-leftbar-theme=light] .side-nav .side-nav-link:focus, body[data-leftbar-theme=light] .side-nav .side-nav-link:active {
  color: var(--ct-menu-item-hover);
}
body[data-leftbar-theme=light] .side-nav .menuitem-active > a {
  color: var(--ct-menu-item-active) !important;
}
body[data-leftbar-theme=light] .side-nav .side-nav-title {
  color: var(--ct-menu-item);
}
body[data-leftbar-theme=light] .side-nav .side-nav-second-level li a,
body[data-leftbar-theme=light] .side-nav .side-nav-third-level li a,
body[data-leftbar-theme=light] .side-nav .side-nav-forth-level li a {
  color: var(--ct-menu-item);
}
body[data-leftbar-theme=light] .side-nav .side-nav-second-level li a:focus, body[data-leftbar-theme=light] .side-nav .side-nav-second-level li a:hover,
body[data-leftbar-theme=light] .side-nav .side-nav-third-level li a:focus,
body[data-leftbar-theme=light] .side-nav .side-nav-third-level li a:hover,
body[data-leftbar-theme=light] .side-nav .side-nav-forth-level li a:focus,
body[data-leftbar-theme=light] .side-nav .side-nav-forth-level li a:hover {
  color: var(--ct-menu-item-hover);
}
body[data-leftbar-theme=light] .side-nav .side-nav-second-level li.active > a,
body[data-leftbar-theme=light] .side-nav .side-nav-third-level li.active > a,
body[data-leftbar-theme=light] .side-nav .side-nav-forth-level li.active > a {
  color: var(--ct-menu-item-active);
}

.badge-min-width{
  min-width: 90px ;
}


/* Global styles for custom badge colors in order list*/
.badge-payment-open,
.badge-payment-initiated,
.badge-payment-credited,
.badge-payment-user-declined,
.badge-payment-provider-declined {
  background-color: #EB9191 !important;
  color: #ffffff !important;
}

.badge-payment-reserved,
.badge-payment-captured {
  background-color: #8EDBCA !important;
  color: #ffffff !important;
}

.badge-payment-invoice-sent,
.badge-payment-queued,
.badge-payment-partially-refunded,
.badge-payment-partially-paid,
.badge-payment-refunded {
  background-color: #E8BA7A !important;
  color: #ffffff !important;
}

.badge-payment-partially-refunded,
.badge-payment-refunded {
  background-color: #E8BA7A !important;
  color: #2c2c2c !important;
}

.badge-payment-no-payment,
.badge-payment-fixed {
  background-color: black !important;
  color: white !important;
}

div.table-container{
  max-width:1180px;
}

.f-14{
  font-size: 14px;
  color: #313A46;
}

.f-16{
  font-size: 16px;
  color: #313A46;
}

.text-bold{
  font-weight: bold;
}

.flag-icon {
  width: 24px;
  height: 16px;
  background-size: cover;
  display: inline-block;
}

td {
  color: #6C757D !important;
}

th {
  color: #313A46 !important;
}

td.fw-bold {
  color: #313A46 !important;
}

.reportinator-list-item {
  border-bottom: 1px solid #DEE2E7;
  height: 44px;
}

.reportinator-list-item:hover {
  background-color: #F5F6F8;
}

.reportinator-list-item:last-child {
  border-bottom: none;
}

.reportinator-list-item:last-child:hover {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-bottom: none;
}

.reportinator-list-item:first-child:hover {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  border-bottom: none;
}


.side-nav-item.active a {
  border-radius: 0 !important;/* Set border-radius to 0 */
}

.clickable-text {
  cursor: pointer;
}

.clickable-text:hover {
  text-decoration-line: underline;
  font-weight: 600;
}

.favourite-star {
  font-size: 20px;
}

.favourite-star.read-only {
  pointer-events: none;
}

.favourite-star:hover {
  cursor: pointer;
  color: #000000;
}

.favourite-star.enabled {
  color: #FFD43B;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #a8a8a8;
}

.favourite-star.enabled:hover {
  color: #c19900;
}

.custom-badge-order-color {
  background-color: #FFA500;
  color: #ffffff;
}

.hidden-id {
  color: white;
  position: absolute;
  top: 2px;
  right: 3px;
  left: auto;
  text-align: right;
  padding: 0 0 0 0;
}

.hidden-id:hover {
  color: #2c2c2c;
}

/* This popover is rendered directly in the body, so it must be a global class */
.order-line-work-order-popover {
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.4);
}
.order-line-work-order-popover .popover-body {
  /*background-color: rgba(68, 140, 116, 0.05);*/
}
